var vmodel = avalon.define({
	$id:'config_basic',
	tab_list:'basic_para',
	timezonevalue:[],    //时区参数值
	timezonestruc:[],    //时区参数结构数据
	timezoneUTC:['(UTC-01:00)','(UTC+00:00)','(UTC+02:00)','(UTC+02:00)','(UTC+03:00)','(UTC+04:00)','(UTC+06:00)','(UTC+07:00)','(UTC+08:00)','(UTC+09:00)','(UTC+10:00)','(UTC+10:00)',
				 '(UTC+12:00)','(UTC+03:30)','(UTC+05:30)','(UTC+09:30)','(UTC-01:00)','(UTC-02:00)','(UTC-03:00)','(UTC-05:00)','(UTC-06:00)','(UTC-07:00)','(UTC-08:00)','(UTC-09:00)',
				 '(UTC-09:00)','(UTC-10:00)','(UTC-11:00)','(UTC+12:00)','(UTC-05:00)','(UTC-05:00)','(UTC-04:00)','(UTC-04:00)','(UTC-03:30)','(UTC+01:00)','(UTC+02:00)','(UTC+05:00)',
				 '(UTC+11:00)'],
    dev_changesiddata : [],     // 存储变化的数据
	dev_set_data:[],     //  存储设置的参数
    dev_setfailure_data:[],     //  存储设置失败的数据
    ac_paras:[],
    batt_paras:[],
    northnet_WiredAttrData:[],
	northnet_WiredValueData:[],
    northnet_show:[],
    changeDevData: function(sidvalue,changekey){
		var changedata = [];
		var changeobject = {};
		changeobject.sid = sidvalue;
		changeobject[changekey] = event.target.value;
		// 参数数据增加action属性，用于二次确认场景中区分是否需要取消释放文件锁
		if (changekey === 'value') {
			changeobject.action = "0";
		}
		changedata.push(changeobject);
		addvalue(this.dev_changesiddata,changedata,"sid");
	},
    jugge_setdata_failure:function(sid, attr_name){
        for (var i in this.dev_setfailure_data) {
			if (sid == this.dev_setfailure_data[i].sid) {
				if (typeof(this.dev_setfailure_data[i][attr_name]) == "undefined") {
					return false;
				}
				if (this.dev_setfailure_data[i][attr_name] == "failure") {
					return true;
				}
			}
		}
		return false;
	},
    get_paraset_rang_value:function(siddata){
		if (typeof(siddata.min_value)=='undefined' || typeof(siddata.max_value)=='undefined') {
			return "";
		}else {
			return siddata.min_value + siddata.unit + " ~~ " + siddata.max_value + siddata.unit;
		}
	},
    changeshow :function(d){
        if(event.path[0].name ==="IP Allocation Mode")
        {
            if(event.path[0].value === "1"){
                vmodel.northnet_show[2].show = 0;
                vmodel.northnet_show[3].show = 0;
                vmodel.northnet_show[4].show = 0;
            }
            else if(event.path[0].value === "0"){
                vmodel.northnet_show[2].show = 1;
                vmodel.northnet_show[3].show = 1;
                vmodel.northnet_show[4].show = 1;
            }
        }
	},
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
		
    }
});

/************************获取时区信息**************************************/
var rqTzList = {data:{objectid:"plat.system_time",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:get_tz_List};
request.addRequest([rqTzList]);

function get_tz_List(d,r)
{
	// 获取 Attr 信息
	var rqTzAttr = {data:{objectid:"plat.system_time",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_tz_Attr};
	request.addRequest([rqTzAttr]);
	// 获取 value 信息
	var rqTzValue = {data:{objectid:"plat.system_time",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:get_tz_Value};
	request.addRequest([rqTzValue]);
}

function get_tz_Attr(d,r){
	addvalue(vmodel.timezonestruc,d.data,"name");
}
function get_tz_Value(d,r){
	addvalue(vmodel.timezonevalue,d.data,"instid");
}

function set_tz_Data(){
	var Rq = {data:{objectid:"plat.system_time",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.timezonevalue)},success:reget_tz_Value};
	request.addRequest([Rq]);
}

function reget_tz_Value(d,r){
	systimeTask.start();
	if (d.result ==="ok"){
	    popupTipsDiv($("#setSuccessalart"), 3000);
	}
	else{
		popupTipsDiv($("#setFailurealart"), 3000);
	}
}
function getSysTime(){
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:setTimeInput};
	request.addRequest([gettimeRq]);
}

function setTimeInput(d, r){
	$("#timeinput").val($.trim($("#systime").text()));
}
function setSysTime(timeStr) {
	if (timeStr == "") {
		alert(mainvalue.i18nkeyword.input_empty);
		return;
	}
	var rslt = systimeTask.settime(timeStr);
	if (rslt == TIME_OVER_MAX) {
		alert(mainvalue.i18nkeyword.system.time_out_range);
		return;
	}
	if (rslt == TIME_NOT_REG) {
		alert(mainvalue.i18nkeyword.system.time_out_range);
		return;
	}
}


/************************获取时区信息结束**************************************/
///////////////////////para_tag:1-告警,2-参数,3-存储属性////////////////////////////////////////////////////
function patch_para_change(paraval,para_tag){
	var set_tag = true;
	var parameter_check = {data:{objectid:"parameter_v2",type:"attr_set",paraval:JSON.stringify(paraval)},success:dev_check_succ};

	showDataInit();
	request.addRequest([parameter_check]);  //先进行参数校验，判断是否需要用户二次确认

	vmodel.dev_changesiddata.clear();

	function dev_check_succ(d,r){
		hideDataInit();
		setTimeout(function(){
			get_ac_para();
            get_batt_para();
		}, 4000);     //  4秒以后再进行系统刷新
		// 配置向导对于超过参数范围的数据直接返回失败，没有像参数设置页面提示错误信息
		if (d.result == "ok" && typeof(d.data[0]['error_para']) === "undefined") {
			if (d.data[0]['change_para'].length > 0) {  //有场景配置参数，需要二次确认，弹出确认框
				var change_para = d.data[0]['change_para'].replace(/\n/g, '<br>');
				$.confirm({
					title: mainvalue.i18nkeyword.para_confirm_title,
					content: mainvalue.i18nkeyword.para_confirm_content + "<br>" + change_para ,
					boxWidth: '30%',
    				useBootstrap: false,
					autoClose: 'close|60000',
					buttons: {
						confirm: {
							text: mainvalue.i18nkeyword.seco_cert.confirm,
							btnClass: 'btn-info',
							action: function () {
								showDataInit();
								var paraval = JSON.parse(r.data.paraval);
								var parameter_set = {data:{objectid:"parameter_v2",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
								request.addRequest([parameter_set]);
							}
						},
						close: {
							text: mainvalue.i18nkeyword.seco_cert.close,
							action: function () {
								showDataInit();
								var paraval = JSON.parse(r.data.paraval);
								paraval[0].action = "1";  //用于二次场景中取消释放文件锁
								var parameter_set = {data:{objectid:"parameter_v2",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
								request.addRequest([parameter_set]);
							}
						}
					}
				});
			}
			else {    //无场景配置参数，且校验通过，直接返回操作成功
				set_tag = true;
				alert(mainvalue.i18nkeyword.operate_successfully);
			}
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
			set_tag = false;
			var paraval = JSON.parse(r.data.paraval);
			var update_req =  {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},success:update_signal_para};
			request.addRequest([update_req]);
		}
	}

	function dev_set_succ(d,r){
		hideDataInit();
		setTimeout(function(){
			get_ac_para();
            get_batt_para();
		}, 4000);     //  4秒以后再进行系统刷新

		if (d.result === "ok") {
			set_tag = true;
			alert(mainvalue.i18nkeyword.operate_successfully);
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
			set_tag = false;
			var paraval = JSON.parse(r.data.paraval);
			var update_req =  {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},success:update_signal_para};
			request.addRequest([update_req]);
		}
	}
	function update_signal_para(d,r) {
		if (set_tag == false) {  //  没有设置成功
			get_failure_data(d.data);
		}
	}
	function get_failure_data(back_data) {
		var datas = [];
		for (var i in vmodel.dev_set_data) {
			if (vmodel.dev_set_data[i].sid == back_data[i].sid) {
				var para = {};
				para['sid'] = vmodel.dev_set_data[i].sid;
				for (var key in vmodel.dev_set_data[i]) {
					if (checkNumber(vmodel.dev_set_data[i][key])) {
						if (Math.abs(vmodel.dev_set_data[i][key] - back_data[i][key]) > 1e-6) {
							para[key] = "failure";
						}
					} else if (vmodel.dev_set_data[i][key] != back_data[i][key]) {  // 如果不相同则说明设置失败
						para[key] = "failure";
					}
				}
				datas.push(para);
			}
		}
		vmodel.dev_setfailure_data = datas;
	}
}

function setDevData(para_tag,attrtype){
	var setData = [];
	var num = [];
	vmodel.dev_setfailure_data.clear();
	for(var i in vmodel.dev_changesiddata){
		var sidData = calSidToDevType(BigInt(vmodel.dev_changesiddata[i].sid));
		setData.push(vmodel.dev_changesiddata[i]);
		num.push(i);
	}
	if(num.length > 0){
		for(var j in num){
			if((j===0)||(num[j]===0)){
				vmodel.dev_changesiddata.splice(num[j],1);
			}
			else{
				vmodel.dev_changesiddata.splice(num[j]-1,1);
			}
		}
	} else{
		return;
	}
	vmodel.dev_set_data = setData;
	if(attrtype === 'attr'){
		patch_para_change(setData,para_tag);
	} else if(attrtype === 'value'){
		patch_para_change(setData,para_tag);
	}
}


/************************交流参数**************************************/
function get_ac_para(obj) {
    var acParaReq = {data:{objectid:"para_guide_ac",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_para_guide_ac_succ};
    request.addRequest([acParaReq]);
}
get_ac_para();


function get_para_guide_ac_succ(d,r) {
    var parameter_array = [];
    if (d.result == "ok") {
		var curr_scene = d.data[0].value;
        for (var i in d.data) {
			if (curr_scene != "1" && d.data[i].sid == "7036944480206849") {
				continue;
			}
			var convention = transformToJson(d.data[i].convention);
			d.data[i].convention = convention;
			parameter_array.push(d.data[i]);
        }
        vmodel.ac_paras = parameter_array;
    }
}
/************************电池参数**************************************/
function get_batt_para(obj) {
    var acParaReq = {data:{objectid:"para_guide_batt",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_para_guide_batt_succ};
    request.addRequest([acParaReq]);
}
get_batt_para();

function get_para_guide_batt_succ(d,r) {
    var parameter_array = [];
    if (d.result == "ok") {
        for (var i in d.data) {
            var convention = transformToJson(d.data[i].convention);
            d.data[i].convention = convention;
            parameter_array.push(d.data[i]);
        }
        vmodel.batt_paras = parameter_array;
    }
}


/************************获取有线网络参数**************************************/
//获取instid
var rqWiredList = {data:{objectid:"plat.wiredconnection",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getNorthnetWiredList};
request.addRequest([rqWiredList]);

function getNorthnetWiredList(d,r)
{
	// 获取 Attr 信息
	var rqWiredAttr = {data:{objectid:"plat.wiredconnection",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:getNorthnetWiredAttr};
	request.addRequest([rqWiredAttr]);
	// 获取value信息
	var rqWiredValue = {data:{objectid:"plat.wiredconnection",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getNorthnetWiredValue};
	request.addRequest([rqWiredValue]);
}

function getNorthnetWiredAttr(d,r){
	addvalue(vmodel.northnet_WiredAttrData,d.data,"name");
    var showattr = [];
    for(var i in d.data){
       var unit = {};
       unit.name = d.data[i].name;
       unit.show = 1;
       showattr.push(unit);
    }
    addvalue(vmodel.northnet_show,showattr,"name");
}

function getNorthnetWiredValue(d,r){
	addvalue(vmodel.northnet_WiredValueData,d.data,"instid");
    if(d.data[0]["IP Allocation Mode"] === "0"){
        vmodel.northnet_show[2].show = 1;
        vmodel.northnet_show[3].show = 1;
        vmodel.northnet_show[4].show = 1;
    }
    else{
        vmodel.northnet_show[2].show = 0;
        vmodel.northnet_show[3].show = 0;
        vmodel.northnet_show[4].show = 0;
    }
}

function setWiredData(){
	var setWiredvalue = {data:{objectid:"plat.wiredconnection",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.northnet_WiredValueData)},success:regetNetconstatusValue};
	request.addRequest([setWiredvalue]);
}

function regetNetconstatusValue(d,r){
	request.addRequest([rqWiredList]);
    if(d.result ==="ok"){
        mainvalue.controlsuccess = "success";
    }
    else{
        mainvalue.controlsuccess = "failure";
    }
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "basic_para" || tab == "ac_para" || tab == "batt_para" || tab == "net_para") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "basic_para";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();