'analog data':
  chinese: '模拟量'
'digital data':
  chinese: '数字量'
'alarm':
  chinese: '告警量'
'control':
  chinese: '控制量'
'parameter':
  chinese: '参数量'
'record data':
  chinese: '记录量'
'stastic data':
  chinese: '统计量'
'device info':
  chinese: '设备信息'
'Factory Config Para':
  chinese: '出厂配置参数'
'User Para':
  chinese: '用户参数'
'Site Config Para':
  chinese: '站点配置参数'
'Site Specific Para':
  chinese: '站点特定参数'
'CSU':
  chinese: 'CSU'
'Pwr.Sys.':
  chinese: '能源系统'
'Power System':
  chinese: '能源系统'
'System AC':
  chinese: '系统交流'
'System AC Input':
  chinese: '系统交流输入'
'AC Dist.':
  chinese: '交流配电'
'AC Distribution':
  chinese: '交流配电'
'Mains':
  chinese: '市电'
'Mains Group':
  chinese: '市电组'
'Rect.':
  chinese: '整流器'
'Rectifier':
  chinese: '整流器'
'Rect.Group':
  chinese: '整流器组'
'Rectifier Group':
  chinese: '整流器组'
'Envi.':
  chinese: '环境'
'System Running Environment':
  chinese: '系统运行环境'
'DC Dist.':
  chinese: '直流配电'
'DC Distribution':
  chinese: '直流配电'
'Batt.':
  chinese: '电池'
'Battery':
  chinese: '电池'
'Batt.Group':
  chinese: '电池组'
'Battery Group':
  chinese: '电池组'
'DC Load':
  chinese: '直流负载'
'VRLA Batt.':
  chinese: '普通铅酸'
'VRLA Battery':
  chinese: '普通铅酸电池'
'Solar':
  chinese: '太阳能'
'Solar Energy':
  chinese: '太阳能'
'PU':
  chinese: '太阳能模块'
'Power Unit':
  chinese: '太阳能模块'
'FC Batt.':
  chinese: '快充电池'
'FC Battery':
  chinese: '快充电池'
'HT Batt.':
  chinese: '高温电池'
'HT Battery':
  chinese: '高温电池'
'DCYC Batt.':
  chinese: '循环电池'
'DCYC Battery':
  chinese: '深循环电池'
'SE Batt.':
  chinese: '储能电池'
'SE Battery':
  chinese: '储能电池'
'VRLA25 Batt.':
  chinese: '铅酸电池25'
'VRLA25 Battery':
  chinese: '铅酸电池25'
'FB15 Batt.':
  chinese: 'FB15电池'
'FB15 Battery':
  chinese: 'FB15电池'
'FBBMS':
  chinese: 'FBBMS'
'DG Group':
  chinese: '油机组'
'Diesel Generator Group':
  chinese: '油机组'
'DG':
  chinese: '油机'
'Diesel Generator':
  chinese: '油机'
'GCP':
  chinese: '油机控制屏'
'LLS':
  chinese: '液位传感器'
'Liquid Level Sensor':
  chinese: '液位传感器'
'ATS':
  chinese: 'ATS'
'ATSCP':
  chinese: 'ATS控制器'
'ACEM':
  chinese: '交流电表'
'FB100B3':
  chinese: 'FB100B3电池'
'LLS Group':
  chinese: '液位传感器组'
'Liquid Level Sensor Group':
  chinese: '液位传感器组'
'NFBBMS':
  chinese: 'NFBBMS'
'SDDU':
  chinese: '智能直流配电单元'
'Smart DC Distribution Unit':
  chinese: '智能直流配电单元'
'SPCU':
  chinese: 'SPCU'
'SPU':
  chinese: 'SPU'
'CPU Usage Rate':
  chinese: 'CPU利用率'
'Memory Usage Rate':
  chinese: '内存利用率'
'Mem.Usage Rate':
  chinese: '内存利用率'
'Normal':
  chinese: '正常'
'Fault':
  chinese: '异常'
'CSU Fault':
  chinese: 'CSU故障'
'Input Relay State':
  chinese: '输入干接点状态'
'Input Rly.State':
  chinese: '输入干接点状态'
'Time':
  chinese: '时间'
'Alarm Change':
  chinese: '告警变化'
'Batt Stat Change':
  chinese: '电池状态变化'
'Gen Start and Stop':
  chinese: '油机启停'
'Date Change':
  chinese: '日期变化'
'History Data Save Cause':
  chinese: '历史数据保存原因'
'HisData Sav.Cause':
  chinese: '历史数据保存原因'
'Power Config State':
  chinese: '电源配置状态'
'Compete':
  chinese: '竞争'
'Master':
  chinese: '主机'
'Slave':
  chinese: '从机'
'Csu Master Slave Status':
  chinese: '主从机状态'
'Csu M.S Status':
  chinese: '主从机状态'
'Null':
  chinese: '无'
'Exist':
  chinese: '有'
'North Connection Status':
  chinese: '北向连接状态'
'N.C Status':
  chinese: '北向连接状态'
'Reset':
  chinese: '恢复'
'Set':
  chinese: '动作'
'Output Relay Control State':
  chinese: '输出干接点控制状态'
'Output Rly.Ctr.State':
  chinese: '输出干接点控制状态'
'Disable':
  chinese: '禁止'
'Enable':
  chinese: '允许'
'CSU Reboot Flag':
  chinese: 'CSU复位标志'
'CSU Send Configure Flag':
  chinese: 'CSU发送设备配置标志'
'CSU Send Conf.Flag':
  chinese: 'CSU发送设备配置标志'
'CSU Clear Heartbeat Timeout Count Flag':
  chinese: 'CSU清除心跳超时计数标志'
'CSU C.H.T.C.Flag':
  chinese: 'CSU清除心跳超时计数标志'
'Not Exist':
  chinese: '不在位'
'Is Exist':
  chinese: '在位'
'UIB Exist State':
  chinese: 'UIB在位状态'
'Critical':
  chinese: '严重'
'Major':
  chinese: '主要'
'Minor':
  chinese: '次要'
'Warning':
  chinese: '警告'
'All Alarm Blocked':
  chinese: '禁止所有告警'
'All Alm.Blocked':
  chinese: '禁止所有告警'
'Mask':
  chinese: '屏蔽'
'MAC Address Not Set':
  chinese: 'MAC地址未设置'
'MAC Not Set':
  chinese: 'MAC地址未设置'
'Input Relay Alarm':
  chinese: '输入干接点告警'
'Input Rly.Alm':
  chinese: '输入干接点告警'
'CPU Usage Rate High Alarm':
  chinese: 'CPU利用率高告警'
'CPU Usage R.H.':
  chinese: 'CPU利用率高'
'Memory Usage Rate High Alarm':
  chinese: '内存利用率高告警'
'Mem.Usage R.H.':
  chinese: '内存利用率高'
'UIB Communication Fail':
  chinese: 'UIB通讯断'
'UIB Comm Fail':
  chinese: 'UIB通讯断'
'IDDB Communication Fail':
  chinese: 'IDDB通讯断'
'IDDB Comm Fail':
  chinese: 'IDDB通讯断'
'History Alarm Full':
  chinese: '历史告警满'
'HisAlm.Full':
  chinese: '历史告警满'
'CSU Fault Alarm':
  chinese: 'CSU故障告警'
'CSU Fault Alm.':
  chinese: 'CSU故障告警'
'Power Config Abnormal':
  chinese: '电源配置异常'
'Power Config Abr.':
  chinese: '电源配置异常'
'Total Alarm Disable':
  chinese: '禁止所有告警'
'Total Alm. Dis':
  chinese: '禁止所有告警'
'Total Alarm Enable':
  chinese: '允许所有告警'
'Total Alm. En':
  chinese: '允许所有告警'
'CAN Bus Device Statistic':
  chinese: 'CAN总线设备统计'
'CAN Dev.Stat':
  chinese: 'CAN设备统计'
'RS485 Bus Device Statistic':
  chinese: 'RS485总线设备统计'
'RS485 Dev.Stat':
  chinese: 'RS485设备统计'
'Open SSH':
  chinese: '开启SSH'
'Close SSH':
  chinese: '关闭SSH'
'Open WEB':
  chinese: '开启WEB'
'Close WEB':
  chinese: '关闭WEB'
'Open SFTP':
  chinese: '开启SFTP'
'Close SFTP':
  chinese: '关闭SFTP'
'iEnergy SSH Security Code Reset':
  chinese: '能源网管SSH口令重置'
'iEnergy SSH Code Reset':
  chinese: '能源网管SSH口令重置'
'Wireless Module Restart':
  chinese: '无线模块重启'
'UIB Statistic':
  chinese: 'UIB统计'
'UIB Stat.':
  chinese: 'UIB统计'
'Log Save Interval':
  chinese: '日志保存间隔'
'Log Save Inter':
  chinese: '日志保存间隔'
'CPU Usage Rate High Threshold':
  chinese: 'CPU利用率高阈值'
'CPU Usage H.Thre.':
  chinese: 'CPU高阈值'
'Memory Usage Rate High Threshold':
  chinese: '内存利用率高阈值'
'Mem.Usage H.Thre.':
  chinese: '内存高阈值'
'Positive Alarm Enable':
  chinese: 'CSU主动告警使能'
'Pos Alm Enbale':
  chinese: '主动告警使能'
'Hisdata Save Interval':
  chinese: '历史数据保存间隔'
'Hisdata Inter':
  chinese: '历史数据间隔'
'Level1 Alarm Report Enabled':
  chinese: '一级告警主动上报使能'
'Level1 Alm.Rep.En':
  chinese: '一级告警主动上报使能'
'Level2 Alarm Report Enabled':
  chinese: '二级告警主动上报使能'
'Level2 Alm.Rep.En':
  chinese: '二级告警主动上报使能'
'Level3 Alarm Report Enabled':
  chinese: '三级告警主动上报使能'
'Level3 Alm.Rep.En':
  chinese: '三级告警主动上报使能'
'Input Relay Current Relative Signal':
  chinese: '输入干接点当前关联信号'
'Input Rly.Curr.Rela.Signal':
  chinese: '输入干接点当前关联信号'
'Input Relay Alias':
  chinese: '输入干接点别名'
'Close':
  chinese: '闭合'
'Break':
  chinese: '断开'
'Input Relay Alarm Or Abnormal State':
  chinese: '输入干接点告警或异常状态'
'Input Rly.Abno.State':
  chinese: '输入干接点告警或异常状态'
'Output Relay Current Relative Signal':
  chinese: '输出干接点当前关联信号'
'Output Rly.Curr.Rela.Signal':
  chinese: '输出干接点当前关联信号'
'Output Relay Default State':
  chinese: '输出干接点默认状态'
'Output Rly.Defa.State':
  chinese: '输出干接点默认状态'
'Temperature AI Current Relative Signal':
  chinese: '温度AI当前关联信号'
'Temp AI.Curr.Rela.Signal':
  chinese: '温度AI当前关联信号'
'Temperature AI Alias':
  chinese: '温度AI别名'
'Temp AI Alias':
  chinese: '温度AI别名'
'Serial Number':
  chinese: '序列号'
'SN':
  chinese: '序列号'
'CSU Software Platform Version':
  chinese: 'CSU软件所属平台版本'
'Platform Version':
  chinese: '平台版本'
'SoftWare Name':
  chinese: '软件名称'
'Boot Version':
  chinese: 'BOOT版本'
'Kernel Version':
  chinese: '内核版本'
'Software Version':
  chinese: '软件版本'
'Manufactory Name':
  chinese: '厂家名称'
'Software Release Date':
  chinese: '软件发布日期'
'Soft.Release Date':
  chinese: '软件发布日期'
'Platform Release Date':
  chinese: '平台发布日期'
'Plat.Release Date':
  chinese: '平台发布日期'
'UIB Version':
  chinese: 'UIB版本信息'
'IDDB Version':
  chinese: 'IDDB版本信息'
'MAC Address':
  chinese: 'MAC地址'
'MAC addr.':
  chinese: 'MAC地址'
'UIB Release Date':
  chinese: 'UIB发布日期'
'IDDB Release Date':
  chinese: 'IDDB发布日期'
'UIB Barcodes':
  chinese: 'UIB条码'
'IDDB Barcodes':
  chinese: 'IDDB条码'
'UIB Type':
  chinese: 'UIB类型'
'Input Relay Channel Name':
  chinese: '输入干接点通道名称'
'Input Rly.Chan.Name':
  chinese: '输入干接点通道名称'
'Input Relay Relative Signal':
  chinese: '输入干接点关联信号'
'Input Rly.Rela.Signal':
  chinese: '输入干接点关联信号'
'Output Relay Channel Name':
  chinese: '输出干接点通道名称'
'Output Rly.Chan.Name':
  chinese: '输出干接点通道名称'
'Output Relay Relative Signal':
  chinese: '输出干接点关联信号'
'Output Rly.Rela.Signal':
  chinese: '输出干接点关联信号'
'Temperature AI Channel Name':
  chinese: '温度AI通道名称'
'Temp AI.Chan.Name':
  chinese: '温度AI通道名称'
'Temperature AI Relative Signal':
  chinese: '温度AI关联信号'
'Temp AI.Rela.Signal':
  chinese: '温度AI关联信号'
'DC Voltage':
  chinese: '直流电压'
'Load Total Current':
  chinese: '负载总电流'
'Load Current':
  chinese: '负载电流'
'DC Load Total Power':
  chinese: '直流负载总功率'
'Load Total Pwr.':
  chinese: '负载总功率'
'Battery Total Current':
  chinese: '电池组总电流'
'Batt.Total Curr.':
  chinese: '电池总电流'
'Battery Status Duration':
  chinese: '电池状态持续时间'
'Batt.Stat.Dura.':
  chinese: '电池状态持续时间'
'SMR Total Output Current':
  chinese: '整流器总输出电流'
'Smr Total Curr':
  chinese: '整流器总电流'
'SMR Setting Voltage':
  chinese: '整流器当前输出设定电压'
'SMR Set Volt':
  chinese: '整流器设定电压'
'SMR Setting Current Limit Rate':
  chinese: '整流器设定限流点比率'
'SMR CL Rate':
  chinese: '整流器限流点比率'
'SMR Working Number':
  chinese: '工作整流器数量'
'SMR Work Num':
  chinese: '工作整流器数量'
'SMR Online Number':
  chinese: '在线整流器数量'
'SMR Online Num':
  chinese: '在线整流器数量'
'Environment Temperature':
  chinese: '环境温度'
'Environment Temp':
  chinese: '环境温度'
'System AC Voltage':
  chinese: '系统交流电压'
'System AC Volt.':
  chinese: '系统交流电压'
'System AC Current':
  chinese: '系统交流电流'
'System AC Curr.':
  chinese: '系统交流电流'
'DC Reserve Input Current':
  chinese: '直流备用输入电流'
'DC Res.Curr.':
  chinese: '直流备用电流'
'PU Total Output Current':
  chinese: 'PU输出总电流'
'PU Total Out.Curr.':
  chinese: 'PU输出总电流'
'PU Total Output Power':
  chinese: 'PU输出总功率'
'PU Total Out.Pwr.':
  chinese: 'PU输出总功率'
'PU Setting Voltage':
  chinese: 'PU当前输出设定电压'
'PU Set.Volt.':
  chinese: 'PU设定电压'
'DC Output Current':
  chinese: '直流输出电流'
'DC Out.Curr.':
  chinese: '直流输出电流'
'DC Output Power':
  chinese: '直流输出功率'
'DC Out.Power':
  chinese: '直流输出功率'
'Float':
  chinese: '浮充'
'Equal':
  chinese: '均充'
'Test':
  chinese: '测试'
'PowerOff':
  chinese: '系统停电'
'Detect':
  chinese: '检测'
'Transition':
  chinese: '过渡'
'Charge':
  chinese: '充电'
'Battery Management Status':
  chinese: '电池管理状态'
'Batt Manag Status':
  chinese: '电池管理状态'
'DG1':
  chinese: '油机1'
'DG2':
  chinese: '油机2'
'AC1':
  chinese: '市电1'
'AC2':
  chinese: '市电2'
'AC Power Source':
  chinese: '交流供电状态'
'AC Source':
  chinese: '交流供电状态'
'Safe':
  chinese: '安全'
'Free Mode':
  chinese: '自由'
'Auto NonSave':
  chinese: '自动非节能'
'Auto Save':
  chinese: '自动节能'
'Temp NonSave':
  chinese: '暂时非节能'
'Perm NonSave':
  chinese: '永久非节能'
'Manual Detect':
  chinese: '人工维护检测'
'AC Save Energy Status':
  chinese: '交流节能状态'
'AC Save Eng.Stu.':
  chinese: '交流节能状态'
'Off':
  chinese: '关闭'
'On':
  chinese: '开启'
'Emergency Light Status':
  chinese: '应急照明状态'
'Emerg.Light Status':
  chinese: '应急照明状态'
'PV Power Status':
  chinese: '光伏供电状态'
'PV Status':
  chinese: '光伏供电状态'
'PLC Digital Output Status':
  chinese: 'PLC自定义状态'
'PLC.DO.Stu':
  chinese: 'PLC自定义状态'
'System Expansion Alarm':
  chinese: '系统扩容告警'
'Sys.Expan.Alm':
  chinese: '系统扩容告警'
'System OverLoad Alarm':
  chinese: '系统过载告警'
'Sys.OverL.Alm':
  chinese: '系统过载告警'
'Common Alarm':
  chinese: '总告警'
'Module Slot Fault Alarm':
  chinese: '模块槽位异常告警'
'Mod.Slot Fault':
  chinese: '模块槽位异常告警'
'AC Input Scenario Fault Alarm':
  chinese: '交流输入场景配置错误告警'
'Ac.Scen.Fault':
  chinese: '交流场景配置错误'
'PLC Custom Alarm':
  chinese: 'PLC自定义告警'
'PLC.Cuz.Alm':
  chinese: 'PLC自定义告警'
'Inverter Alarm':
  chinese: '逆变器告警'
'Inverter Alm':
  chinese: '逆变器告警'
'Delete All Statistics':
  chinese: '删除所有统计量'
'Del.All Stat.':
  chinese: '删除所有统计量'
'SDDU Device Statistic':
  chinese: 'SDDU设备统计'
'SDDU Dev.Stat.':
  chinese: 'SDDU设备统计'
'System OverLoad Alarm Threshold':
  chinese: '系统过载告警阈值'
'Sys.OverLoad Thre':
  chinese: '系统过载阈值'
'VRLA Batt':
  chinese: '普通铅酸电池'
'Cycling Batt':
  chinese: '循环电池'
'Fast Charge Batt':
  chinese: '快充电池'
'Store Energy Batt':
  chinese: '储能电池'
'Battery Type':
  chinese: '电池类型'
'Mains and DG':
  chinese: '油电'
'Only DG':
  chinese: '纯油机'
'None':
  chinese: '无'
'AC Input Scenario':
  chinese: '交流输入场景'
'AC In.Scenario':
  chinese: '交流输入场景'
'System Expansion Threshold':
  chinese: '系统扩容告警阈值'
'Sys.Expan.Thre':
  chinese: '系统扩容阈值'
'Site Name':
  chinese: '站点名称'
'Env Survey Enable':
  chinese: '环境监测使能'
'DC Reserve Input Configuration':
  chinese: '直流备用输入配置'
'DC Res.In.Config.':
  chinese: '直流备用输入配置'
'Only VRLA Batt':
  chinese: '纯铅酸'
'Smart Li Batt':
  chinese: '智能锂电'
'VRLA&Li Batt':
  chinese: '铅酸&锂电混用'
'Normal Li Batt':
  chinese: '常规锂电池'
'Battery Config':
  chinese: '电池配置'
'FB15':
  chinese: 'FB15'
'Li Battery Type':
  chinese: '锂电类型'
'Li Batt.Type':
  chinese: '锂电类型'
'Vrla Battery Type':
  chinese: '铅酸类型'
'Vrla Batt.Type':
  chinese: '铅酸类型'
'Longitude':
  chinese: '经度'
'Latitude':
  chinese: '纬度'
'Altitude':
  chinese: '海拔高度'
'MAINS Prior':
  chinese: '市电优先'
'Batt.Prior':
  chinese: '电池优先'
'Set MAINS and Battery Priority':
  chinese: '市电电池优先级设置'
'MAINS Batt.Prior.':
  chinese: '市电电池优先级'
'SMR Energy Save Off Current Max':
  chinese: '整流器节能关闭最大电流'
'Save Off Curr.Max.':
  chinese: '节能关闭最大电流'
'Address Street':
  chinese: '街道'
'Addr.Street':
  chinese: '街道'
'Address City':
  chinese: '城市'
'Addr.City':
  chinese: '城市'
'Address Administrative Region':
  chinese: '行政区'
'Addr.Admin.Region':
  chinese: '行政区'
'NFB15':
  chinese: 'NFB15'
'Normal Li Battery Type':
  chinese: '常规锂电类型'
'Nor.Li Batt.Type':
  chinese: '常规锂电类型'
'Smart Li Battery Type':
  chinese: '智能锂电类型'
'Smart Li Batt.Type':
  chinese: '智能锂电类型'
'Site IDentity':
  chinese: '站点编号'
'Site ID':
  chinese: '站点编号'
'Room IDentity':
  chinese: '机房编号'
'Room ID':
  chinese: '机房编号'
'Room Name':
  chinese: '机房名称'
'Auto':
  chinese: '自动'
'Para Change':
  chinese: '参数变化'
'Mains Start and Stop':
  chinese: '市电启停'
'Gen Run 10min':
  chinese: '油机运行10分钟'
'Gen Run 30min':
  chinese: '油机运行30分钟'
'Gen Run 60min':
  chinese: '油机运行60分钟'
'Batt Testing':
  chinese: '电池测试'
'Batt Detect':
  chinese: '电池检测'
'Historical data save Cause':
  chinese: '历史数据保存原因'
'Real Time Alarm Occurrence Time':
  chinese: '实时告警时间'
'Real Alm.Time':
  chinese: '实时告警时间'
'History Alarm Time':
  chinese: '历史告警时间'
'His.Alm.Time':
  chinese: '历史告警时间'
'Photovoltaic Cell':
  chinese: '光伏'
'PV':
  chinese: '光伏'
'Wind Turbine':
  chinese: '风机'
'Maximum Peak Time':
  chinese: '最大极值时间'
'Max.Peak.Time':
  chinese: '最大极值时间'
'Minimum Peak Time':
  chinese: '最小极值时间'
'Min.Peak.Time':
  chinese: '最小极值时间'
'History Data Record Time':
  chinese: '历史数据记录时间'
'Data Rec.Time':
  chinese: '数据记录时间'
'control Record Time':
  chinese: '操作记录时间'
'Ctrl.Rec.Time':
  chinese: '操作记录时间'
'History Event Record Time':
  chinese: '历史事件记录时间'
'Event Rec.Time':
  chinese: '事件记录时间'
'Maintain Record Time':
  chinese: '维护记录时间'
'Maint.Rec.Time':
  chinese: '维护记录时间'
'Log Record Time':
  chinese: '日志记录时间'
'Energy Statistics':
  chinese: '电量统计记录'
'Energy Stat':
  chinese: '电量统计记录'
'Peak Statistics':
  chinese: '极值统计记录'
'Peak Stat':
  chinese: '极值统计记录'
'History Record Serial Number':
  chinese: '历史记录序号'
'His.Recd.SN':
  chinese: '记录序号'
'Event Duration':
  chinese: '事件维持时间'
'Count Statistics':
  chinese: '次数统计记录'
'Count Stat':
  chinese: '次数统计记录'
'Operator':
  chinese: '操作者'
'Operation Content':
  chinese: '操作内容'
'DC Reserve Input Power':
  chinese: '直流备用输入发电量'
'DC Res.Power':
  chinese: '直流备用发电量'
'DC Reserve Input Work Duration':
  chinese: '直流备用输入工作时间'
'DC Res.Work Dura.':
  chinese: '直流备用工作时间'
'Total Input Energy':
  chinese: '输入总电量'
'Total Output Energy':
  chinese: '输出总电量'
'System Name':
  chinese: '系统名称'
'Phase Voltage UL1':
  chinese: '相电压UL1'
'UL1':
  chinese: '相电压UL1'
'Phase Voltage UL2':
  chinese: '相电压UL2'
'UL2':
  chinese: '相电压UL2'
'Phase Voltage UL3':
  chinese: '相电压UL3'
'UL3':
  chinese: '相电压UL3'
'Line Voltage UL12':
  chinese: '线电压UL12'
'UL12':
  chinese: '线电压UL12'
'Line Voltage UL23':
  chinese: '线电压UL23'
'UL23':
  chinese: '线电压UL23'
'Line Voltage UL31':
  chinese: '线电压UL31'
'UL31':
  chinese: '线电压UL31'
'Phase Current IL1':
  chinese: '相电流IL1'
'IL1':
  chinese: '相电流IL1'
'Phase Current IL2':
  chinese: '相电流IL2'
'IL2':
  chinese: '相电流IL2'
'Phase Current IL3':
  chinese: '相电流IL3'
'IL3':
  chinese: '相电流IL3'
'Line Current IL12':
  chinese: '线电流IL12'
'IL12':
  chinese: '线电流IL12'
'Line Current IL23':
  chinese: '线电流IL23'
'IL23':
  chinese: '线电流IL23'
'Line Current IL31':
  chinese: '线电流IL31'
'IL31':
  chinese: '线电流IL31'
'AC Power Off':
  chinese: '交流停电'
'AC Phase Lack':
  chinese: '交流缺相'
'AC Voltage Low':
  chinese: '交流电压低'
'AC Volt.Low':
  chinese: '交流电压低'
'AC Voltage High':
  chinese: '交流电压高'
'AC Volt.High':
  chinese: '交流电压高'
'AC Current High':
  chinese: '交流电流高'
'AC Curr.High':
  chinese: '交流电流高'
'AC Voltage Imbalance':
  chinese: '交流电压不平衡'
'AC Volt.Imbala':
  chinese: '交流电压不平衡'
'AC Voltage Too Low':
  chinese: '交流电压过低'
'AC Volt.T.Low':
  chinese: '交流电压过低'
'AC Voltage Too High':
  chinese: '交流电压过高'
'AC Volt.T.High':
  chinese: '交流电压过高'
'AC Input Abnormal':
  chinese: '交流输入异常'
'AC In.Abr.':
  chinese: '交流输入异常'
'ACEM Loop Config Abnormal':
  chinese: '交流电表回路配置异常'
'AC Current High Threshold':
  chinese: '交流电流高阈值'
'AC Curr.High Thre':
  chinese: '交流电流高阈值'
'L1L2L3N-220V':
  chinese: 'L1L2L3N-220V'
'L1L2L3-110V':
  chinese: 'L1L2L3-110V'
'L1N-220V':
  chinese: 'L1N-220V'
'L1L2-110V':
  chinese: 'L1L2-110V'
'L1N-110V':
  chinese: 'L1N-110V'
'AC Power Supply':
  chinese: '交流制式'
'AC Voltage High Threshold':
  chinese: '交流电压高阈值'
'AC Volt.H.Thre':
  chinese: '交流电压高阈值'
'AC Voltage Low Threshold':
  chinese: '交流电压低阈值'
'AC Volt.L.Thre':
  chinese: '交流电压低阈值'
'AC Voltage Imbalance Threshold':
  chinese: '交流电压不平衡阈值'
'Mains Config':
  chinese: '市电配置'
'AC Voltage Too High Threshold':
  chinese: '交流电压过高阈值'
'AC Volt.T.H.Thre':
  chinese: '交流电压过高值'
'AC Voltage Too Low Threshold':
  chinese: '交流电压过低阈值'
'AC Volt.T.L.Thre':
  chinese: '交流电压过低值'
'Diesel Generator Power':
  chinese: '柴油发电机额定功率'
'DG Power':
  chinese: '油机额定功率'
'Single Phase AC Power Supply':
  chinese: '单相交流制式'
'Three Phase AC Power Supply':
  chinese: '三相交流制式'
'Uncontrolled':
  chinese: '非受控'
'Controlled':
  chinese: '受控'
'Diesel Generator Application Scenario':
  chinese: '柴油发电机应用场景'
'DG Apl.Scenario':
  chinese: '油机应用场景'
'AC Voltage':
  chinese: '交流电压'
'AC Current':
  chinese: '交流电流'
'close':
  chinese: '闭合'
'break':
  chinese: '断开'
'AC Input Switch Status':
  chinese: '交流输入空开状态'
'AC Input Switch':
  chinese: '交流输入空开'
'AC Output Switch Status':
  chinese: '交流输出空开状态'
'AC Out.Switch':
  chinese: '交流输出空开'
'AC SPD Status':
  chinese: '交流防雷器状态'
'AC SPD':
  chinese: '交流防雷器'
'L1':
  chinese: 'L1'
'L2':
  chinese: 'L2'
'L3':
  chinese: 'L3'
'L1-L2':
  chinese: 'L1-L2'
'L3-L1':
  chinese: 'L3-L1'
'L2-L3':
  chinese: 'L2-L3'
'AC Connect Mode':
  chinese: '交流接线方式'
'AC Input Switch Off':
  chinese: '交流输入空开断'
'AC In.Switch Off':
  chinese: '交流输入空开断'
'AC SPD Abnormal':
  chinese: '交流防雷器异常'
'AC SPD Abr':
  chinese: '交流防雷器异常'
'AC Output Switch Off':
  chinese: '交流输出空开断'
'AC Out.SW Off':
  chinese: '交流输出空开断'
'AC Voltage Offset':
  chinese: '交流电压零点'
'AC Volt.Offset':
  chinese: '交流电压零点'
'AC Voltage Slope':
  chinese: '交流电压斜率'
'AC Volt.Slope':
  chinese: '交流电压斜率'
'AC Current Offset':
  chinese: '交流电流零点'
'AC Curr.Offset':
  chinese: '交流电流零点'
'AC Current Slope':
  chinese: '交流电流斜率'
'AC Curr.Slope':
  chinese: '交流电流斜率'
'MAINS Phase Voltage':
  chinese: '市电相电压'
'Mns.Ph.Volt':
  chinese: '市电相电压'
'MAINS Line Voltage':
  chinese: '市电线电压'
'Mns.Line Volt':
  chinese: '市电线电压'
'MAINS Phase Current':
  chinese: '市电相电流'
'Mns.Ph.Curr':
  chinese: '市电相电流'
'MAINS Active Power':
  chinese: '市电有功功率'
'Mns.Act.Pwr':
  chinese: '市电有功功率'
'MAINS Reactive Power':
  chinese: '市电无功功率'
'Mns.Rct.Pwr':
  chinese: '市电无功功率'
'MAINS Apparent Power':
  chinese: '市电视在功率'
'Mns.App.Pwr':
  chinese: '市电视在功率'
'MAINS Power Factor':
  chinese: '市电功率因数'
'Mns.PF':
  chinese: '市电功率因数'
'MAINS Output Frequency':
  chinese: '市电输出频率'
'Mns.Out.Freq':
  chinese: '市电输出频率'
'MAINS Total Active Power':
  chinese: '市电总有功功率'
'Mns.Tot.Act.Pwr':
  chinese: '市电总有功功率'
'MAINS Total Reactive Power':
  chinese: '市电总无功功率'
'Mns.Tot.Rct.Pwr':
  chinese: '市电总无功功率'
'MAINS Total Apparent Power':
  chinese: '市电总视在功率'
'Mns.Tot.App.Pwr':
  chinese: '市电总视在功率'
'MAINS Average Power Factor':
  chinese: '市电平均功率因数'
'Mns.Avr.PF':
  chinese: '市电平均功率因数'
'MAINS Status':
  chinese: '市电输入状态'
'Mns.Status':
  chinese: '市电输入状态'
'MAINS High Frequency':
  chinese: '市电频率高'
'Mns.High Freq':
  chinese: '市电频率高'
'MAINS Phase Voltage Imbalance':
  chinese: '市电相电压不平衡'
'Mns.Volt.Imba':
  chinese: '市电相电压不平衡'
'MAINS Phase Sequence Error':
  chinese: '市电相序错误'
'Mns.Ph.Seq.Err':
  chinese: '市电相序错误'
'MAINS Failure':
  chinese: '市电停电'
'Mns.Failure':
  chinese: '市电停电'
'MAINS Phase Voltage High':
  chinese: '市电电压过压'
'Mns.Pha.Volt.H.':
  chinese: '市电电压过压'
'MAINS Phase Voltage Low':
  chinese: '市电电压欠压'
'Mns.Pha.Volt.L.':
  chinese: '市电电压欠压'
'MAINS Phase Current High':
  chinese: '市电相电流高'
'Mns.Pha.Curr.H.':
  chinese: '市电相电流高'
'MAINS Low Frequency':
  chinese: '市电频率低'
'Mns.Low Freq':
  chinese: '市电频率低'
'Bad Grid':
  chinese: '差'
'Good Grid':
  chinese: '好'
'MAINS Condition':
  chinese: '市电状况'
'Mns.Con':
  chinese: '市电状况'
'MAINS Failure Time':
  chinese: '市电停电历史时间'
'Mns.Fail.Time':
  chinese: '市电停电历史时间'
'MAINS Work Time':
  chinese: '市电工作历史时间'
'Mns.Work Time':
  chinese: '市电工作历史时间'
'Local':
  chinese: '前台'
'SC':
  chinese: '后台'
'Low Voltage':
  chinese: '电压低'
'Low SOC':
  chinese: 'SOC低'
'Batt Time':
  chinese: '电池放电时间'
'Full Charge':
  chinese: '长充'
'Period On':
  chinese: '定时开启'
'MAINS regain':
  chinese: '市电来电'
'Green Energy Short':
  chinese: '绿色能源不足'
'Sys Abnormal':
  chinese: '系统异常'
'Gen Mode Switch':
  chinese: '油机模式切换'
'MAINS Start Reason':
  chinese: '市电启动原因'
'MAINS.Start Rsn':
  chinese: '市电启动原因'
'High Voltage':
  chinese: '电压高'
'High SOC':
  chinese: 'SOC高'
'Batt Curr':
  chinese: '电池电流'
'Max Time':
  chinese: '最大时间'
'Period Off':
  chinese: '定时关闭'
'MAINS Fail':
  chinese: '市电停电'
'Green Energ Enough':
  chinese: '绿色能源充足'
'MAINS Stop Reason':
  chinese: '市电停止原因'
'MAINS.Stop Rsn':
  chinese: '市电停止原因'
'Power Failure Times Statistics':
  chinese: '停电次数统计'
'P.F.Times Stat':
  chinese: '停电次数统计'
'January':
  chinese: '1月'
'February':
  chinese: '2月'
'March':
  chinese: '3月'
'April':
  chinese: '4月'
'May':
  chinese: '5月'
'June':
  chinese: '6月'
'July':
  chinese: '7月'
'August':
  chinese: '8月'
'September':
  chinese: '9月'
'October':
  chinese: '10月'
'November':
  chinese: '11月'
'December':
  chinese: '12月'
'Period for Power Failure Times Statistics':
  chinese: '停电次数统计时段'
'P.F.Times Period':
  chinese: '停电次数统计时段'
'Power Failure Time Statistics':
  chinese: '停电时间统计'
'P.F.Time Stat':
  chinese: '停电时间统计'
'Period for Power Failure Time Statistics':
  chinese: '停电时间统计时段'
'P.F.Time Period':
  chinese: '停电时间统计时段'
'MAINS Initial Energy Production':
  chinese: '市电起始供电量'
'Mns.Init.Eng.Prod':
  chinese: '市电起始供电量'
'MAINS Final Energy Production':
  chinese: '市电终止供电量'
'Mns.Final Eng.Prod':
  chinese: '市电终止供电量'
'MAINS Failure Duration':
  chinese: '市电停电时间'
'Mns.Fail.Dura':
  chinese: '市电停电时间'
'MAINS Work Duration':
  chinese: '市电工作时间'
'Mns.Work Dura':
  chinese: '市电工作时间'
'MAINS Energy Production':
  chinese: '市电供电量'
'Mns.Eng.Prod':
  chinese: '市电供电量'
'Power Failure Times':
  chinese: '停电次数'
'P.F. Times':
  chinese: '停电次数'
'MAINS AC Energy Production':
  chinese: '市电交流发电量'
'Mns.AC Eng.Prod':
  chinese: '市电交流发电量'
'Acin Power Limit Early Warning':
  chinese: '交流输入限功率预警'
'Ac Power Lim.EW.':
  chinese: '交流限功率预警'
'Acin Power Limit alarm':
  chinese: '交流输入限功率告警'
'Ac Power Lim.Alm':
  chinese: '交流限功率告警'
'MAINS Rated Active Power':
  chinese: '市电额定有功功率'
'MAINS Rat.Act.Pwr':
  chinese: '市电额定有功功率'
'MAINS Derate Factor':
  chinese: '市电降额系数'
'Acin Power Limit Early Warning Threshold':
  chinese: '交流输入限功率预警阈值'
'Ac Power Limit EW.Thre.':
  chinese: '交流限功率预警阈值'
'Acin Power Limit alm Threshold':
  chinese: '交流输入限功率告警阈值'
'Ac Power Limit alm Threshold':
  chinese: '交流限功率告警阈值'
'MAINS Start Voltage Enable':
  chinese: '市电启动电压使能'
'MAINS Start Vol.En.':
  chinese: '市电启动电压使能'
'MAINS Start SOC Enable':
  chinese: '市电启动SOC使能'
'MAINS Start SOC En.':
  chinese: '市电启动SOC使能'
'MAINS Start Time Enable':
  chinese: '市电启动时间使能'
'MAINS Start Time En.':
  chinese: '市电启动时间使能'
'MAINS Start System Abnormal Enable':
  chinese: '市电启动系统异常使能'
'MAINS Start Sys.Abr.En.':
  chinese: '市电启动异常使能'
'MAINS Start Voltage Threshold':
  chinese: '市电启动电压阈值'
'Mns.Start Volt.':
  chinese: '市电启动电压'
'MAINS Start SOC Threshold':
  chinese: '市电启动SOC阈值'
'Mns.Start SOC':
  chinese: '市电启动SOC'
'MAINS Start Discharge Duration Threshold':
  chinese: '市电启动放电时间阈值'
'Mns.St.Dis.Dura.':
  chinese: '市电启动放电时间'
'MAINS Stop Voltage Enable':
  chinese: '市电停止电压使能'
'MAINS Stop Vol.En.':
  chinese: '市电停止电压使能'
'MAINS Stop SOC Enable':
  chinese: '市电停止SOC使能'
'MAINS Stop SOC En.':
  chinese: '市电停止SOC使能'
'MAINS Stop Current Enable':
  chinese: '市电停止电流使能'
'MAINS Stop Cur.En.':
  chinese: '市电停止电流使能'
'MAINS Stop Voltage Threshold':
  chinese: '市电停止电压'
'MAINS Stop Volt.':
  chinese: '市电停止电压'
'MAINS Stop SOC Threshold':
  chinese: '市电关闭SOC阈值'
'MAINS Stop SOC':
  chinese: '市电关闭SOC'
'MAINS Stop Battery Current':
  chinese: '市电关闭电池电流'
'MAINS Stop Bat.Curr.':
  chinese: '市电关闭电流'
'Min MAINS Running Duration':
  chinese: '市电最短运行时间'
'Min Mns.Run.Dura.':
  chinese: '市电最短运行时间'
'MAINS Max Running Duration':
  chinese: '市电最长运行时间'
'MAINS Max Run.Dura.':
  chinese: '市电最长运行时间'
'Disabled':
  chinese: '禁止'
'Enabled':
  chinese: '允许'
'Current Walk-In Enable':
  chinese: '电流缓启动使能'
'Cur Walk-In En':
  chinese: '电流缓启动使能'
'SMR Output Voltage':
  chinese: '整流器输出电压'
'Output Voltage':
  chinese: '输出电压'
'SMR Output Current':
  chinese: '整流器输出电流'
'Output Current':
  chinese: '输出电流'
'SMR Internal Temperature':
  chinese: '整流器机内温度'
'Internal Temp':
  chinese: '机内温度'
'SMR Maximum Output Current':
  chinese: '整流器最大输出电流'
'Max Out Curr':
  chinese: '最大输出电流'
'SMR Input Voltage':
  chinese: '整流器输入电压'
'Input Voltage':
  chinese: '输入电压'
'SMR Input Current':
  chinese: '整流器输入电流'
'Input Current':
  chinese: '输入电流'
'SMR In Freq':
  chinese: '整流器输入频率'
'In Freq':
  chinese: '输入频率'
'SMR In Power':
  chinese: '整流器输入功率'
'In Power':
  chinese: '输入功率'
'SMR Output Power':
  chinese: '整流器输出功率'
'Output Power':
  chinese: '输出功率'
'SMR Fan Speed':
  chinese: '整流器风扇转速'
'Fan Speed':
  chinese: '风扇转速'
'SMR Group Address':
  chinese: '整流器组地址'
'SMR Group Inner Address':
  chinese: '整流器组内地址'
'SMR Slot Address':
  chinese: '整流器槽位地址'
'SMR Positive Bus Volt':
  chinese: '正bus电压'
'Positive Bus Volt':
  chinese: '正bus电压'
'SMR Negative Bus Volt':
  chinese: '负bus电压'
'Negative Bus Volt':
  chinese: '负bus电压'
'SMR Bus1 Volt':
  chinese: 'Bus1电压'
'Bus1 Volt':
  chinese: 'Bus1电压'
'SMR Bus2 Volt':
  chinese: 'Bus2电压'
'Bus2 Volt':
  chinese: 'Bus2电压'
'SMR Bus3 Volt':
  chinese: 'Bus3电压'
'Bus3 Volt':
  chinese: 'Bus3电压'
'SMR Bus4 Volt':
  chinese: 'Bus4电压'
'Bus4 Volt':
  chinese: 'Bus4电压'
'SMR Raw Output Current':
  chinese: '整流器原始输出电流'
'Raw Output Current':
  chinese: '原始输出电流'
'SMR Input Line Voltage1':
  chinese: '整流器线电压1'
'Input Line Volt.1':
  chinese: '输入线电压1'
'SMR Input Line Voltage2':
  chinese: '整流器线电压2'
'Input Line Volt.2':
  chinese: '输入线电压2'
'SMR Input Line Voltage3':
  chinese: '整流器线电压3'
'Input Line Volt.3':
  chinese: '输入线电压3'
'SMR Input Current1':
  chinese: '整流器输入电流1'
'Input Curr.1':
  chinese: '输入电流1'
'SMR Input Current2':
  chinese: '整流器输入电流2'
'Input Curr.2':
  chinese: '输入电流2'
'SMR Input Current3':
  chinese: '整流器输入电流3'
'Input Curr.3':
  chinese: '输入电流3'
'SMR Ac Mains Input Energy':
  chinese: '整流器市电输入电量'
'Ac Mains Input Energy':
  chinese: '市电输入电量'
'SMR Ac Dg Input Energy':
  chinese: '整流器油机输入电量'
'Ac Dg Input Energy':
  chinese: '油机输入电量'
'SMR Dc Input Energy':
  chinese: '整流器直流输入电量'
'Dc Input Energy':
  chinese: '直流输入电量'
'SMR Input Energy':
  chinese: '整流器输入电量'
'Input Energy':
  chinese: '输入电量'
'SMR Output Energy':
  chinese: '整流器输出电量'
'Output Energy':
  chinese: '输出电量'
'SMR Load Current':
  chinese: '整流器分路电流'
'Load Curr.':
  chinese: '分路电流'
'SMR Load Energy':
  chinese: '整流器分路电量'
'Load Energy':
  chinese: '分路电量'
'No':
  chinese: '否'
'Yes':
  chinese: '是'
'SMR Off Status':
  chinese: '整流器关机状态'
'SMR Off':
  chinese: '关机'
'SMR Current Limit Status':
  chinese: '整流器限流状态'
'Current Limit':
  chinese: '限流'
'SMR Input Power Limit':
  chinese: '整流器输入限功率'
'In Power Limit':
  chinese: '输入限功率'
'SMR Temperature Power Limit':
  chinese: '整流器温度限功率'
'TH Power Limit':
  chinese: '温度限功率'
'SMR Sleep Status':
  chinese: '整流器休眠状态'
'Sleep':
  chinese: '休眠'
'SMR P2P Status':
  chinese: '整流器一键功能状态'
'P2P':
  chinese: '一键功能'
'SMR Closedown Status':
  chinese: '整流器闭锁状态'
'Closedown':
  chinese: '闭锁'
'SMR DC Power Limit':
  chinese: '整流器输出限功率状态'
'DC Power Limit':
  chinese: '输出限功率'
'SMR Input Phase':
  chinese: '整流器交流输入相位'
'SMR In.Phase':
  chinese: '输入相位'
'Full Speed':
  chinese: '全速'
'SMR Fan Control State':
  chinese: '整流器风扇控制状态'
'SMR Fan Ctrl':
  chinese: '整流器风扇控制'
'SMR Software Update Enable':
  chinese: '整流器升级使能状态'
'Update Enable':
  chinese: '升级使能状态'
'Alarm':
  chinese: '告警'
'CommFail':
  chinese: '通讯断'
'Update':
  chinese: '升级'
'SMR Work Status':
  chinese: '整流器工作状态'
'Work Status':
  chinese: '整流器工作状态'
'SMR Fan Fault State':
  chinese: '整流器风扇故障状态'
'SMR Fan Fault':
  chinese: '整流器风扇故障'
'SMR Communication State':
  chinese: '整流器通讯状态'
'SMR Radiator Temperature High Off State':
  chinese: '整流器散热器过温关机状态'
'SMR Ra.T.H.O sta.':
  chinese: '过温关机状态'
'SMR Input Voltage High Off State':
  chinese: '整流器交流过压关机状态'
'SMR In.V.H.O sta.':
  chinese: '交流过压关机状态'
'SMR Input Voltage Low Off State':
  chinese: '整流器交流欠压关机状态'
'SMR In.V.L.O sta.':
  chinese: '交流欠压关机状态'
'SMR Output Voltage High Off State':
  chinese: '整流器输出过压关机状态'
'SMR Out.V.H.O sta.':
  chinese: '输出过压关机状态'
'SMR Output Current High State':
  chinese: '整流器输出过流状态'
'SMR Out.C.H sta.':
  chinese: '输出过流状态'
'SMR PFC Fault State':
  chinese: '整流器PFC故障状态'
'SMR PFC.Fault sta.':
  chinese: 'PFC故障状态'
'SMR Internal Temperature High State':
  chinese: '整流器机内温度过高状态'
'SMR Inter.T.H sta.':
  chinese: '机内过温状态'
'SMR Output Fuse Broken State':
  chinese: '整流器输出熔丝断状态'
'SMR Out Fuse sta.':
  chinese: '输出熔丝状态'
'SMR Current Share Alarm State':
  chinese: '整流器均流不良状态'
'SMR Curr Share sta.':
  chinese: '均流不良状态'
'SMR AC Input Power Off State':
  chinese: '整流器交流输入断状态'
'SMR Input Off sta.':
  chinese: '交流输入断状态'
'SMR PFC Output Voltage High State':
  chinese: '整流器PFC输出过压告警状态'
'PFC Out.V.H sta.':
  chinese: 'PFC输出过压状态'
'SMR PFC Output Voltage Low State':
  chinese: '整流器PFC输出欠压告警状态'
'PFC Out.V.L sta.':
  chinese: 'PFC输出欠压状态'
'SMR EEPROM Fault State':
  chinese: '整流器EEPROM故障状态'
'SMR EEPROM sta.':
  chinese: 'EEPROM状态'
'SMR Internal Communication Fail State':
  chinese: '整流器机内通讯断状态'
'SMR In.Comm F sta.':
  chinese: '机内通讯状态'
'SMR Primary Current High State':
  chinese: '整流器原边过流状态'
'SMR Primy.C.H sta.':
  chinese: '原边过流状态'
'SMR PFC Input Current High State':
  chinese: '整流器PFC输入过流状态'
'PFC Input C.H sta.':
  chinese: 'PFC输入过流状态'
'SMR Slow Start Abnormal State':
  chinese: '整流器缓启动异常状态'
'SMR Start Abr sta.':
  chinese: '启动异常状态'
'SMR Input Fuse Break State':
  chinese: '整流器输入熔丝断状态'
'SMR In.Fuse sta.':
  chinese: '输入熔丝状态'
'SMR Input Frequency Abnormal State':
  chinese: '整流器输入频率异常状态'
'SMR In.Freq sta.':
  chinese: '输入频率状态'
'SMR Output Voltage Low State':
  chinese: '整流器输出欠压状态'
'SMR Out.V.L sta.':
  chinese: '输出欠压状态'
'SMR Output Over Power State':
  chinese: '整流器输出过载状态'
'SMR Out.O.P sta.':
  chinese: '输出过载状态'
'SMR SN Clash State':
  chinese: '整流器序列号冲突状态'
'SMR SN Clash sta.':
  chinese: '序列号冲突状态'
'SMR Protocol Error State':
  chinese: '整流器协议错误状态'
'SMR Pro.Err sta.':
  chinese: '整流器协议错误状态'
'SMR Type No Match State':
  chinese: '整流器机型不匹配状态'
'SMR No Match sta.':
  chinese: '机型不匹配状态'
'SMR External Over Temperature State':
  chinese: '整流器机外温度过高状态'
'SMR Ext.O.T sta.':
  chinese: '机外过温状态'
'Starting Resistor Temperature High State':
  chinese: '启动电阻过热告警状态'
'Start.R.T.H sta.':
  chinese: '启动电阻过热状态'
'SMR Exist State':
  chinese: '整流器在位状态'
'SMR Exist Sta.':
  chinese: '在位状态'
'SMR Bus1 Under Volt State':
  chinese: '整流器Bus1欠压状态'
'Bus1 Un.Volt Sta.':
  chinese: 'Bus1欠压'
'SMR Bus2 Under Volt State':
  chinese: '整流器Bus2欠压状态'
'Bus2 Un.Volt Sta.':
  chinese: 'Bus2欠压'
'SMR Bus3 Under Volt State':
  chinese: '整流器Bus3欠压状态'
'Bus3 Un.Volt Sta.':
  chinese: 'Bus3欠压'
'SMR Bus4 Under Volt State':
  chinese: '整流器Bus4欠压状态'
'Bus4 Un.Volt Sta.':
  chinese: 'Bus4欠压'
'SMR Bus1 Over Volt State':
  chinese: '整流器Bus1过压状态'
'Bus1 Ov.Volt Sta.':
  chinese: 'Bus1过压'
'SMR Bus2 Over Volt State':
  chinese: '整流器Bus2过压状态'
'Bus2 Ov.Volt Sta.':
  chinese: 'Bus2过压'
'SMR Bus3 Over Volt State':
  chinese: '整流器Bus3过压状态'
'Bus3 Ov.Volt Sta.':
  chinese: 'Bus3过压'
'SMR Bus4 Over Volt State':
  chinese: '整流器Bus4过压状态'
'Bus4 Ov.Volt Sta.':
  chinese: 'Bus4过压'
'SMR Bus Unbalance State':
  chinese: '整流器Bus不平衡'
'SMR Unbalance State':
  chinese: 'Bus不平衡状态'
'SMR AC Monment State':
  chinese: '整流器交流瞬断状态'
'SMR Monment State':
  chinese: '整流器瞬断状态'
'Master Slave Curr Share':
  chinese: '主从均流'
'Packet Curr Share':
  chinese: '分组均流'
'SMR Current Share Type':
  chinese: '整流器均流类型'
'SMR Curr Share Type':
  chinese: '整流器均流类型'
'Not Ready':
  chinese: '未到位'
'Ready':
  chinese: '到位'
'SMR Ready state':
  chinese: '整流器到位状态'
'SMR Ready Sta.':
  chinese: '到位状态'
'SMR Phase Lack or Input Fuse Break State':
  chinese: '整流器缺相或输入熔丝断状态'
'SMR Phase Lack State':
  chinese: '缺相或输入熔丝断状态'
'SMR Input Unbalance State':
  chinese: '整流器输入电压不平衡状态'
'SMR Input Unba.Sta.':
  chinese: '输入电压不平衡状态'
'SMR Output Voltage Low Early Warning State':
  chinese: '整流器输出欠压预警状态'
'SMR Out.V.L EW.sta.':
  chinese: '输出欠压预警状态'
'SMR Output Voltage High Early Warning State':
  chinese: '整流器输出过压预警状态'
'SMR Out.V.H EW.sta.':
  chinese: '输出过压预警状态'
'SMR Input Voltage Low Early Warning State':
  chinese: '整流器输入欠压预警状态'
'SMR In.V.L EW.sta.':
  chinese: '输入欠压预警状态'
'SMR Input Voltage High Early Warning State':
  chinese: '整流器输入过压预警状态'
'SMR In.V.H EW.sta.':
  chinese: '输入过压预警状态'
'SMR Input Reserve State':
  chinese: '整流器输入反接状态'
'SMR In.Reserve sta.':
  chinese: '输入反接状态'
'SMR Output Current High Early Warning State':
  chinese: '整流器输出过流预警状态'
'SMR Out.C.H EW.sta.':
  chinese: '输出过流预警状态'
'SMR Internal Temperature High Early Warning State':
  chinese: '整流器机内温度过温预警状态'
'SMR Inter.T.H EW.sta.':
  chinese: '机内温度过温预警状态'
'SMR Input Frequency Low State':
  chinese: '整流器输入频率低状态'
'SMR In.Freq.L.sta.':
  chinese: '输入频率低状态'
'SMR AC Lightning Protection Abnormal State':
  chinese: '交流防雷异常状态'
'AC Lightning Prt Abr sta.':
  chinese: '交流防雷异常状态'
'SMR DC Lightning Protection Abnormal State':
  chinese: '直流防雷异常状态'
'DC Lightning Prt Abr sta.':
  chinese: '直流防雷异常状态'
'SMR Input Frequency High State':
  chinese: '整流器输入频率高状态'
'SMR In.Freq.H.sta.':
  chinese: '输入频率高状态'
'SMR Input Current High State':
  chinese: '整流器输入过流状态'
'SMR In.C.H.sta.':
  chinese: '输入过流状态'
'SMR Input Frequency High Protect State':
  chinese: '整流器输入频率高保护状态'
'SMR In.Freq.H.Pro.sta.':
  chinese: '输入频率高保护状态'
'SMR Inter Low Temperature State':
  chinese: '整流器机内温度低状态'
'SMR Inter.L.T.sta.':
  chinese: '机内温度低状态'
'No Input':
  chinese: '无输入'
'Mains Input':
  chinese: '市电输入'
'Dg Input':
  chinese: '油机输入'
'DC Input':
  chinese: '直流输入'
'SMR Power Input Mode':
  chinese: '整流器用电输入模式'
'SMR Power In.Mode':
  chinese: '用电输入模式'
'SMR Input Power Down State':
  chinese: '整流器输入掉电状态'
'SMR In.PW.Sta.':
  chinese: '输入掉电状态'
'SMR Output Current High Off State':
  chinese: '整流器输出过流关机状态'
'SMR Out.C.H.O sta.':
  chinese: '输出过流关机状态'
'SMR Inter Temperature High Off State':
  chinese: '整流器机内温度高关机状态'
'SMR In.T.H.O sta.':
  chinese: '机内温度高关机状态'
'SMR Input Frequency Low Off State':
  chinese: '整流器输入频率低关机状态'
'SMR In.F.L.O sta.':
  chinese: '输入频率低关机状态'
'SMR Input Frequency High Off State':
  chinese: '整流器输入频率高关机状态'
'SMR In.F.H.O sta.':
  chinese: '输入频率高关机状态'
'SMR Alarm':
  chinese: '整流器告警'
'SMR Radiator Temperature High Off':
  chinese: '整流器散热器过温关机'
'SMR Ra.T.H.O':
  chinese: '散热器过温关机'
'SMR Input Voltage High Off':
  chinese: '整流器交流过压关机'
'SMR In.V.H.O':
  chinese: '整流器输入高关'
'SMR Input Voltage Low Off':
  chinese: '整流器交流欠压关机'
'SMR In.V.L.O':
  chinese: '整流器输入低关'
'SMR Output Voltage High Off':
  chinese: '整流器输出过压关机'
'SMR Out.V.H.O':
  chinese: '整流器输出高关'
'SMR Output Current High':
  chinese: '整流器输出过流'
'SMR Out.C.H':
  chinese: '整流器输出过流'
'SMR PFC Fault':
  chinese: '整流器PFC故障'
'SMR PFC.Fault':
  chinese: '整流器PFC故障'
'SMR Internal Temperature High':
  chinese: '整流器机内温度过高'
'SMR Inter.T.H':
  chinese: '整流器机内过温'
'SMR Output Fuse Broken':
  chinese: '整流器输出熔丝断'
'SMR Out Fuse':
  chinese: '整流器输出熔丝'
'SMR Current Share Alarm':
  chinese: '整流器均流不良'
'SMR Curr Share':
  chinese: '整流器均流不良'
'SMR AC Input Power Off':
  chinese: '整流器交流输入断'
'SMR Input Off':
  chinese: '整流器输入断'
'SMR PFC Output Voltage High':
  chinese: '整流器PFC输出过压告警'
'PFC Out.V.H':
  chinese: 'PFC输出过压'
'SMR PFC Output Voltage Low':
  chinese: '整流器PFC输出欠压告警'
'PFC Out.V.L':
  chinese: 'PFC输出欠压'
'SMR EEPROM Fault':
  chinese: '整流器EEPROM故障'
'SMR EEPROM':
  chinese: '整流器EEPROM'
'SMR Internal Communication Fail':
  chinese: '整流器机内通讯断'
'SMR In.Comm Fail':
  chinese: '整流器内通讯断'
'SMR Primary Current High':
  chinese: '整流器原边过流'
'SMR Primy.C.H':
  chinese: '整流器原边过流'
'SMR PFC Input Current High':
  chinese: '整流器PFC输入过流'
'PFC Input C.H':
  chinese: 'PFC输入过流'
'SMR Slow Start Abnormal':
  chinese: '整流器缓启动异常'
'SMR Start Abr':
  chinese: '整流器启动异常'
'SMR Input Fuse Break or Internal Comm.Broken':
  chinese: '整流器输入熔丝断或机内通讯断'
'SMR In.Fuse':
  chinese: '整流器输入熔丝'
'SMR Input Frequency Abnormal':
  chinese: '整流器输入频率异常'
'SMR In.Freq':
  chinese: '整流器输入频率'
'SMR Output Voltage Low':
  chinese: '整流器输出欠压'
'SMR Out.V.L':
  chinese: '整流器输出欠压'
'SMR Output Over Power':
  chinese: '整流器输出过载'
'SMR Out.O.P':
  chinese: '整流器输出过载'
'SMR SN Clash':
  chinese: '整流器序列号冲突'
'SMR Protocol Error':
  chinese: '整流器协议错误'
'SMR Pro.Err':
  chinese: '整流器协议错误'
'SMR Type No Match':
  chinese: '整流器机型不匹配'
'SMR No Match':
  chinese: '机型不匹配'
'SMR External Over Temperature':
  chinese: '整流器机外温度过高'
'SMR Ext.O.T':
  chinese: '整流器机外过温'
'Starting Resistor Temperature High Alarm':
  chinese: '启动电阻过热告警'
'Start.R.T.H':
  chinese: '启动电阻过热'
'SMR Communication Fail':
  chinese: '整流器通讯中断'
'SMR Comm.Fail':
  chinese: '整流器通讯中断'
'SMR Bus1 Under Volt':
  chinese: '整流器Bus1欠压'
'Bus1 Un.Volt':
  chinese: '整流器Bus1欠压'
'SMR Bus2 Under Volt':
  chinese: '整流器Bus2欠压'
'Bus2 Un.Volt':
  chinese: '整流器Bus2欠压'
'SMR Bus3 Under Volt':
  chinese: '整流器Bus3欠压'
'Bus3 Un.Volt':
  chinese: '整流器Bus3欠压'
'SMR Bus4 Under Volt':
  chinese: '整流器Bus4欠压'
'Bus4 Un.Volt':
  chinese: '整流器Bus4欠压'
'SMR Bus1 Over Volt':
  chinese: '整流器Bus1过压'
'Bus1 Ov.Volt':
  chinese: '整流器Bus1过压'
'SMR Bus2 Over Volt':
  chinese: '整流器Bus2过压'
'Bus2 Ov.Volt':
  chinese: '整流器Bus2过压'
'SMR Bus3 Over Volt':
  chinese: '整流器Bus3过压'
'Bus3 Ov.Volt':
  chinese: '整流器Bus3过压'
'SMR Bus4 Over Volt':
  chinese: '整流器Bus4过压'
'Bus4 Ov.Volt':
  chinese: '整流器Bus4过压'
'SMR Bus Unbalance':
  chinese: '整流器Bus不平衡'
'SMR Unbalance':
  chinese: '整流器Bus不平衡'
'SMR Fault':
  chinese: '整流器故障'
'SMR Input Unbalance':
  chinese: '整流器输入电压不平衡'
'SMR Input Unba.':
  chinese: '输入电压不平衡'
'SMR Phase Lack or Input Fuse Break':
  chinese: '整流器缺相或输入熔丝断'
'SMR Phase Lack':
  chinese: '缺相或输入熔丝断'
'SMR Not Ready':
  chinese: '整流器未到位'
'SMR Output Voltage Low Early Warning':
  chinese: '整流器输出欠压预警'
'SMR Out.V.L EW.':
  chinese: '输出欠压预警'
'SMR Output Voltage High Early Warning':
  chinese: '整流器输出过压预警'
'SMR Out.V.H EW.':
  chinese: '输出过压预警'
'SMR Input Voltage Low Early Warning':
  chinese: '整流器输入欠压预警'
'SMR In.V.L EW.':
  chinese: '输入欠压预警'
'SMR Input Voltage High Early Warning':
  chinese: '整流器输入过压预警'
'SMR In.V.H EW.':
  chinese: '输入过压预警'
'SMR Input Reserve':
  chinese: '整流器输入反接'
'SMR In.Reserve':
  chinese: '整流器输入反接'
'SMR Output Current High Early Warning':
  chinese: '整流器输出过流预警'
'SMR Out.C.H EW.':
  chinese: '输出过流预警'
'SMR Internal Temperature High Early Warning':
  chinese: '整流器机内温度过温预警'
'SMR Inter.T.H EW.':
  chinese: '机内温度过温预警'
'SMR Input Frequency Low':
  chinese: '整流器输入频率低'
'SMR In.Freq.L.':
  chinese: '输入频率低'
'SMR AC Lightning Protection Abnormal':
  chinese: '交流防雷异常'
'AC Lightning Prt Abr':
  chinese: '交流防雷异常'
'SMR DC Lightning Protection Abnormal':
  chinese: '直流防雷异常'
'DC Lightning Prt Abr':
  chinese: '直流防雷异常'
'SMR Input Frequency High':
  chinese: '整流器输入频率高'
'SMR In.Freq.H.':
  chinese: '输入频率高'
'SMR Input Current High':
  chinese: '整流器输入过流'
'Input Curr.H':
  chinese: '输入过流'
'SMR Input Frequency High Protect':
  chinese: '整流器输入频率高保护'
'SMR In.Freq.H.Pro.':
  chinese: '输入频率高保护'
'SMR Inter Low Temperature':
  chinese: '整流器机内温度低'
'SMR Inter.L.T.':
  chinese: '机内温度低'
'SMR Off Alarm':
  chinese: '整流器关机告警'
'SMR Input Power Down':
  chinese: '整流器输入掉电'
'SMR In.PW.':
  chinese: '输入掉电'
'SMR Output Current High Off':
  chinese: '整流器输出过流关机'
'SMR Out.C.H.O':
  chinese: '输出过流关机'
'SMR Inter Temperature High Off':
  chinese: '整流器机内温度高关机'
'SMR In.T.H.O':
  chinese: '机内温度高关机'
'SMR Input Frequency Low Off':
  chinese: '整流器输入频率低关机'
'SMR In.F.L.O':
  chinese: '输入频率低关机'
'SMR Input Frequency High Off':
  chinese: '整流器输入频率高关机'
'SMR In.F.H.O':
  chinese: '输入频率高关机'
'Modify SMR Address':
  chinese: '修改整流器地址'
'Mod SMR Addr':
  chinese: '修改整流器地址'
'SMR Sleep':
  chinese: '整流器休眠'
'SMR Waken':
  chinese: '整流器唤醒'
'SMR Enter Peer to Peer':
  chinese: '整流器进入一键功能'
'SMR Enter P2P':
  chinese: '进入一键功能'
'SMR Quit Peer to Peer':
  chinese: '整流器退出一键功能'
'SMR Quit P2P':
  chinese: '退出一键功能'
'SMR Fan Control Enable':
  chinese: '整流器风扇调速允许'
'SMR Fan Ctrl.En':
  chinese: '风扇调速允许'
'SMR Fan Control Disable':
  chinese: '整流器风扇调速禁止'
'SMR Fan Ctrl.Dis':
  chinese: '风扇调速禁止'
'SMR Restart':
  chinese: '整流器复位'
'SMR Communication Fail alarm clear':
  chinese: '整流器通讯中断告警清除'
'Comm.Fail clear':
  chinese: '通讯中断清除'
'SMR Address':
  chinese: '整流器地址'
'SMR Addr':
  chinese: '整流器地址'
'SMR Input Instant Peak Voltage':
  chinese: '整流器输入电压极值'
'Instant Peak Volt.':
  chinese: '输入电压瞬时极值'
'SMR Bus Peak Voltage':
  chinese: '整流器Bus电压极值'
'Bus Peak Voltage':
  chinese: 'Bus电压瞬时极值'
'SMR Input Effective Peak Voltage':
  chinese: '整流器输入电压有效极值'
'Effective Peak Volt.':
  chinese: '输入电压有效极值'
'SMR Instant Peak Voltage Count':
  chinese: '输入电压瞬时极值统计'
'Instant Peak Volt.Count':
  chinese: '瞬时极值统计'
'SMR Bus Peak Voltage Count':
  chinese: '整流器Bus电压极值统计'
'Bus Peak Volt.Count':
  chinese: 'Bus电压瞬时极值统计值'
'SMR Effective Peak Voltage Count':
  chinese: '整流器输入电压有效极值统计'
'Effect Peak Volt. Count':
  chinese: '输入电压有效极值统计'
'SMR Ac Mains Input Total Energy':
  chinese: '整流器市电输入总电量'
'Ac Mains Input Total Energy':
  chinese: '市电输入总电量'
'SMR Ac Dg Input Total Energy':
  chinese: '整流器油机输入总电量'
'Ac Dg Input Total Energy':
  chinese: '油机输入总电量'
'SMR Dc Input Total Energy':
  chinese: '整流器直流输入总电量'
'Dc Input Total Energy':
  chinese: '直流输入总电量'
'SMR ID':
  chinese: '整流器ID'
'SMR Rating Maximum Output Current':
  chinese: '整流器额定最大输出电流'
'Rating Out.Curr':
  chinese: '额定最大输出电流'
'SMR PFC Software Version':
  chinese: '整流器前级软件版本'
'SMR PFC Ver':
  chinese: '整流器前级版本'
'SMR DC Software Version':
  chinese: '整流器后级软件版本'
'SMR DC Ver':
  chinese: '整流器后级版本'
'SMR PFC Software Release Date':
  chinese: '整流器前级软件发布日期'
'SMR PFC Date':
  chinese: '整流器前级日期'
'SMR DC Software Release Date':
  chinese: '整流器后级软件发布日期'
'SMR DC Date':
  chinese: '整流器后级日期'
'SMR Digital Control Platform Version':
  chinese: '整流器数控平台版本'
'SMR Platform':
  chinese: '整流器平台'
'SMR System Name':
  chinese: '整流器系统名称'
'SMR Sys Name':
  chinese: '整流器系统名称'
'SMR Manufactory ID':
  chinese: '制造商ID'
'SMR Manufty.ID':
  chinese: '制造商ID'
'SMR Manufactory Address':
  chinese: '制造商地址'
'Manufty.Address':
  chinese: '制造商地址'
'SMR Barcodes':
  chinese: '整流器条码'
'SMR Manufacture Date':
  chinese: '生产日期'
'Manufacture Date':
  chinese: '生产日期'
'CORE Communication Address':
  chinese: 'CORE板通信地址'
'CORE Comm. Addr.':
  chinese: 'CORE板通信地址'
'CORE Serial Number':
  chinese: 'CORE板序列号'
'CORE SN':
  chinese: 'CORE板序列号'
'CORE Software Version':
  chinese: 'CORE板软件版本'
'CORE Software Version Date':
  chinese: 'CORE板软件版本日期'
'SMR Setting Voltage comp':
  chinese: '整流器当前输出设定补偿电压'
'SMR Set Volt Comp':
  chinese: '整流器设定补偿电压'
'Multi-SMR Alarm':
  chinese: '多个整流器模块告警'
'Multi-SMR Alm':
  chinese: '多个整流器告警'
'All SMR Commfail Alarm':
  chinese: '所有整流器通讯断告警'
'All SMR Commfail':
  chinese: '所有整流器通讯断'
'Auto Save Control':
  chinese: '自动节能控制'
'Auto Save Ctrl':
  chinese: '自动节能控制'
'Temporary NonSave Control':
  chinese: '暂时非节能控制'
'Temp NonSave Ctrl':
  chinese: '暂时非节能控制'
'Permanent NonSave Control':
  chinese: '永久非节能控制'
'Perm NonSave Ctrl':
  chinese: '永久非节能控制'
'SMR Device Statistic':
  chinese: 'SMR设备统计'
'SMR Dev Stat':
  chinese: 'SMR设备统计'
'Start Smr Test':
  chinese: '启动整流器测试'
'Start Auto Save Mode Manually':
  chinese: '开启自动节能模式'
'Start Auto Save':
  chinese: '开启自动节能'
'Save':
  chinese: '节能'
'Free':
  chinese: '自由'
'AC Save Energy Mode':
  chinese: '交流节能模式'
'AC Save Eng.Mode':
  chinese: '交流节能模式'
'SMR Rotated Period':
  chinese: '整流器轮换周期'
'SMR Rota Period':
  chinese: '整流器轮换周期'
'Minimum Qty of Started SMRs':
  chinese: '整流器最小开机数量'
'Min Num Start SMR':
  chinese: '整流器最小开机数'
'Temporary NonSave Delay':
  chinese: '暂时非节能延时时间'
'Temp NonSave Delay':
  chinese: '暂时非节能延时'
'SMR Smart Cooling Enabled':
  chinese: '整流器智能温控功能'
'SMR Smart Cool En':
  chinese: '整流器智能温控'
'SMR Soft Start Interval':
  chinese: '整流器软启动间隔'
'Soft Start Inter':
  chinese: '软启动间隔'
'SMR Output High Off Voltage':
  chinese: '整流器输出高停机电压'
'Out High Off Volt':
  chinese: '输出高停机电压'
'SMR Default Output Voltage':
  chinese: '整流器默认输出电压'
'Def Out Volt':
  chinese: '默认输出电压'
'SMR Default Current Limit Rate':
  chinese: '整流器默认限流点比率'
'SMR Def CL Rate':
  chinese: '默认限流点比率'
'SMR Maximum Quantity':
  chinese: '整流器最大数量'
'SMR Max Qty':
  chinese: '整流器最大数量'
'Save Load Rate Maximum':
  chinese: '节能带载率上限'
'Load Rate Max':
  chinese: '带载率上限'
'Save Load Rate Minimum':
  chinese: '节能带载率下限'
'Load Rate Min':
  chinese: '带载率下限'
'Current Walk-In Time':
  chinese: '电流缓启动时间'
'Cur Walk-In Time':
  chinese: '电流缓启动时间'
'Single Phase SMR Default Output Voltage':
  chinese: '单相整流器默认输出电压'
'Single Phase Def Out Volt':
  chinese: '单相整流器默认输出电压'
'Three Phase SMR Default Output Voltage':
  chinese: '三相整流器默认输出电压'
'Three Phase Def Out Volt':
  chinese: '三相整流器默认输出电压'
'Single Phase SMR':
  chinese: '单相整流器'
'Three Phase SMR':
  chinese: '三相整流器'
'SMR Type Config':
  chinese: '整流器类型配置'
'SMR Current Limit Rate':
  chinese: '整流器当前限流点比率'
'CL Rate':
  chinese: '当前限流点比率'
'SMR Output Low Off Voltage':
  chinese: '整流器输出低关机电压'
'Out Low Off Volt':
  chinese: '输出低关机电压'
'SMR Input Voltage Over Threshold':
  chinese: '整流器输入过压阈值'
'In Volt.O.Thre.':
  chinese: '输入过压阈值'
'SMR Input Current Over Threshold':
  chinese: '整流器输入过流阈值'
'In Curr.O.Thre.':
  chinese: '输入过流阈值'
'SMR Inter Temperatrue High Threshold':
  chinese: '整流器机内高温阈值'
'Inter Temp.H.Thre.':
  chinese: '机内高温阈值'
'SMR Inter Temperatrue Low Threshold':
  chinese: '整流器机内低温阈值'
'Inter Temp.L.Thre.':
  chinese: '机内低温阈值'
'SMR Input Voltage Low Threshold':
  chinese: '整流器输入电压低阈值'
'In Volt.L.Thre.':
  chinese: '输入电压低阈值'
'SMR Input Frequency High Threshold':
  chinese: '整流器输入频率上限'
'In Freq.H.Thre':
  chinese: '输入频率上限'
'SMR Input Frequency Low Threshold':
  chinese: '整流器输入频率下限'
'In Freq.L.Thre':
  chinese: '输入频率下限'
'Mains Max Frequency':
  chinese: '市电最高频率'
'Mains Max Freq.':
  chinese: '市电最高频率'
'Mains Min Frequency':
  chinese: '市电最低频率'
'Mains Min Freq.':
  chinese: '市电最低频率'
'Next Rotate Time':
  chinese: '下次轮换时间'
'Environment Humidity':
  chinese: '环境湿度'
'Environment Hum':
  chinese: '环境湿度'
'Extend Temperature':
  chinese: '扩展温度'
'Ext.Temp.':
  chinese: '扩展温度'
'Smog Status':
  chinese: '烟雾状态'
'Smog':
  chinese: '烟雾状态'
'Flood Status':
  chinese: '水淹状态'
'Flood':
  chinese: '水淹状态'
'Door Status':
  chinese: '门磁状态'
'Access Control Status':
  chinese: '门禁状态'
'Access Control':
  chinese: '门禁状态'
'No Config':
  chinese: '未配置'
'Environment Temperature Sensor Status':
  chinese: '环境温度传感器状态'
'Env.Temp.Sensor':
  chinese: '环境温度传感器'
'Environment Humidity Sensor Status':
  chinese: '环境湿度传感器状态'
'Env.Hum.Sensor':
  chinese: '环境湿度传感器'
'Extend Temperature Sensor Status':
  chinese: '扩展温度传感器状态'
'Ext.Temp.Sensor':
  chinese: '扩展温度传感器'
'Smog Alarm':
  chinese: '烟雾告警'
'Flood Alarm':
  chinese: '水淹告警'
'Door Alarm':
  chinese: '门磁告警'
'Access Control Alarm':
  chinese: '门禁告警'
'Access Ctrl.Alm.':
  chinese: '门禁告警'
'Environment Temperature Invalid':
  chinese: '环境温度无效'
'Env.Temp.Invalid':
  chinese: '环境温度无效'
'Environment Temperature High':
  chinese: '环境温度高'
'Env.Temp. High':
  chinese: '环境温度高'
'Environment Temperature Low':
  chinese: '环境温度低'
'Env.Temp. Low':
  chinese: '环境温度低'
'Environment Humidity Invalid':
  chinese: '环境湿度无效'
'Env.Hum.Invalid':
  chinese: '环境湿度无效'
'Environment Humidity High':
  chinese: '环境湿度高'
'Env.Hum. High':
  chinese: '环境湿度高'
'Environment Humidity Low':
  chinese: '环境湿度低'
'Env.Hum. Low':
  chinese: '环境湿度低'
'Environment Temperature Too High':
  chinese: '环境温度过高'
'Env.Temp.Too H.':
  chinese: '环境温度过高'
'Environment Temperature Too Low':
  chinese: '环境温度过低'
'Env.Temp.Too L.':
  chinese: '环境温度过低'
'Temperature Control Unit Alarm':
  chinese: '温控单元异常'
'T.Ctrl Unit Alm':
  chinese: '温控单元异常'
'DC Air Conditioner Alarm':
  chinese: '直流空调异常'
'DC Air Cond.Alm.':
  chinese: '直流空调异常'
'Heater Fault Alarm':
  chinese: '加热器异常'
'Heater Fault.Alm.':
  chinese: '加热器异常'
'External Cabinet Smog Alarm':
  chinese: '扩展柜烟雾告警'
'Ext.Cab.Smog Alm.':
  chinese: '扩展柜烟雾告警'
'Extend Temperature Invalid':
  chinese: '扩展温度无效'
'Ext.Temp.Invalid':
  chinese: '扩展温度无效'
'Extend Temperature High':
  chinese: '扩展温度高'
'Ext.Temp.High':
  chinese: '扩展温度高'
'Extend Temperature Low':
  chinese: '扩展温度低'
'Ext.Temp.Low':
  chinese: '扩展温度低'
'Environment Temperature High Threshold':
  chinese: '环境温度高阈值'
'Env.Temp.H.Thre.':
  chinese: '环境过温值'
'Environment Temperature Low Threshold':
  chinese: '环境温度低阈值'
'Env.Temp.L.Thre.':
  chinese: '环境低温值'
'Environment Humidity High Threshold':
  chinese: '环境湿度高阈值'
'Env.Hum.H.Thre.':
  chinese: '环境湿度高阈值'
'Environment Humidity Low Threshold':
  chinese: '环境湿度低阈值'
'Env.Hum.L.Thre.':
  chinese: '环境湿度低阈值'
'Environment Temperature Too High Threshold':
  chinese: '环境温度过高阈值'
'Env.Temp.Too H.Thre.':
  chinese: '环境温度过高阈值'
'Environment Temperature Too Low Threshold':
  chinese: '环境温度过低阈值'
'Env.Temp.Too L.Thre.':
  chinese: '环境温度过低阈值'
'Extend Temperature High Threshold':
  chinese: '扩展温度高阈值'
'Ext.Temp.H.Thre.':
  chinese: '扩展温度高阈值'
'Extend Temperature Low Threshold':
  chinese: '扩展温度低阈值'
'Ext.Temp.L.Thre.':
  chinese: '扩展温度低阈值'
'DC Load Current':
  chinese: '直流负载电流'
'Battery Shunt Current':
  chinese: '电池分流器电流'
'Batt.Shunt Curr.':
  chinese: '电池分流器电流'
'Battery Block Voltage Detect':
  chinese: '12V单节电池电压检测'
'Batt.Block Vol.Det.':
  chinese: '12V单节电压检测'
'Load Shunt Current':
  chinese: '负载分流器电流'
'Load Shunt Curr.':
  chinese: '负载分流器电流'
'Distribution Unit Shunt Current':
  chinese: '配电单元分流器电流'
'Dist.Unit Shunt Curr.':
  chinese: '配电单元分流器电流'
'LLVD1 Load Current':
  chinese: '一次下电回路负载电流'
'LLVD1 Load Curr.':
  chinese: '一次下电回路电流'
'LLVD1 Load Power':
  chinese: '一次下电回路负载功率'
'LLVD1 Load Pwr.':
  chinese: '一次下电回路功率'
'LLVD2 Load Current':
  chinese: '二次下电回路负载电流'
'LLVD2 Load Curr.':
  chinese: '二次下电回路电流'
'LLVD2 Load Power':
  chinese: '二次下电回路负载功率'
'LLVD2 Load Pwr.':
  chinese: '二次下电回路功率'
'BLVD Load Current':
  chinese: '电池下电回路负载电流'
'BLVD Load Curr.':
  chinese: '电池下电回路电流'
'BLVD Load Power':
  chinese: '电池下电回路负载功率'
'BLVD Load Pwr.':
  chinese: '电池下电回路功率'
'Tenant Load Current':
  chinese: '租户负载电流'
'Ten.Load Curr.':
  chinese: '租户负载电流'
'Public Load Current':
  chinese: '公用负载电流'
'Pub.Load Curr.':
  chinese: '公用负载电流'
'Distribution Unit Current':
  chinese: '配电单元电流'
'Dist.Unit Curr.':
  chinese: '配电单元电流'
'DC Output Voltage':
  chinese: '直流输出电压'
'DC Out.Vol.':
  chinese: '直流输出电压'
'Extend Distribution Unit Total Current':
  chinese: '扩展配电单元总电流'
'Ex.Du Total Curr.':
  chinese: '扩展配电单元总电流'
'DCLP Status':
  chinese: '直流防雷状态'
'DCLP':
  chinese: '直流防雷'
'DC Loop Status':
  chinese: '直流负载回路状态'
'DC Loop':
  chinese: '负载回路状态'
'DC Extend Loop Status':
  chinese: '直流负载扩展分路状态'
'DC Ext.Loop':
  chinese: '负载扩展分路状态'
'LLVD1 Loop Status':
  chinese: '一次下电分路状态'
'LLVD1 Loop':
  chinese: '一次下电分路'
'LLVD1 Extend Loop Status':
  chinese: '一次下电扩展分路状态'
'LLVD1 Extend Loop':
  chinese: '一次下电扩展分路'
'LLVD2 Loop Status':
  chinese: '二次下电分路状态'
'LLVD2 Loop':
  chinese: '二次下电分路'
'LLVD2 Extend Loop Status':
  chinese: '二次下电扩展分路状态'
'LLVD2 Extend Loop':
  chinese: '二次下电扩展分路'
'BLVD Loop Status':
  chinese: '电池下电分路状态'
'BLVD Loop':
  chinese: '电池下电分路'
'BLVD Extend Loop Status':
  chinese: '电池下电扩展分路状态'
'BLVD Extend Loop':
  chinese: '电池下电扩展分路'
'LLVD1 Load Module status':
  chinese: '一次下电回路模组状态'
'LLVD1 Load Module':
  chinese: '一次下电回路模组'
'LLVD2 Load Module status':
  chinese: '二次下电回路模组状态'
'LLVD2 Load Module':
  chinese: '二次下电回路模组'
'F09 Detect Channel Status':
  chinese: 'F09检测通道状态'
'F09 Detect Ch.Status':
  chinese: 'F09检测通道状态'
'F10 Detect Channel Status':
  chinese: 'F10检测通道状态'
'F10 Detect Ch.Status':
  chinese: 'F10检测通道状态'
'F11 Detect Channel Status':
  chinese: 'F11检测通道状态'
'F11 Detect Ch.Status':
  chinese: 'F11检测通道状态'
'F12 Detect Channel Status':
  chinese: 'F12检测通道状态'
'F12 Detect Ch.Status':
  chinese: 'F12检测通道状态'
'BLVD Load Module status':
  chinese: '电池下电回路模组状态'
'BLVD Load Module':
  chinese: '电池下电回路模组'
'DC DU LLVD1 Loop Status':
  chinese: '直流配电单元一次下电分路状态'
'DU LLVD1 Loop':
  chinese: '配电单元一次下电分路'
'DC DU LLVD2 Loop Status':
  chinese: '直流配电单元二次下电分路状态'
'DU LLVD2 Loop':
  chinese: '配电单元二次下电分路'
'DC DU BLVD Loop Status':
  chinese: '直流配电单元电池下电分路状态'
'DU BLVD Loop':
  chinese: '配电单元电池下电分路'
'Common LLVD2 Loop Status':
  chinese: '公用二次下电分路状态'
'Common LLVD2 Loop':
  chinese: '公用二次下电分路'
'Common BLVD Loop Status':
  chinese: '公用电池下电分路状态'
'Common BLVD Loop':
  chinese: '公用电池下电分路'
'F01 Detect Channel Status':
  chinese: 'F01检测通道状态'
'F01 Detect Ch.Status':
  chinese: 'F01检测通道状态'
'F02 Detect Channel Status':
  chinese: 'F02检测通道状态'
'F02 Detect Ch.Status':
  chinese: 'F02检测通道状态'
'F03 Detect Channel Status':
  chinese: 'F03检测通道状态'
'F03 Detect Ch.Status':
  chinese: 'F03检测通道状态'
'F04 Detect Channel Status':
  chinese: 'F04检测通道状态'
'F04 Detect Ch.Status':
  chinese: 'F04检测通道状态'
'F05 Detect Channel Status':
  chinese: 'F05检测通道状态'
'F05 Detect Ch.Status':
  chinese: 'F05检测通道状态'
'F06 Detect Channel Status':
  chinese: 'F06检测通道状态'
'F06 Detect Ch.Status':
  chinese: 'F06检测通道状态'
'F07 Detect Channel Status':
  chinese: 'F07检测通道状态'
'F07 Detect Ch.Status':
  chinese: 'F07检测通道状态'
'F08 Detect Channel Status':
  chinese: 'F08检测通道状态'
'F08 Detect Ch.Status':
  chinese: 'F08检测通道状态'
'DC SPD Status':
  chinese: '直流防雷器状态'
'DC SPD':
  chinese: '直流防雷器'
'Load Switch Trip Status':
  chinese: '负载空开脱扣状态'
'Load Sw.Trip Stat':
  chinese: '负载空开脱扣状态'
'Control CB Status':
  chinese: '外部控制开关状态'
'Control CB Stat.':
  chinese: '外部控制开关状态'
'DC Output Loop Status':
  chinese: '直流输出回路状态'
'DC Out.Loop Status':
  chinese: '直流输出回路状态'
'DC Output Over Voltage Protection Status':
  chinese: '直流输出过压保护状态'
'DC Out.OVP.Status':
  chinese: '直流输出过压保护状态'
'Power Down Status':
  chinese: '直流配电单元下电状态'
'P.D Status':
  chinese: '直流配电单元下电状态'
'DCLP Abnormal':
  chinese: '直流防雷异常'
'DCLP Abr':
  chinese: '直流防雷异常'
'DC Loop Broken':
  chinese: '直流负载回路断'
'DC.Loop.Brk':
  chinese: '负载回路断'
'Load Extend Loop Broken':
  chinese: '直流负载扩展分路断'
'Load Ext.Brk.':
  chinese: '负载扩展分路断'
'DC Voltage High':
  chinese: '直流电压高'
'DC Volt.High':
  chinese: '直流电压高'
'DC Voltage Low':
  chinese: '直流电压低'
'DC Volt.Low':
  chinese: '直流电压低'
'LLVD1 Loop Broken':
  chinese: '一次下电分路断'
'LLVD1 Loop Brk.':
  chinese: '一次下电分路断'
'LLVD1 Extend Loop Broken':
  chinese: '一次下电扩展分路断'
'LLVD1 Exten.Brk.':
  chinese: '一次下电扩展断'
'LLVD2 Loop Broken':
  chinese: '二次下电分路断'
'LLVD2 Loop Brk.':
  chinese: '二次下电分路断'
'LLVD2 Extend Loop Broken':
  chinese: '二次下电扩展分路断'
'LLVD2 Exten.Brk.':
  chinese: '二次下电扩展断'
'BLVD Loop Broken':
  chinese: '电池下电分路断'
'BLVD Loop Brk.':
  chinese: '电池下电分路断'
'BLVD Extend Loop Broken':
  chinese: '电池下电扩展分路断'
'BLVD Exten.Brk.':
  chinese: '电池下电扩展断'
'LLVD1 Load Module Alarm':
  chinese: '一次下电回路模组告警'
'LLVD1 L.Mod.Alm.':
  chinese: '一次下电回路模组告警'
'LLVD2 Load Module Alarm':
  chinese: '二次下电回路模组告警'
'LLVD2 L.Mod.Alm.':
  chinese: '二次下电回路模组告警'
'DC Voltage Too High':
  chinese: '直流电压过高'
'DC Volt.T.High':
  chinese: '直流电压过高'
'DC Voltage Too Low':
  chinese: '直流电压过低'
'DC Volt.T.Low':
  chinese: '直流电压过低'
'BLVD Load Module Alarm':
  chinese: '电池下电回路模组告警'
'BLVD L.Mod.Alm.':
  chinese: '电池下电回路模组告警'
'DC DU LLVD1 Loop Broken':
  chinese: '直流配电单元一次下电分路断'
'DU LLVD1 Loop Brk.':
  chinese: '配电单元一次下电分路断'
'DC DU LLVD2 Loop Broken':
  chinese: '直流配电单元二次下电分路断'
'DU LLVD2 Loop Brk.':
  chinese: '配电单元二次下电分路断'
'DC DU BLVD Loop Broken':
  chinese: '直流配电单元电池下电分路断'
'DU BLVD Loop Brk.':
  chinese: '配电单元电池下电分路断'
'Common LLVD2 Loop Broken':
  chinese: '公用二次下电分路断'
'Comn.LLVD2 L.Brk.':
  chinese: '公用二次下电分路断'
'Common BLVD Loop Broken':
  chinese: '公用电池下电分路断'
'Comn.BLVD L.Brk.':
  chinese: '公用电池下电分路断'
'DC SPD Abnormal':
  chinese: '直流防雷器异常'
'DC SPD Abr.':
  chinese: '直流防雷器异常'
'Load Switch Trip Alarm':
  chinese: '负载空开脱扣告警'
'Load Switch Trip':
  chinese: '负载空开脱扣'
'Control CB Alarm':
  chinese: '外部控制开关断'
'DC DU LLVD Alarm':
  chinese: '直流配电单元下电告警'
'DU LLVD Alm.':
  chinese: '配电单元下电告警'
'DC Output Loop Broken':
  chinese: '直流输出回路断'
'DC Out.Loop Brk.':
  chinese: '直流输出回路断'
'DC Output Over Voltage Protection':
  chinese: '直流输出过压保护'
'DC Out.OVP.':
  chinese: '直流输出过压保护'
'DC Voltage High Threshold':
  chinese: '直流电压高阈值'
'DC Volt.High Thre.':
  chinese: '直流电压高阈值'
'DC Voltage Low Threshold':
  chinese: '直流电压低阈值'
'DC Volt.Low Thre.':
  chinese: '直流电压低阈值'
'Load Current Slope':
  chinese: '负载电流斜率'
'Load Curr.Slope':
  chinese: '负载电流斜率'
'Load Current Offset':
  chinese: '负载电流零点'
'Load Curr.Offset':
  chinese: '负载电流零点'
'DC Voltage Too High Threshold':
  chinese: '直流电压过高阈值'
'DC V.T.High Thre.':
  chinese: '直流电压过高值'
'DC Voltage Too Low Threshold':
  chinese: '直流电压过低阈值'
'DC V.T.Low Thre.':
  chinese: '直流电压过低值'
'Tenant Name':
  chinese: '租户名称'
'Common Load Config':
  chinese: '公共负载配置'
'Common Load Cfg.':
  chinese: '公共负载配置'
'DC DU LLVD Enabled':
  chinese: '直流配电单元下电使能'
'DU LLVD En.':
  chinese: '配电单元下电使能'
'DC DU LLVD Duration':
  chinese: '直流配电单元下电时间'
'DU LLVD Dura.':
  chinese: '配电单元下电时间'
'DC DU LLVD Voltage':
  chinese: '直流配电单元下电电压'
'DU LLVD Volt.':
  chinese: '配电单元下电电压'
'DC DU LLVD SOC Threshold':
  chinese: '直流配电单元下电SOC阈值'
'DU LLVD SOC':
  chinese: '配电单元下电SOC'
'DC DU LLVD Remote Enabled':
  chinese: '直流配电单元远程下电使能'
'DU LLVD Remote En.':
  chinese: '配电单元远程下电使能'
'Recover':
  chinese: '上电'
'Disconnect':
  chinese: '下电'
'DC DU LLVD Remote':
  chinese: '直流配电单元远程下电'
'DU LLVD Remote':
  chinese: '配电单元远程下电'
'Tenant1':
  chinese: '租户1'
'Tenant2':
  chinese: '租户2'
'Tenant3':
  chinese: '租户3'
'Tenant4':
  chinese: '租户4'
'Tenant5':
  chinese: '租户5'
'Tenant6':
  chinese: '租户6'
'Tenant7':
  chinese: '租户7'
'Tenant8':
  chinese: '租户8'
'Distribution Unit Tenant Config':
  chinese: '配电单元租户设置'
'Dist.Unit Tenant Cfg.':
  chinese: '配电单元租户设置'
'Distribution Unit Name':
  chinese: '配电单元名称'
'Dist.Unit Name':
  chinese: '配电单元名称'
'DC Output Loop Broken Threshold':
  chinese: '直流输出回路断阈值'
'DC Out.Loop Brk.Thres.':
  chinese: '直流输出回路断阈值'
'DC Output Over Voltage Protection Threshold':
  chinese: '直流输出过压保护阈值'
'DC Out.OVP.Thres.':
  chinese: '直流输出过压保护阈值'
'DC Voltage Detect Interval':
  chinese: '直流电压检测间隔'
'DC Vol.Det.Inter.':
  chinese: '直流电压检测间隔'
'DC Output Low Off Enable':
  chinese: '直流输出低关机使能'
'DC Out.Low Off En.':
  chinese: '直流输出低关机使能'
'DC Output Voltage Minimum Threshold':
  chinese: '直流输出最低电压阈值'
'DC Out.Vol.Min.Thres.':
  chinese: '直流输出最低电压阈值'
'PU Default Set Voltage':
  chinese: 'PU默认设定电压'
'PU Def.Set Vol.':
  chinese: 'PU默认设定电压'
'Distribution Unit Fixed Time Disconnect Enabled':
  chinese: '配电单元定时下电使能'
'DU FixT.Disc En.':
  chinese: '配电单元定时下电使能'
'Distribution Unit Fixed Time Disconnect Start Clock1':
  chinese: '配电单元定时下电起始时刻1'
'DU FixT.Disc St.1':
  chinese: '定时下电起始时刻1'
'Distribution Unit Fixed Time Disconnect End Clock1':
  chinese: '配电单元定时下电终止时刻1'
'DU FixT.Disc End.1':
  chinese: '定时下电终止时刻1'
'Distribution Unit Fixed Time Disconnect Start Clock2':
  chinese: '配电单元定时下电起始时刻2'
'DU FixT.Disc St.2':
  chinese: '定时下电起始时刻2'
'Distribution Unit Fixed Time Disconnect End Clock2':
  chinese: '配电单元定时下电终止时刻2'
'DU FixT.Disc End.2':
  chinese: '定时下电终止时刻2'
'Distribution Unit Fixed Time Disconnect Start Clock3':
  chinese: '配电单元定时下电起始时刻3'
'DU FixT.Disc St.3':
  chinese: '定时下电起始时刻3'
'Distribution Unit Fixed Time Disconnect End Clock3':
  chinese: '配电单元定时下电终止时刻3'
'DU FixT.Disc End.3':
  chinese: '定时下电终止时刻3'
'Distribution Unit Fixed Time Disconnect Start Clock4':
  chinese: '配电单元定时下电起始时刻4'
'DU FixT.Disc St.4':
  chinese: '定时下电起始时刻4'
'Distribution Unit Fixed Time Disconnect End Clock4':
  chinese: '配电单元定时下电终止时刻4'
'DU FixT.Disc End.4':
  chinese: '定时下电终止时刻4'
'Busbar Reference Voltage Deviation Threshold':
  chinese: '母排基准电压偏差阈值'
'Busbar Ref.Vol.Dev.Thre.':
  chinese: '母排基准电压偏差阈值'
'DC Output Voltage Maximinum Threshold':
  chinese: '直流输出最高电压阈值'
'DC Out.Vol.Max.Thres.':
  chinese: '直流输出最高电压阈值'
'Distribution Unit Fixed Time Disconnect Start Clock5':
  chinese: '配电单元定时下电起始时刻5'
'DU FixT.Disc St.5':
  chinese: '定时下电起始时刻5'
'Distribution Unit Fixed Time Disconnect End Clock5':
  chinese: '配电单元定时下电终止时刻5'
'DU FixT.Disc End.5':
  chinese: '定时下电终止时刻5'
'Distribution Unit Supply Deadline':
  chinese: '配电单元供电截止日期'
'Dist.Unit Supply Deadline':
  chinese: '配电单元供电截止日期'
'Batt Volt':
  chinese: '电池电压'
'PowerOff Time':
  chinese: '停电时间'
'Batt Rem Cap':
  chinese: '电池剩余容量'
'Distribution Unit Disconnect Mode':
  chinese: '配电单元下电模式'
'DU Disconnect Mode':
  chinese: '配电单元下电模式'
'LLVD1 Load Energy Consumption':
  chinese: '一次下电回路负载电量'
'LLVD1 Load NRG Cons.':
  chinese: '一次下电回路电量'
'LLVD2 Load Energy Consumption':
  chinese: '二次下电回路负载电量'
'LLVD2 Load NRG Cons.':
  chinese: '二次下电回路电量'
'BLVD Load Energy Consumption':
  chinese: '电池下电回路负载电量'
'BLVD Load NRG Cons.':
  chinese: '电池下电回路电量'
'Tenant Total Energy Consumption':
  chinese: '租户总耗电量'
'Ten.NRG Cons.':
  chinese: '租户总电量'
'Public Total Energy consumption':
  chinese: '公用总耗电量'
'Pub.NRG Cons.':
  chinese: '公用总电量'
'Distribution Unit Total Energy Consumption':
  chinese: '配电单元总耗电量'
'Dist.Unit NRG Cons.':
  chinese: '配电单元总电量'
'Extend Distribution Unit Total Energy Consumption':
  chinese: '扩展配电单元总耗电量'
'Ex.DU NRG Cons.':
  chinese: '扩展配电单元总电量'
'DC Distribution Load Total Energy Consumption':
  chinese: '直流配电负载总电量'
'DC Dist. Load NRG Cons.':
  chinese: '直流配电负载总电量'
'Battery Voltage':
  chinese: '电池电压'
'Battery Volt':
  chinese: '电池电压'
'Battery Middle Voltage':
  chinese: '电池中点电压'
'Batt.Midd.Volt':
  chinese: '电池中点电压'
'Battery Current':
  chinese: '电池电流'
'Battery Curr':
  chinese: '电池电流'
'Battery Temperature':
  chinese: '电池温度'
'Battery Temp':
  chinese: '电池温度'
'Estimated Battery Discharge Duration':
  chinese: '预估电池放电时间'
'Estimate Dura.':
  chinese: '预估放电时间'
'Battery Present SOC':
  chinese: '电池组当前容量比率'
'Batt.Prst.SOC':
  chinese: '电池当前容量比'
'Battery Total Recycle Times':
  chinese: '电池累计循环次数'
'Total Cyc.Times':
  chinese: '累计循环次数'
'Battery SOH':
  chinese: '电池SOH'
'Battery Present Cap':
  chinese: '电池组当前容量'
'Batt.Prst.Cap':
  chinese: '电池当前容量'
'Battery Block Voltage':
  chinese: '12V单节电池电压'
'Batt.Block Volt.':
  chinese: '12V单节电压'
'Battery Middle Voltage Detect Value':
  chinese: '电池中点电压检测值'
'Batt.Midd.Volt Det.Value':
  chinese: '电池中点电压检测值'
'Battery Middle Voltage 1':
  chinese: '电池中点电压1'
'Batt.Midd.Volt.1':
  chinese: '电池中点电压1'
'Battery Middle Voltage 2':
  chinese: '电池中点电压2'
'Batt.Midd.Volt.2':
  chinese: '电池中点电压2'
'Battery Middle Voltage Imbalance Rate':
  chinese: '电池中点电压不平衡比率'
'Midd.Volt.Imbala.Rate':
  chinese: '中点电压不平衡比率'
'Battery Middle Voltage Difference':
  chinese: '电池中点电压差值'
'Batt.Mid.Volt.Dif.':
  chinese: '电池中点电压差值'
'Battery Loop Status':
  chinese: '电池回路状态'
'Battery Loop':
  chinese: '电池回路'
'Battery Temperature Sensor Status':
  chinese: '电池温度传感器状态'
'Batt.T.Sensor':
  chinese: '电池温度传感器'
'Health':
  chinese: '健康'
'Sub-health':
  chinese: '亚健康'
'Battery Health':
  chinese: '电池健康程度'
'Battery Exist State':
  chinese: '电池在位状态'
'Batt.Exist':
  chinese: '电池在位'
'Battery Switch Trip Status':
  chinese: '电池空开脱扣状态'
'Batt.Sw.Trip Stat':
  chinese: '电池空开脱扣状态'
'Battery Temperature High':
  chinese: '电池温度高'
'Batt.Temp.High':
  chinese: '电池温度高'
'Battery Temperature Low':
  chinese: '电池温度低'
'Batt.Temp.Low':
  chinese: '电池温度低'
'Battery Loop Broken':
  chinese: '电池回路断'
'Batt.Loop Brk':
  chinese: '电池回路断'
'Battery Temperature Invalid':
  chinese: '电池温度无效'
'Batt.T.Invalid':
  chinese: '电池温度无效'
'Battery Voltage Low':
  chinese: '电池电压低'
'Batt.Volt.Low':
  chinese: '电池电压低'
'Battery Fault':
  chinese: '电池异常'
'Batt.Fault':
  chinese: '电池异常'
'Battery Discharge':
  chinese: '电池放电'
'Batt.Dischg.':
  chinese: '电池放电'
'Battery Missing':
  chinese: '电池丢失'
'Batt. Missing':
  chinese: '电池丢失'
'Battery Current Abnormal':
  chinese: '电池电流异常'
'Batt.Curr.Abr.':
  chinese: '电池电流异常'
'Battery Test Fail':
  chinese: '电池测试失败'
'Batt.Test Fail':
  chinese: '电池测试失败'
'Battery Voltage Too Low':
  chinese: '电池电压过低'
'Batt.Volt.Too L.':
  chinese: '电池电压过低'
'Battery Middle Voltage Imbalance':
  chinese: '电池中点电压不平衡'
'Batt.Mid.V.Imbal.':
  chinese: '中点电压不平衡'
'Battery Switch Trip Alarm':
  chinese: '电池空开脱扣告警'
'Batt.Switch Trip':
  chinese: '电池空开脱扣'
'Battery Start Use Date':
  chinese: '电池启用日期'
'Batt.St.Use Date':
  chinese: '电池启用日期'
'VRLA':
  chinese: '铅酸'
'Li Batt':
  chinese: '锂电'
'Battery Initial SOC':
  chinese: '电池组起始容量比率'
'Batt.Init.SOC':
  chinese: '电池起始容量比'
'Battery Final SOC':
  chinese: '电池组终止容量比率'
'Batt.Final SOC':
  chinese: '电池终止容量比'
'Battery Changed SOC':
  chinese: '电池组变化容量比率'
'Batt.Chg.SOC':
  chinese: '电池变化容量比'
'Initial Battery Voltage':
  chinese: '电池起始电压'
'Init.Batt.Volt.':
  chinese: '电池起始电压'
'Final Battery Voltage':
  chinese: '电池终止电压'
'Fina.Batt.Volt.':
  chinese: '电池终止电压'
'Battery Average Discharge Current':
  chinese: '电池平均放电电流'
'Avg.Dis.Curr':
  chinese: '平均放电电流'
'Battery Test Result':
  chinese: '电池测试结果'
'Test Result':
  chinese: '电池测试结果'
'Battery Initial Charge Power':
  chinese: '电池起始充电电量'
'Batt.Init.Chg.Pwr.':
  chinese: '电池起始充电电量'
'Battery Final Charge Power':
  chinese: '电池终止充电电量'
'Batt.Fina.Chg.Pwr.':
  chinese: '电池终止充电电量'
'Battery Initial Discharge Power':
  chinese: '电池起始放电电量'
'Batt.Init.Dis.Pwr.':
  chinese: '电池起始放电电量'
'Battery Final Discharge Power':
  chinese: '电池终止放电电量'
'Batt.Fina.Dis.Pwr.':
  chinese: '电池终止放电电量'
'Battery Discharge Capacity':
  chinese: '电池放电容量'
'Batt.Dischg.Cap.':
  chinese: '电池放电容量'
'Battery Discharge Power':
  chinese: '电池放电电量'
'Batt.Dis Power':
  chinese: '电池放电电量'
'Battery Charge Accumulated Duration':
  chinese: '电池充电累计时间'
'Batt.Chg.Acc.Dura':
  chinese: '电池充电累计时间'
'Battery Charge Capacity':
  chinese: '电池充电容量'
'Batt.Chg. Cap.':
  chinese: '电池充电容量'
'Battery Charge Power':
  chinese: '电池充电电量'
'Batt.Chg.Pwr.':
  chinese: '电池充电电量'
'Battery Discharge Accumulated Duration':
  chinese: '电池放电累计时间'
'Batt.Dischg.Accum.Dura.':
  chinese: '电池放电时间'
'Battery Charge Times':
  chinese: '电池充电次数'
'Batt.Chg.Times':
  chinese: '电池充电次数'
'Battery Equal Times':
  chinese: '电池均充次数'
'Batt.Equ.Times':
  chinese: '电池均充次数'
'Battery Discharge Times':
  chinese: '电池放电次数'
'Batt.Dischg.Times':
  chinese: '电池放电次数'
'Battery Test Times':
  chinese: '电池测试次数'
'Batt.Test Times':
  chinese: '电池测试次数'
'BMS Discharge Output Voltage':
  chinese: 'BMS放电输出电压'
'BMS Output Volt.':
  chinese: 'BMS放电电压'
'BMS Battery Charge Coefficient':
  chinese: 'BMS电池充电系数'
'BMS Charge Coeff.':
  chinese: 'BMS充电系数'
'Full Charge Duration':
  chinese: '长充持续时间'
'FullChg.Dura.':
  chinese: '长充持续时间'
'Li Battery Average Current':
  chinese: '锂电平均电流'
'Li Batt.Avg.Curr.':
  chinese: '锂电平均电流'
'BMS Power Down Voltage':
  chinese: 'BMS掉电电压'
'BMS Powerdown Volt.':
  chinese: 'BMS掉电电压'
'BMS Mixed Battery Current':
  chinese: 'BMS混用电池电流'
'BMS Mixed Curr.':
  chinese: 'BMS混用电流'
'LLVD1 Control Status':
  chinese: '一次下电控制状态'
'LLVD1 Ctrl.St.':
  chinese: '一次下电控制状态'
'LLVD2 Control Status':
  chinese: '二次下电控制状态'
'LLVD2 Ctrl.St.':
  chinese: '二次下电控制状态'
'BLVD Control Status':
  chinese: '电池下电控制状态'
'BLVD Ctrl.St.':
  chinese: '电池下电控制状态'
'Battery Detect Status':
  chinese: '电池检测状态'
'Battery Status':
  chinese: '电池检测状态'
'BHTD Control Status':
  chinese: '电池高温下电控制状态'
'BHTD Ctrl.St.':
  chinese: '电池高温下电控制状态'
'BLTD Control Status':
  chinese: '电池低温下电控制状态'
'BLTD Ctrl.St.':
  chinese: '电池低温下电控制状态'
'Battery Group Exist State':
  chinese: '电池组在位状态'
'Batt.Group Exist':
  chinese: '电池组在位'
'DC DU LLVD1 Control Status':
  chinese: '直流配电单元一次下电控制状态'
'DU LLVD1 Ctrl.St.':
  chinese: '配电单元一次下电控制状态'
'Full Charge State':
  chinese: '长充状态'
'FullChg.Sta.':
  chinese: '长充状态'
'Li Battery Enable Update Status':
  chinese: '锂电池允许升级状态'
'Enable Update Status':
  chinese: '允许升级状态'
'LLTD Control Status':
  chinese: '负载低温下电控制状态'
'LLTD Ctrl.St.':
  chinese: '负载低温下电控制状态'
'LHTD Control Status':
  chinese: '负载高温下电控制状态'
'LHTD Ctrl.St.':
  chinese: '负载高温下电控制状态'
'LLVD1 Alarm':
  chinese: '一次下电告警'
'LLVD2 Alarm':
  chinese: '二次下电告警'
'BLVD Alarm':
  chinese: '电池下电告警'
'Battery Detection Abnormal':
  chinese: '电池检测异常'
'Batt.Det.Abr.':
  chinese: '电池检测异常'
'Battery Current Imbalance':
  chinese: '电池电流不平衡'
'B.Curr.Imbal.':
  chinese: '电池电流不平衡'
'Battery Testing':
  chinese: '电池测试'
'Battery Equalized Charge':
  chinese: '电池均充'
'Batt. Equal':
  chinese: '电池均充'
'BHTD Alarm':
  chinese: '电池高温下电'
'BLTD Alarm':
  chinese: '电池低温下电'
'Battery Group Missing':
  chinese: '电池组丢失'
'Batt.Group Miss.':
  chinese: '电池组丢失'
'LHTD Alarm':
  chinese: '负载高温下电'
'LLTD Alarm':
  chinese: '负载低温下电'
'Start Float Charge':
  chinese: '启动浮充'
'Start Float':
  chinese: '启动浮充'
'Start Equalized Charge':
  chinese: '启动均充'
'Start Equal':
  chinese: '启动均充'
'Start Test':
  chinese: '启动测试'
'Manual Abort Battery Test':
  chinese: '手动终止电池测试'
'Manual Abort Test':
  chinese: '手动终止测试'
'Start Battery Detect':
  chinese: '启动电池检测'
'Start Batt.Det.':
  chinese: '启动电池检测'
'Battery Cycle Times Reset':
  chinese: '电池循环次数清零'
'Batt.Cyc.Times.Reset':
  chinese: '电池循环次数清零'
'BMS Start Charge':
  chinese: 'BMS启动充电'
'BMS Start Discharge':
  chinese: 'BMS启动放电'
'BMS Device Statistic':
  chinese: 'BMS设备统计'
'BMS Dev.Stat.':
  chinese: 'BMS设备统计'
'Start Charge':
  chinese: '启动充电'
'Start Address Compete':
  chinese: '启动地址竞争'
'Start Addr Compete':
  chinese: '启动地址竞争'
'Battery Capacity':
  chinese: '电池组容量'
'Battery Cap':
  chinese: '电池组容量'
'Float Charge Voltage':
  chinese: '浮充电压'
'Float Voltage':
  chinese: '浮充电压'
'Equalized Charge Voltage':
  chinese: '均充电压'
'Equalized Voltage':
  chinese: '均充电压'
'Test Stop Voltage':
  chinese: '测试终止电压'
'Test Stop Volt':
  chinese: '测试终止电压'
'Equalized Charge Enabled':
  chinese: '均充使能'
'Equalized Enabled':
  chinese: '均充使能'
'Start Voltage Deviation in Transition':
  chinese: '过渡阶段启调电压偏差'
'Start Volt.Dev.':
  chinese: '启调电压偏差'
'Shunt Limit Current Ratio':
  chinese: '分流器限流比率'
'Shu.Lim.Curr.Rat':
  chinese: '分流器限流比率'
'Standby Scenario':
  chinese: '备电场景'
'Cycle Scenario':
  chinese: '循环场景'
'Battery Application Scenario':
  chinese: '电池应用场景'
'Batt.Apl.Scenario':
  chinese: '电池应用场景'
'Battery Temperature High Threshold':
  chinese: '电池温度高阈值'
'Batt.Temp.H.Thre.':
  chinese: '电池过温值'
'Battery Temperature Low Threshold':
  chinese: '电池温度低阈值'
'Batt.Temp.L.Thre.':
  chinese: '电池低温值'
'Battery Detect Period':
  chinese: '电池检测周期'
'Batt.Det.Period':
  chinese: '电池检测周期'
'Battery Loop Broken Threshold':
  chinese: '电池回路断阈值电压'
'Batt.Loop B.Thre.':
  chinese: '电池回路断阈值'
'Battery Discharge Current Threshold':
  chinese: '电池放电阈值'
'Batt.Dischg Thre.':
  chinese: '电池放电阈值'
'Battery Voltage Low Threshold':
  chinese: '电池电压低阈值'
'Batt.Volt.L.Thre.':
  chinese: '电池电压低值'
'Battery Current Abnormal Rate':
  chinese: '电池电流异常比率'
'Batt.Curr.Abr.Rate':
  chinese: '电池电流异常比率'
'Battery Charge Current Coefficient':
  chinese: '电池充电电流系数'
'Chg.Curr.Coeff.':
  chinese: '充电电流系数'
'Battery Test Period':
  chinese: '电池测试周期'
'Test Period':
  chinese: '测试周期'
'Test Maximum Duration':
  chinese: '测试最长时间'
'Test Max. Dura.':
  chinese: '测试最长时间'
'Test Stop SOC Threshold':
  chinese: '测试终止SOC阈值'
'Test Stop SOC':
  chinese: '测试终止SOC'
'Battery Test Fail SOC Threshold':
  chinese: '测试失败SOC阈值'
'Test Fail SOC':
  chinese: '测试失败SOC'
'Preset Equalized Charge Enabled':
  chinese: '预约均充使能'
'Preset Equ.En.':
  chinese: '预约均充使能'
'Preset Equalized Charge Date':
  chinese: '预约均充日期'
'Preset Equ.Date':
  chinese: '预约均充日期'
'Preset Equalized Charge Duration':
  chinese: '预约均充时长'
'Pre.Equ.Chg.Dura.':
  chinese: '预约均充时长'
'Disconnect Mode':
  chinese: '下电模式'
'Disconnect Ctrl Delay':
  chinese: '下电控制延时'
'Discon. Ctrl Delay':
  chinese: '下电控制延时'
'Disconnect Recover Backlash':
  chinese: '下电恢复回差'
'Disc.Rec.Back.':
  chinese: '下电恢复回差'
'Temperature Compensation Mode':
  chinese: '温度补偿模式'
'Temp. Comp. Mode':
  chinese: '温补模式'
'Temperature Compensation Reference':
  chinese: '电池温度补偿基准'
'Temp.Comp.Ref.':
  chinese: '温补基准'
'Equalized Charge Period':
  chinese: '电池均充周期'
'Equalized Period':
  chinese: '均充周期'
'Equalized Charge Maximum Duration':
  chinese: '均充最长时间'
'Equ.Max.Dura.':
  chinese: '均充最长时间'
'Equalized Charge Minimum Duration':
  chinese: '均充最短时间'
'Equ.Min.Dura.':
  chinese: '均充最短时间'
'Equalized Charge End Current Coefficient':
  chinese: '均充末期电流系数'
'Equ.End C.Coeff.':
  chinese: '均充末期电流系数'
'Equalized Charge End Duration':
  chinese: '均充末期维持时间'
'Equ.End Dura.':
  chinese: '均充维持时间'
'Equalized Charge Threshold Duration':
  chinese: '均充阈值放电时间'
'Equ.Thre.Dura.':
  chinese: '均充阈值时间'
'Equalized Charge Threshold Voltage':
  chinese: '均充阈值电压'
'Equ.Thre.Volt.':
  chinese: '均充阈值电压'
'Equalized Charge Threshold Current':
  chinese: '均充阈值电流'
'Equ.Thre.Curr.':
  chinese: '均充阈值电流'
'Equalized Charge Threshold SOC':
  chinese: '均充阈值SOC'
'Equ.Thre.SOC':
  chinese: '均充阈值SOC'
'Environment Temperature Contorl Reference':
  chinese: '环境温度控制基准值'
'Env. T. Ctrl. Ref':
  chinese: '环境温控基准'
'Environment Temperature Contorl Compensation Coefficient':
  chinese: '环境温度控制补偿系数'
'Env.T.Ctrl.Coeff':
  chinese: '环境温控系数'
'LLVD1 Enabled':
  chinese: '负载一次下电使能'
'LLVD1 Duration':
  chinese: '负载一次下电时间'
'LLVD1 Dura.':
  chinese: '一次下电时间'
'LLVD1 Voltage':
  chinese: '负载一次下电电压'
'LLVD1 Volt.':
  chinese: '一次下电电压'
'LLVD1 SOC Threshold':
  chinese: '负载一次下电SOC阈值'
'LLVD1 SOC':
  chinese: '一次下电SOC'
'LLVD2 Enabled':
  chinese: '负载二次下电使能'
'LLVD2 Duration':
  chinese: '负载二次下电时间'
'LLVD2 Dura.':
  chinese: '二次下电时间'
'LLVD2 Voltage':
  chinese: '负载二次下电电压'
'LLVD2 Volt.':
  chinese: '二次下电电压'
'LLVD2 SOC Threshold':
  chinese: '负载二次下电SOC阈值'
'LLVD2 SOC':
  chinese: '二次下电SOC'
'BLVD Enabled':
  chinese: '电池下电使能'
'BLVD Duration':
  chinese: '电池下电时间'
'BLVD Dura.':
  chinese: '电池下电时间'
'BLVD Voltage':
  chinese: '电池下电电压'
'BLVD SOC Threshold':
  chinese: '电池下电SOC阈值'
'BLVD SOC':
  chinese: '电池下电SOC'
'Battery Resistance Fault Threshold':
  chinese: '电池内阻异常阈值'
'Batt. Res. Fault Thres.':
  chinese: '电池内阻异常阈值'
'Battery Detect Duration':
  chinese: '电池检测持续时间'
'Battery Det.Dura.':
  chinese: '电池检测时间'
'BHTD Enabled':
  chinese: '电池高温下电使能'
'BHTD Temperature':
  chinese: '电池高温下电温度'
'BHTD Temp.':
  chinese: '高温下电温度'
'Battery Voltage Temperature Compensation Coefficient':
  chinese: '电池电压温度补偿系数'
'Volt.Temp.Coeff.':
  chinese: '电压温补系数'
'Battery Current Temperature Compensation Coefficient':
  chinese: '电池电流温度补偿系数'
'Curr.Temp.Coeff.':
  chinese: '电流温补系数'
'BLTD Enabled':
  chinese: '电池低温下电使能'
'BLTD Temperature':
  chinese: '电池低温下电温度'
'BLTD Temp.':
  chinese: '低温下电温度'
'Battery Test Start Time':
  chinese: '电池测试启动时刻'
'Test Start Time':
  chinese: '测试启动时刻'
'SOH Abnormal Set Capacity Ratio':
  chinese: 'SOH异常设定容量比例'
'SOH Abr.Cap.Ratio':
  chinese: 'SOH异常容量比例'
'Common':
  chinese: '普通'
'Smart':
  chinese: '智能'
'Battery Charge Mode':
  chinese: '电池充电模式'
'Batt.Charge Mode':
  chinese: '电池充电模式'
'Temperature Compensation Voltage Maximum':
  chinese: '温度补偿电压上限'
'Temp.Volt.Max.':
  chinese: '温补电压上限'
'Temperature Compensation Voltage Minimum':
  chinese: '温度补偿电压下限'
'Temp.Volt.Min.':
  chinese: '温补电压下限'
'Battery Fast Charge Current Coefficient':
  chinese: '电池快充电流系数'
'Fast Chg.Coeff':
  chinese: '快充电流系数'
'Battery Fast Charge Current Temperature Compensation Coefficient':
  chinese: '电池快充温度补偿系数'
'Fast Chg.T.Coeff':
  chinese: '快充温补系数'
'Battery Voltage Too Low Threshold':
  chinese: '电池电压过低阈值'
'Batt.V.T.L.Thre.':
  chinese: '电池电压过低值'
'ZXDC12 CA':
  chinese: 'ZXDC12 CA'
'ZXDC12 CG':
  chinese: 'ZXDC12 CG'
'12V GEL':
  chinese: '12V GEL'
'12V FT':
  chinese: '12V FT'
'12V AGM':
  chinese: '12V AGM'
'2V AGM':
  chinese: '2V AGM'
'2V GEL':
  chinese: '2V GEL'
'ZXDC12 HP':
  chinese: 'ZXDC12 HP'
'ZXDC02 HC':
  chinese: 'ZXDC02 HC'
'ZXDC02 HL':
  chinese: 'ZXDC02 HL'
'ZXDC02 HL E':
  chinese: 'ZXDC02 HL E'
'ZXDC12 HL':
  chinese: 'ZXDC12 HL'
'ZXDC02 HP':
  chinese: 'ZXDC02 HP'
'ZXDC02 HP E':
  chinese: 'ZXDC02 HP E'
'ZXDC12HP A':
  chinese: 'ZXDC12HP A'
'2V Other':
  chinese: '2V Other'
'12V Other':
  chinese: '12V Other'
'Battery Series':
  chinese: '电池系列'
'Battery Exempt Temperatrue Complement Max.':
  chinese: '电池免温补上限'
'Batt.Exempt Temp.Comp.Max.':
  chinese: '电池免温补上限'
'Battery Current Temperatrue Complement Enabled':
  chinese: '电池电流温补使能'
'Batt.C.T.Comp.En.':
  chinese: '电流温补使能'
'Battery Middle Voltage Imbalance Threshold':
  chinese: '电池中点电压不平衡阈值'
'Batt.M.V.Imba.Thre.':
  chinese: '中点电压不平衡阈值'
'LLVD1 Remote Enabled':
  chinese: '远程一次下电使能'
'LLVD1 Remote En.':
  chinese: '远程一次下电使能'
'LLVD2 Remote Enabled':
  chinese: '远程二次下电使能'
'LLVD2 Remote En.':
  chinese: '远程二次下电使能'
'BLVD Remote Enabled':
  chinese: '远程电池下电使能'
'LLVD1 Remote':
  chinese: '远程一次下电'
'LLVD2 Remote':
  chinese: '远程二次下电'
'BLVD Remote':
  chinese: '远程电池下电'
'Battery Charge Over Current Protection Threshold':
  chinese: '电池充电过流保护阈值'
'Batt.Chg.OCP':
  chinese: '电池充过流保护值'
'Battery Discharge Over Current Protection Threshold':
  chinese: '电池放电过流保护阈值'
'Batt.Disch.OCP':
  chinese: '电池放过流保护'
'Battery Over Voltage Protection Threhold':
  chinese: '电池过压保护阈值'
'Batt.OVP':
  chinese: '电池过压保护'
'Board Over Temperature Protect Threshold':
  chinese: '单板过温保护阈值'
'Board OTP.Thrd':
  chinese: '单板过温保护'
'Battery Charge Over Current Alarm Threshold':
  chinese: '电池充电过流告警阈值'
'Batt.Chg.OCA':
  chinese: '电池充过流告警'
'Battery Discharge Over Current Alarm Threshold':
  chinese: '电池放电过流告警阈值'
'Batt.Disch.OCA':
  chinese: '电池放过流告警'
'Battery Over Voltage Alarm Threhold':
  chinese: '电池过压告警阈值'
'Batt.OVA.Thr':
  chinese: '电池过压阈值'
'Board Over Temperature AlarmThreshold':
  chinese: '单板过温告警阈值'
'Board OTA.Thrd':
  chinese: '单板过温告警'
'Inner Environment Over Temperature Alarm Threshold':
  chinese: '机内环境温度高告警阈值'
'Inner Env.OTA':
  chinese: '机内环境过温值'
'Inner Environment Under Temperature Alarm Threshold':
  chinese: '机内环境温度低告警阈值'
'Inner Env.UTA':
  chinese: '机内环境低温值'
'Battery Under Voltage Alarm Threhold':
  chinese: '电池欠压告警阈值'
'Batt.UVA':
  chinese: '电池欠压告警'
'Battery Under Voltage Protection Threhold':
  chinese: '电池欠压保护阈值'
'Batt.UVP':
  chinese: '电池欠压保护'
'Cell Over Volatge Alarm Threshold':
  chinese: '单体过压告警阈值'
'Cell OVA':
  chinese: '单体过压告警'
'Cell Charge Over Voltage Protection Threshold':
  chinese: '单体过压保护阈值'
'Cell OVP':
  chinese: '单体过压保护值'
'Cell Under Voltage Alarm Threhold':
  chinese: '单体欠压告警阈值'
'Cell UVA':
  chinese: '单体欠压告警'
'Cell Under Voltage Protection Threhold':
  chinese: '单体欠压保护阈值'
'Cell UVP Thre':
  chinese: '单体欠压保护值'
'Cell Charge Over Temperature Alarm Threshold':
  chinese: '单体充电高温告警阈值'
'Cell Chg.OTA':
  chinese: '单体充高温告警'
'Cell Charge Over Temperature Protection Threshold':
  chinese: '单体充电高温保护阈值'
'Cell Chg.OTP':
  chinese: '单体充高温保护'
'Cell Discharge Over Temperature Alarm Threshold':
  chinese: '单体放电高温告警阈值'
'Cell Disch.OTA':
  chinese: '单体放高温告警'
'Cell Discharge Over Temperature Protection Threshold':
  chinese: '单体放电高温保护阈值'
'Cell Disch.OTP':
  chinese: '单体放高温保护'
'Cell Charge Under Temperature Alarm Threshold':
  chinese: '单体充电低温告警阈值'
'Cell Chg.UTA':
  chinese: '单体充低温告警'
'Cell Charge Under Temperature Protection Threshold':
  chinese: '单体充电低温保护阈值'
'Cell Chg.UTP':
  chinese: '单体充低温保护'
'Cell Discharge Under Temperature Alarm Threshold':
  chinese: '单体放电低温告警阈值'
'Cell Disch.UTA':
  chinese: '单体放低温告警'
'Cell Discharge Under Temperature Protection Threshold':
  chinese: '单体放电低温保护阈值'
'Cell Disch.UTP':
  chinese: '单体放低温保护'
'Cell Voltage Deviation Threhold':
  chinese: '单体落后电压差阈值'
'Cell Volt.Dev':
  chinese: '单体落后电压差'
'Cell Poor Protect Voltage Deviation Threhold':
  chinese: '单体落后保护电压差阈值'
'Cell Poor Prot.':
  chinese: '单体落后保护压差'
'Battery SOC Low Alarm Threshold':
  chinese: '电池SOC低告警阈值'
'Batt.Soc.Low.A':
  chinese: '电池SOC低告警阈值'
'Battery SOC Low Protect Threshold':
  chinese: '电池SOC低保护阈值'
'Batt.Soc.Low.P':
  chinese: '电池SOC低保护阈值'
'Battery SOH Alarm Threshold':
  chinese: '电池SOH告警阈值'
'Batt.Soh.A':
  chinese: '电池SOH告警阈值'
'Battery SOH Protect Threshold':
  chinese: '电池SOH保护阈值'
'Batt.Soh.P':
  chinese: '电池SOH保护阈值'
'Cell Damage Protect Threshold':
  chinese: '单体损坏保护阈值'
'Cell Damage Prt.':
  chinese: '单体损坏保护阈值'
'Charge Voltage':
  chinese: '充电电压'
'Chg.Volt':
  chinese: '充电电压'
'Charge End Current Coefficient':
  chinese: '充电末期电流系数'
'Chg.End Curr':
  chinese: '充电末期电流'
'Charge End Duration':
  chinese: '充电末期维持时间'
'Chg.End Dura.':
  chinese: '充电维持时间'
'Charge Maximum Duration':
  chinese: '充电最长时间'
'Chg.Max.Dura.':
  chinese: '充电最长时间'
'Battery Self ReCharge Voltage Threhold':
  chinese: '自补电电压阈值'
'Self Chg.Volt.':
  chinese: '自补电电压阈值'
'Battery Self Charge Duration':
  chinese: '自补电周期'
'Self Chg.Dura':
  chinese: '自补电周期'
'Equalization Start Voltage':
  chinese: '单体均衡启动压差'
'Equ.Start Volt':
  chinese: '均衡启动压差'
'Li Battery Charge Current Coefficient':
  chinese: '锂电充电电流系数'
'Li Batt.Chg.C.Coeff':
  chinese: '锂电充电系数'
'Software Shutdown Enable':
  chinese: '软关机使能'
'Sft.Shutdown En.':
  chinese: '软关机使能'
'Bms Buzz Enable':
  chinese: 'BMS蜂鸣器使能'
'Bms Buzz En.':
  chinese: 'BMS蜂鸣器使能'
'System Power Off Voltage':
  chinese: '系统停电电压阈值'
'Sys.Poweroff Volt':
  chinese: '系统停电电压阈值'
'System Power On Voltage':
  chinese: '系统来电电压阈值'
'Sys.Poweron Volt':
  chinese: '系统来电电压阈值'
'History Data Record Interval':
  chinese: '历史数据保存时间间隔'
'His.Data Inter.':
  chinese: '历史数据间隔'
'Out Voltage Deviation in Transition':
  chinese: '输出电压偏差阈值'
'Out Volt.Dev.':
  chinese: '输出电压偏差'
'Li Battery Capacity':
  chinese: '电池容量'
'Batt.Cap.':
  chinese: '电池容量'
'Li Battery Switch SOC':
  chinese: '锂电切换SOC'
'Li Batt.Sw.SOC':
  chinese: '锂电切换SOC'
'BMS Running Mode':
  chinese: 'BMS运行模式'
'Only Li Batt':
  chinese: '纯锂电'
'Mixed':
  chinese: '混用'
'Remote':
  chinese: '远供'
'Li Battery Application Scenario':
  chinese: '锂电应用场景'
'Li Batt.Apl.Scen':
  chinese: '锂电应用场景'
'Li Batt First':
  chinese: '锂电优先放电'
'N_1Cycle':
  chinese: 'N_1循环'
'VRLA Batt First':
  chinese: '铅酸优先放电'
'Recycle Discharge Mode':
  chinese: '循环放电模式'
'Discharge Mode':
  chinese: '放电模式'
'VRLA Battery Switch Voltage':
  chinese: '铅酸切换电压'
'VRLA Batt.Sw.Volt':
  chinese: '铅酸切换电压'
'Remote Discharge Out Voltage':
  chinese: '远供放电输出电压'
'Remote Out Volt':
  chinese: '远供输出电压'
'Cycle Ration':
  chinese: '锂电铅酸循环比率'
'Li Battery Discharge DOD':
  chinese: '锂电放电DOD'
'Li Batt.Disc.DOD':
  chinese: '锂电放电DOD'
'VRLA Battery Discharge Duration':
  chinese: '铅酸单次放电时间'
'VRLA Disc.Dura':
  chinese: '铅酸单次放电时间'
'Software Anti-theft Delay':
  chinese: '软件防盗延时'
'Soft.At.Delay':
  chinese: '软件防盗延时'
'Li Battery Series':
  chinese: '锂电系列'
'BMS Step up Voltage Charge':
  chinese: 'BMS升压充电'
'BMS Step up Volt.Chg':
  chinese: 'BMS升压充电'
'Full Charge Interval':
  chinese: '长充启动周期'
'FullChg.Intv.':
  chinese: '长充周期'
'Full Charge Stop Battery Current':
  chinese: '长充关闭电池电流'
'FullChg.Stop Curr':
  chinese: '长充关闭电流'
'Full Charge Max Duration':
  chinese: '长充最大时长'
'FullChg.Max Dura':
  chinese: '长充最大时长'
'LLVD1 Upload Voltage Backlash':
  chinese: '一次下电恢复回差'
'LLVD1 Upload Back.':
  chinese: '一次下电恢复回差'
'LLVD2 Upload Voltage Backlash':
  chinese: '二次下电恢复回差'
'LLVD2 Upload Back.':
  chinese: '二次下电恢复回差'
'BLVD Upload Voltage Backlash':
  chinese: '电池下电恢复回差'
'BLVD Upload Back.':
  chinese: '电池下电恢复回差'
'LLVD1 Upload Time':
  chinese: '一次下电恢复时间'
'LLVD2 Upload Time':
  chinese: '二次下电恢复时间'
'Period Full Charge Start Clock':
  chinese: '周期长充启动时刻'
'FullChg.St.Clk.':
  chinese: '长充启动时刻'
'Vibration Alarm Enable':
  chinese: '振动告警使能'
'Vibration Alm. En.':
  chinese: '振动告警使能'
'Low':
  chinese: '低灵敏度'
'High':
  chinese: '高灵敏度'
'Gyroscope Sensitivity':
  chinese: '陀螺仪灵敏度'
'Heartbeat Cycle':
  chinese: '心跳周期'
'UVP Temperature Compensation Enable':
  chinese: '整组欠压保护温度补偿'
'UVP Temp. Compensation':
  chinese: '整组欠压保护温度补偿'
'Temperature Compensation Minimum':
  chinese: '电池温度补偿下限'
'Temp.Comp.Min.':
  chinese: '温度补偿下限'
'Temperature Compensation Maximum':
  chinese: '电池温度补偿上限'
'Temp.Comp.Max.':
  chinese: '温度补偿上限'
'Battery Full Voltage':
  chinese: '电池充满电压'
'Batt. Full Volt.':
  chinese: '电池充满电压'
'Li Battery Switch Voltage':
  chinese: '锂电切换电压'
'Li Batt.Sw.Volt':
  chinese: '锂电切换电压'
'Full Charge Voltage':
  chinese: '长充电压'
'Full Chg.Volt':
  chinese: '长充电压'
'Li Battery Force Update Enable':
  chinese: '锂电池强制升级使能'
'Li Batt. Force Update Enable':
  chinese: '锂电池强制升级使能'
'Single Phase Float Charge Voltage':
  chinese: '单相浮充电压'
'Three Phase Float Charge Voltage':
  chinese: '三相浮充电压'
'Single Phase Charge Voltage':
  chinese: '单相充电电压'
'Single Phase Chg.Volt':
  chinese: '单相充电电压'
'Three Phase Charge Voltage':
  chinese: '三相充电电压'
'Three Phase Chg.Volt':
  chinese: '三相充电电压'
'Single Phase Battery Voltage Temperature Compensation Coefficient':
  chinese: '单相电池电压温度补偿系数'
'Single Phase Volt.Temp.Coeff.':
  chinese: '单相电压温补系数'
'Three Phase Battery Voltage Temperature Compensation Coefficient':
  chinese: '三相电池电压温度补偿系数'
'Three Phase Volt.Temp.Coeff.':
  chinese: '三相电压温补系数'
'NFB Cell Under Voltage Alarm Threshold':
  chinese: 'NFB单体欠压告警阈值'
'NFB Cell UVA.Thre.':
  chinese: 'NFB单体欠压告警'
'NFB Cell Under Voltage Protection Threshold':
  chinese: 'NFB单体欠压保护阈值'
'NFB Cell UVP.Thre.':
  chinese: 'NFB单体欠压保护'
'NFB Cell Under Voltage Protection Resume Threshold':
  chinese: 'NFB单体欠压保护恢复阈值'
'NFB Cell UVP.Res.':
  chinese: 'NFB单体UVP恢复'
'NFB Cell Under Voltage Alarm Resume Threshold':
  chinese: 'NFB单体欠压告警恢复阈值'
'NFB Cell UVA.Res.':
  chinese: 'NFB单体UVA恢复'
'NFB Battery Under Voltage Alarm Threshold':
  chinese: 'NFB电池欠压告警阈值'
'NFB Batt.UVA.Thre.':
  chinese: 'NFB电池欠压告警'
'NFB Battery Under Voltage Protection Threshold':
  chinese: 'NFB电池欠压保护阈值'
'NFB Batt.UVP.Thre.':
  chinese: 'NFB电池欠压保护'
'NFB Battery Under Voltage Protection Resume Threshold':
  chinese: 'NFB电池欠压保护恢复阈值'
'NFB Batt.UVP.Res.':
  chinese: 'NFB电池UVP恢复'
'NFB Battery Under Voltage Alarm Resume Threshold':
  chinese: 'NFB电池欠压告警恢复阈值'
'NFB Batt.UVA.Res.':
  chinese: 'NFB电池UVA恢复'
'Battery Missing Enable':
  chinese: '电池丢失使能'
'Batt.Missing En.':
  chinese: '电池丢失使能'
'Battery Gyroscope Tilt':
  chinese: '陀螺仪倾角'
'Batt.Gyro.Tilt':
  chinese: '陀螺仪倾角'
'Anti-theft Line Enable':
  chinese: '防盗线使能'
'Anti-theft Line En.':
  chinese: '防盗线使能'
'Battery Buzzer Duration':
  chinese: '蜂鸣器持续时间'
'Buzz.Dura.':
  chinese: '蜂鸣器时间'
'Anti-theft CommFail Delay':
  chinese: '防盗通信断延时'
'CommFail Delay':
  chinese: '通信断延时'
'Anti-theft GPS Distance':
  chinese: 'GPS防盗距离'
'Anti-theft GPS Dista.':
  chinese: 'GPS防盗距离'
'Through Enable':
  chinese: '直通使能'
'LLTD Enabled':
  chinese: '负载低温下电使能'
'LLTD Temperature':
  chinese: '负载低温下电温度'
'LLTD Temp.':
  chinese: '负载低温下电温度'
'LHTD Enabled':
  chinese: '负载高温下电使能'
'LHTD Temperature':
  chinese: '负载高温下电温度'
'LHTD Temp.':
  chinese: '负载高温下电温度'
'LHTD Recover Temperature':
  chinese: '负载高温下电恢复温度'
'LHTD Re.Temp.':
  chinese: '负载高温下电恢复温度'
'LHTD Judge Time':
  chinese: 'LHTD判断时间'
'BMS Remote IP Address':
  chinese: 'BMS远程IP地址'
'BMS Remote IP':
  chinese: 'BMS远程IP'
'BMS Remote Port':
  chinese: 'BMS远程端口'
'BMS GPRS User Name':
  chinese: 'BMS GPRS用户名'
'BMS GPRS Password':
  chinese: 'BMS GPRS密码'
'BMS GPRS Access Point Name':
  chinese: 'BMS GPRS接入点名称'
'BMS GPRS APN':
  chinese: 'BMS GPRS接入点名称'
'BMS SMS Center Number':
  chinese: 'BMS短信中心号码'
'BMS SMS Center Num':
  chinese: 'BMS短信中心号码'
'Battery Discharge Maximum Current':
  chinese: '放电最大电流'
'Batt.Dischg.Max.Curr.':
  chinese: '放电最大电流'
'Power Down Voltage Threshold':
  chinese: '掉电电压阈值'
'Power Down Volt.Ths':
  chinese: '掉电电压阈值'
'Cell Dynamic Under Voltage Protect Enable':
  chinese: '单体动态欠压保护'
'Cell under volt.prot':
  chinese: '单体动态欠压保护'
'Through Discharge Enable':
  chinese: '直通放电使能'
'Through Dischg.En.':
  chinese: '直通放电使能'
'LLVD1 Fixed Time Disconnect Enabled':
  chinese: '定时一次下电使能'
'LLVD1 FixT.Disc En.':
  chinese: '定时一次下电使能'
'LLVD1 Fixed Time Disconnect Start Clock':
  chinese: '定时一次下电起始时刻'
'LLVD1 FixT.Disc St.':
  chinese: '定时一次下电起始时刻'
'LLVD1 Fixed Time Disconnect End Clock':
  chinese: '定时一次下电终止时刻'
'LLVD1 FixT.Disc End.':
  chinese: '定时一次下电终止时刻'
'LLVD2 Fixed Time Disconnect Enabled':
  chinese: '定时二次下电使能'
'LLVD2 FixT.Disc En.':
  chinese: '定时二次下电使能'
'LLVD2 Fixed Time Disconnect Start Clock':
  chinese: '定时二次下电起始时刻'
'LLVD2 FixT.Disc St.':
  chinese: '定时二次下电起始时刻'
'LLVD2 Fixed Time Disconnect End Clock':
  chinese: '定时二次下电终止时刻'
'LLVD2 FixT.Disc End.':
  chinese: '定时二次下电终止时刻'
'Battery Charging Current Limit':
  chinese: '电池充电限流值'
'Batt.Chg. Limit':
  chinese: '电池充电限流值'
'Inner Environment Over Temperature Protection Threshold':
  chinese: '机内环境温度高保护阈值'
'Inner Env.OTP':
  chinese: '机内环境过温保护阈值'
'Inner Environment Under Temperature Protection Threshold':
  chinese: '机内环境温度低保护阈值'
'Inner Env.UTP':
  chinese: '机内环境低温保护阈值'
'Outage Time Protection Threshold':
  chinese: '停电时间保护阈值'
'Outage Time PT':
  chinese: '停电时间保护阈值'
'Inner Environment Over Temperature Protection Enabled':
  chinese: '机内环境温度高保护使能'
'Inner Env.OTPE':
  chinese: '机内环境过温保护使能'
'Inner Environment Under Temperature Protection Enabled':
  chinese: '机内环境温度低保护使能'
'Inner Env.UTPE':
  chinese: '机内环境低温保护使能'
'SOC Low Protection Enabled':
  chinese: 'SOC低保护使能'
'SOC LPE':
  chinese: 'SOC低保护使能'
'Outage Time Protection Enabled':
  chinese: '停电时间保护使能'
'Outage Time PE':
  chinese: '停电时间保护使能'
'Smart Intermittent Charge Enable':
  chinese: '智能间歇充电使能'
'SIC.En.':
  chinese: '智能间歇充电使能'
'Charge Current Limit Enable':
  chinese: '充电限流使能'
'Chg.Limit En.':
  chinese: '充电限流使能'
'[0%,10%)':
  chinese: '[0%,10%)'
'[10%,20%)':
  chinese: '[10%,20%)'
'[20%,30%)':
  chinese: '[20%,30%)'
'[30%,40%)':
  chinese: '[30%,40%)'
'[40%,50%)':
  chinese: '[40%,50%)'
'[50%,60%)':
  chinese: '[50%,60%)'
'[60%,70%)':
  chinese: '[60%,70%)'
'[70%,80%)':
  chinese: '[70%,80%)'
'[80%,90%)':
  chinese: '[80%,90%)'
'[90%,100%]':
  chinese: '[90%,100%]'
'Discharge Depth Range':
  chinese: '放电深度区间'
'Discharge Depth':
  chinese: '放电深度区间'
'Battery Temperature Statistics':
  chinese: '电池温度统计'
'Batt.Temp.Stat.':
  chinese: '电池温度统计'
'[-40℃,-30℃)':
  chinese: '[-40℃,-30℃)'
'[-30℃,-20℃)':
  chinese: '[-30℃,-20℃)'
'[-20℃,-10℃)':
  chinese: '[-20℃,-10℃)'
'[-10℃,0℃)':
  chinese: '[-10℃,0℃)'
'[0℃,10℃)':
  chinese: '[0℃,10℃)'
'[10℃,20℃)':
  chinese: '[10℃,20℃)'
'[20℃,25℃)':
  chinese: '[20℃,25℃)'
'[25℃,30℃)':
  chinese: '[25℃,30℃)'
'[30℃,40℃)':
  chinese: '[30℃,40℃)'
'[40℃,50℃)':
  chinese: '[40℃,50℃)'
'[50℃,60℃)':
  chinese: '[50℃,60℃)'
'[60℃,70℃)':
  chinese: '[60℃,70℃)'
'[70℃,80℃)':
  chinese: '[70℃,80℃)'
'[80℃,90℃)':
  chinese: '[80℃,90℃)'
'[90℃,100℃]':
  chinese: '[90℃,100℃]'
'Battery Temperature Range':
  chinese: '电池温度区间'
'Batt.Temp.Range':
  chinese: '电池温度区间'
'Discharge Statistics':
  chinese: '电池放电统计'
'Discharge Stat.':
  chinese: '电池放电统计'
'Battery Discharge History Time':
  chinese: '电池放电记录时间'
'Dischg.His.Time':
  chinese: '放电历史时间'
'Battery Charge Record Time':
  chinese: '电池充电记录时间'
'Batt.Chag.Rec.Time':
  chinese: '电池充电记录时间'
'Battery Equalized Charge Record Time':
  chinese: '电池均充记录时间'
'Batt.Equal.Rec.Time':
  chinese: '电池均充记录时间'
'Battery Test Record Time':
  chinese: '电池测试记录时间'
'Batt.Test Rec.Time':
  chinese: '电池测试记录时间'
'Battery Discharge Duration':
  chinese: '电池放电持续时间'
'Discharge Dura.':
  chinese: '放电持续时间'
'Manual':
  chinese: '手动'
'TimerFault':
  chinese: '定时器异常'
'Smr None':
  chinese: '无整流器'
'Batt Curr Fault':
  chinese: '电池电流异常'
'Batt Cap':
  chinese: '电池容量'
'System Reset':
  chinese: '系统复位'
'PowerOFF':
  chinese: '系统停电'
'Batt Fault':
  chinese: '电池组异常'
'DC Volt':
  chinese: '直流电压'
'Battery Test Stop Cause':
  chinese: '电池测试退出原因'
'Test Stop Cause':
  chinese: '测试退出原因'
'Battery Test Type':
  chinese: '电池测试类型'
'Batt.Test Type':
  chinese: '电池测试类型'
'Periodic':
  chinese: '定期'
'Last Charge':
  chinese: '连续充电'
'Battery Test Start Cause':
  chinese: '电池测试进入原因'
'Test Start Cause':
  chinese: '测试进入原因'
'Battery Test Stop Time':
  chinese: '电池测试结束时间'
'Test Stop Time':
  chinese: '测试结束时间'
'Battery Test History Time':
  chinese: '电池测试记录时间'
'Test His.Time':
  chinese: '测试历史时间'
'Battery Test Duration':
  chinese: '电池测试持续时间'
'Batt.Test Dura.':
  chinese: '测试持续时间'
'Next Equalized Charge Time':
  chinese: '下次均充时间'
'Next Equ.Time':
  chinese: '下次均充时间'
'Next Detect Time':
  chinese: '下次检测时间'
'Next Det.Time':
  chinese: '下次检测时间'
'Next Test Time':
  chinese: '下次测试时间'
'Battery Resistance Detect Left Time':
  chinese: '电池内阻检测剩余时间'
'Res.Det Left Time':
  chinese: '内阻检测剩余时间'
'SMR None':
  chinese: '无整流器'
'Duration':
  chinese: '维持时间'
'Fixed-time':
  chinese: '固定时间'
'Batt Temp Fault':
  chinese: '电池温度异常'
'DcVoltFault':
  chinese: '电压异常'
'Equal Disabled':
  chinese: '均充禁止'
'Env Temp Fault':
  chinese: '环境温度异常'
'Energy Short':
  chinese: '能源不足'
'Battery Equalized Charge Stop Cause':
  chinese: '电池均充退出原因'
'Equ.Stop Cause':
  chinese: '均充退出原因'
'Order':
  chinese: '预约'
'Repair SOH':
  chinese: 'SOH修复'
'Battery Equalized Charge Start Cause':
  chinese: '电池均充进入原因'
'Equ.Start Cause':
  chinese: '均充进入原因'
'Record Duration':
  chinese: '记录持续时间'
'Batt Disconnect':
  chinese: '电池下电'
'Input None':
  chinese: '无输入'
'Batt Temp High':
  chinese: '电池温度高'
'Dc_Volt_High':
  chinese: '直流电压高'
'Env Temp High':
  chinese: '环境温度高'
'Test Max Time':
  chinese: '测试最大时间'
'Test Volt':
  chinese: '测试电压'
'Test Cap':
  chinese: '测试容量'
'IDDB comm. fail':
  chinese: 'IDDB通讯断'
'FBBMS comm. fail':
  chinese: 'FBBMS通讯断'
'Record Stop Cause':
  chinese: '记录结束原因'
'Stop Cause':
  chinese: '结束原因'
'Equal Continue':
  chinese: '均充延续'
'Test Time':
  chinese: '测试时间'
'Equ.Thre.Cap':
  chinese: '均充阈值容量'
'Record Start Cause':
  chinese: '记录开始原因'
'Start Cause':
  chinese: '开始原因'
'Power Off':
  chinese: '停电'
'Trans':
  chinese: '过渡'
'Record Stop Battery Status':
  chinese: '记录结束电池状态'
'Stop Battery Status':
  chinese: '结束电池状态'
'Record Start Battery Status':
  chinese: '记录开始电池状态'
'Start Battery Status':
  chinese: '开始电池状态'
'Next Full Charge Time':
  chinese: '下次长充时间'
'Next Full Chg. Time':
  chinese: '下次长充时间'
'Load Power Consumption':
  chinese: '负载用电量'
'Load Power Cons.':
  chinese: '负载用电量'
'Batt.Volt.T.L.Thre.':
  chinese: '电池电压过低值'
'DG Start Voltage Threshold':
  chinese: '油机启动电压'
'DG Start Volt.':
  chinese: '油机启动电压'
'DG Start SOC Threshold':
  chinese: '油机启动SOC阈值'
'DG Start SOC':
  chinese: '油机启动SOC'
'DG Start Discharge Duration':
  chinese: '油机启动放电时间阈值'
'DG St.Dis.Dura.':
  chinese: '油机启动放电时间'
'DG Stop Voltage Threshold':
  chinese: '油机停止电压'
'DG Stop Volt.':
  chinese: '油机停止电压'
'DG Stop SOC Threshold':
  chinese: '油机关闭SOC阈值'
'DG Stop SOC':
  chinese: '油机关闭SOC'
'DG Stop Battery Current':
  chinese: '油机关闭电池电流'
'DG Stop Bat.Curr.':
  chinese: '油机关闭电流'
'Mains and DG Equalized Charge Threshold SOC':
  chinese: '油电均充阈值SOC'
'Mains.DG Equ.Thre.SOC':
  chinese: '油电均充阈值SOC'
'None Battery Charge Current Coefficient':
  chinese: '无交流电池充电电流系数'
'None Chg.Curr.Coeff.':
  chinese: '无交流充电电流系数'
'Mains and DG Minimum Qty of Started SMRs':
  chinese: '油电整流器最小开机数量'
'Mains.DG Min Num Start SMR':
  chinese: '油电整流器最小开机数'
'Mains and DG Battery Detect Period':
  chinese: '油电电池检测周期'
'Mains.DG Batt.Det.Period':
  chinese: '油电电池检测周期'
'Mains and DG DG Start Discharge Duration':
  chinese: '油电油机启动放电时间阈值'
'Mains.DG DG St.Dis.Dura.':
  chinese: '油电油机启动放电时间'
'Mains and DG DC Voltage Low Threshold':
  chinese: '油电直流电压低阈值'
'Mains.DG DC Volt.Low Thre.':
  chinese: '油电直流电压低阈值'
'Mains and DG Battery Voltage Low Threshold':
  chinese: '电池电压低阈值'
'Mains.DG Batt.Volt.L.Thre.':
  chinese: '电池电压低值'
'Mains and DG MAINS Start SOC Enable':
  chinese: '油电市电启动SOC使能'
'Mains.DG MAINS Start SOC En.':
  chinese: '油电市电启动SOC使能'
'DG Start SOC Enable':
  chinese: '油机启动SOC使能'
'DG Start SOC En.':
  chinese: '油机启动SOC使能'
'Mains and DG DG Start SOC Enable':
  chinese: '油电油机启动SOC使能'
'Mains.DG DG Start SOC En.':
  chinese: '油电油机启动SOC使能'
'Mains and DG MAINS Max Running Duration':
  chinese: '油电市电最长运行时间'
'Mains.DG MAINS Max Run.Dura.':
  chinese: '油电市电最长运行时间'
'DG Max Running Duration':
  chinese: '油机最长运行时间'
'DG Max Run.Dura.':
  chinese: '油机最长运行时间'
'Mains and DG DG Max Running Duration':
  chinese: '油电油机最长运行时间'
'Mains.DG DG Max Run.Dura.':
  chinese: '油电油机最长运行时间'
'Min DG Running Duration':
  chinese: '油机最短运行时间'
'Min DG Run.Dura.':
  chinese: '油机最短运行时间'
'Mains and DG Diesel Generator Power':
  chinese: '油电柴油发电机额定功率'
'Mains.DG DG Power':
  chinese: '油电油机额定功率'
'Mains and DG DC Voltage Too Low Threshold':
  chinese: '油电直流电压过低阈值'
'Mains.DG DC V.T.Low Thre.':
  chinese: '油电直流电压过低值'
'PV SPD Abnormal':
  chinese: '光伏防雷器异常'
'PV SPD Abr.':
  chinese: '光伏防雷器异常'
'PV component Missing':
  chinese: '光伏组件丢失'
'PV.Comp.Missing':
  chinese: '光伏组件丢失'
'All PU Commfail Alarm':
  chinese: '所有PU模块通讯断告警'
'All PU Commfail.':
  chinese: '所有PU模块通讯断'
'All SPCU Commfail Alarm':
  chinese: '所有SPCU模块通讯断告警'
'All SPCU Commfail.':
  chinese: '所有SPCU模块通讯断'
'PU Device Statistic':
  chinese: 'PU设备统计'
'PU Dev.Stat.':
  chinese: 'PU设备统计'
'SPCU Device Statistic':
  chinese: 'SPCU设备统计'
'SPCU Dev.Stat.':
  chinese: 'SPCU设备统计'
'PU Output OVP Voltage Threshold':
  chinese: 'PU输出过压保护值'
'PU Out.OVP Volt.':
  chinese: 'PU输出过压值'
'PU Default Ouput Voltage':
  chinese: 'PU默认输出电压值'
'PU.Def.Out.Volt.':
  chinese: '默认输出电压值'
'PU Maximum Quantity':
  chinese: 'PU最大数量'
'PU Max.Qty.':
  chinese: 'PU最大数量'
'SPU Setting Current Limit Rate':
  chinese: 'SPU当前输出限流点比率'
'SPU Set.Curr.Rate':
  chinese: 'SPU当前输出限流点'
'SPU Default Current Limit Rate':
  chinese: 'SPU默认输出限流点比率'
'SPU Set.Def.Rate':
  chinese: 'SPU默认输出限流点'
'Smart Photovoltaic System Default Output Voltage':
  chinese: '叠光电源默认输出电压'
'SPV Def.Out.Vol.':
  chinese: '叠光默认输出电压'
'PV Power Generation':
  chinese: '光伏发电量'
'PV Power Generation Range':
  chinese: '光伏发电量区间'
'PV.Pow.Gen.Range':
  chinese: '光伏发电量区间'
'PV Initial Power Generation':
  chinese: '光伏起始发电量'
'PV Init.Power Generation':
  chinese: '光伏起始发电量'
'PV Final Power Generation':
  chinese: '光伏终止发电量'
'PV final Power Generation':
  chinese: '光伏终止发电量'
'PV Power Total Generation':
  chinese: '累计光伏发电量'
'PV Total Pwr.Gene.':
  chinese: '累计光伏发电量'
'PV Work Duration':
  chinese: '光伏工作时间'
'PV Work Dura.':
  chinese: '光伏工作时间'
'PV Work Record Time':
  chinese: '太阳能工作记录时间'
'PV.Wrk.Rec.':
  chinese: '太阳能工作记录时间'
'Power Unit Output Voltage':
  chinese: 'PU输出电压'
'PU Output Volt.':
  chinese: 'PU输出电压'
'Power Unit Output Current':
  chinese: 'PU输出电流'
'PU Output Curr.':
  chinese: 'PU输出电流'
'Power Unit Input Voltage':
  chinese: 'PU输入电压'
'PU Input Volt.':
  chinese: 'PU输入电压'
'Power Unit Temperature':
  chinese: 'PU温度'
'PU Temp.':
  chinese: 'PU温度'
'Power Unit Input Current':
  chinese: 'PU输入电流'
'PU Input Curr.':
  chinese: 'PU输入电流'
'Power Unit Heat Sink Temperature':
  chinese: 'PU散热片温度'
'PU Heat Sink Temp.':
  chinese: 'PU散热片温度'
'Power Unit Fan Speed':
  chinese: 'PU风扇转速'
'PU Fan Speed.':
  chinese: 'PU风扇转速'
'Power Unit Group Address':
  chinese: 'PU组地址'
'PU Group Address':
  chinese: 'PU组地址'
'Power Unit Group Inner Address':
  chinese: 'PU组内地址'
'PU Group Inner Address':
  chinese: 'PU组内地址'
'Power Unit Output Power':
  chinese: 'PU输出功率'
'PU Output Power':
  chinese: 'PU输出功率'
'Power Unit Slot Address':
  chinese: 'PU槽位地址'
'PU Slot Address':
  chinese: 'PU槽位地址'
'Power Unit Off Status':
  chinese: 'PU关机状态'
'PU Off':
  chinese: 'PU关机'
'Power Unit P2P Status':
  chinese: 'PU一键功能状态'
'PU P2P':
  chinese: 'PU一键功能'
'Power Unit MPPT Status':
  chinese: 'PU MPPT状态'
'MPPT':
  chinese: 'MPPT状态'
'Power Unit Working Status':
  chinese: 'PU工作状态'
'PU Work Status':
  chinese: 'PU工作状态'
'Power Unit Input Invalid Status':
  chinese: 'PU输入无效状态'
'Input Invalid':
  chinese: '输入无效'
'Power Unit Output Current Limit Status':
  chinese: 'PU输出限流状态'
'PU Ouput CL':
  chinese: 'PU输出限流'
'Power Unit Sleep Status':
  chinese: 'PU休眠状态'
'PU Sleep Sts.':
  chinese: 'PU休眠状态'
'PU Fan Control State':
  chinese: 'PU风扇控制状态'
'PU Fan Ctrl.':
  chinese: 'PU风扇控制'
'PU Software Update Enable':
  chinese: 'PU升级使能状态'
'PU Update Enable':
  chinese: 'PU升级使能状态'
'PU Input CurrentLimit':
  chinese: 'PU输入限流状态'
'PU Input C.Lim.':
  chinese: 'PU输入限流状态'
'Power Unit Input OVP Status':
  chinese: 'PU输入过压状态'
'PU In. OVP.Status':
  chinese: 'PU输入过压状态'
'Power Unit Output OVP Status':
  chinese: 'PU输出过压状态'
'PU Out. OVP Status':
  chinese: 'PU输出过压状态'
'Power Unit OTP Status':
  chinese: 'PU过温状态'
'PU OTP Status':
  chinese: 'PU过温告警状态'
'Power Unit Output Over Current Status':
  chinese: 'PU输出过流状态'
'PU Output OC Status':
  chinese: 'PU输出过流状态'
'Power Unit Output Fuse Failure Status':
  chinese: 'PU输出熔丝断状态'
'PU Out.Fuse Status':
  chinese: 'PU输出熔丝断状态'
'Power Unit Radiator OTP Status':
  chinese: 'PU散热器过温状态'
'PU Ra. OTP.Status':
  chinese: 'PU散热器过温状态'
'Power Unit Fan Fault Status':
  chinese: 'PU风扇故障状态'
'PU Fan Fault Status':
  chinese: 'PU风扇故障状态'
'Power Unit EEPROM Failure Status':
  chinese: 'PU EEPROM异常状态'
'PU EEPROM Status':
  chinese: 'PU EEPROM异常状态'
'Power Unit Output UVP Status':
  chinese: 'PU 输出欠压状态'
'PU Out.UVP.Status':
  chinese: 'PU输出欠压状态'
'PV Loop Abnormal Status':
  chinese: '光伏回路异常状态'
'PV Loop Abn.Status':
  chinese: '光伏回路异常状态'
'PU SN Clash Status':
  chinese: 'PU序列号冲突状态'
'PU SN Clash Sts':
  chinese: 'PU SN冲突状态'
'PU Protocol Error Status':
  chinese: 'PU协议错误状态'
'PU Pro.Err.Status':
  chinese: 'PU协议错误状态'
'Power Unit Input Over Current Status':
  chinese: 'PU输入过流状态'
'PU Input OC.Status':
  chinese: 'PU输入过流状态'
'PU Communication Status':
  chinese: 'PU通讯状态'
'Power Unit Input UVP Status':
  chinese: 'PU输入欠压状态'
'PU Input UVP Stu.':
  chinese: 'PU输入欠压状态'
'Power Unit Input none Status':
  chinese: 'PU无输入状态'
'PU In.none Stu.':
  chinese: 'PU无输入状态'
'Power Unit Output Shortcut Status':
  chinese: 'PU输出短路状态'
'PU Shortcut Status':
  chinese: 'PU输出短路状态'
'Power Unit Input Flow Back Status':
  chinese: 'PU输入倒灌状态'
'PU In.Flw.Bck.Stu':
  chinese: 'PU输入倒灌状态'
'PU Exist State':
  chinese: 'PU在位状态'
'Power Unit Input OVP Alarm':
  chinese: 'PU输入过压告警'
'PU In. OVP.':
  chinese: 'PU输入过压'
'Power Unit Output OVP Alarm':
  chinese: 'PU输出过压告警'
'PU Out. OVP':
  chinese: 'PU输出过压'
'Power Unit OTP Alarm':
  chinese: 'PU过温告警'
'PU OTP Alm.':
  chinese: 'PU过温告警'
'Power Unit Output Over Current Alarm':
  chinese: 'PU输出过流告警'
'PU Output OC':
  chinese: 'PU输出过流'
'Power Unit Output Fuse Failure Alarm':
  chinese: 'PU输出熔丝断告警'
'PU Out.Fuse':
  chinese: 'PU输出熔丝断'
'Power Unit Radiator OTP Alarm':
  chinese: 'PU散热器过温告警'
'PU Ra. OTP.':
  chinese: 'PU散热器过温'
'Power Unit Fan Fault Alarm':
  chinese: 'PU风扇故障'
'PU Fan Fault':
  chinese: 'PU风扇故障'
'Power Unit EEPROM Failure Alarm':
  chinese: 'PU EEPROM异常'
'PU EEPROM':
  chinese: 'PU EEPROM异常'
'Power Unit Output UVP Alarm':
  chinese: 'PU 输出欠压'
'PU Out.UVP.':
  chinese: 'PU输出欠压'
'PV Loop Abnormal Alarm':
  chinese: '光伏回路异常告警'
'PV Loop Abn.':
  chinese: '光伏回路异常'
'PU SN Clash Alarm':
  chinese: 'PU序列号冲突'
'PU SN Clash':
  chinese: 'PU SN冲突'
'PU Protocol Error':
  chinese: 'PU协议错误'
'PU Pro.Err.':
  chinese: 'PU协议错误'
'Power Unit Input Over Current Alarm':
  chinese: 'PU输入过流告警'
'PU Input OC.':
  chinese: 'PU输入过流'
'PU Alarm':
  chinese: 'PU告警'
'PU Communication Fail':
  chinese: 'PU通讯中断'
'PU Comm.Fail':
  chinese: 'PU通讯中断'
'Power Unit Input UVP Alarm':
  chinese: 'PU输入欠压告警'
'PU Input UVP':
  chinese: 'PU输入欠压'
'Power Unit Input none Alarm':
  chinese: 'PU无输入告警'
'PU Input none':
  chinese: 'PU无输入'
'Power Unit Output Shortcut Alarm':
  chinese: 'PU输出短路告警'
'PU Shortcut Alm.':
  chinese: 'PU输出短路'
'Power Unit Input Flow Back Alarm':
  chinese: 'PU输入倒灌告警'
'PU Input Flw.Back':
  chinese: 'PU输入倒灌'
'PU Fault':
  chinese: 'PU故障'
'PU Waken':
  chinese: 'PU唤醒'
'PU Sleep':
  chinese: 'PU休眠'
'Power Unit Enter P2P Mode':
  chinese: 'PU进入一键模式'
'PU Enter P2P':
  chinese: 'PU进入一键模式'
'Power Unit Quit P2P Mode':
  chinese: 'PU退出一键模式'
'PU Quit P2P':
  chinese: 'PU退出一键模式'
'PU Fan Control Enable':
  chinese: 'PU风扇调速允许'
'Fan Ctrl.En.':
  chinese: 'PU风扇调速允许'
'PU Fan Control Disable':
  chinese: 'PU风扇调速禁止'
'Fan Ctrl.Dis.':
  chinese: 'PU风扇调速禁止'
'Modify PU Address':
  chinese: '修改PU地址'
'Modify PU Addr':
  chinese: '修改PU地址'
'PU Communication Fail alarm clear':
  chinese: 'PU通讯中断告警清除'
'PU Commfail clear':
  chinese: 'PU通讯中断清除'
'Power Unit Serial Number':
  chinese: 'PU序列号'
'PU SN':
  chinese: 'PU序列号'
'Power Unit Software Version':
  chinese: 'PU软件版本'
'PU Software Ver.':
  chinese: 'PU软件版本'
'Power Unit Software Release Date':
  chinese: 'PU软件发布日期'
'PU Software Date':
  chinese: 'PU软件发布日期'
'PU Digital Control Platform Version':
  chinese: 'PU数控平台版本'
'PU Platform.':
  chinese: 'PU数控平台'
'PU System Name':
  chinese: 'PU系统名称'
'PU Sys Name':
  chinese: 'PU系统名称'
'PU Manufactory ID':
  chinese: '制造商ID'
'PU Manufactory Address':
  chinese: '制造商地址'
'PU Barcodes':
  chinese: 'PU条码'
'PU Manufacture Date':
  chinese: '生产日期'
'Batt.Curr.Temp.Comp.Enabled':
  chinese: '电流温补使能'
'DC Volt.T.High Thre.':
  chinese: '直流电压过高值'
'Mains.DG DC Volt.T.Low Thre.':
  chinese: '油电直流电压过低值'
'Cell Poor Prot.Thre':
  chinese: '单体落后保护压差'
'Li Batt.Chg.Curr.Coeff':
  chinese: '锂电充电系数'
'Software Shutdown En.':
  chinese: '软关机使能'
'Soft.Anti-theft Delay':
  chinese: '软件防盗延时'
'DG Stop Curr':
  chinese: '油机关闭电流'
'Mns.St.Volt.':
  chinese: '市电启动电压'
'Mns.St.Dis.Dur':
  chinese: '市电启动放电时间'
'MAINS Stop Curr':
  chinese: '市电关闭电流'
'Mains and DG Min DG Running Duration':
  chinese: '油电油机最短运行时间'
'Mains.DG Min DG Run.Dura.':
  chinese: '油电油机最短运行时间'
'PACK Voltage':
  chinese: 'PACK电压'
'PACK Volt.':
  chinese: 'PACK电压'
'Inner Environment Temperature':
  chinese: '机内环境温度'
'Inner Env.Temp.':
  chinese: '机内环境温度'
'PACK Current':
  chinese: 'PACK电流'
'PACK Curr.':
  chinese: 'PACK电流'
'Cell Voltage':
  chinese: '单体电压'
'Cell Volt':
  chinese: '单体电压'
'Cell Temperature':
  chinese: '单体温度'
'Cell Temp':
  chinese: '单体温度'
'Battery SOC':
  chinese: '电池SOC'
'Batt.SOC':
  chinese: '电池SOC'
'Li Battery Voltage':
  chinese: '锂电电压'
'Li Batt.Volt':
  chinese: '锂电电压'
'Li Battery Current':
  chinese: '锂电电流'
'Li Batt.Curr':
  chinese: '锂电电流'
'Charge Remain Time':
  chinese: '充电剩余时间'
'Chg.Remain Time':
  chinese: '充电剩余时间'
'DisCharge Remain Time':
  chinese: '放电剩余时间'
'DisChg.Remain Time':
  chinese: '放电剩余时间'
'Current Charging Voltage':
  chinese: '当前设定充电电压'
'Curr.Chg.Volt.':
  chinese: '当前设定充电电压'
'Current Discharging Voltage':
  chinese: '当前设定放电电压'
'Curr.Dischg.Volt.':
  chinese: '当前设定放电电压'
'Current Charging Limit Ratio':
  chinese: '当前设定充电限电流比例'
'Curr.Chg.Lim.Rat.':
  chinese: '设定充电限电流比例'
'Current Discharge Limit Ratio':
  chinese: '当前设定放电限电流比例'
'Curr.Dischg.Lim.Rat.':
  chinese: '设定放电限电流比例'
'Board Temperature':
  chinese: '单板温度'
'Board Temper.':
  chinese: '单板温度'
'Discharge Total Power':
  chinese: '累计放电电量'
'Dis.Chg Total Power':
  chinese: '累计放电电量'
'Discharge Toatal Capacity':
  chinese: '累计放电容量'
'Dis.Chg Total Cap.':
  chinese: '累计放电容量'
'Major Battery':
  chinese: '主用电池'
'Major Batt.':
  chinese: '主用电池'
'Battery Enable Time':
  chinese: '启用日期'
'Battery En.Time':
  chinese: '启用日期'
'Open':
  chinese: '启动'
'Cell Equalization Circuit Status':
  chinese: '单体均衡启动状态'
'Cell Equ.Stu.':
  chinese: '单体均衡状态'
'Discharge':
  chinese: '放电管理'
'On Not Float':
  chinese: '在线非浮充'
'Off-line':
  chinese: '离线'
'Battery Run Status':
  chinese: '电池运行状态'
'Batt.Run Stat.':
  chinese: '电池运行状态'
'Battery Charge Protection':
  chinese: '电池充电保护'
'Batt.CP':
  chinese: '电池充电保护'
'Battery Discharge Protection':
  chinese: '电池放电保护'
'Batt.DP':
  chinese: '电池放电保护'
'Battery Charge Over Current Protection Status':
  chinese: '电池充电过流保护状态'
'Batt.Chg.OCP Stat':
  chinese: '电池充过流保护状态'
'Battery Discharge Over Current Protection Stat':
  chinese: '电池放电过流保护状态'
'Batt.Disch.OCP Stat':
  chinese: '电池放过流保护状态'
'Battery Over Voltage Protection Status':
  chinese: '电池过压保护状态'
'Batt.OVP Stat':
  chinese: '电池过压保护状态'
'Board Over Temperature Protection Stat':
  chinese: '单板过温保护状态'
'Board OTP.Stat':
  chinese: '单板过温保护状态'
'Battery Charge Over Current Alarm Status':
  chinese: '电池充电过流告警状态'
'Batt.Chg.OCA Stat':
  chinese: '电池充过流状态'
'Battery Discharge Over Current Alarm Status':
  chinese: '电池放电过流告警状态'
'Batt.Disch.OCA Stat':
  chinese: '电池放过流告警状态'
'Battery Over Voltage Alarm Status':
  chinese: '电池过压告警状态'
'Batt.OVA.Stat':
  chinese: '电池过压状态'
'Inner Environment Over Temperature Alarm Status':
  chinese: '机内环境温度高告警状态'
'Inner Env.OTA Stat':
  chinese: '机内环境过温状态'
'Inner Environment Under Temperature Alarm Status':
  chinese: '机内环境温度低告警状态'
'Inner Env.UTA Stat':
  chinese: '机内环境低温状态'
'Battery Under Voltage Alarm Status':
  chinese: '电池欠压告警状态'
'Batt.UVA Stat':
  chinese: '电池欠压状态'
'Battery Under Voltage Protection Status':
  chinese: '电池欠压保护状态'
'Batt.UVP Stat':
  chinese: '电池欠压保护状态'
'Cell Over Volatge Alarm Status':
  chinese: '单体过压告警状态'
'Cell OVA Stat':
  chinese: '单体过压状态'
'Cell Over Volatge Protection Stat':
  chinese: '单体过压保护状态'
'Cell OVP Stat':
  chinese: '单体过压保护状态'
'Cell Under Voltage Alarm Status':
  chinese: '单体欠压告警状态'
'Cell UVA Stat':
  chinese: '单体欠压状态'
'Cell Under Voltage Protection Stat':
  chinese: '单体欠压保护状态'
'Cell UVP Stat':
  chinese: '单体欠压保护状态'
'Cell Charge Over Temperature Alarm Status':
  chinese: '单体充电高温告警状态'
'Cell Chg.OTA Stat':
  chinese: '单体充高温状态'
'Cell Charge Over Temperature Protection Status':
  chinese: '单体充电高温保护状态'
'Cell Chg.OTP Stat':
  chinese: '单体充高温保护状态'
'Cell Discharge Over Temperature Alarm Status':
  chinese: '单体放电高温告警状态'
'Cell Disch.OTA Stat':
  chinese: '单体放高温状态'
'Cell Discharge Over Temperature Protection Status':
  chinese: '单体放电高温保护状态'
'Cell Disch.OTP Stat':
  chinese: '单体放高温保护状态'
'Cell Charge Under Temperature Alarm Status':
  chinese: '单体充电低温告警状态'
'Cell Chg.UTA Stat':
  chinese: '单体充低温状态'
'Cell Charge Under Temperature Protection Status':
  chinese: '单体充电低温保护状态'
'Cell Chg.UTP Stat':
  chinese: '单体充低温保护状态'
'Cell Discharge Under Temperature Alarm Status':
  chinese: '单体放电低温告警状态'
'Cell Disch.UTA Stat':
  chinese: '单体放低温状态'
'Cell Discharge Under Temperature Protection Status':
  chinese: '单体放电低温保护状态'
'Cell Disch.UTP Stat':
  chinese: '单体放低温保护状态'
'Cell Voltage Dev Stat':
  chinese: '单体落后电压差状态'
'Cell Volt.Dev Stat':
  chinese: '单体落后电压差状态'
'Cell Poor Protect Status':
  chinese: '单体落后保护电压差状态'
'Cell Poor Prot.Stat':
  chinese: '单体落后保护压差状态'
'Battery SOC Low Alarm Status':
  chinese: '电池SOC低告警状态'
'Batt.Soc.Low.Stat':
  chinese: '电池SOC低状态'
'Battery Soc Low Protect Stat':
  chinese: '电池SOC低保护状态'
'Batt.Soc.Low.Prot.Stat':
  chinese: '电池SOC低保护状态'
'Battery SOH Alarm Status':
  chinese: '电池SOH告警状态'
'Batt.Soh.Stat':
  chinese: '电池SOH状态'
'Battery SOH Protect Status':
  chinese: '电池SOH保护状态'
'Cell Damage Protect Stat':
  chinese: '单体损坏保护状态'
'Cell Damage Prt.Stat':
  chinese: '单体损坏保护状态'
'Battery Missing Status':
  chinese: '电池丢失告警状态'
'Batt. Missing Stat':
  chinese: '电池丢失状态'
'Charge Switch Invalid Status':
  chinese: '充电回路开关失效告警状态'
'Chg.Sw.Inv. Stat':
  chinese: '充开关失效状态'
'Discharge Breaker Invalid Status':
  chinese: '放电回路开关失效告警状态'
'Dch.Sw.Inv .Stat':
  chinese: '放开关失效状态'
'Limit Current Invalid Status':
  chinese: '限流回路失效告警状态'
'Limit Curr.Inv .Stat':
  chinese: '限流回路失效状态'
'Short Cut Protection Stat':
  chinese: '短路保护状态'
'Battery Reserve Status':
  chinese: '电池反接告警状态'
'Batt.Reserve Stat':
  chinese: '电池反接状态'
'Cell Temperature Sensor Invalid Status':
  chinese: '单体温度传感器失效告警状态'
'Cell TI Stat':
  chinese: '单体温传失效状态'
'Inner Over Temperature Protection Status':
  chinese: '机内过温保护告警状态'
'Inner OTP Stat':
  chinese: '机内过温保护状态'
'BDCU Under Voltage Protection Status':
  chinese: 'BDCU电池欠压保护状态'
'BDCU UVP Stat':
  chinese: 'BDCU欠压保护状态'
'BDCU Bus Under Voltage Protection Status':
  chinese: 'BDCU母排欠压保护告警状态'
'BDCU Bus UVP Stat':
  chinese: 'BDCU母排欠压保护状态'
'BDCU EEPROM Abnormal Status':
  chinese: 'BDCU EEPROM故障告警状态'
'BDCU EEPROMStat':
  chinese: 'BDCU EEPROM故障状态'
'Cell Temperature Abnormal Stat':
  chinese: '单体温度异常状态'
'Cell Temp.Abnormal Stat':
  chinese: '单体温度异常状态'
'Bms Address Clash Status':
  chinese: '地址冲突告警状态'
'Bms Addr.Clash Stat':
  chinese: '地址冲突状态'
'Bms Shake Status':
  chinese: '振动告警状态'
'Cell Volt Sample Abnormal Alm Stat':
  chinese: '单体电压采样异常告警状态'
'Cell Volt Sample Abnormal Stat':
  chinese: '单体电压采样异常状态'
'BDCU Communication Fail Status':
  chinese: 'BDCU通信断告警状态'
'BDCU Comm.Fail Stat':
  chinese: 'BDCU通信断状态'
'BMS Communication Fail Status':
  chinese: 'BMS通信断状态'
'BMS Comm.Fail Stat':
  chinese: 'BMS通信断状态'
'BMS Work Status':
  chinese: 'BMS工作状态'
'BMS Exist State':
  chinese: 'BMS在位状态'
'BDCU Bus Over Voltage Protection Status':
  chinese: 'BDCU母排过压保护告警状态'
'BDCU Bus OVP Stat':
  chinese: 'BDCU母排过压保护状态'
'Charge Input Break State':
  chinese: '充电输入断状态'
'Charge In.Break.Sta.':
  chinese: '充电输入断状态'
'Battery Volt Sample Alarm State':
  chinese: '电压采样故障状态'
'Batt. Volt.Sample Alm.sta.':
  chinese: '电压采样故障状态'
'Charge Prohibit Status':
  chinese: '充电禁止状态'
'Charge Proh.Sta.':
  chinese: '充电禁止状态'
'Discharge Prohibit Status':
  chinese: '放电禁止状态'
'Discharge Proh.Sta.':
  chinese: '放电禁止状态'
'Common Charge':
  chinese: '常规充电'
'Common Disch.':
  chinese: '常规放电'
'Set Up Charge':
  chinese: '升压充电'
'Set Up Disch.':
  chinese: '升压放电'
'Set Down Charge':
  chinese: '降压充电'
'Set Down Disch.':
  chinese: '降压放电'
'Charge or Disch.Stop':
  chinese: '充放电停止'
'Lock':
  chinese: '闭锁'
'Through Charge':
  chinese: '直通充电'
'Through Disch.':
  chinese: '直通放电'
'BDCU Management Status':
  chinese: 'BDCU管理状态'
'BDCU Manag.Sta.':
  chinese: 'BDCU管理状态'
'Master Or Slave State':
  chinese: '主从机状态'
'Master.Slave Sta.':
  chinese: '主从机状态'
'Battery Loop Abnormal Alarm State':
  chinese: '回路异常状态'
'Loop Abnor.Alm.sta.':
  chinese: '回路异常状态'
'BDCU Charge Under Voltage Protect State':
  chinese: 'BDCU电池充电欠压保护状态'
'BDCU Chg.Under Prot.sta.':
  chinese: 'BDCU充电欠压保护状态'
'BDCU Battery Lock Alarm State':
  chinese: 'BDCU电池闭锁状态'
'BDCU Batt.lock sta.':
  chinese: 'BDCU电池闭锁状态'
'Environment Temperature High Protect State':
  chinese: '环境温度高保护状态'
'Env.Temp.High Prot.sta.':
  chinese: '环境温度高保护状态'
'Environment Temperature Low Protect State':
  chinese: '环境温度低保护状态'
'Env.Temp.Low Prot.sta.':
  chinese: '环境温度低保护状态'
'Board Temperature High Alarm State':
  chinese: '单板过温告警状态'
'Board Temp.High sta.':
  chinese: '单板过温告警状态'
'Battery Charge Over Current Protection Alm':
  chinese: '电池充电过流保护'
'Batt.Chg.OCP.':
  chinese: '电池充过流保护'
'Battery Discharge Over Current Protection Alm':
  chinese: '电池放电过流保护'
'Batt.Disch.OCP.':
  chinese: '电池放过流保护'
'Battery Over Voltage Protection Alm':
  chinese: '电池过压保护'
'Batt.OVP Alm':
  chinese: '电池过压保护'
'Board Over Temperature Protection Alm':
  chinese: '单板过温保护'
'Board OTP.Alm':
  chinese: '单板过温保护'
'Battery Charge Over Current Alm':
  chinese: '电池充电过流告警'
'Batt.Chg.OCA.':
  chinese: '电池充过流'
'Battery Discharge Over Current Alm':
  chinese: '电池放电过流告警'
'Batt.Disch.OCA.':
  chinese: '电池放过流'
'Battery Over Voltage Alm':
  chinese: '电池过压告警'
'Batt.OVA.Alm':
  chinese: '电池过压'
'Inner Environment Over Temperature Alm':
  chinese: '机内环境温度高告警'
'Inner Env.OTA.':
  chinese: '机内环境过温'
'Inner Environment Under Temperature Alm':
  chinese: '机内环境温度低告警'
'Inner Env.UTA.':
  chinese: '机内环境低温'
'Battery Under Voltage Alm':
  chinese: '电池欠压告警'
'Batt.UVA Alm':
  chinese: '电池欠压'
'Battery Under Voltage Protection Alm':
  chinese: '电池欠压保护'
'Batt.UVP Alm':
  chinese: '电池欠压保护'
'Cell Over Volatge Alm':
  chinese: '单体过压告警'
'Cell OVA Alm':
  chinese: '单体过压'
'Cell Over Volatge Protection Alm':
  chinese: '单体过压保护'
'Cell OVP Alm':
  chinese: '单体过压保护'
'Cell Under Voltage Alm':
  chinese: '单体欠压告警'
'Cell UVA Alm':
  chinese: '单体欠压'
'Cell Under Voltage Protection Alm':
  chinese: '单体欠压保护'
'Cell UVP Alm':
  chinese: '单体欠压保护'
'Cell Charge Over Temperature Alm':
  chinese: '单体充电高温告警'
'Cell Chg.OTA.':
  chinese: '单体充高温'
'Cell Charge Over Temperature Protection Alm':
  chinese: '单体充电高温保护告警'
'Cell Chg.OTP.':
  chinese: '单体充高温保护'
'Cell Discharge Over Temperature Alm':
  chinese: '单体放电高温告警'
'Cell Disch.OTA.':
  chinese: '单体放高温'
'Cell Discharge Over Temperature Protection Alm':
  chinese: '单体放电高温保护告警'
'Cell Disch.OTP.':
  chinese: '单体放高温保护'
'Cell Charge Under Temperature Alm':
  chinese: '单体充电低温告警'
'Cell Chg.UTA.':
  chinese: '单体充低温'
'Cell Charge Under Temperature Protection Alm':
  chinese: '单体充电低温保护告警'
'Cell Chg.UTP.':
  chinese: '单体充低温保护'
'Cell Discharge Under Temperature Alm':
  chinese: '单体放电低温告警'
'Cell Disch.UTA.':
  chinese: '单体放低温'
'Cell Discharge Under Temperature Protection Alm':
  chinese: '单体放电低温保护告警'
'Cell Disch.UTP.':
  chinese: '单体放低温保护'
'Cell Voltage Dev Alm':
  chinese: '单体落后电压差'
'Cell Volt.Dev.':
  chinese: '单体落后电压差'
'Cell Poor Protection Alm':
  chinese: '单体落后保护电压差'
'Battery SOC Low Alm':
  chinese: '电池SOC低告警'
'Batt.Soc.L.Alm':
  chinese: '电池SOC低'
'Battery Soc Low Protect Alm':
  chinese: '电池SOC低保护'
'Batt.Soc.L.Prot.':
  chinese: '电池SOC低保护'
'Battery SOH Alm':
  chinese: '电池SOH告警'
'Batt.SOH.Alm':
  chinese: '电池SOH'
'Battery SOH Protect Alm':
  chinese: '电池SOH保护'
'Batt.SOH Prot.':
  chinese: '电池SOH保护'
'Cell Damage Protect Alm':
  chinese: '单体损坏保护'
'Cell Dmg.Prt.':
  chinese: '单体损坏保护'
'Battery Missing Alm':
  chinese: '电池丢失告警'
'Batt.Missing Alm':
  chinese: '电池丢失'
'Charge Switch Invalid Alm':
  chinese: '充电回路开关失效告警'
'Chg.Sw.Inv.Alm':
  chinese: '充开关失效'
'Discharge Breaker Invalid Alm':
  chinese: '放电回路开关失效告警'
'Dch.Sw.Inv.Alm':
  chinese: '放开关失效'
'Limit Current Invalid Alm':
  chinese: '限流回路失效告警'
'Lim.Curr.Inv.Alm':
  chinese: '限流回路失效'
'Short Cut Protection Alm':
  chinese: '短路保护'
'Short Cut Prot.':
  chinese: '短路保护'
'Battery Reserve Alm':
  chinese: '电池反接告警'
'Batt.Reserve Alm':
  chinese: '电池反接'
'Cell Temperature Sensor Invalid Alm':
  chinese: '单体温度传感器失效告警'
'Cell TI Alm':
  chinese: '单体温传失效告警'
'Inner Over Temperature Protection Alm':
  chinese: '机内过温保护告警'
'Inner OTP Alm':
  chinese: '机内过温保护'
'BDCU Under Voltage Protection Alm':
  chinese: 'BDCU电池欠压保护'
'BDCU UVP Alm':
  chinese: 'BDCU欠压保护'
'BDCU Bus Under Voltage Protection Alm':
  chinese: 'BDCU母排欠压保护告警'
'BDCU Bus UVP Alm':
  chinese: 'BDCU母排欠压保护'
'BDCU EEPROM Abnormal Alm':
  chinese: 'BDCU EEPROM故障告警'
'BDCU EEPROMAlm':
  chinese: 'BDCU EEPROM故障'
'Cell Temperature Abnormal Alm':
  chinese: '单体温度异常'
'Cell Temp.Abn.':
  chinese: '单体温度异常'
'Bms Address Clash Alm':
  chinese: '地址冲突告警'
'Bms Addr.Clash':
  chinese: '地址冲突'
'Bms Shake Alm':
  chinese: '振动告警'
'Bms Shake almus':
  chinese: '振动告警'
'Cell Volt Sample Abnormal Alm':
  chinese: '单体电压采样异常告警'
'Cell V.Samp.Abn.':
  chinese: '单体电压采样异常'
'BDCU Communication Fail Alm':
  chinese: 'BDCU通信断告警'
'BDCU Comm.Fail.':
  chinese: 'BDCU通信断'
'BMS Communication Fail Alm':
  chinese: 'BMS通信断告警'
'BMS Comm.Fail.':
  chinese: 'BMS通信断告警'
'BDCU Bus Over Voltage Protection Alm':
  chinese: 'BDCU母排过压保护告警'
'BDCU Bus OVP Alm.':
  chinese: 'BDCU母排过压保护'
'Battery Volt Sample Alarm':
  chinese: '电压采样故障'
'Batt. Volt.Sample Alm.':
  chinese: '电压采样故障'
'Battery Loop Abnormal Alm':
  chinese: '回路异常'
'Batt.Loop Abnor.Alm':
  chinese: '回路异常'
'BDCU Charge Under Voltage Protect Alm':
  chinese: 'BDCU电池充电欠压保护'
'BDCU Chg.Under Prot.':
  chinese: 'BDCU充电欠压保护'
'Environment Temperature High Protect':
  chinese: '环境温度高保护'
'Env.Temp.High Prot.':
  chinese: '环境温度高保护'
'Environment Temperature Low Protect':
  chinese: '环境温度低保护'
'Env.Temp.Low Prot.':
  chinese: '环境温度低保护'
'Board Temperature High Alarm':
  chinese: '单板过温告警'
'Board Temp.High Alm':
  chinese: '单板过温告警'
'Bms Reset':
  chinese: 'BMS复位'
'BMS Communication Fail alarm clear':
  chinese: 'BMS通讯中断告警清除'
'BMS Comm.Fail clear':
  chinese: 'BMS通讯中断清除'
'Release Lock':
  chinese: '解除闭锁'
'BMS System Name':
  chinese: 'BMS系统名称'
'BMS Software Version':
  chinese: 'BMS软件版本'
'BMS Software Ver.':
  chinese: 'BMS软件版本'
'BMS Software Release Date':
  chinese: 'BMS软件发布日期'
'BMS Serial Number':
  chinese: 'BMS序列号'
'BMS No.':
  chinese: 'BMS序列号'
'BDCU System Name':
  chinese: 'BDCU名称'
'BDCU Digital Control Platform Version':
  chinese: 'BDCU数控平台版本'
'BDCU Platform.':
  chinese: 'BDCU数控平台'
'BDCU Software Version':
  chinese: 'BDCU软件版本'
'BDCU Software Release Date':
  chinese: 'BDCU软件发布日期'
'BDCU Serial Number':
  chinese: 'BDCU序列号'
'BDCU No.':
  chinese: 'BDCU序列号'
'Unset Type':
  chinese: '未设置'
'Coslight':
  chinese: '光宇'
'Shoto':
  chinese: '双登'
'Reserve':
  chinese: '保留'
'PACK Manufactory Name':
  chinese: 'PACK厂家'
'Battery Module Serial Number':
  chinese: '电池模组序列号'
'Batt.Module SN':
  chinese: '电池模组序列号'
'Battery Manufacture Date':
  chinese: '电池生产日期'
'Whole Machine Serial Number':
  chinese: '整机序列号'
'Whole Machine No.':
  chinese: '整机序列号'
'Pack Date of Active':
  chinese: '电池启用日期'
'BMS Hardware Type Name':
  chinese: 'BMS硬件型号'
'BMS Hardware Type':
  chinese: 'BMS硬件型号'
'PACK Factory Name':
  chinese: 'PACK厂家名称'
'BOOT Version Identify':
  chinese: 'BOOT版本标识'
'BOOT Version Ident.':
  chinese: 'BOOT版本标识'
'Mobile DG Work':
  chinese: '移动油机供电'
'DG Disable Run Enable':
  chinese: '油机禁止运行使能'
'DG Disable Run Start Clock':
  chinese: '油机禁止运行起始时刻'
'DG Prd.Stop.St.Clk.':
  chinese: '油机禁止运行起始时刻'
'DG Period Stop End Clock':
  chinese: '油机禁止运行终止时刻'
'DG Prd.Stop.End.Clk.':
  chinese: '油机禁止运行终止时刻'
'DG Period Start Enable':
  chinese: '油机定时启动使能'
'DG Prd.Start En.':
  chinese: '油机定时启动使能'
'DG Period Start Clock':
  chinese: '油机定时启动时刻'
'DG Prd.St.Clk.':
  chinese: '油机定时启动时刻'
'DG Period End Clock':
  chinese: '油机定时关闭时刻'
'DG Prd.End Clk.':
  chinese: '油机定时关闭时刻'
'DG Start Voltage Enable':
  chinese: '油机启动电压使能'
'DG Start Vol.En.':
  chinese: '油机启动电压使能'
'DG Start Time Enable':
  chinese: '油机启动时间使能'
'DG Start Time En.':
  chinese: '油机启动时间使能'
'DG Start System Abnormal Enable':
  chinese: '油机启动系统异常使能'
'DG Start Sys.Abr.En.':
  chinese: '油机启动异常使能'
'DG Stop Voltage Enable':
  chinese: '油机停止电压使能'
'DG Stop Vol.En.':
  chinese: '油机停止电压使能'
'DG Stop SOC Enable':
  chinese: '油机停止SOC使能'
'DG Stop SOC En.':
  chinese: '油机停止SOC使能'
'DG Stop Current Enable':
  chinese: '油机停止电流使能'
'DG Stop Cur.En.':
  chinese: '油机停止电流使能'
'DG Cool Down Enabled':
  chinese: '油机冷却使能'
'DG Cool Down En.':
  chinese: '油机冷却使能'
'DG Cool Down Duration':
  chinese: '油机冷却时间'
'DG Cool Down Dura.':
  chinese: '油机冷却时间'
'DG Detect Period':
  chinese: '油机检测周期'
'DG Det.Period':
  chinese: '油机检测周期'
'DG Detect Duration':
  chinese: '油机检测持续时间'
'DG Det.Dura.':
  chinese: '油机检测时间'
'DG Detect Start Clock':
  chinese: '油机检测启动时刻'
'DG Detect Clock':
  chinese: '油机检测时刻'
'DG Three-phase Power Factor':
  chinese: '三相油机功率因数'
'DG Three-phase PF':
  chinese: '三相功率因数'
'DG Single-phase Power Factor':
  chinese: '单相油机功率因数'
'DG Single-phase PF':
  chinese: '单相功率因数'
'DG Load Rate Threshold':
  chinese: '油机带载率阈值'
'DG Ld.Rate.Thres.':
  chinese: '油机带载率阈值'
'DG Load Rate High Threshold':
  chinese: '油机带载率高阈值'
'DG Ld.Rate.H.':
  chinese: '油机带载率高阈值'
'DG Load Rate Low Threshold':
  chinese: '油机带载率低阈值'
'DG Ld.Rate.L.':
  chinese: '油机带载率低阈值'
'Fuel Level Low Alarm Threshold':
  chinese: '油机油位低告警阈值'
'Fuel Low Thres.':
  chinese: '油位低告警阈值'
'Fuel Level High Alarm Threshold':
  chinese: '油机油位高告警阈值'
'Fuel High Thres.':
  chinese: '油位高告警阈值'
'DG Phase Voltage':
  chinese: '油机相电压'
'DG Phase Volt.':
  chinese: '油机相电压'
'DG Line Voltage':
  chinese: '油机线电压'
'DG Line Volt.':
  chinese: '油机线电压'
'DG Phase Current':
  chinese: '油机相电流'
'DG Phase Curr.':
  chinese: '油机相电流'
'DG Power Factor':
  chinese: '油机功率因数'
'DG.PF.':
  chinese: '油机功率因数'
'DG Output Frequency':
  chinese: '油机输出频率'
'DG Out.Freq.':
  chinese: '油机输出频率'
'DG Apparent Power':
  chinese: '油机视在功率'
'DG.App.Pwr.':
  chinese: '油机视在功率'
'DG Active Power':
  chinese: '油机有功功率'
'DG.Act.Pwr.':
  chinese: '油机有功功率'
'DG Reactive Power':
  chinese: '油机无功功率'
'DG.Rct.Pwr.':
  chinese: '油机无功功率'
'DG Speed':
  chinese: '油机转速'
'DG.Speed':
  chinese: '油机转速'
'DG Oil Pressure':
  chinese: '油机油压'
'DG.Oil Pres.':
  chinese: '油机油压'
'DG Coolant Temperature':
  chinese: '油机水温'
'DG.Cool.Temp.':
  chinese: '油机水温'
'DG Battery Voltage':
  chinese: '油机电池电压'
'DG.Batt.Volt.':
  chinese: '油机电池电压'
'DG Load Rate':
  chinese: '油机带载率'
'DG.Load Rate':
  chinese: '油机带载率'
'DG Oil Temperature':
  chinese: '油机油温'
'DG.Oil Temp.':
  chinese: '油机油温'
'DG Running Duration':
  chinese: '油机持续运行时间'
'DG Run.Dura.':
  chinese: '油机运行时间'
'DG Fuel Level':
  chinese: '油机油位'
'DG Fuel Gauge':
  chinese: '油机剩余油量'
'Fuel Tank Capacity':
  chinese: '油箱容量'
'Fuel Tank Cap.':
  chinese: '油箱容量'
'Other Mode':
  chinese: '其他模式'
'DG Controler Mode':
  chinese: '油机控制器工作模式'
'Not Config':
  chinese: '未配置'
'OFF':
  chinese: '停止'
'ON':
  chinese: '运行'
'Abnormal':
  chinese: '异常'
'DG Status':
  chinese: '油机状态'
'DG Control Status':
  chinese: '油机控制状态'
'DG Application Scenario':
  chinese: '油机应用场景'
'DG Start Alarm':
  chinese: '油机启动告警'
'DG Start Alm.':
  chinese: '油机启动告警'
'DG Abnormal Alarm':
  chinese: '油机异常告警'
'DG Abnormal Alm.':
  chinese: '油机异常告警'
'DG Common Alarm':
  chinese: '油机公共告警'
'DG Common Alm.':
  chinese: '油机公共告警'
'DG Low Fuel Level':
  chinese: '油机油位低告警'
'DG Low Fl.Lev.':
  chinese: '油机油位低告警'
'DG Output Voltage Imbalance':
  chinese: '油机电压不平衡'
'DG Volt.Imbala.':
  chinese: '油机电压不平衡'
'Air Filter Block Alarm':
  chinese: '空气滤清器堵塞告警'
'A.F.Block Alm.':
  chinese: '空滤堵塞告警'
'Fuel Water Separator High Water Level Alarm':
  chinese: '油水分离器水位高告警'
'Sep.H.W.Lev.':
  chinese: '分离器水位高'
'DG Access Control Alarm':
  chinese: '油机门磁告警'
'DG Access Ctrl.':
  chinese: '油机门磁告警'
'DG Manual Mode Alarm':
  chinese: '油机手动模式告警'
'DG Manual Mode':
  chinese: '油机手动模式'
'DG Fuel Leakage':
  chinese: '油机燃油泄漏告警'
'Tank high water level Alarm':
  chinese: '油箱水位高告警'
'Tank H.W.Lev.':
  chinese: '油箱水位高'
'Fuel Waterlog Alarm':
  chinese: '燃油水浸告警'
'Fl.Waterlog':
  chinese: '燃油水浸告警'
'DG Output Under Voltage':
  chinese: '油机输出欠压'
'DG.Out.UVP':
  chinese: '油机输出欠压'
'DG Output Over Voltage':
  chinese: '油机输出过压'
'DG.Out.OVP':
  chinese: '油机输出过压'
'DG Output Over Current':
  chinese: '油机输出过流'
'DG.Over Curr.':
  chinese: '油机输出过流'
'DG Output Over Load':
  chinese: '油机输出过载'
'DG.Over Load':
  chinese: '油机输出过载'
'DG Output Under Frequency':
  chinese: '油机输出频率低'
'DG.Und.Freq.':
  chinese: '油机输出频率低'
'DG Output Over Frequency':
  chinese: '油机输出频率高'
'DG.Over Freq.':
  chinese: '油机输出频率高'
'DG Under Speed':
  chinese: '油机低速告警'
'DG.Und.Speed':
  chinese: '油机低速告警'
'DG Over Speed':
  chinese: '油机超速告警'
'DG.Over Speed':
  chinese: '油机超速告警'
'DG Low Oil Pressure':
  chinese: '油机油压低告警'
'DG.Low Oil Pres.':
  chinese: '油机油压低告警'
'DG Coolant Over Temperature':
  chinese: '油机水温高告警'
'DG.Coolant OTP':
  chinese: '油机水温高告警'
'DG Oil Over Temperature':
  chinese: '油机油温高告警'
'DG.Oil OTP':
  chinese: '油机油温高告警'
'DG Battery Under Voltage':
  chinese: '油机电池欠压告警'
'DG.Bat.UVP':
  chinese: '油机电池欠压'
'DG Battery Over Voltage':
  chinese: '油机电池过压告警'
'DG.Bat.OVP':
  chinese: '油机电池过压'
'DG Charge Failure':
  chinese: '油机充电失败告警'
'DG.Charge.Fail.':
  chinese: '油机充电失败'
'DG Engine High Temperature':
  chinese: '油机发动机高温告警'
'DG.Engine OTP':
  chinese: '油机发动机高温'
'DG Engine Low Temperature':
  chinese: '油机发动机低温告警'
'DG.Engine UTP':
  chinese: '油机发动机低温'
'DG Start Failure':
  chinese: '油机启动失败告警'
'DG.Start Fail.':
  chinese: '油机启动失败'
'DG Stop Failure':
  chinese: '油机停机失败告警'
'DG.Stop Fail.':
  chinese: '油机停机失败'
'DG High Fuel level':
  chinese: '油机油位高告警'
'DG.High Fl Lev.':
  chinese: '油机油位高告警'
'DG Phase Sequence Error':
  chinese: '油机相序错误'
'DG.Ph.Seq.Err.':
  chinese: '油机相序错误'
'DG Low Oil Level Alarm':
  chinese: '油机机油液位低告警'
'DG.L.Oil Lev.':
  chinese: '机油液位低告警'
'DG Emergency Stop Alarm':
  chinese: '油机紧急停机告警'
'DG.Emerg.Stop':
  chinese: '紧急停机告警'
'DG High Alternator Temperature Alarm':
  chinese: '电球高温告警'
'H.Alt.Temp.Alm.':
  chinese: '电球高温告警'
'DG Water Temperature Sensor Open Alarm':
  chinese: '水温传感器开路告警'
'DG.Wa.Te.S.Open':
  chinese: '水温传感器开路'
'DG Fuel Press Sensor Open Alarm':
  chinese: '油压传感器开路告警'
'DG.F.Pre.S.Open':
  chinese: '油压传感器开路'
'DG Fuel Level Sensor Open Alarm':
  chinese: '油位传感器开路告警'
'DG.F.Lev.S.Open':
  chinese: '油位传感器开路'
'DG Overlong Running Alarm':
  chinese: '油机超长运行告警'
'DG Ol.Run.Alm':
  chinese: '油机超长运行告警'
'DG Start':
  chinese: '油机开启'
'DG Stop':
  chinese: '油机关闭'
'Start DG Test':
  chinese: '启动油机测试'
'DG Overlong Running Alarm Clear':
  chinese: '油机超长运行告警清除'
'DG Overlong Run.Alm Clear':
  chinese: '超长运行告警清除'
'Cube':
  chinese: '立方体'
'Ellipse Vectical':
  chinese: '椭圆形立放'
'Ellipse Horizontal':
  chinese: '椭圆形横放'
'Tank Shape':
  chinese: '油箱形状'
'Tank Feature':
  chinese: '油箱规格'
'Fuel Add Record Threshold':
  chinese: '燃油加油记录阈值'
'Fuel Add Rec.Thre.':
  chinese: '燃油加油记录阈值'
'Fuel Leakage Alarm Threshold':
  chinese: '燃油泄漏告警阈值'
'Fuel Lkg.Thre.':
  chinese: '燃油泄漏告警阈值'
'Tank Length':
  chinese: '油箱长度'
'Tank Width':
  chinese: '油箱宽度'
'Tank Height':
  chinese: '油箱高度'
'DG Overlong Running Threshold':
  chinese: '油机超长运行告警阈值'
'DG Overlong Run.Thre.':
  chinese: '油机超长运行阈值'
'ATS Config':
  chinese: 'ATS配置'
'Mains Access Config':
  chinese: '市电接入配置'
'Manual Contrl':
  chinese: '手动控制'
'Batt.Time':
  chinese: '电池放电时间'
'CSU Rotate':
  chinese: 'CSU轮换'
'ATS Rotate':
  chinese: 'ATS轮换'
'Interrupte':
  chinese: '故障恢复'
'Mains Int.':
  chinese: '市电中断'
'Mains Abnormal.':
  chinese: '市电异常'
'Sys.Abnormal':
  chinese: '系统异常'
'Period Detect':
  chinese: '周期检测'
'Upload':
  chinese: '利旧电源上电'
'InRelay Detect':
  chinese: '输入干接点检测'
'High Load':
  chinese: '带载率高'
'DG Start Reason':
  chinese: '油机启动原因'
'DG Start Rsn.':
  chinese: '油机启动原因'
'Not Stop':
  chinese: '未停止'
'High SOC.':
  chinese: 'SOC高'
'Batt.Curr.':
  chinese: '电池电流'
'Max.Time':
  chinese: '最大时间'
'Mains Interrupt':
  chinese: '市电打断'
'Gen.Fault':
  chinese: '油机故障'
'Green Energ.Enough':
  chinese: '绿色能源充足'
'Test Interrupt':
  chinese: '测试打断'
'Upload End':
  chinese: '上电完成'
'Mode Switch':
  chinese: '模式切换'
'Low Load':
  chinese: '带载率低'
'DG Stop Reason':
  chinese: '油机停止原因'
'DG Stop Rsn.':
  chinese: '油机停止原因'
'DG Start Fuel Gauge':
  chinese: '油机起始油量'
'DG St.Fl.Gug':
  chinese: '油机起始油量'
'DG End Fuel Gauge':
  chinese: '油机终止油量'
'DG End Fl.Gug':
  chinese: '油机终止油量'
'Next DG Detect Time':
  chinese: '下次油机检测时间'
'Next DG Det.':
  chinese: '下次油机检测'
'DG Run Extra Information':
  chinese: '油机运行附加信息'
'DG Run Ex.Info.':
  chinese: '油机运行附加信息'
'DG Initial Runtime':
  chinese: '油机起始运行时间'
'DG Final Runtime':
  chinese: '油机终止运行时间'
'DG Initial Power Generation':
  chinese: '油机起始发电量'
'DG Init.Power Generation':
  chinese: '油机起始发电量'
'DG Final Power Generation':
  chinese: '油机终止发电量'
'DG Index':
  chinese: '油机序号'
'DG Single Refuel Volume':
  chinese: '油机单次加油量'
'DG Single Ref.Vol.':
  chinese: '油机单次加油量'
'DG Single Leak Volume':
  chinese: '油机单次漏油量'
'DG Single Leak Vol.':
  chinese: '油机单次漏油量'
'Generator Run Record Time':
  chinese: '油机启停记录时间'
'DG. Run Rec.Time':
  chinese: '油机启停记录时间'
'Tank Refuel Record Time':
  chinese: '油箱加油记录时间'
'Refuel Rec.Time':
  chinese: '油箱加油记录时间'
'DG Fuel Leakage Record Time':
  chinese: '燃油泄漏记录时间'
'Fuel Leak.Rec.Time':
  chinese: '燃油泄漏记录时间'
'Manual DG Initial Runtime':
  chinese: '手动油机起始运行时间'
'Man.DG Initial Runtime':
  chinese: '手动油机起始运行时间'
'Manual DG Final Runtime':
  chinese: '手动油机终止运行时间'
'Man.DG Final Runtime':
  chinese: '手动油机终止运行时间'
'Manual DG Initial Power Generation':
  chinese: '手动油机起始发电量'
'Man.DG Init.Power Generation':
  chinese: '手动油机起始发电量'
'Manual DG Final Power Generation':
  chinese: '手动油机终止发电量'
'Man.DG Final Power Generation':
  chinese: '手动油机终止发电量'
'DG Run Times':
  chinese: '油机启动次数'
'DG Power Total Generation':
  chinese: '油机累计发电量'
'DG Total Power Gene.':
  chinese: '油机累计发电量'
'DG Runtime':
  chinese: '油机累计运行时间'
'DG Fuel Consumption':
  chinese: '油机耗油量'
'DG Fuel Consp.':
  chinese: '油机耗油量'
'DG Refuel Volume':
  chinese: '油机加油量'
'DG Ref.Vol.':
  chinese: '油机加油量'
'Manual DG Running Duration':
  chinese: '手动油机运行时长'
'Manu.DG Dura.':
  chinese: '手动油机时长'
'Manual DG Run Times':
  chinese: '手动油机启动次数'
'Manu.DG Times':
  chinese: '手动油机次数'
'Manual DG Power Generation':
  chinese: '手动油机发电量'
'Man.DG Pwr.Gene.':
  chinese: '手动油机发电量'
'Manual DG Fuel Consumption':
  chinese: '手动油机耗油量'
'Man.DG Fuel Cons.':
  chinese: '手动油机耗油量'
'DG AC Power Total Generation':
  chinese: '油机交流发电量'
'DG AC Total Power Gene.':
  chinese: '油机交流发电量'
'Manual DG AC Power Total Generation':
  chinese: '手动油机交流发电量'
'Man.DG AC Total Power Gene.':
  chinese: '手动油机交流发电量'
'DG.Phase Volt.':
  chinese: '油机相电压'
'DG.Line Volt.':
  chinese: '油机线电压'
'DG.Phase Curr.':
  chinese: '油机相电流'
'DG.Out.Freq.':
  chinese: '油机输出频率'
'Mains Phase Voltage':
  chinese: '市电相电压'
'Mains Phase Volt.':
  chinese: '市电相电压'
'Mains Line Voltage':
  chinese: '市电线电压'
'Mains Line Volt.':
  chinese: '市电线电压'
'Mains Phase Current':
  chinese: '市电相电流'
'Mains Phase Curr.':
  chinese: '市电相电流'
'Mains Power Factor':
  chinese: '市电功率因数'
'Mains PF.':
  chinese: '市电功率因数'
'Mains Frequency':
  chinese: '市电频率'
'Mains .Freq.':
  chinese: '市电频率'
'Mains Active Power':
  chinese: '市电有功功率'
'Mains Act.Pwr.':
  chinese: '市电有功功率'
'Mains Reactive Power':
  chinese: '市电无功功率'
'Mains Rct.Pwr.':
  chinese: '市电无功功率'
'Mains Apparent Power':
  chinese: '市电视在功率'
'Mains App.Pwr.':
  chinese: '市电视在功率'
'DG Oil Level':
  chinese: '油机机油油位'
'DG.Oil Level':
  chinese: '油机机油油位'
'Valid':
  chinese: '有效'
'Invalid.':
  chinese: '无效'
'Digital Input State':
  chinese: '开关量输入状态'
'Dig.Input State':
  chinese: '开关量输入'
'Relay Output State':
  chinese: '继电器输出状态'
'Rly.Out.State':
  chinese: '继电器输出'
'Light Off':
  chinese: '灯灭'
'Light On':
  chinese: '灯亮'
'DG Load State Light':
  chinese: '发电合闸灯'
'DG Gen Normal Light':
  chinese: '发电正常灯'
'Auto Light':
  chinese: 'AUTO键灯'
'Start Light':
  chinese: 'START键灯'
'Fault Light':
  chinese: '故障灯'
'Stop Light':
  chinese: 'STOP键灯'
'Mains Load State Light':
  chinese: '市电合闸灯'
'Mains Normal Light':
  chinese: '市电正常灯'
'Manual Light':
  chinese: 'MAN键灯'
'GCP Work Status':
  chinese: '油机控制屏工作状态'
'GCP Communication State':
  chinese: '油机控制屏通讯状态'
'GCP Comm.Sta.':
  chinese: '油机控制屏通信状态'
'GCP Exist State':
  chinese: '油机控制屏在位状态'
'GCP Exist Sta.':
  chinese: '油机控制屏在位状态'
'DG Output Under Voltage State':
  chinese: '油机输出欠压状态'
'DG.Out.UVP Sta.':
  chinese: '油机输出欠压状态'
'DG Output Over Voltage State':
  chinese: '油机输出过压状态'
'DG.Out.OVP Sta.':
  chinese: '油机输出过压状态'
'DG Output Over Current State':
  chinese: '油机输出过流状态'
'DG.Over Curr.Sta.':
  chinese: '油机输出过流状态'
'DG Output Over Load State':
  chinese: '油机输出过载状态'
'DG.Over Load Sta.':
  chinese: '油机输出过载状态'
'DG Output Under Frequency State':
  chinese: '油机输出频率低状态'
'DG.Und.Freq.Sta.':
  chinese: '油机输出频率低状态'
'DG Output Over Frequency State':
  chinese: '油机输出频率高状态'
'DG.Over Freq.Sta.':
  chinese: '油机输出频率高状态'
'DG Under Speed State':
  chinese: '油机低速告警状态'
'DG.Und.Speed Sta.':
  chinese: '油机低速告警状态'
'DG Over Speed State':
  chinese: '油机超速告警状态'
'DG.Over Speed Sta.':
  chinese: '油机超速告警状态'
'DG Low Oil Pressure State':
  chinese: '油机油压低告警状态'
'DG.Low Oil Pres. Sta.':
  chinese: '油机油压低告警状态'
'DG Battery Under Voltage State':
  chinese: '油机电池欠压告警状态'
'DG.Bat.UVP Sta.':
  chinese: '油机电池欠压状态'
'DG Battery Over Voltage State':
  chinese: '油机电池过压告警状态'
'DG.Bat.OVP Sta.':
  chinese: '油机电池过压状态'
'DG Charge Failure State':
  chinese: '油机充电失败告警状态'
'DG.Charge.Fail.Sta.':
  chinese: '油机充电失败状态'
'DG Engine High Temperature State':
  chinese: '油机发动机高温告警状态'
'DG.Engine OTP Sta.':
  chinese: '发动机高温告警状态'
'DG Engine Low Temperature State':
  chinese: '油机发动机低温告警状态'
'DG.Engine UTP Sta.':
  chinese: '油机发动机低温状态'
'DG Start Failure State':
  chinese: '油机启动失败告警状态'
'DG.Start Fail.Sta.':
  chinese: '油机启动失败状态'
'DG Stop Failure State':
  chinese: '油机停机失败告警状态'
'DG.Stop Fail.Sta.':
  chinese: '油机停机失败状态'
'DG Low Fuel level State':
  chinese: '油机油位低告警状态'
'DG.Low Fl Lev.Sta.':
  chinese: '油机油位低告警状态'
'DG High Fuel level State':
  chinese: '油机油位高告警状态'
'DG.High Fl Lev.Sta.':
  chinese: '油机油位高告警状态'
'DG Phase Sequence Error State':
  chinese: '油机相序错误状态'
'DG.Ph.Seq.Err.Sta.':
  chinese: '油机相序错误状态'
'DG Fail to Load State':
  chinese: '油机合闸失败状态'
'DG.Fail Load Sta.':
  chinese: '油机合闸失败状态'
'DG Fail to Open State':
  chinese: '油机分闸失败状态'
'DG.Fail to Open Sta.':
  chinese: '油机分闸失败状态'
'Mains GCB Fail to Load State':
  chinese: '市电合闸失败状态'
'Mns.GCB Fail Load Sta.':
  chinese: '市电合闸失败状态'
'Mains GCB Fail to Open State':
  chinese: '市电分闸失败状态'
'Mns.GCB Fail Open Sta.':
  chinese: '市电分闸失败状态'
'Mains Phase Voltage Low State':
  chinese: '市电电压欠压状态'
'Mns.Pha.Volt.Low Sta.':
  chinese: '市电电压欠压状态'
'Mains Phase Voltage High State':
  chinese: '市电电压过压状态'
'Mns.Pha.Volt.High Sta.':
  chinese: '市电电压过压状态'
'Mains Phase Current High State':
  chinese: '市电相电流高状态'
'Mns.Pha.Curr.High Sta.':
  chinese: '市电相电流高状态'
'Mains Low Frequency State':
  chinese: '市电频率低状态'
'Mns.Low Freq. Sta.':
  chinese: '市电频率低状态'
'Mains High Frequency State':
  chinese: '市电频率高状态'
'Mns.High Freq.Sta.':
  chinese: '市电频率高状态'
'Mains Phase Sequence Error State':
  chinese: '市电相序错误状态'
'Mns.Ph.Seq.Err. Sta.':
  chinese: '市电相序错误状态'
'Mains Fault State':
  chinese: '市电故障状态'
'Mns.Fault Sta.':
  chinese: '市电故障状态'
'Mains Over Load State':
  chinese: '市电过载状态'
'Mns.Over Load Sta.':
  chinese: '市电过载状态'
'DG Fuel Press Sensor Open Alarm State':
  chinese: '油压传感器开路告警状态'
'DG.F.Pre.S.Open Sta.':
  chinese: '油压传感器开路状态'
'GCP Communication Fail':
  chinese: '油机控制屏通讯中断'
'GCP Comm.Fail':
  chinese: '油机控制屏通讯断'
'DG Fail to Load':
  chinese: '油机合闸失败'
'DG.Fail Load':
  chinese: '油机合闸失败'
'DG Fail to Open':
  chinese: '油机分闸失败'
'DG.Fail to Open':
  chinese: '油机分闸失败'
'Mains GCB Fail to Load':
  chinese: '市电合闸失败'
'Mns.GCB Fail Load':
  chinese: '市电合闸失败'
'Mains GCB Fail to Open':
  chinese: '市电分闸失败'
'Mns.GCB Fail Open':
  chinese: '市电分闸失败'
'Ctrl DG Enter Manual Mode':
  chinese: '油机进入手动模式'
'Ctrl DG Manual':
  chinese: '油机手动'
'Ctrl DG Enter Automatic Mode':
  chinese: '油机进入自动模式'
'Ctrl DG Auto.':
  chinese: '油机自动'
'GCP Reset':
  chinese: '油机控制屏复位'
'GCP Device Statistic':
  chinese: '油机控制屏设备统计'
'GCP Dev Stat':
  chinese: '油机控制屏设备统计'
'L1-3P4W':
  chinese: '禁止缺相'
'L1-2P4W':
  chinese: 'L2L3可缺一相'
'L1-1P4W':
  chinese: 'L2L3可缺相'
'2P4W':
  chinese: '任缺一相'
'1P4W':
  chinese: '任缺二相'
'Mains Normal Mode':
  chinese: '市电正常模式'
'GCP System Name':
  chinese: '油机控制屏系统名称'
'GCP Sys.Name':
  chinese: '油机屏系统名称'
'GCP Software Version':
  chinese: '油机控制屏软件版本'
'GCP Soft.Ver.':
  chinese: '油机屏软件版本'
'GCP Software Release Date':
  chinese: '油机控制屏软件发布日期'
'GCP Soft.Date':
  chinese: '油机屏软件日期'
'GCP Serial Number':
  chinese: '油机控制屏序列号'
'GCP Serial No.':
  chinese: '油机屏序列号'
'GCP System Address':
  chinese: '油机控制屏系统地址'
'GCP Sys.Addr.':
  chinese: '油机屏系统地址'
'LLS Sensor Height':
  chinese: '液位传感器高度'
'LLS Height':
  chinese: '液位传感器高度'
'LLS Sensor Original Height':
  chinese: '液位传感器原始高度'
'LLS Original Height':
  chinese: '液位传感器原始高度'
'LLS Exist State':
  chinese: '液位传感器在位状态'
'LLS Work State':
  chinese: '液位传感器工作状态'
'LLS Communication State':
  chinese: '液位传感器通讯状态'
'LLS Communication Fail':
  chinese: '液位传感器通讯中断'
'LLS Comm.Fail':
  chinese: '液位传感器通讯断'
'LLS Sensor Offset Adjust':
  chinese: '液位传感器校准零点'
'LLS Lev.Adj.':
  chinese: '液位传感器零点'
'LLS Sensor Slope':
  chinese: '液位传感器校准斜率'
'LLS Sensor.Slope':
  chinese: '液位传感器斜率'
'LLS System Address':
  chinese: '液位传感器系统地址'
'LLS Sys.Addr.':
  chinese: '液传系统地址'
'LLS System Name':
  chinese: '液位传感器系统名称'
'LLS Sys.Name':
  chinese: '液传系统名称'
'LLS Software Version':
  chinese: '液位传感器软件版本'
'LLS Soft.Ver.':
  chinese: '液传软件版本'
'LLS Software Release Date':
  chinese: '液位传感器软件发布日期'
'LLS Soft.Date':
  chinese: '液传软件发布日期'
'Modify LLS Address':
  chinese: '修改液位传感器地址'
'Mod LLS Addr':
  chinese: '修改液位传感器地址'
'ATS Fault':
  chinese: 'ATS故障'
'ATS SPD Fault':
  chinese: 'ATS防雷故障'
'ACEM Loop 1 Voltage':
  chinese: '交流电表回路1电压'
'ACEM Loop 1 Volt.':
  chinese: '交流电表回路1电压'
'ACEM Loop 1 Current':
  chinese: '交流电表回路1电流'
'ACEM Loop 1 Curr.':
  chinese: '交流电表回路1电流'
'ACEM Loop 1 Total Active Power':
  chinese: '交流电表回路1总有功功率'
'ACEM Loop 1 Tol.Act.P.':
  chinese: '交流电表回路1总有功'
'ACEM Loop 1 Total Power Factor':
  chinese: '交流电表回路1总功率因数'
'ACEM Loop 1 Tol.Pwr.F.':
  chinese: '交流电表回路1总因数'
'ACEM Loop 1 Phase Power Factor':
  chinese: '交流电表回路1相功率因数'
'ACEM Loop 1 Ph.Pwr.F.':
  chinese: '交流电表回路1相因数'
'ACEM Loop 1 Frequency':
  chinese: '交流电表回路1频率'
'ACEM Loop 1 Freq.':
  chinese: '交流电表回路1频率'
'ACEM Loop 1 Energy':
  chinese: '交流电表回路1电量'
'ACEM Loop 1 Eng.':
  chinese: '交流电表回路1电量'
'ACEM Loop 2 Voltage':
  chinese: '交流电表回路2电压'
'ACEM Loop 2 Volt.':
  chinese: '交流电表回路2电压'
'ACEM Loop 2 Current':
  chinese: '交流电表回路2电流'
'ACEM Loop 2 Curr.':
  chinese: '交流电表回路2电流'
'ACEM Loop 2 Total Active Power':
  chinese: '交流电表回路2总有功功率'
'ACEM Loop 2 Tol.Act.P.':
  chinese: '交流电表回路2总有功'
'ACEM Loop 2 Total Power Factor':
  chinese: '交流电表回路2总功率因数'
'ACEM Loop 2 Tol.Pwr.F.':
  chinese: '交流电表回路2总因数'
'ACEM Loop 2 Phase Power Factor':
  chinese: '交流电表回路2相功率因数'
'ACEM Loop 2 Ph.Pwr.F.':
  chinese: '交流电表回路2相因数'
'ACEM Loop 2 Frequency':
  chinese: '交流电表回路2频率'
'ACEM Loop 2 Freq.':
  chinese: '交流电表回路2频率'
'ACEM Loop 2 Energy':
  chinese: '交流电表回路2电量'
'ACEM Loop 2 Eng.':
  chinese: '交流电表回路2电量'
'ACEM Loop 3 Voltage':
  chinese: '交流电表回路3电压'
'ACEM Loop 3 Volt.':
  chinese: '交流电表回路3电压'
'ACEM Loop 3 Current':
  chinese: '交流电表回路3电流'
'ACEM Loop 3 Curr.':
  chinese: '交流电表回路3电流'
'ACEM Loop 3 Total Active Power':
  chinese: '交流电表回路3总有功功率'
'ACEM Loop 3 Tol.Act.P.':
  chinese: '交流电表回路3总有功'
'ACEM Loop 3 Total Power Factor':
  chinese: '交流电表回路3总功率因数'
'ACEM Loop 3 Tol.Pwr.F.':
  chinese: '交流电表回路3总因数'
'ACEM Loop 3 Phase Power Factor':
  chinese: '交流电表回路3相功率因数'
'ACEM Loop 3 Ph.Pwr.F.':
  chinese: '交流电表回路3相因数'
'ACEM Loop 3 Frequency':
  chinese: '交流电表回路3频率'
'ACEM Loop 3 Freq.':
  chinese: '交流电表回路3频率'
'ACEM Loop 3 Energy':
  chinese: '交流电表回路3电量'
'ACEM Loop 3 Eng.':
  chinese: '交流电表回路3电量'
'ACEM Loop 4 Voltage':
  chinese: '交流电表回路4电压'
'ACEM Loop 4 Volt.':
  chinese: '交流电表回路4电压'
'ACEM Loop 4 Current':
  chinese: '交流电表回路4电流'
'ACEM Loop 4 Curr.':
  chinese: '交流电表回路4电流'
'ACEM Loop 4 Total Active Power':
  chinese: '交流电表回路4总有功功率'
'ACEM Loop 4 Tol.Act.P.':
  chinese: '交流电表回路4总有功'
'ACEM Loop 4 Total Power Factor':
  chinese: '交流电表回路4总功率因数'
'ACEM Loop 4 Tol.Pwr.F.':
  chinese: '交流电表回路4总因数'
'ACEM Loop 4 Phase Power Factor':
  chinese: '交流电表回路4相功率因数'
'ACEM Loop 4 Ph.Pwr.F.':
  chinese: '交流电表回路4相因数'
'ACEM Loop 4 Frequency':
  chinese: '交流电表回路4频率'
'ACEM Loop 4 Freq.':
  chinese: '交流电表回路4频率'
'ACEM Loop 4 Energy':
  chinese: '交流电表回路4电量'
'ACEM Loop 4 Eng.':
  chinese: '交流电表回路4电量'
'ACEM Communication State':
  chinese: '交流电表通讯状态'
'ACEM Comm.Sta.':
  chinese: '交流电表通讯状态'
'ACEM Exist State':
  chinese: '交流电表在位状态'
'ACEM Work State':
  chinese: '交流电表工作状态'
'ACEM Communication Fail':
  chinese: '交流电表通讯断告警'
'ACEM Comm.Fail':
  chinese: '交流电表通讯断'
'ACEM Energy Reset':
  chinese: '交流电表电量清零'
'ACEM Eng.Reset':
  chinese: '交流电表电量清零'
'ACInput':
  chinese: '电源交流输入'
'ACAircd':
  chinese: '交流空调'
'ACLoad':
  chinese: '交流负载'
'Other':
  chinese: '其他'
'Mains2':
  chinese: '市电2'
'ATS Out':
  chinese: 'ATS输出'
'ACEM Loop 1 Config':
  chinese: '交流电表回路1配置'
'ACEM Loop 2 Config':
  chinese: '交流电表回路2配置'
'ACEM Loop 3 Config':
  chinese: '交流电表回路3配置'
'ACEM Loop 4 Config':
  chinese: '交流电表回路4配置'
'ACEM Loop 1 Config Name':
  chinese: '交流电表回路1配置名称'
'ACEM Loop 2 Config Name':
  chinese: '交流电表回路2配置名称'
'ACEM Loop 3 Config Name':
  chinese: '交流电表回路3配置名称'
'ACEM Loop 4 Config Name':
  chinese: '交流电表回路4配置名称'
'ACEM Loop 1 Active Energy':
  chinese: '交流电表回路1有功电量'
'ACEM Loop 1 Act.Eng.':
  chinese: '交流电表回路1有功电量'
'ACEM Loop 2 Active Energy':
  chinese: '交流电表回路2有功电量'
'ACEM Loop 2 Act.Eng.':
  chinese: '交流电表回路2有功电量'
'ACEM Loop 3 Active Energy':
  chinese: '交流电表回路3有功电量'
'ACEM Loop 3 Act.Eng.':
  chinese: '交流电表回路3有功电量'
'ACEM Loop 4 Active Energy':
  chinese: '交流电表回路4有功电量'
'ACEM Loop 4 Act.Eng.':
  chinese: '交流电表回路4有功电量'
'ACEM System Name':
  chinese: '交流电表系统名称'
'ACEM Sys.Name':
  chinese: 'ACEM系统名称'
'ACEM Software Version':
  chinese: '交流电表软件版本'
'ACEM Soft.Ver.':
  chinese: 'ACEM软件版本'
'ACEM Software Release Date':
  chinese: '交流电表软件发布日期'
'ACEM Soft.Date':
  chinese: 'ACEM软件日期'
'ACEM System Address':
  chinese: '交流电表系统地址'
'ACEM Sys.Addr.':
  chinese: 'ACEM系统地址'
'Li Batt.Volt.':
  chinese: '锂电电压'
'Li Batt.Curr.':
  chinese: '锂电电流'
'Cell Volt.':
  chinese: '单体电压'
'Cell Temp.':
  chinese: '单体温度'
'Battery Board Temprature':
  chinese: '单板温度'
'Batt.Board Temp.':
  chinese: '单板温度'
'Cell Maximum Voltage':
  chinese: '单体最大电压'
'Cell Max.Volt.':
  chinese: '单体最大电压'
'Cell Minimum Voltage':
  chinese: '单体最小电压'
'Cell Min.Volt.':
  chinese: '单体最小电压'
'Cell Maximum Voltage Channel':
  chinese: '单体最大电压通道'
'Cell Max.Volt.Cha.':
  chinese: '单体最大电压通道'
'Cell Minimum Voltage Channel':
  chinese: '单体最小电压通道'
'Cell Min.Volt.Cha.':
  chinese: '单体最小电压通道'
'Cell Maximum Temperature':
  chinese: '单体最高温度'
'Cell Max.Temp.':
  chinese: '单体最高温度'
'Cell Minimum Temperature':
  chinese: '单体最低温度'
'Cell Min.Temp.':
  chinese: '单体最低温度'
'Cell Maximum Temperature Channel':
  chinese: '单体最高温度通道'
'Cell Max.Temp.Cha.':
  chinese: '单体最高温度通道'
'Cell Minimum Temperature Channel':
  chinese: '单体最低温度通道'
'Cell Min.Temp.Cha.':
  chinese: '单体最低温度通道'
'Battery Input Voltage':
  chinese: '电池输入电压'
'Batt.Input Volt.':
  chinese: '电池输入电压'
'Battery Rated Capacity':
  chinese: '电池标称容量'
'Batt.Rated.Cap.':
  chinese: '电池标称容量'
'Battery Charge Loop Switch Status':
  chinese: '充电回路开关状态'
'Chg.Sw.Stu.':
  chinese: '充开关状态'
'Battery Discharge Loop Switch Status':
  chinese: '放电回路开关状态'
'Dch.Sw.Stu.':
  chinese: '放开关状态'
'Buzzer Status':
  chinese: '蜂鸣器状态'
'Buzz.Stu.':
  chinese: '蜂鸣器状态'
'Heater Switch Status':
  chinese: '加热垫状态'
'Heater Switch Stu.':
  chinese: '加热垫状态'
'Battery Full of Charge':
  chinese: '电池充满电标志'
'Full of Chg.':
  chinese: '电池充满电标志'
'Standby':
  chinese: '待机'
'Protection':
  chinese: '保护'
'Battery Group Status':
  chinese: '电池状态'
'Batt.Stu.':
  chinese: '电池状态'
'No Adjust':
  chinese: '不需调压'
'Adjust':
  chinese: '需要调压'
'Battery Requiring Charge Status':
  chinese: '电池充电请求标志'
'Req.Chg.':
  chinese: '电池充电请求标志'
'Limit':
  chinese: '限流'
'Battery Charge Limit Status':
  chinese: '电池充电限流状态'
'Batt.Chg.Lim.Stu.':
  chinese: '电池充电限流状态'
'Batt.OVA.Alm.Stu.':
  chinese: '电池过压状态'
'Battery Over Voltage Protection Alarm Status':
  chinese: '电池过压保护状态'
'Batt.OVP.Alm.Stu.':
  chinese: '电池过压保护状态'
'Batt.UVA.Alm.Stu.':
  chinese: '电池欠压状态'
'Battery Under Voltage Protection Alarm Status':
  chinese: '电池欠压保护状态'
'Batt.UVP.Alm.Stu.':
  chinese: '电池欠压保护状态'
'Board Over Temperature Alarm Status':
  chinese: '单板过温告警状态'
'Board Over Temp.Alm.Stu.':
  chinese: '单板过温告警状态'
'Board Over Temperature Protection Alarm Status':
  chinese: '单板过温保护状态'
'Board OTP.Alm.Stu.':
  chinese: '单板过温保护状态'
'Batt.Chg.OCA.Stu.':
  chinese: '电池充过流状态'
'Battery Charge Over Current Protection Alarm Status':
  chinese: '电池充电过流保护状态'
'Batt.Chg.OCP.Stu.':
  chinese: '电池充过流保护状态'
'Batt.Disch.OCA.Stu.':
  chinese: '电池放过流状态'
'Battery Discharge Over Current Protection Alarm Status':
  chinese: '电池放电过流保护状态'
'Batt.Disch.OCP.Stu.':
  chinese: '电池放过流保护状态'
'Inner Env.OTA.Stu.':
  chinese: '机内环境过温状态'
'Inner Env.UTA.Stu.':
  chinese: '机内环境低温状态'
'Cell OVA.Alm.Stu.':
  chinese: '单体过压状态'
'Cell Over Volatge Protection Alarm Status':
  chinese: '单体过压保护状态'
'Cell OVP.Alm.Stu.':
  chinese: '单体过压保护状态'
'Cell UVA.Alm.Stu.':
  chinese: '单体欠压状态'
'Cell Under Voltage Protection Alarm Status':
  chinese: '单体欠压保护状态'
'Cell UVP.Alm.Stu.':
  chinese: '单体欠压保护状态'
'Cell Chg.OTA.Stu.':
  chinese: '单体充高温状态'
'Cell Charge Over Temperature Protection Alarm Status':
  chinese: '单体充电高温保护告警状态'
'Cell Chg.OTP.Stu.':
  chinese: '单体充高温保护状态'
'Cell Disch.OTA.Stu.':
  chinese: '单体放高温状态'
'Cell Discharge Over Temperature Protection Alarm Status':
  chinese: '单体放电高温保护告警状态'
'Cell Disch.OTP.Stu.':
  chinese: '单体放高温保护状态'
'Cell Chg.UTA.Stu.':
  chinese: '单体充低温状态'
'Cell Charge Under Temperature Protection Alarm Status':
  chinese: '单体充电低温保护告警状态'
'Cell Chg.UTP.Stu.':
  chinese: '单体充低温保护状态'
'Cell Disch.UTA.Stu.':
  chinese: '单体放低温状态'
'Cell Discharge Under Temperature Protection Alarm Status':
  chinese: '单体放电低温保护告警状态'
'Cell Disch.UTP.Stu.':
  chinese: '单体放低温保护状态'
'Batt.Soc.L.Alm.Stu.':
  chinese: '电池SOC低状态'
'Battery Soc Low Protect Alarm Status':
  chinese: '电池SOC低保护状态'
'Batt.Soc.L.Prot.Stu.':
  chinese: '电池SOC低保护状态'
'Batt.SOH.Alm.Stu.':
  chinese: '电池SOH状态'
'Battery SOH Protect Alarm Status':
  chinese: '电池SOH保护状态'
'Batt.SOH Prot.Stu.':
  chinese: '电池SOH保护状态'
'Cell Damage Protect Alarm Status':
  chinese: '单体损坏保护状态'
'Cell Dmg.Prt.Stu.':
  chinese: '单体损坏保护状态'
'Battery Missing Alarm Status':
  chinese: '电池丢失告警状态'
'Batt.Missing Alm.Stu.':
  chinese: '电池丢失状态'
'Charge Switch Invalid Alarm Status':
  chinese: '充电回路开关失效告警状态'
'Chg.Sw.Inv.Alm.Stu.':
  chinese: '充开关失效状态'
'Discharge Breaker Invalid Alarm Status':
  chinese: '放电回路开关失效告警状态'
'Dch.Sw.Inv.Alm.Stu.':
  chinese: '放开关失效状态'
'Limit Current Invalid Alarm Status':
  chinese: '限流回路失效告警状态'
'Lim.Curr.Inv.Alm.Stu.':
  chinese: '限流回路失效状态'
'Short Cut Protection Alarm Status':
  chinese: '短路保护状态'
'Short Cut Prot.Stu.':
  chinese: '短路保护状态'
'Battery Reserve Alarm Status':
  chinese: '电池反接告警状态'
'Batt.Reserve Alm.Stu.':
  chinese: '电池反接状态'
'Cell Temperature Sensor Invalid Alarm Status':
  chinese: '单体温度传感器失效告警状态'
'Cell TI.Alm.Stu.Stu.':
  chinese: '单体温传失效告警状态'
'Volt Sample Fault Alarm Status':
  chinese: '电压采样故障状态'
'Volt.Sample Faul tStu.':
  chinese: '电压采样故障状态'
'Environment Temperature Invalid Alarm Status':
  chinese: '环境温度失效状态'
'Env.Temp.Inv.Alm.Stu.':
  chinese: '环境温度失效状态'
'Cell Poor Alarm Status':
  chinese: '单体落后告警状态'
'Cell Poor Alm.Stu.':
  chinese: '单体落后告警状态'
'Cell Poor Protection Alarm Status':
  chinese: '单体落后保护状态'
'Cell Poor Prot.Alm.Stu.':
  chinese: '单体落后保护状态'
'Battery Power Line Disconnection Alarm Status':
  chinese: '电池功率线断开状态'
'Batt.Power L.Disc.Alm.Stu.':
  chinese: '电池功率线断开状态'
'Stop':
  chinese: '停止'
'Start':
  chinese: '启动'
'Battery Fan Status':
  chinese: '电池风扇状态'
'Batt.Fan Stu.':
  chinese: '电池风扇状态'
'Heating Invalid Alarm Status':
  chinese: '加热膜失效状态'
'Heat.Inv.Alm.Stu.':
  chinese: '加热膜失效状态'
'Cell Invalid Alarm Status':
  chinese: '单体失效告警状态'
'Cell Inv.Alm.Stu.':
  chinese: '单体失效告警状态'
'Cell Invalid Protection Status':
  chinese: '单体失效保护状态'
'Cell Inv.Prot.Stu.':
  chinese: '单体失效保护状态'
'Battery Fuse Damage Alarm Status':
  chinese: '电池熔丝损坏状态'
'Batt.Fuse Damage Alm.Stu.':
  chinese: '电池熔丝损坏状态'
'Battery Over Voltage Alarm':
  chinese: '电池过压告警'
'Batt.OVA.Alm.':
  chinese: '电池过压'
'Battery Over Voltage Protection Alarm':
  chinese: '电池过压保护'
'Batt.OVP.Alm.':
  chinese: '电池过压保护'
'Battery Under Voltage Alarm':
  chinese: '电池欠压告警'
'Batt.UVA.Alm.':
  chinese: '电池欠压'
'Battery Under Voltage Protection Alarm':
  chinese: '电池欠压保护'
'Batt.UVP.Alm.':
  chinese: '电池欠压保护'
'Board Over Temperature Alarm':
  chinese: '单板过温告警'
'Board Over Temp.Alm.':
  chinese: '单板过温告警'
'Board Over Temperature Protection Alarm':
  chinese: '单板过温保护'
'Board OTP.Alm.':
  chinese: '单板过温保护'
'Battery Charge Over Current Alarm':
  chinese: '电池充电过流告警'
'Battery Charge Over Current Protection Alarm':
  chinese: '电池充电过流保护'
'Battery Discharge Over Current Alarm':
  chinese: '电池放电过流告警'
'Battery Discharge Over Current Protection Alarm':
  chinese: '电池放电过流保护'
'Inner Environment Over Temperature Alarm':
  chinese: '机内环境温度高告警'
'Inner Environment Under Temperature Alarm':
  chinese: '机内环境温度低告警'
'Cell Over Volatge Alarm':
  chinese: '单体过压告警'
'Cell OVA.Alm.':
  chinese: '单体过压'
'Cell Over Volatge Protection Alarm':
  chinese: '单体过压保护'
'Cell OVP.Alm.':
  chinese: '单体过压保护'
'Cell Under Voltage Alarm':
  chinese: '单体欠压告警'
'Cell UVA.Alm.':
  chinese: '单体欠压'
'Cell Under Voltage Protection Alarm':
  chinese: '单体欠压保护'
'Cell UVP.Alm.':
  chinese: '单体欠压保护'
'Cell Charge Over Temperature Alarm':
  chinese: '单体充电高温告警'
'Cell Charge Over Temperature Protection Alarm':
  chinese: '单体充电高温保护告警'
'Cell Discharge Over Temperature Alarm':
  chinese: '单体放电高温告警'
'Cell Discharge Over Temperature Protection Alarm':
  chinese: '单体放电高温保护告警'
'Cell Charge Under Temperature Alarm':
  chinese: '单体充电低温告警'
'Cell Charge Under Temperature Protection Alarm':
  chinese: '单体充电低温保护告警'
'Cell Discharge Under Temperature Alarm':
  chinese: '单体放电低温告警'
'Cell Discharge Under Temperature Protection Alarm':
  chinese: '单体放电低温保护告警'
'Battery SOC Low Alarm':
  chinese: '电池SOC低告警'
'Batt.Soc.L.Alm.':
  chinese: '电池SOC低'
'Battery Soc Low Protect Alarm':
  chinese: '电池SOC低保护'
'Battery SOH Alarm':
  chinese: '电池SOH告警'
'Batt.SOH.Alm.':
  chinese: '电池SOH'
'Battery SOH Protect Alarm':
  chinese: '电池SOH保护'
'Cell Damage Protect Alarm':
  chinese: '单体损坏保护'
'Battery Missing Alarm':
  chinese: '电池丢失告警'
'Batt.Missing Alm.':
  chinese: '电池丢失'
'Charge Switch Invalid Alarm':
  chinese: '充电回路开关失效告警'
'Chg.Sw.Inv.Alm.':
  chinese: '充开关失效'
'Discharge Breaker Invalid Alarm':
  chinese: '放电回路开关失效告警'
'Dch.Sw.Inv.Alm.':
  chinese: '放开关失效'
'BMS Communication Fail Alarm':
  chinese: 'BMS通信断告警'
'BMS Commfail.Alm.':
  chinese: 'BMS通信断'
'Limit Current Invalid Alarm':
  chinese: '限流回路失效告警'
'Lim.Curr.Inv.Alm.':
  chinese: '限流回路失效'
'Short Cut Protection Alarm':
  chinese: '短路保护'
'Battery Reserve Alarm':
  chinese: '电池反接告警'
'Batt.Reserve Alm.':
  chinese: '电池反接'
'Cell Temperature Sensor Invalid Alarm':
  chinese: '单体温度传感器失效告警'
'Cell TI.Alm.':
  chinese: '单体温传失效告警'
'Volt Sample Fault Alarm':
  chinese: '电压采样故障'
'Volt.Sample Fault':
  chinese: '电压采样故障'
'Environment Temperature Invalid Alarm':
  chinese: '环境温度失效'
'Env.Temp.Inv.Alm.':
  chinese: '环境温度失效'
'Cell Poor Alarm':
  chinese: '单体落后告警'
'Cell Poor Alm.':
  chinese: '单体落后告警'
'Cell Poor Protection Alarm':
  chinese: '单体落后保护'
'Cell Poor Prot.Alm.':
  chinese: '单体落后保护'
'Battery Power Line Close Alarm':
  chinese: '电池功率线断开告警'
'Batt.Power LC.Alm.':
  chinese: '电池功率线断开告警'
'Heating Invalid Alarm':
  chinese: '加热膜失效告警'
'Heat Invalid Alm.':
  chinese: '加热膜失效告警'
'Cell Invalid Alarm':
  chinese: '单体失效告警'
'Cell Inv.Alm':
  chinese: '单体失效告警'
'Cell Invalid Protect Alarm':
  chinese: '单体失效保护'
'Cell Inv.Prot.Alm':
  chinese: '单体失效保护'
'Battery Fuse Damaged Alarm':
  chinese: '电池熔丝损坏告警'
'Batt.Fuse Damaged Alm.':
  chinese: '电池熔丝损坏告警'
'Cell Over Temperature Protection Alarm':
  chinese: '单体过温保护告警'
'Cell OTP.Alm.':
  chinese: '单体过温保护告警'
'Close Charge Loop':
  chinese: '闭合充电回路'
'Break Charge Loop':
  chinese: '断开充电回路'
'Close Discharge Loop':
  chinese: '闭合放电回路'
'Break Discharge Loop':
  chinese: '断开放电回路'
'BMS Sys.Name':
  chinese: 'BMS系统名称'
'BMS Manufactory Name':
  chinese: 'BMS厂家名称'
'Whole Machine SN.':
  chinese: '整机序列号'
'DU Curr.':
  chinese: '配电单元电流'
'Distribution Unit Energy':
  chinese: '配电单元电量'
'DU NRG':
  chinese: '配电单元电量'
'Distribution Unit Load Power':
  chinese: '配电单元负载功率'
'DU Load Pwr.':
  chinese: '配电单元功率'
'SDDU Total Current':
  chinese: 'SDDU总电流'
'SDDU Total Curr.':
  chinese: 'SDDU总电流'
'Smart Switch Online Number':
  chinese: '智能空开在线数量'
'SW Online Num.':
  chinese: '空开在线数量'
'Smart Switch Output Voltage':
  chinese: '智能空开输出电压'
'SW Out.Volt.':
  chinese: '空开输出电压'
'Smart Switch Current':
  chinese: '智能空开电流'
'SW Curr.':
  chinese: '空开电流'
'Smart Switch Power':
  chinese: '智能空开功率'
'SW Power':
  chinese: '空开功率'
'Smart Switch Input Energy':
  chinese: '智能空开输入电量'
'SW In.NRG':
  chinese: '空开输入电量'
'Smart Switch Output Energy':
  chinese: '智能空开输出电量'
'SW Out.NRG':
  chinese: '空开输出电量'
'Smart Switch Contact Temperature':
  chinese: '智能空开触点温度'
'SW Contact Temp.':
  chinese: '空开触点温度'
'Smart Switch Internal Temperature':
  chinese: '智能空开内部温度'
'SW Inter.Temp.':
  chinese: '空开内部温度'
'Smart Switch Fault Opening Times':
  chinese: '智能空开故障分闸次数'
'SW Fault Opening Times':
  chinese: '空开故障分闸次数'
'Smart Switch Group Address':
  chinese: '智能空开组地址'
'SW Group Addr.':
  chinese: '空开组地址'
'Smart Switch Group Inner Address':
  chinese: '智能空开组内地址'
'SW Group Inner Addr.':
  chinese: '空开组内地址'
'Distribution Unit Loop Status':
  chinese: '配电单元回路状态'
'DU Loop Stu.':
  chinese: '配电单元回路'
'Distribution Unit Minor Load Loop Status':
  chinese: '配电单元次要负载分路状态'
'DU Minor Load Stu.':
  chinese: '配电单元次要负载'
'Distribution Unit Major Load Loop Status':
  chinese: '配电单元主要负载分路状态'
'DU Major Load Stu.':
  chinese: '配电单元主要负载'
'Distribution Unit Minor LLVD Status':
  chinese: '配电单元次要负载下电状态'
'Minor LLVD Stu.':
  chinese: '次要负载下电状态'
'SDDU Work State':
  chinese: 'SDDU工作状态'
'SDDU Work Sta.':
  chinese: 'SDDU工作状态'
'SDDU Communication State':
  chinese: 'SDDU通讯状态'
'SDDU Comm.Sta.':
  chinese: 'SDDU通讯状态'
'SDDU Exist State':
  chinese: 'SDDU在位状态'
'SDDU Exist Sta.':
  chinese: 'SDDU在位状态'
'Smart Switch Loop Status':
  chinese: '智能空开回路状态'
'SW Loop Stu.':
  chinese: '空开回路状态'
'Smart Switch LVD Status':
  chinese: '智能空开下电状态'
'SW LVD Stu.':
  chinese: '空开下电状态'
'Unauthorized':
  chinese: '未授权'
'Authorized':
  chinese: '已授权'
'Smart Switch Authorized Status':
  chinese: '智能空开授权状态'
'SW Authorized Stu.':
  chinese: '空开授权状态'
'No Power':
  chinese: '无电'
'Power':
  chinese: '有电'
'Smart Switch Power Status':
  chinese: '智能空开有电状态'
'SW Power Stu.':
  chinese: '空开有电状态'
'Smart Switch P2P Status':
  chinese: '智能空开一键功能状态'
'SW P2P Stu.':
  chinese: '空开一键功能状态'
'Smart Switch Exist Status':
  chinese: '智能空开在位状态'
'SW Exist Stu.':
  chinese: '空开在位状态'
'SDDU DC SPD Status':
  chinese: 'SDDU直流防雷器状态'
'SDDU DC SPD Stu.':
  chinese: 'SDDU直流防雷器状态'
'Smart Switch Over Current Status':
  chinese: '智能空开过流状态'
'SW Over Curr.Stu.':
  chinese: '空开过流状态'
'Smart Switch Open Status':
  chinese: '智能空开断开状态'
'SW Open Stu.':
  chinese: '空开断开状态'
'Smart Switch Contact Over Temperature Status':
  chinese: '智能空开触点过温状态'
'SW Contact Over Temp.Stu.':
  chinese: '空开触点过温状态'
'Smart Switch Internal Over Temperature Status':
  chinese: '智能空开内部过温状态'
'SW Inter.Over Temp.Stu.':
  chinese: '空开内部过温状态'
'Smart Switch Low Voltage Download Status':
  chinese: '智能空开低压下电状态'
'SW Low.Volt Download Stu.':
  chinese: '空开低压下电状态'
'Smart Switch Close Failure Status':
  chinese: '智能空开合闸失败状态'
'SW Close Fail.Stu.':
  chinese: '空开合闸失败状态'
'Smart Switch Opening Failure Status':
  chinese: '智能空开分闸失败状态'
'SW Opening Fail.Stu.':
  chinese: '空开分闸失败状态'
'Smart Switch Connected Reversely Status':
  chinese: '智能空开接反状态'
'SW Connected Reversely Stu.':
  chinese: '空开接反状态'
'Smart Switch Controller Fault Status':
  chinese: '智能空开控制器故障状态'
'SW Ctrl.Fault.Stu.':
  chinese: '空开控制器故障状态'
'Smart Switch Over Current Protect Status':
  chinese: '智能空开过流保护状态'
'SW OCP Stu.':
  chinese: '空开过流保护状态'
'Smart Switch Over Temperature Protect Status':
  chinese: '智能空开过温保护状态'
'SW OTP Stu.':
  chinese: '空开过温保护状态'
'Smart Switch Overload Trip Status':
  chinese: '智能空开过载跳闸状态'
'SW Overload Trip Stu.':
  chinese: '空开过载跳闸状态'
'Smart Switch Communication Failure Status':
  chinese: '智能空开通讯断状态'
'SW Comm.Fail.Stu.':
  chinese: '空开通讯断状态'
'SDDU Communication Fail':
  chinese: 'SDDU通讯中断'
'SDDU Comm.Fail':
  chinese: 'SDDU通讯断'
'Distribution Unit Minor LLVD Alarm':
  chinese: '配电单元次要负载下电告警'
'Minor LLVD Alm.':
  chinese: '次要负载下电告警'
'Distribution Unit Minor Load Loop Broken':
  chinese: '配电单元次要负载分路断'
'DU Minor Load Brk.':
  chinese: '配电单元次要负载断'
'Distribution Unit Major Load Loop Broken':
  chinese: '配电单元主要负载分路断'
'DU Major Load Brk.':
  chinese: '配电单元主要负载断'
'SDDU DC SPD Abnormal':
  chinese: 'SDDU直流防雷器异常'
'SDDU DC SPD Abr.':
  chinese: 'SDDU直流防雷器异常'
'Smart Switch Over Current Alarm':
  chinese: '智能空开过流告警'
'SW Over Curr.Alm.':
  chinese: '空开过流告警'
'Smart Switch Open Alarm':
  chinese: '智能空开断开'
'SW Open Alm.':
  chinese: '空开断开'
'Smart Switch Contact Over Temperature':
  chinese: '智能空开触点过温'
'SW Contact Over Temp.':
  chinese: '空开触点过温'
'Smart Switch Internal Over Temperature':
  chinese: '智能空开内部过温'
'SW Inter.Over Temp.':
  chinese: '空开内部过温'
'Smart Switch Low Voltage Download':
  chinese: '智能空开低压下电'
'SW Low.Volt Download':
  chinese: '空开低压下电'
'Smart Switch LVD Alarm':
  chinese: '智能空开下电告警'
'SW LVD Alm.':
  chinese: '空开下电告警'
'Smart Switch Close Failure':
  chinese: '智能空开合闸失败'
'SW Close Fail.':
  chinese: '空开合闸失败'
'Smart Switch Opening Failure':
  chinese: '智能空开分闸失败'
'SW Opening Fail.':
  chinese: '空开分闸失败'
'Smart Switch Connected Reversely':
  chinese: '智能空开接反'
'SW Connected Reversely':
  chinese: '空开接反'
'Smart Switch Controller Fault':
  chinese: '智能空开控制器故障'
'SW Ctrl.Fault':
  chinese: '空开控制器故障'
'Smart Switch Over Current Protect':
  chinese: '智能空开过流保护'
'SW OCP':
  chinese: '空开过流保护'
'Smart Switch Over Temperature Protect':
  chinese: '智能空开过温保护'
'SW OTP':
  chinese: '空开过温保护'
'Smart Switch Overload Trip':
  chinese: '智能空开过载跳闸'
'SW Overload Trip':
  chinese: '空开过载跳闸'
'Smart Switch Communication Failure':
  chinese: '智能空开通讯断'
'SW Comm.Fail.':
  chinese: '空开通讯断'
'Distribution Unit LLVD':
  chinese: '配电单元下电'
'DU LLVD':
  chinese: '配电单元下电'
'Distribution Unit Upload':
  chinese: '配电单元上电'
'DU Upload':
  chinese: '配电单元上电'
'Distribution Unit Energy Reset':
  chinese: '配电单元电量清零'
'DU Eng.Reset':
  chinese: '配电单元电量清零'
'Smart Switch LVD':
  chinese: '智能空开下电'
'SW LVD':
  chinese: '空开下电'
'Smart Switch Upload':
  chinese: '智能空开上电'
'SW Upload':
  chinese: '空开上电'
'Smart Switch Energy Reset':
  chinese: '智能空开电量清零'
'SW Eng.Reset':
  chinese: '空开电量清零'
'Smart Switch Enter P2P':
  chinese: '智能空开进入一键功能'
'SW Enter P2P':
  chinese: '空开进入一键功能'
'Smart Switch Quit P2P':
  chinese: '智能空开退出一键功能'
'SW Quit P2P':
  chinese: '空开退出一键功能'
'Smart Switch Reset':
  chinese: '智能空开复位'
'SW Reset':
  chinese: '空开复位'
'Smart Switch Component Statistic':
  chinese: '智能空开部件统计'
'SW Component Stat.':
  chinese: '空开部件统计'
'Distribution Unit LLVD Enabled':
  chinese: '配电单元下电使能'
'DU LLVD Enabled':
  chinese: '配电单元下电使能'
'Distribution Unit LLVD Voltage Threshold':
  chinese: '配电单元下电电压阈值'
'DU LLVD Volt.Thre.':
  chinese: '配电单元下电电压'
'Distribution Unit LLVD Duration':
  chinese: '配电单元下电时间'
'Distribution Unit LLVD SOC Threshold':
  chinese: '配电单元下电SOC阈值'
'Distribution Unit Upload Voltage Threshold':
  chinese: '配电单元上电电压'
'DU Upload Volt.':
  chinese: '配电单元上电电压'
'DU Tenant Cfg.':
  chinese: '配电单元租户设置'
'DU Name':
  chinese: '配电单元名称'
'Distribution Unit Energy Parameter':
  chinese: '配电单元电量参数'
'DU NRG Para.':
  chinese: '配电单元电量参数'
'Distribution Unit Remote Disconnect Enabled':
  chinese: '配电单元远程下电使能'
'DU Remote Disc.En.':
  chinese: '配电单元远程下电使能'
'Distribution Unit Remote Disconnect':
  chinese: '配电单元远程下电'
'DU Remote Disc.':
  chinese: '配电单元远程下电'
'Busbar':
  chinese: '母排'
'Load':
  chinese: '负载'
'SDDU Access Configuration':
  chinese: 'SDDU接入配置'
'SDDU Access Config.':
  chinese: 'SDDU接入配置'
'Smart Switch LVD Enabled':
  chinese: '智能空开下电使能'
'SW LVD Enabled':
  chinese: '空开下电使能'
'Smart Switch LVD Voltage Threshold':
  chinese: '智能空开下电电压阈值'
'SW LVD Volt.Thre.':
  chinese: '空开下电电压'
'Smart Switch LVD Duration':
  chinese: '智能空开下电时间'
'SW LVD Dura.':
  chinese: '空开下电时间'
'Smart Switch LVD SOC Threshold':
  chinese: '智能空开下电SOC阈值'
'SW LVD SOC':
  chinese: '空开下电SOC'
'Smart Switch Remote Disconnect Enabled':
  chinese: '智能空开远程下电使能'
'SW Remote Disc.En.':
  chinese: '空开远程下电使能'
'Smart Switch Remote Disconnect':
  chinese: '智能空开远程下电'
'SW Remote Disc.':
  chinese: '空开远程下电'
'Smart Switch Fixed Time Disconnect Enabled':
  chinese: '智能空开定时下电使能'
'SW FixT.Disc En.':
  chinese: '空开定时下电使能'
'Smart Switch Fixed Time Disconnect Start Clock1':
  chinese: '智能空开定时下电起始时刻1'
'SW FixT.Disc St.1':
  chinese: '空开定时下电起始时刻1'
'Smart Switch Fixed Time Disconnect End Clock1':
  chinese: '智能空开定时下电终止时刻1'
'SW FixT.Disc End.1':
  chinese: '空开定时下电终止时刻1'
'Smart Switch Fixed Time Disconnect Start Clock2':
  chinese: '智能空开定时下电起始时刻2'
'SW FixT.Disc St.2':
  chinese: '空开定时下电起始时刻2'
'Smart Switch Fixed Time Disconnect End Clock2':
  chinese: '智能空开定时下电终止时刻2'
'SW FixT.Disc End.2':
  chinese: '空开定时下电终止时刻2'
'Smart Switch Fixed Time Disconnect Start Clock3':
  chinese: '智能空开定时下电起始时刻3'
'SW FixT.Disc St.3':
  chinese: '空开定时下电起始时刻3'
'Smart Switch Fixed Time Disconnect End Clock3':
  chinese: '智能空开定时下电终止时刻3'
'SW FixT.Disc End.3':
  chinese: '空开定时下电终止时刻3'
'Smart Switch Fixed Time Disconnect Start Clock4':
  chinese: '智能空开定时下电起始时刻4'
'SW FixT.Disc St.4':
  chinese: '空开定时下电起始时刻4'
'Smart Switch Fixed Time Disconnect End Clock4':
  chinese: '智能空开定时下电终止时刻4'
'SW FixT.Disc End.4':
  chinese: '空开定时下电终止时刻4'
'Smart Switch Fixed Time Disconnect Start Clock5':
  chinese: '智能空开定时下电起始时刻5'
'SW FixT.Disc St.5':
  chinese: '空开定时下电起始时刻5'
'Smart Switch Fixed Time Disconnect End Clock5':
  chinese: '智能空开定时下电终止时刻5'
'SW FixT.Disc End.5':
  chinese: '空开定时下电终止时刻5'
'Smart Switch Upload Voltage':
  chinese: '智能空开上电电压'
'SW Upload Volt.':
  chinese: '空开上电电压'
'Smart Switch Over Current Protect Enabled':
  chinese: '智能空开过流保护使能'
'SW OCP Enabled':
  chinese: '空开过流保护使能'
'Smart Switch Over Current Alarm Threshold':
  chinese: '智能空开过流告警阈值'
'SW OCA Thre.':
  chinese: '空开过流告警阈值'
'Smart Switch Over Current Protect Threshold':
  chinese: '智能空开过流保护阈值'
'SW OCP Thre.':
  chinese: '空开过流保护阈值'
'Smart Switch Upload Authorization':
  chinese: '智能空开上电授权'
'SW Upload Auth.':
  chinese: '空开上电授权'
'Smart Switch Over Current Protect Delay':
  chinese: '智能空开过流保护延时'
'SW OCP Delay':
  chinese: '空开过流保护延时'
'Smart Switch Over Current Protect Resume Times':
  chinese: '智能空开过流保护自动恢复次数'
'SW OCP Res.Times':
  chinese: '空开过流自动恢复次数'
'Smart Switch Over Current Protect Resume Interval':
  chinese: '智能空开过流保护自动恢复间隔'
'SW OCP Res.Inter.':
  chinese: '空开过流自动恢复间隔'
'Direct Judgment':
  chinese: '直接判断'
'Judge Load':
  chinese: '判断负载'
'Smart Switch Disconnect Judge Mode':
  chinese: '智能空开断开判断方式'
'SW Disc.Judge Mode':
  chinese: '空开断开判断方式'
'Smart Switch Input Energy Benchmark Parameters':
  chinese: '智能空开输入电量基准参数'
'SW In.NRG Benchmark':
  chinese: '空开输入电量基准'
'Smart Switch OutPut Energy Benchmark Parameters':
  chinese: '智能空开输出电量基准参数'
'SW Out.NRG Benchmark':
  chinese: '空开输出电量基准'
'Smart Switch Name':
  chinese: '智能空开名称'
'SW Name':
  chinese: '空开名称'
'DU Total NRG Cons.':
  chinese: '配电单元总电量'
'SDDU Total Energy Consumption':
  chinese: 'SDDU总耗电量'
'SDDU NRG Cons.':
  chinese: 'SDDU总电量'
'Smart Switch Output Total Energy':
  chinese: '智能空开输出总电量'
'SW Out.Total NRG':
  chinese: '空开输出总电量'
'Smart Switch Input Total Energy':
  chinese: '智能空开输入总电量'
'SW In.Total NRG':
  chinese: '空开输入总电量'
'SDDU System Name':
  chinese: 'SDDU系统名称'
'SDDU Sys.Name':
  chinese: 'SDDU系统名称'
'SDDU Software Version':
  chinese: 'SDDU软件版本'
'SDDU Soft.Ver.':
  chinese: 'SDDU软件版本'
'SDDU Software Release Date':
  chinese: 'SDDU软件发布日期'
'SDDU Soft.Date':
  chinese: 'SDDU软件发布日期'
'SDDU Serial Number':
  chinese: 'SDDU序列号'
'SDDU SN':
  chinese: 'SDDU序列号'
'Common SW':
  chinese: '普通空开'
'Smart SW':
  chinese: '智能空开'
'SDDU Switch Type':
  chinese: 'SDDU空开类型'
'SDDU SW Type':
  chinese: 'SDDU空开类型'
'Smart Switch System Name':
  chinese: '智能空开系统名称'
'SW Sys.Name':
  chinese: '空开系统名称'
'Smart Switch Software Version':
  chinese: '智能空开软件版本'
'SW Soft.Ver.':
  chinese: '空开软件版本'
'Smart Switch Software Release Date':
  chinese: '智能空开软件发布日期'
'SW Soft.Date':
  chinese: '空开软件发布日期'
'Smart Switch Serial Number':
  chinese: '智能空开序列号'
'SW SN':
  chinese: '空开序列号'
'Smart Switch Rated Current':
  chinese: '智能空开额定电流'
'SW Rat.Curr':
  chinese: '空开额定电流'
'SPU Output Voltage':
  chinese: 'SPU输出电压'
'SPU Output Volt.':
  chinese: 'SPU输出电压'
'SPU Output Current':
  chinese: 'SPU输出电流'
'SPU Output Curr.':
  chinese: 'SPU输出电流'
'SPU Output Power':
  chinese: 'SPU输出功率'
'SPU Energy':
  chinese: 'SPU电量'
'SPU Input Voltage':
  chinese: 'SPU输入电压'
'SPU Input Volt.':
  chinese: 'SPU输入电压'
'SPU Input Current':
  chinese: 'SPU输入电流'
'SPU Input Curr.':
  chinese: 'SPU输入电流'
'SPU Input Power':
  chinese: 'SPU输入功率'
'SPU Temperature':
  chinese: 'SPU温度'
'SPU Temp.':
  chinese: 'SPU温度'
'SPU Upgrade Progress':
  chinese: 'SPU升级进度'
'SPU Upgrade Progress':
  chinese: 'SPU升级进度'
'SPU Exist Number':
  chinese: 'SPU在位数量'
'SPU Exist Num.':
  chinese: 'SPU在位数量'
'SPCU Temperature':
  chinese: 'SPCU温度'
'SPCU Temp.':
  chinese: 'SPCU温度'
'SPCU Group Address':
  chinese: 'SPCU组地址'
'SPCU Group Addr.':
  chinese: 'SPCU组地址'
'SPCU Group Inner Address':
  chinese: 'SPCU组内地址'
'SPCU Group Inn.Addr.':
  chinese: 'SPCU组内地址'
'SPCU Slot Address':
  chinese: 'SPCU槽位地址'
'SPCU Slot Addr.':
  chinese: 'SPCU槽位地址'
'SPU Off Status':
  chinese: 'SPU关机状态'
'SPU Off':
  chinese: 'SPU关机状态'
'SPU MPPT Status':
  chinese: 'SPU MPPT状态'
'SPU Input UVP Status':
  chinese: 'SPU输入欠压状态'
'SPU Input UVP':
  chinese: 'SPU输入欠压'
'SPU Input Invalid Status':
  chinese: 'SPU输入无效状态'
'SPU Input Invalid':
  chinese: 'SPU输入无效'
'SPU Output Current Limit Status':
  chinese: 'SPU输出限流状态'
'SPU Output CL':
  chinese: 'SPU输出限流'
'SPU Software Update Enable':
  chinese: 'SPU升级使能状态'
'SPU Update Enable':
  chinese: 'SPU升级使能状态'
'SPU Sleep Status':
  chinese: 'SPU休眠状态'
'SPU Output Power Limit Status':
  chinese: 'SPU输出限功率状态'
'SPU Output PL':
  chinese: 'SPU输出限功率'
'SPU Work Status':
  chinese: 'SPU工作状态'
'SPU Input OVP Alarm Status':
  chinese: 'SPU输入过压状态'
'SPU Input OVP Alm.St.':
  chinese: 'SPU输入过压状态'
'SPU Output OVP Alarm Status':
  chinese: 'SPU输出过压状态'
'SPU Output OVP Alm.St.':
  chinese: 'SPU输出过压状态'
'SPU Output Over Current Alarm Status':
  chinese: 'SPU输出过流状态'
'SPU Output OC Alm.St.':
  chinese: 'SPU输出过流告警状态'
'SPU EEPROM Failure Alarm Status':
  chinese: 'SPU EEPROM异常状态'
'SPU EEPROM Fault St.':
  chinese: 'SPU EEPROM异常状态'
'SPU Output UVP Status':
  chinese: 'SPU输出欠压状态'
'SPU Output UVP':
  chinese: 'SPU输出欠压'
'SPU Communication Fail Status':
  chinese: 'SPU通讯中断状态'
'SPU Comm.Fail St.':
  chinese: 'SPU通讯中断状态'
'SPU SN Clash Status':
  chinese: 'SPU序列号冲突状态'
'SPU SN Clash St.':
  chinese: 'SPU序列号冲突状态'
'SPU Protocol Error Status':
  chinese: 'SPU协议错误状态'
'SPU Pro.Err.St.':
  chinese: 'SPU协议错误状态'
'SPU Input Flow Back Status':
  chinese: 'SPU输入倒灌状态'
'SPU Input Flw.Back St.':
  chinese: 'SPU输入倒灌状态'
'No Upgrade':
  chinese: '未执行升级'
'Upgrading':
  chinese: '升级中'
'Upgrade Fail':
  chinese: '升级失败'
'Upgrade Success':
  chinese: '升级成功'
'SPU Upgrade Status':
  chinese: 'SPU升级状态'
'SPU Upgrade St.':
  chinese: 'SPU升级状态'
'NULL':
  chinese: '空'
'Unknown Mistake':
  chinese: '未知错误'
'No Upgrade File':
  chinese: '升级文件不存在'
'Upgrade Trigger Failed':
  chinese: '升级触发失败'
'Upgrade File Transfer Failed':
  chinese: '升级文件传输失败'
'Upgrade Prohibited':
  chinese: 'SPU禁止升级'
'SPU Switch BOOT Failed':
  chinese: 'SPU切换到BOOT区失败'
'SM Erase Flash Failed':
  chinese: 'SM擦除Flash失败'
'CRC Verifiy Failed':
  chinese: 'CRC校验失败'
'Filename Mismatch':
  chinese: 'SPCU与SPU文件名不匹配'
'Failed Verify After Write data':
  chinese: 'SPU烧写完SPCU下发的一包数据帧后校验失败'
'Failed Write Data Frame':
  chinese: 'SPU烧写SPCU发送过来的一帧数据失败'
'SPU Upgrade Failure Error Code':
  chinese: 'SPU升级失败错误码'
'Upgrade Fail.Err.Code':
  chinese: '升级失败错误码'
'SPCU P2P Status':
  chinese: 'SPCU一键功能状态'
'SPCU P2P':
  chinese: 'SPCU一键功能'
'Input Switch Status':
  chinese: '输入空开状态'
'All SPU Com Fail Status':
  chinese: '所有SPU通讯断状态'
'All SPU Com Fail St.':
  chinese: '所有SPU通讯断'
'SPCU EEPROM Status':
  chinese: 'SPCU EEPROM故障状态'
'SPCU EEPROM St.':
  chinese: 'SPCU EEPROM状态'
'SPCU SN Clash Status':
  chinese: 'SPCU序列号冲突状态'
'SPCU SN Clash St.':
  chinese: 'SPCU序列号冲突状态'
'SPCU Work Status':
  chinese: 'SPCU工作状态'
'SPCU Work Stat.':
  chinese: 'SPCU工作状态'
'SPCU Communication Status':
  chinese: 'SPCU通讯状态'
'SPCU Comm.Stat.':
  chinese: 'SPCU通讯状态'
'SPCU Exist Status':
  chinese: 'SPCU在位状态'
'SPCU Exist Stat.':
  chinese: 'SPCU在位状态'
'SPU Input OVP Alarm':
  chinese: 'SPU输入过压告警'
'SPU Input OVP Alm.':
  chinese: 'SPU输入过压告警'
'SPU Output OVP Alarm':
  chinese: 'SPU输出过压告警'
'SPU Output OVP Alm.':
  chinese: 'SPU输出过压告警'
'SPU Output Over Current Alarm':
  chinese: 'SPU输出过流告警'
'SPU Output OC':
  chinese: 'SPU输出过流告警'
'SPU EEPROM Failure Alarm':
  chinese: 'SPU EEPROM异常'
'SPU EEPROM':
  chinese: 'SPU EEPROM异常'
'SPU Output UVP Alarm':
  chinese: 'SPU输出欠压'
'SPU Output UVP Alm.':
  chinese: 'SPU输出欠压'
'SPU Communication Fail':
  chinese: 'SPU通讯中断'
'SPU Comm.Fail':
  chinese: 'SPU通讯中断'
'SPU SN Clash':
  chinese: 'SPU序列号冲突'
'SPU Protocol Error':
  chinese: 'SPU协议错误'
'SPU Pro.Err.':
  chinese: 'SPU协议错误'
'SPU Input Flow Back':
  chinese: 'SPU输入防倒灌'
'Input Flw.Back':
  chinese: '输入防倒灌'
'SPCU Communication Fail':
  chinese: 'SPCU通讯断'
'SPCU Comm.Fail':
  chinese: 'SPCU通讯断'
'All SPU Com Fail':
  chinese: '所有SPU通讯断'
'All SPU Comm.Fail':
  chinese: '所有SPU通讯断'
'SPCU EEPROM':
  chinese: 'SPCU EEPROM故障'
'SPCU SN Clash':
  chinese: 'SPCU序列号冲突'
'SPU Alarm':
  chinese: 'SPU告警'
'SPU Fault':
  chinese: 'SPU故障'
'SPCU Alarm':
  chinese: 'SPCU告警'
'SPCU Fault':
  chinese: 'SPCU故障'
'SPU GRID SN':
  chinese: '光伏阵列序号'
'SPU GRID X':
  chinese: 'SPU空间X坐标'
'SPU GRID Y':
  chinese: 'SPU空间Y坐标'
'SPU Pan ID':
  chinese: 'SPU网络标识'
'SPU Pan Address':
  chinese: 'SPU网络地址'
'SPU Pan Addr.':
  chinese: 'SPU网络地址'
'PLC PAN CAP':
  chinese: 'PLC网络容量'
'SPCU Pan ID':
  chinese: 'SPCU网络标识'
'SPU Waken':
  chinese: 'SPU唤醒'
'SPU Sleep':
  chinese: 'SPU休眠'
'SPU Energy Reset':
  chinese: 'SPU电量清零'
'SPU Reset':
  chinese: 'SPU重启'
'SPU Factory Reset':
  chinese: 'SPU恢复出厂设置'
'SPU Fact.Reset':
  chinese: 'SPU恢复出厂设置'
'SPU Network Configuration Reset':
  chinese: 'SPU恢复网络配置'
'SPU Net.Conf.Reset':
  chinese: 'SPU恢复网络配置'
'SPCU Enter P2P Mode':
  chinese: 'SPCU进入一键模式'
'SPCU Enter P2P':
  chinese: 'SPCU进入一键模式'
'SPCU Quit P2P Mode':
  chinese: 'SPCU退出一键模式'
'SPCU Quit P2P':
  chinese: 'SPCU退出一键模式'
'SPCU Network Configuration Reset':
  chinese: 'SPCU恢复网络配置'
'SPCU Net.Conf.Reset':
  chinese: 'SPCU恢复网络配置'
'SPU Power Total Generation':
  chinese: 'SPU累计发电量'
'SPU Total Pwr.Gene.':
  chinese: 'SPU累计发电量'
'SPU Serial Number':
  chinese: 'SPU序列号'
'SPU SN':
  chinese: 'SPU序列号'
'SPU Name':
  chinese: 'SPU名称'
'SPU Software Version':
  chinese: 'SPU软件版本'
'SPU Software Ver.':
  chinese: 'SPU软件版本'
'SPU Software Release Date':
  chinese: 'SPU软件发布日期'
'SPU Software Date':
  chinese: 'SPU软件发布日期'
'SPU Digital Control Platform Version':
  chinese: 'SPU数控平台版本'
'SPU Platform':
  chinese: 'SPU数控平台版本'
'SPU Manufactory ID':
  chinese: 'SPU制造商ID'
'SPU Manufy.ID':
  chinese: 'SPU制造商ID'
'SPU Manufactory Address':
  chinese: 'SPU制造商地址'
'SPU Manufty.Addr.':
  chinese: 'SPU制造商地址'
'SPU Barcodes':
  chinese: 'SPU条码'
'SPU Manufacture Date':
  chinese: 'SPU生产日期'
'SPU Manufy.Date':
  chinese: 'SPU生产日期'
'SPCU Serial Number':
  chinese: 'SPCU序列号'
'SPCU SN':
  chinese: 'SPCU序列号'
'SPCU Name':
  chinese: 'SPCU名称'
'SPCU Software Version':
  chinese: 'SPCU软件版本'
'SPCU Software Ver.':
  chinese: 'SPCU软件版本'
'SPCU Software Release Date':
  chinese: 'SPCU软件发布日期'
'SPCU Software Date':
  chinese: 'SPCU软件发布日期'
'SPCU Manufactory ID':
  chinese: 'SPCU制造商ID'
'SPCU Manufy.ID':
  chinese: 'SPCU制造商ID'
'SPCU Manufactory Address':
  chinese: 'SPCU制造商地址'
'SPCU Manufty.Addr.':
  chinese: 'SPCU制造商地址'
'SPCU Barcodes':
  chinese: 'SPCU条码'
'SPCU Manufacture Date':
  chinese: 'SPCU生产日期'
'SPCU Manufty.Date':
  chinese: 'SPCU生产日期'
'DC Air Conditioner':
  chinese: '直流空调'
'DCEM':
  chinese: '直流电表'
'System DC Input':
  chinese: '系统直流输入'
'DC Input Distribution':
  chinese: '直流输入配电'
'Smart Switch':
  chinese: '智能空开'
'Software Version Before Upgrade':
  chinese: '升级前软件版本'
'Soft.Ver.Bef.Upgrade':
  chinese: '升级前软件版本'
'SPU Setting Voltage':
  chinese: 'SPU当前输出设定电压'
'SPU Set.Volt.':
  chinese: 'SPU设定电压'
'DCEM Device Statistic':
  chinese: '直流电表设备统计'
'DCEM Dev.Stat.':
  chinese: '直流电表设备统计'
'Power Module Slot Output Current Specifications':
  chinese: '功率模块槽位输出电流规格'
'Power Module Slot Specif.':
  chinese: '功率模块槽位规格'
'SMR Current Input Energy':
  chinese: '整流器当前输入电量'
'Current Input Energy':
  chinese: '当前输入电量'
'SMR DC Fin Temperature':
  chinese: '整流器后级散热器温度'
'DC Fin Temp.':
  chinese: '后级散热器温度'
'SMR Connect Temperature':
  chinese: '整流器金手指温度'
'Connect Temp.':
  chinese: '金手指温度'
'Compete':
  chinese: '竞争'
'Master':
  chinese: '主机'
'Slave':
  chinese: '从机'
'SMR Master Slave Status':
  chinese: '主从机状态'
'SMR M.S Sta.':
  chinese: '主从机状态'
'Normal':
  chinese: '正常'
'Fault':
  chinese: '异常'
'SMR Bad Contact State':
  chinese: '整流器接触不良状态'
'Bad Conta. Sta.':
  chinese: '接触不良状态'
'SMR Abonormal Fan Speed State':
  chinese: '整流器风扇转速异常状态'
'Abn. Fan Spe. Sta.':
  chinese: '风扇转速异常状态'
'SMR Connector Temperature High State':
  chinese: '整流器连接器温度高状态'
'Connect. T. H. Sta.':
  chinese: '连接器温度高状态'
'Mask':
  chinese: '屏蔽'
'Critical':
  chinese: '严重'
'Major':
  chinese: '主要'
'Minor':
  chinese: '次要'
'Warning':
  chinese: '警告'
'SMR Bad Contact':
  chinese: '整流器接触不良'
'Bad Conta.':
  chinese: '接触不良'
'SMR Abnormal Fan Speed':
  chinese: '整流器风扇转速异常'
'Ab. Fan Speed':
  chinese: '风扇转速异常'
'smr_connector_temp_high':
  chinese: '整流器连接器温度高'
'connect. temp. high':
  chinese: '连接器温度高'
'SMR Software Name':
  chinese: '整流器软件名称'
'Soft.Name':
  chinese: '软件名称'
'SMR Module Redundancy Backup Insufficient Alarm':
  chinese: '整流模块冗余备份不足告警'
'SMR Mod.Redun.Back.Insuf.Alm':
  chinese: '整流模块备份不足'
'Disabled':
  chinese: '禁止'
'Enabled':
  chinese: '允许'
'SMR Temperature Self-Balance Enable':
  chinese: '整流器温度自平衡使能'
'Temp. Self-balance. En.':
  chinese: '温度自平衡使能'
'SMR Backup Number':
  chinese: '整流器备份数量'
'None':
  chinese: '无'
'Batt1_Temp_Channel':
  chinese: '电池1_温度采样通道'
'Batt2_Temp_Channel':
  chinese: '电池2_温度采样通道'
'Batt3_Temp_Channel':
  chinese: '电池3_温度采样通道'
'Batt4_Temp_Channel':
  chinese: '电池4_温度采样通道'
'Environment Temperature Configuration':
  chinese: '环境温度配置'
'Env.Temp.Config.':
  chinese: '环境温度配置'
'Unauthorized':
  chinese: '未授权'
'Authorized':
  chinese: '已授权'
'DC DU Authorized Status':
  chinese: '直流配电单元授权状态'
'DC DU Authorized Stu.':
  chinese: '直流配电授权状态'
'Distribution Unit LLVD Power Reserve':
  chinese: '配电单元下电备电电量'
'DU LLVD Power Reserve':
  chinese: '配电单元下电备电电量'
'DC Output Voltage High Backlash':
  chinese: '直流输出电压高回差'
'DC Out.Volt.High Back.':
  chinese: '直流输出电压高回差'
'DC Output Voltage Low Backlash':
  chinese: '直流输出电压低回差'
'DC Out.Volt.Low Back.':
  chinese: '直流输出电压低回差'
'Distribution Unit Upload Authorizes':
  chinese: '配电单元上电授权'
'DU Upload Authorizes':
  chinese: '配电单元上电授权'
'Env_Temp_Channel':
  chinese: '环境温度采样通道'
'Battery Temperature Configuration':
  chinese: '电池温度配置'
'Batt.Temp.Config.':
  chinese: '电池温度配置'
'Third Party Battery Current':
  chinese: '第三方电池电流'
'Third Party Batt.Curr.':
  chinese: '第三方电池电流'
'Battery Period Test Enable':
  chinese: '电池周期测试使能'
'Period Test En.':
  chinese: '周期测试使能'
'Third Party Battery Setting Current Limit':
  chinese: '第三方电池设定限流点'
'Third Party Batt.Curr.Limit':
  chinese: '第三方电池限流点'
'Third Party Battery Current High Rate Maximum':
  chinese: '第三方电池过流比率上限'
'Third Party Batt.Curr.High.Rate Max.':
  chinese: '第三方电池过流比率上限'
'Third Party Battery Current High Rate Minimum':
  chinese: '第三方电池过流比率下限'
'Third Party Batt.Curr.High.Rate Min.':
  chinese: '第三方电池过流比率下限'
'SPCU No Network Configuration':
  chinese: '无SPCU网络配置'
'SPCU No Net.Conf.':
  chinese: '无SPCU网络配置'
'SPU Network Configuration Fault':
  chinese: 'SPU网络配置异常'
'SPU Net.Conf.Fault':
  chinese: 'SPU网络配置异常'
'All SPCU Get Network Configuration':
  chinese: '获取所有SPCU网络配置'
'All SPCU Get Net.Conf.':
  chinese: '获取所有SPCU网络配置'
'All SPCU Set Network Configuration':
  chinese: '设置所有SPCU网络配置'
'All SPCU Set Net.Conf.':
  chinese: '设置所有SPCU网络配置'
'All SPCU Delete Network Configuration':
  chinese: '删除所有SPCU网络配置'
'All SPCU Del Net.Conf.':
  chinese: '删除所有SPCU网络配置'
'SPCU Maximum Quantity':
  chinese: 'SPCU最大数量'
'SPCU Max Qty':
  chinese: 'SPCU最大数量'
'SPU Setting Voltage Maximum':
  chinese: 'SPU设定电压上限'
'SPU Set.Volt.MAx.':
  chinese: 'SPU设定电压上限'
'SPU Output OVP Voltage Threshold':
  chinese: 'SPU输出过压保护值'
'SPU Out.OVP Volt.':
  chinese: 'SPU输出过压值'
'SPU Default Ouput Voltage':
  chinese: 'SPU默认输出电压值'
'SPU Def.Out.Volt.':
  chinese: 'SPU默认输出电压值'
'SPU Smart Photovoltaic System Default Output Voltage':
  chinese: 'SPU叠光电源默认输出电压'
'SPU SPV Def.Out.Vol.':
  chinese: 'SPU叠光默认输出电压'
'SPU Busbar Reference Voltage Deviation Threshold':
  chinese: 'SPU母排基准电压偏差阈值'
'SPU Busbar Ref.Vol.Dev.Thre.':
  chinese: 'SPU母排基准压差阈值'
'Estimated Battery Discharge Duration':
  chinese: '预估电池放电时间'
'Estimate Dura.':
  chinese: '预估放电时间'
'Not Exist':
  chinese: '不在位'
'Is Exist':
  chinese: '在位'
'Distribution Unit Exist Status':
  chinese: '配电单元在位状态'
'DU Exist Stu.':
  chinese: '配电单元在位状态'
'VRLA Smart Switch LVD Voltage Threshold':
  chinese: '铅酸智能空开下电电压阈值'
'VRLA SW LVD Volt.Thre.':
  chinese: '铅酸空开下电电压'
'VRLA Smart Switch LVD SOC Threshold':
  chinese: '铅酸智能空开下电SOC阈值'
'VRLA SW LVD SOC':
  chinese: '铅酸空开下电SOC'
'VRLA Smart Switch LVD Duration':
  chinese: '铅酸智能空开下电时间'
'VRLA SW LVD Dura.':
  chinese: '铅酸空开下电时间'
'FB15 Smart Switch LVD Voltage Threshold':
  chinese: 'FB15智能空开下电电压阈值'
'FB15 SW LVD Volt.Thre.':
  chinese: 'FB15空开下电电压'
'FB15 Smart Switch LVD SOC Threshold':
  chinese: 'FB15智能空开下电SOC阈值'
'FB15 SW LVD SOC':
  chinese: 'FB15空开下电SOC'
'FB15 Smart Switch LVD Duration':
  chinese: 'FB15智能空开下电时间'
'FB15 SW LVD Dura.':
  chinese: 'FB15空开下电时间'
'VRLA25 Smart Switch LVD Voltage Threshold':
  chinese: '铅酸25智能空开下电电压阈值'
'VRLA25 SW LVD Volt.Thre.':
  chinese: '铅酸25空开下电电压'
'VRLA DC DU LLVD Voltage':
  chinese: '铅酸直流配电单元下电电压'
'VRLA DU LLVD Volt.':
  chinese: '铅酸配电单元下电电压'
'VRLA DC DU LLVD SOC Threshold':
  chinese: '铅酸直流配电单元下电SOC阈值'
'VRLA DU LLVD SOC':
  chinese: '铅酸配电单元下电SOC'
'VRLA DC DU LLVD Duration':
  chinese: '铅酸直流配电单元下电时间'
'VRLA DU LLVD Dura.':
  chinese: '铅酸配电单元下电时间'
'FB15 DC DU LLVD Voltage':
  chinese: 'FB15直流配电单元下电电压'
'FB15 DU LLVD Volt.':
  chinese: 'FB15配电单元下电电压'
'FB15 DC DU LLVD SOC Threshold':
  chinese: 'FB15直流配电单元下电SOC阈值'
'FB15 DU LLVD SOC':
  chinese: 'FB15配电单元下电SOC'
'FB15 DC DU LLVD Duration':
  chinese: 'FB15直流配电单元下电时间'
'FB15 DU LLVD Dura.':
  chinese: 'FB15配电单元下电时间'
'VRLA25 DC DU LLVD Voltage':
  chinese: '铅酸25直流配电单元下电电压'
'VRLA25 DU LLVD Volt.':
  chinese: '铅酸25配电单元下电电压'
'DCYB Smart Switch LLVD SOC Threshold':
  chinese: '深循环智能空开下电SOC阈值'
'DCYB SW LLVD SOC':
  chinese: '深循环空开下电SOC'
'DCYB Smart Switch LLVD Duration':
  chinese: '深循环智能空开下电时间'
'DCYB SW LLVD Dura.':
  chinese: '深循环空开下电时间'
'DCYB DC DU LLVD SOC Threshold':
  chinese: '深循环直流配电单元下电SOC阈值'
'DCYB DU LLVD SOC':
  chinese: '深循环配电单元下电SOC'
'DCYB DC DU LLVD Duration':
  chinese: '深循环直流配电单元下电时间'
'DCYB DU LLVD Dura.':
  chinese: '深循环配电单元下电时间'
'SPCU Output Voltage':
  chinese: 'SPCU输出电压'
'SPCU Output Volt.':
  chinese: 'SPCU输出电压'
'SPCU Output Current':
  chinese: 'SPCU输出电流'
'SPCU Output Curr.':
  chinese: 'SPCU输出电流'
'SPU Heat Sink Temperature':
  chinese: 'SPU散热片温度'
'SPU Heat Sink Temp.':
  chinese: 'SPU散热片温度'
'SPU Outside Output Voltage':
  chinese: 'SPU外部输出电压'
'SPU Outside Volt.':
  chinese: 'SPU外部输出电压'
'SPCU Network Configuration Miss State':
  chinese: 'SPCU网络配置缺失状态'
'SPCU Net.Conf.Miss State':
  chinese: 'SPCU网络配置缺失状态'
'SPCU Network Configuration Inconsistent State':
  chinese: 'SPCU网络配置不一致状态'
'SPCU Net.Conf.Inconsist.State':
  chinese: 'SPCU网络配置不一致状态'
'No Upgrade':
  chinese: '未执行升级'
'Upgrading':
  chinese: '升级中'
'Upgrade Finish':
  chinese: '升级完成'
'SPU Upgrade Task Status':
  chinese: 'SPU升级任务状态'
'SPU Upgrade Task St.':
  chinese: 'SPU升级任务状态'
'SPU OTP Alarm Status':
  chinese: 'SPU过温告警状态'
'SPU OTP Status':
  chinese: 'SPU过温状态'
'SPU Radiator OTP Alarm Status':
  chinese: 'SPU散热器过温告警状态'
'SPU Ra.OTP.Status':
  chinese: 'SPU散热器过温状态'
'SPU Input None Status':
  chinese: 'SPU无输入告警状态'
'SPU Input None Alarm Stu.':
  chinese: 'SPU无输入状态'
'SPU Output Shortcut Alarm Status':
  chinese: 'SPU输出短路告警状态'
'SPU Shortcut Status':
  chinese: 'SPU输出短路状态'
'SPCU OTP Alarm Status':
  chinese: 'SPCU过温告警状态'
'SPCU OTP Status':
  chinese: 'SPCU过温状态'
'No':
  chinese: '否'
'Yes':
  chinese: '是'
'SPU MPPT Scan Status':
  chinese: 'SPU MPPT扫描状态'
'SPU MPPT Scan Stu.':
  chinese: 'SPU MPPT扫描状态'
'SPU Input Current Limit Status':
  chinese: 'SPU输入限流状态'
'SPU Input CL Stu.':
  chinese: 'SPU输入限流状态'
'SPU Input Current Over Alarm Status':
  chinese: 'SPU输入过流告警状态'
'SPU Input OC Stu.':
  chinese: 'SPU输入过流状态'
'SPCU Network Configuration Miss':
  chinese: 'SPCU网络配置缺失'
'SPCU Net.Conf.Miss':
  chinese: 'SPCU网络配置缺失'
'SPU OTP Alarm':
  chinese: 'SPU过温告警'
'SPU OTP Alm.':
  chinese: 'SPU过温告警'
'SPU Radiator OTP Alarm':
  chinese: 'SPU散热器过温告警'
'SPU Ra.OTP.':
  chinese: 'SPU散热器过温'
'SPU Input None Alarm':
  chinese: 'SPU无输入告警'
'SPU Input None':
  chinese: 'SPU无输入'
'SPU Output Shortcut Alarm':
  chinese: 'SPU输出短路告警'
'SPU Shortcut Alm.':
  chinese: 'SPU输出短路'
'SPCU OTP Alarm':
  chinese: 'SPCU过温告警'
'SPCU OTP Alm.':
  chinese: 'SPCU过温告警'
'SPCU Network Configuration Inconsistent':
  chinese: 'SPCU网络配置不一致'
'SPCU Net.Conf.Inconsist':
  chinese: 'SPCU网络配置不一致'
'SPU Input UVP Alarm':
  chinese: 'SPU输入欠压告警'
'SPU Input UVP Alm.':
  chinese: 'SPU输入欠压告警'
'SPU Input Over Current Alarm':
  chinese: 'SPU输入过流告警'
'SPU Input OC Alm.':
  chinese: 'SPU输入过流告警'
'SPU Short Address':
  chinese: 'SPU短地址'
'SPU Short Addr.':
  chinese: 'SPU短地址'
'SPU Serial Number Configuration':
  chinese: 'SPU序列号配置'
'SPU SN Config':
  chinese: 'SPU序列号配置'
'SPCU Serial Number Configuration':
  chinese: 'SPCU序列号配置'
'SPCU SN Config':
  chinese: 'SPCU序列号配置'
'SPCU Get Network Configuration':
  chinese: 'SPCU获取网络配置'
'SPCU Get Net.Conf.':
  chinese: 'SPCU获取网络配置'
'SPCU Set Network Configuration':
  chinese: 'SPCU设置网络配置'
'SPCU Set Net.Conf.':
  chinese: 'SPCU设置网络配置'
'SPCU Enter Manufacture Mode':
  chinese: 'SPCU进入生产模式'
'SPCU Enter Manufy.':
  chinese: 'SPCU进入生产模式'
'SPCU Quit Manufacture Mode':
  chinese: 'SPCU退出生产模式'
'SPCU Quit Manufy.':
  chinese: 'SPCU退出生产模式'
'SPU No Networked SN':
  chinese: '待组网的SPU序列号'
'DC Input Voltage':
  chinese: '直流输入电压'
'DC Input Volt.':
  chinese: '直流输入电压'
'DC Input Current':
  chinese: '直流输入电流'
'DC Input Curr.':
  chinese: '直流输入电流'
'DC Input Power':
  chinese: '直流输入功率'
'Cabinet Return Air Temperature':
  chinese: '柜内回风温度'
'Cabinet Return Air Temp.':
  chinese: '柜内回风温度'
'Cabinet Outside Temperature':
  chinese: '柜外温度'
'Cabinet Outside Temp.':
  chinese: '柜外温度'
'Device Running Time':
  chinese: '设备运行时间'
'Compressor Running Time':
  chinese: '压缩机运行时间'
'Comp.Running Time':
  chinese: '压缩机运行时间'
'Indoor Fan Running Time':
  chinese: '内风机运行时间'
'Standby':
  chinese: '待机'
'Working':
  chinese: '运行'
'fault':
  chinese: '故障'
'Device Running Status':
  chinese: '设备运行状态'
'Device Running Sta.':
  chinese: '设备运行状态'
'Compressor Status':
  chinese: '压缩机状态'
'Comp.Sta.':
  chinese: '压缩机状态'
'Indoor Fan Status':
  chinese: '内风机状态'
'Indoor Fan Sta.':
  chinese: '内风机状态'
'Outdoor Fan Status':
  chinese: '外风机状态'
'Outdoor Fan Sta.':
  chinese: '外风机状态'
'Null':
  chinese: '无'
'Alarm':
  chinese: '告警'
'CommFail':
  chinese: '通讯断'
'Update':
  chinese: '升级'
'DC Air Conditioner Work Status':
  chinese: '直流空调工作状态'
'DC AirCon Work Sta.':
  chinese: '直流空调工作状态'
'DC Air Conditioner Exist Status':
  chinese: '直流空调在位状态'
'DC AirCon Exist Sta.':
  chinese: '直流空调在位状态'
'DC Air Conditioner Communication Status':
  chinese: '直流空调通讯状态'
'DC AirCon Comm.Sta.':
  chinese: '直流空调通讯状态'
'Abnormal':
  chinese: '异常'
'DC Air Conditioner High Temperature Alarm Status':
  chinese: '直流空调高温告警状态'
'High Temp.Alm.Sta.':
  chinese: '高温告警状态'
'DC Air Conditioner Low Temperature Alarm Status':
  chinese: '直流空调低温告警状态'
'Low Temp.Alm.Sta.':
  chinese: '低温告警状态'
'DC Air Conditioner High Voltage Alarm Status':
  chinese: '直流空调过压告警状态'
'High Volt.Alm.Sta.':
  chinese: '过压告警状态'
'DC Air Conditioner Low Voltage Alarm Status':
  chinese: '直流空调欠压告警状态'
'Low Volt.Alm.Sta.':
  chinese: '欠压告警状态'
'Compressor Fault Status':
  chinese: '压缩机故障状态'
'Comp.Fault Sta.':
  chinese: '压缩机故障状态'
'Indoor Fan Fault Status':
  chinese: '内风机故障状态'
'Indoor Fan Fault Sta.':
  chinese: '内风机故障状态'
'Outdoor Fan Fault Status':
  chinese: '外风机故障状态'
'Outdoor Fan Fault Sta.':
  chinese: '外风机故障状态'
'System High Pressure Alarm Status':
  chinese: '系统高气压告警状态'
'High Pressure Alm.Sta.':
  chinese: '高气压告警状态'
'Evaporator Freezing Alarm Status':
  chinese: '蒸发器冻结告警状态'
'Eva.Freezing Alm.Sta.':
  chinese: '蒸发器冻结告警状态'
'Evaporator Temperature Sensor Fault Status':
  chinese: '蒸发器温度传感器故障状态'
'Eva.Sensor Fault Sta.':
  chinese: '蒸发器故障状态'
'Condenser Temperature Sensor Fault Status':
  chinese: '冷凝器温度传感器故障状态'
'Cond.Sensor Fault Sta.':
  chinese: '冷凝器故障状态'
'Ambient Temperature Sensor Fault Status':
  chinese: '环境温度传感器故障状态'
'Ambi.Temp.Sensor Fault Sta.':
  chinese: '环境温度传感器故障状态'
'Frequent High Pressure Alarm Status':
  chinese: '频繁高气压告警状态'
'Freq.High Pressure Alm.Sta.':
  chinese: '频繁高气压告警状态'
'DC Air Conditioner Communication Failure':
  chinese: '直流空调通讯断'
'DC AirCon Comm.Fail.':
  chinese: '直流空调通讯断'
'DC Air Conditioner High Temperature Alarm':
  chinese: '直流空调高温告警'
'High Temp.Alm':
  chinese: '高温告警'
'DC Air Conditioner Low Temperature Alarm':
  chinese: '直流空调低温告警'
'Low Temp.Alm':
  chinese: '低温告警'
'DC Air Conditioner High Voltage Alarm':
  chinese: '直流空调过压告警'
'High Volt.Alm':
  chinese: '过压告警'
'DC Air Conditioner Low Voltage Alarm':
  chinese: '直流空调欠压告警'
'Low Volt.Alm':
  chinese: '欠压告警'
'Compressor Fault':
  chinese: '压缩机故障'
'Comp.Fault':
  chinese: '压缩机故障'
'Indoor Fan Fault':
  chinese: '内风机故障'
'Outdoor Fan Fault':
  chinese: '外风机故障'
'System High Pressure Alarm':
  chinese: '系统高气压告警'
'High Pressure Alm':
  chinese: '高气压告警'
'Temperature Sensor Fault':
  chinese: '温度传感器故障'
'Temp.Sensor Fault':
  chinese: '温度传感器故障'
'Evaporator Freezing Alarm':
  chinese: '蒸发器冻结告警'
'Eva.Freezing Alm':
  chinese: '蒸发器冻结告警'
'DC Air Conditioner Reset':
  chinese: '直流空调复位'
'DC AirCon Reset':
  chinese: '直流空调复位'
'Open DC AirCon':
  chinese: '直流空调开启'
'Close DC AirCon':
  chinese: '直流空调关闭'
'DC Air Conditioner Cooling Open':
  chinese: '空调制冷开启'
'DC Air Conditioner Cooling Close':
  chinese: '空调制冷关闭'
'DC Air Conditioner Open Temperature':
  chinese: '直流空调开启温度'
'Open Temp.':
  chinese: '开启温度'
'DC Air Conditioner Close Temperature':
  chinese: '直流空调关闭温度'
'Close Temp.':
  chinese: '关闭温度'
'DC Air Conditioner High Temperature Alarm Threshold':
  chinese: '直流空调高温告警阈值'
'DC AirCon HTA':
  chinese: '直流空调高温告警'
'DC Air Conditioner Low Temperature Alarm Threshold':
  chinese: '直流空调低温告警阈值'
'DC AirCon LTA':
  chinese: '直流空调低温告警'
'DC Air Conditioner High Voltage Alarm Threshold':
  chinese: '直流空调过压告警阈值'
'DC AirCon HVA':
  chinese: '直流空调过压告警'
'DC Air Conditioner Low Voltage Alarm Threshold':
  chinese: '直流空调欠压告警阈值'
'DC AirCon LVA':
  chinese: '直流空调欠压告警'
'DC Air Conditioner Type':
  chinese: '直流空调型号'
'DC AirCon Type':
  chinese: '直流空调型号'
'DC Air Conditioner Software Version':
  chinese: '直流空调软件版本'
'DC AirCon Soft.Ver.':
  chinese: '直流空调软件版本'
'DCEM Loop 1 Voltage':
  chinese: '直流电表回路1电压'
'DCEM Loop 1 Volt.':
  chinese: '直流电表回路1电压'
'DCEM Loop 1 Current':
  chinese: '直流电表回路1电流'
'DCEM Loop 1 Curr.':
  chinese: '直流电表回路1电流'
'DCEM Loop 1 Positive Energy':
  chinese: '直流电表回路1正向有功电量'
'DCEM Loop 1 Pos. Eng.':
  chinese: '直流电表回路1正向有功电量'
'DCEM Loop 1 Reverse Energy':
  chinese: '直流电表回路1反向有功电量'
'DCEM Loop 1 Rev. Eng.':
  chinese: '直流电表回路1反向有功电量'
'DCEM Loop 2 Voltage':
  chinese: '直流电表回路2电压'
'DCEM Loop 2 Volt.':
  chinese: '直流电表回路2电压'
'DCEM Loop 2 Current':
  chinese: '直流电表回路2电流'
'DCEM Loop 2 Curr.':
  chinese: '直流电表回路2电流'
'DCEM Loop 2 Positive Energy':
  chinese: '直流电表回路2正向有功电量'
'DCEM Loop 2 Pos. Eng.':
  chinese: '直流电表回路2正向有功电量'
'DCEM Loop 2 Reverse Energy':
  chinese: '直流电表回路2反向有功电量'
'DCEM Loop 2 Rev. Eng.':
  chinese: '直流电表回路2反向有功电量'
'DCEM Loop 3 Voltage':
  chinese: '直流电表回路3电压'
'DCEM Loop 3 Volt.':
  chinese: '直流电表回路3电压'
'DCEM Loop 3 Current':
  chinese: '直流电表回路3电流'
'DCEM Loop 3 Curr.':
  chinese: '直流电表回路3电流'
'DCEM Loop 3 Positive Energy':
  chinese: '直流电表回路3正向有功电量'
'DCEM Loop 3 Pos. Eng.':
  chinese: '直流电表回路3正向有功电量'
'DCEM Loop 3 Reverse Energy':
  chinese: '直流电表回路3反向有功电量'
'DCEM Loop 3 Rev. Eng.':
  chinese: '直流电表回路3反向有功电量'
'DCEM Loop 4 Voltage':
  chinese: '直流电表回路4电压'
'DCEM Loop 4 Volt.':
  chinese: '直流电表回路4电压'
'DCEM Loop 4 Current':
  chinese: '直流电表回路4电流'
'DCEM Loop 4 Curr.':
  chinese: '直流电表回路4电流'
'DCEM Loop 4 Positive Energy':
  chinese: '直流电表回路4正向有功电量'
'DCEM Loop 4 Pos. Eng.':
  chinese: '直流电表回路4正向有功电量'
'DCEM Loop 4 Reverse Energy':
  chinese: '直流电表回路4反向有功电量'
'DCEM Loop 4 Rev. Eng.':
  chinese: '直流电表回路4反向有功电量'
'DCEM Loop 1 Active Power':
  chinese: '直流电表回路1有功功率'
'DCEM Loop 1 Act. Pow.':
  chinese: '直流电表回路1有功功率'
'DCEM Loop 2 Active Power':
  chinese: '直流电表回路2有功功率'
'DCEM Loop 2 Act. Pow.':
  chinese: '直流电表回路2有功功率'
'DCEM Loop 3 Active Power':
  chinese: '直流电表回路3有功功率'
'DCEM Loop 3 Act. Pow.':
  chinese: '直流电表回路3有功功率'
'DCEM Loop 4 Active Power':
  chinese: '直流电表回路4有功功率'
'DCEM Loop 4 Act. Pow.':
  chinese: '直流电表回路4有功功率'
'DCEM Communication State':
  chinese: '直流电表通讯状态'
'DCEM Comm.Sta.':
  chinese: '直流电表通讯状态'
'DCEM Exist State':
  chinese: '直流电表在位状态'
'DCEM Work State':
  chinese: '直流电表工作状态'
'DCEM Communication Fail':
  chinese: '直流电表通讯断告警'
'DCEM Comm.Fail':
  chinese: '直流电表通讯断'
'DCEM Energy Reset':
  chinese: '直流电表电量清零'
'DCEM Eng.Reset':
  chinese: '直流电表电量清零'
'DC Load LLVD 1':
  chinese: '直流负载LLVD1'
'DC Load LLVD 2':
  chinese: '直流负载LLVD2'
'DC Common Load':
  chinese: '直流公共负载'
'DC DU 1 Load':
  chinese: '直流配电单元1负载'
'DC DU 2 Load':
  chinese: '直流配电单元2负载'
'DC DU 3 Load':
  chinese: '直流配电单元3负载'
'DC DU 4 Load':
  chinese: '直流配电单元4负载'
'DC Aircd 1':
  chinese: '直流空调1'
'DC Aircd 2':
  chinese: '直流空调2'
'DC Aircd 3':
  chinese: '直流空调3'
'DC Aircd 4':
  chinese: '直流空调4'
'Batt. 1':
  chinese: '电池1'
'Batt. 2':
  chinese: '电池2'
'Batt. 3':
  chinese: '电池3'
'Batt. 4':
  chinese: '电池4'
'Batt. 5':
  chinese: '电池5'
'Batt. 6':
  chinese: '电池6'
'Third Party Batt. 1':
  chinese: '第三方电池1'
'Third Party Batt. 2':
  chinese: '第三方电池2'
'Third Party Batt. 3':
  chinese: '第三方电池3'
'Third Party Batt. 4':
  chinese: '第三方电池4'
'Third Party Batt. 5':
  chinese: '第三方电池5'
'Third Party Batt. 6':
  chinese: '第三方电池6'
'DC Reserve Input':
  chinese: '直流备用输入'
'Other':
  chinese: '其他'
'DCEM Loop 1 Config':
  chinese: '直流电表回路1配置'
'DCEM Loop 2 Config':
  chinese: '直流电表回路2配置'
'DCEM Loop 3 Config':
  chinese: '直流电表回路3配置'
'DCEM Loop 4 Config':
  chinese: '直流电表回路4配置'
'DCEM Loop 1 Config Name':
  chinese: '直流电表回路1配置名称'
'DCEM Loop 2 Config Name':
  chinese: '直流电表回路2配置名称'
'DCEM Loop 3 Config Name':
  chinese: '直流电表回路3配置名称'
'DCEM Loop 4 Config Name':
  chinese: '直流电表回路4配置名称'
'DCEM Loop 1 Active Energy':
  chinese: '直流电表回路1有功电量'
'DCEM Loop 1 Act.Eng.':
  chinese: '直流电表回路1有功电量'
'DCEM Loop 2 Active Energy':
  chinese: '直流电表回路2有功电量'
'DCEM Loop 2 Act.Eng.':
  chinese: '直流电表回路2有功电量'
'DCEM Loop 3 Active Energy':
  chinese: '直流电表回路3有功电量'
'DCEM Loop 3 Act.Eng.':
  chinese: '直流电表回路3有功电量'
'DCEM Loop 4 Active Energy':
  chinese: '直流电表回路4有功电量'
'DCEM Loop 4 Act.Eng.':
  chinese: '直流电表回路4有功电量'
'DCEM System Name':
  chinese: '直流电表系统名称'
'DCEM Sys.Name':
  chinese: '直流电表系统名称'
'DCEM Software Version':
  chinese: '直流电表软件版本'
'DCEM Soft.Ver.':
  chinese: '直流电表软件版本'
'DCEM Software Release Date':
  chinese: '直流电表软件发布日期'
'DCEM Soft.Date':
  chinese: '直流电表软件日期'
'DCEM System Address':
  chinese: '直流电表系统地址'
'DCEM Sys.Addr.':
  chinese: '直流电表系统地址'
'DC In.Voltage':
  chinese: '直流输入电压'
'DC In.Current':
  chinese: '直流输入电流'
'DC Input Power Failure':
  chinese: '直流输入停电'
'DC In.Power fail.':
  chinese: '直流输入停电'
'DC Input Voltage High':
  chinese: '直流输入电压高'
'DC In.Volt.High':
  chinese: '直流输入电压高'
'DC Input Voltage Low':
  chinese: '直流输入电压低'
'DC In.Volt.Low':
  chinese: '直流输入电压低'
'DC Input Voltage High Threshold':
  chinese: '直流输入电压高阈值'
'DC In.Volt.High Thre.':
  chinese: '直流输入电压高阈值'
'DC Input Voltage Low Threshold':
  chinese: '直流输入电压低阈值'
'DC In.Volt.Low Thre.':
  chinese: '直流输入电压低阈值'
'DC Input Voltage High Backlash':
  chinese: '直流输入电压高回差'
'DC In.Volt.High Back.':
  chinese: '直流输入电压高回差'
'DC Input Voltage Low Backlash':
  chinese: '直流输入电压低回差'
'DC In.Volt.Low Back.':
  chinese: '直流输入电压低回差'
'DC Input SPD Status':
  chinese: '直流输入防雷器状态'
'DC In.SPD':
  chinese: '直流输入防雷器状态'
'DC Input SPD Abnormal':
  chinese: '直流输入防雷器异常'
'DC In.SPD Abr.':
  chinese: '直流输入防雷器异常'
'DC Input Voltage Slope':
  chinese: '直流输入电压斜率'
'DC In.Volt.Slope':
  chinese: '直流输入电压斜率'
'DC Input Voltage Offset':
  chinese: '直流输入电压零点'
'DC In.Volt.Offset':
  chinese: '直流输入电压零点'
'DC Input Current Slope':
  chinese: '直流输入电流斜率'
'DC In.Curr.Slope':
  chinese: '直流输入电流斜率'
'DC Input Current Offset':
  chinese: '直流输入电流零点'
'DC In.Curr.Offset':
  chinese: '直流输入电流零点'
'NFB16 Battery':
  chinese: 'NFB16电池'
'NFB15 Battery':
  chinese: 'NFB15电池'
'Close':
  chinese: '闭合'
'Break':
  chinese: '断开'
'Input Relay Physical State':
  chinese: '输入干接点物理状态'
'Input Rly.Phy.State':
  chinese: '输入干接点物理状态'
'0°':
  chinese: '0°'
'90°':
  chinese: '90°'
'180°':
  chinese: '180°'
'270°':
  chinese: '270°'
'Gui Rotate Angle':
  chinese: 'GUI翻转角度'
'Third Party Battery Current':
  chinese: '第三方电池电流'
'Third Party Batt.Curr.':
  chinese: '第三方电池电流'
'Normal':
  chinese: '正常'
'Fault':
  chinese: '异常'
'PLC Custom Alarm Status':
  chinese: 'PLC自定义告警状态'
'PLC.Cuz.Alm.Stu':
  chinese: 'PLC自定义告警状态'
'Third Party Battery Setting Current Limit':
  chinese: '第三方电池设定限流点'
'Third Party Batt.Curr.Limit':
  chinese: '第三方电池限流点'
'Third Party Battery Current High Rate Maximum':
  chinese: '第三方电池过流比率上限'
'Third Party Batt.Curr.High.Rate Max.':
  chinese: '第三方电池过流比率上限'
'Third Party Battery Current High Rate Minimum':
  chinese: '第三方电池过流比率下限'
'Third Party Batt.Curr.High.Rate Min.':
  chinese: '第三方电池过流比率下限'
'System AC Frequency':
  chinese: '系统交流频率'
'Mask':
  chinese: '屏蔽'
'Critical':
  chinese: '严重'
'Major':
  chinese: '主要'
'Minor':
  chinese: '次要'
'Warning':
  chinese: '警告'
'AC Input Frequency Low':
  chinese: '交流输入频率低'
'AC In.Freq.Low':
  chinese: '交流输入频率低'
'AC Input Frequency High':
  chinese: '交流输入频率高'
'AC In.Freq.High':
  chinese: '交流输入频率高'
'AC Input Frequency Low Threshold':
  chinese: '交流输入频率低阈值'
'AC In.Freq.L.Thre':
  chinese: '交流输入频率低阈值'
'AC Input Frequency High Threshold':
  chinese: '交流输入频率高阈值'
'AC In.Freq.H.Thre':
  chinese: '交流输入频率高阈值'
'SMR Rated Capacity':
  chinese: '整流器额定容量'
'Rated Capacity':
  chinese: '额定容量'
'SMR Normal Number':
  chinese: '正常整流器数量'
'SMR Normal Num':
  chinese: '正常整流器数量'
'SMR Total Rated Capacity':
  chinese: '整流模块总额定容量'
'SMR Total Rated Cap.':
  chinese: '总额定容量'
'SMR Load Rate':
  chinese: '整流器带载率'
'Rectifier System Conversion Efficiency':
  chinese: '整流系统转换效率'
'Rectifier Sys.Conv.Eff.':
  chinese: '整流系统转换效率'
'SMR Input Voltage Low Protection Threshold':
  chinese: '整流器输入欠压保护阈值'
'In.Volt.L.Pro.Thre.':
  chinese: '输入欠压保护阈值'
'SMR Input Voltage Over Protection Threshold':
  chinese: '整流器输入过压保护阈值'
'In.Volt.O.Pro.Thre.':
  chinese: '输入过压保护阈值'
'SMR Output Voltage Low Alarm Threshold':
  chinese: '整流器输出欠压告警阈值'
'Out.Volt.L.Alm.Thre.':
  chinese: '输出欠压告警阈值'
'SMR Output Voltage Over Alarm Threshold':
  chinese: '整流器输出过压告警阈值'
'Out.Volt.O.Alm.Thre.':
  chinese: '输出过压告警阈值'
'Load Threshold For Conversion Efficiency Display':
  chinese: '转换效率呈现带载阈值'
'Load Thres.for Eff.Disp.':
  chinese: '效率呈现带载阈值'
'Fan Speed':
  chinese: '风扇转速'
'Fan PWM':
  chinese: '风扇PWM'
'ACEM Loop1 Current Slope':
  chinese: '交流电表回路1电流斜率'
'ACEM Loop1 Curr.Slope':
  chinese: '交流电表回路1电流斜率'
'ACEM Loop1 Current Offset':
  chinese: '交流电表回路1电流零点'
'ACEM Loop1 Curr.Offset':
  chinese: '交流电表回路1电流零点'
'ACEM Loop1 Voltage Slope':
  chinese: '交流电表回路1电压斜率'
'ACEM Loop1 Volt.Slope':
  chinese: '交流电表回路1电压斜率'
'ACEM Loop1 Voltage Offset':
  chinese: '交流电表回路1电压零点'
'ACEM Loop1 Volt.Offset':
  chinese: '交流电表回路1电压零点'
'ACEM Loop2 Current Slope':
  chinese: '交流电表回路2电流斜率'
'ACEM Loop2 Curr.Slope':
  chinese: '交流电表回路2电流斜率'
'ACEM Loop2 Current Offset':
  chinese: '交流电表回路2电流零点'
'ACEM Loop2 Curr.Offset':
  chinese: '交流电表回路2电流零点'
'ACEM Loop2 Voltage Slope':
  chinese: '交流电表回路2电压斜率'
'ACEM Loop2 Volt.Slope':
  chinese: '交流电表回路2电压斜率'
'ACEM Loop2 Voltage Offset':
  chinese: '交流电表回路2电压零点'
'ACEM Loop2 Volt.Offset':
  chinese: '交流电表回路2电压零点'
'ACEM Loop3 Current Slope':
  chinese: '交流电表回路3电流斜率'
'ACEM Loop3 Curr.Slope':
  chinese: '交流电表回路3电流斜率'
'ACEM Loop3 Current Offset':
  chinese: '交流电表回路3电流零点'
'ACEM Loop3 Curr.Offset':
  chinese: '交流电表回路3电流零点'
'ACEM Loop3 Voltage Slope':
  chinese: '交流电表回路3电压斜率'
'ACEM Loop3 Volt.Slope':
  chinese: '交流电表回路3电压斜率'
'ACEM Loop3 Voltage Offset':
  chinese: '交流电表回路3电压零点'
'ACEM Loop3 Volt.Offset':
  chinese: '交流电表回路3电压零点'
'ACEM Loop4 Current Slope':
  chinese: '交流电表回路4电流斜率'
'ACEM Loop4 Curr.Slope':
  chinese: '交流电表回路4电流斜率'
'ACEM Loop4 Current Offset':
  chinese: '交流电表回路4电流零点'
'ACEM Loop4 Curr.Offset':
  chinese: '交流电表回路4电流零点'
'ACEM Loop4 Voltage Slope':
  chinese: '交流电表回路4电压斜率'
'ACEM Loop4 Volt.Slope':
  chinese: '交流电表回路4电压斜率'
'ACEM Loop4 Voltage Offset':
  chinese: '交流电表回路4电压零点'
'ACEM Loop4 Volt.Offset':
  chinese: '交流电表回路4电压零点'
'SPU Communicate Packet Loss Rate':
  chinese: 'SPU通讯丢包率'
'SPU Comm. Pack. Loss Rate':
  chinese: 'SPU通讯丢包率'
'SPU Communicate Bit Error Rate':
  chinese: 'SPU通讯误码率'
'SPU Comm. Bit Err. Rate':
  chinese: 'SPU通讯误码率'
'SPCU Enter PLC Communicate Quality Monitor Mode':
  chinese: 'SPCU进入PLC通信质量监测模式'
'Enter Comm. Qual. Monit.':
  chinese: '进入通信质量监测'
'SPCU Quit PLC Communicate Quality Monitor Mode':
  chinese: 'SPCU退出PLC通信质量监测模式'
'Quit Comm. Qual. Monit.':
  chinese: '退出通信质量监测'
'Charge Voltage':
  chinese: '充电电压'
'Chg.Volt':
  chinese: '充电电压'
'DC Voltage High Threshold':
  chinese: '直流电压高阈值'
'DC Volt.High Thre.':
  chinese: '直流电压高阈值'
'DC Voltage Low Threshold':
  chinese: '直流电压低阈值'
'DC Volt.Low Thre.':
  chinese: '直流电压低阈值'
'DC Voltage Too High Threshold':
  chinese: '直流电压过高阈值'
'DC V.T.High Thre.':
  chinese: '直流电压过高值'
'DC Voltage Too Low Threshold':
  chinese: '直流电压过低阈值'
'DC V.T.Low Thre.':
  chinese: '直流电压过低值'
'Test Stop Voltage':
  chinese: '测试终止电压'
'Test Stop Volt':
  chinese: '测试终止电压'
'LLVD1 Voltage':
  chinese: '负载一次下电电压'
'LLVD1 Volt.':
  chinese: '一次下电电压'
'LLVD2 Voltage':
  chinese: '负载二次下电电压'
'LLVD2 Volt.':
  chinese: '二次下电电压'
'BLVD Voltage':
  chinese: '电池下电电压'
'SMR Default Output Voltage':
  chinese: '整流器默认输出电压'
'Def Out Volt':
  chinese: '默认输出电压'
'MAINS Stop Voltage Threshold':
  chinese: '市电停止电压'
'MAINS Stop Volt.':
  chinese: '市电停止电压'
'MAINS Start Voltage Threshold':
  chinese: '市电启动电压阈值'
'Mns.St.Volt.':
  chinese: '市电启动电压'
'DG Start Voltage Threshold':
  chinese: '油机启动电压'
'DG Start Volt.':
  chinese: '油机启动电压'
'DG Stop Voltage Threshold':
  chinese: '油机停止电压'
'DG Stop Volt.':
  chinese: '油机停止电压'
'Mains and DG DC Voltage Low Threshold':
  chinese: '油电直流电压低阈值'
'Mains.DG DC Volt.Low Thre.':
  chinese: '油电直流电压低阈值'
'Mains and DG DC Voltage Too Low Threshold':
  chinese: '油电直流电压过低阈值'
'Mains.DG DC V.T.Low Thre.':
  chinese: '油电直流电压过低值'
'Battery Voltage Low Threshold':
  chinese: '电池电压低阈值'
'Batt.Volt.L.Thre.':
  chinese: '电池电压低值'
'Battery Voltage Too Low Threshold':
  chinese: '电池电压过低阈值'
'Batt.Volt.T.L.Thre.':
  chinese: '电池电压过低值'
