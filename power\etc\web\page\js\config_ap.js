var vmodel = avalon.define({
	$id:'ap_connection',
	APAttrData:[],
	apcon_value:{},		//设置的AP信息
	apcon_infos:[],		//获得的AP数据
	apcon_backup:[],	//备份的AP数据

	show_disallow_input:function(){
		return mainvalue.i18nkeyword.Disallow_input + " & ";
	}
});

var ap_pswd_minlen = 8;
var ap_pswd_maxlen = 32;

apcon_info = {
	//AP配置管理
	HEAD:function(obj) {
		var req = {data:{objectid:"plat.apconnection",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_ap_head_succ};
		request.addRequest([req]);

		//回调函数
		function get_ap_head_succ(d,r) {
			addvalue(vmodel.APAttrData,d.data,"name");
		}
	},
	GET:function(obj) {
		var req = {data:{objectid:"plat.apconnection",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:getapvalue_succ};
			request.addRequest([req]);

			//回调函数
			function getapvalue_succ(d,r) {
				if (d.result == 'ok') {
					vmodel.apcon_infos = d.data;
				} else {
					vmodel.apcon_infos = [];
				}
				vmodel.apcon_value = vmodel.apcon_infos[0];
				vmodel.apcon_backup = JSON.parse(JSON.stringify(vmodel.apcon_infos));
			}
		},
	PUT:function(paraval) {
			var self = this;
			if (paraval[0]['AP Name'].indexOf("&") != -1 || paraval[0]['AP Password'].indexOf("&") != -1) {
				apcon_info.GET();
				alert(mainvalue.i18nkeyword.Disallow_input + "&");
				return;
			}
			var req = {data:{objectid:"plat.apconnection",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)},success:setapvalue_succ};
			request.addRequest([req]);

			//回调函数
			function setapvalue_succ(d,r) {
				if(d.result ==="ok"){
					mainvalue.controlsuccess = "success";
				} else {
					mainvalue.controlsuccess = "failure";
				}
				self.GET();
			}
		}
};

apcon_info.HEAD();
apcon_info.GET();

function AP_value_set(obj) {
	if(vmodel.apcon_value['AP Name'] == "") {
		apcon_info.GET();
		alert(mainvalue.i18nkeyword.ap.ap_name_empty);
		return;
	}
	if(vmodel.apcon_value['AP Password']!="" && vmodel.apcon_value['AP Password'].length < ap_pswd_minlen) {
		apcon_info.GET();
		alert(mainvalue.i18nkeyword.ap.ap_pswd_min_length);
		return;
	}
	if(vmodel.apcon_value['AP Password'] != WEB_SPECIFY_STR && vmodel.apcon_value['AP Password'].length > ap_pswd_maxlen) {
		apcon_info.GET();
		alert(mainvalue.i18nkeyword.ap.ap_pswd_max_length);
		return;
	}
	if (vmodel.apcon_value['AP Password'] != vmodel.apcon_backup[0]['AP Password']) {
		if (vmodel.apcon_value['AP Strong Passwd Enable'] == 1 && !pswd_strong_check_commom(vmodel.apcon_value['AP Password'])) {
			apcon_info.GET();
			alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
			return;
		}
		if (vmodel.apcon_value['AP Strong Passwd Enable'] == 1 && !check_paswd_include_username_rev(vmodel.apcon_value['AP Name'], vmodel.apcon_value['AP Password'])) {
			apcon_info.GET();
			alert(mainvalue.i18nkeyword.user.sameuserandpwd);
			return;
		}
	}
	if(vmodel.apcon_value['AP Name'] == vmodel.apcon_backup[0]['AP Name'] && vmodel.apcon_value['AP Password'] == vmodel.apcon_backup[0]['AP Password'] 
	   && vmodel.apcon_value['AP Strong Passwd Enable'] == vmodel.apcon_backup[0]['AP Strong Passwd Enable']) {
		return;
	}
	apcon_info.PUT([vmodel.apcon_value]);
}