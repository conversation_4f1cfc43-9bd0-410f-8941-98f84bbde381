 @keyframes glowing {0% {box-shadow: 0 0 0 rgba(44, 154, 219, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);}50% {box-shadow: 0 0 16px rgba(44, 154, 219, 0.8), 0 1px 2px rgba(0, 0, 0, 0.2);}100% {box-shadow: 0 0 0 rgba(44, 154, 219, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);}}@keyframes glowing {0% {box-shadow: 0 0 0 rgba(44, 154, 219, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);}50% {box-shadow: 0 0 16px rgba(44, 154, 219, 0.8), 0 1px 2px rgba(0, 0, 0, 0.2);}100% {box-shadow: 0 0 0 rgba(44, 154, 219, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);}}.button {background: -moz-linear-gradient(center top , #fbfbfb, #e1e1e1) repeat scroll 0 0 rgba(0, 0, 0, 0);border: 1px solid #d4d4d4;box-shadow: 0 1px 0 rgba(255, 255, 255, 0.5) inset, 0 1px 2px rgba(0, 0, 0, 0.2);color: #666666;display: inline-block;font-family: "HelveticaNeue-Light","Helvetica Neue Light","Helvetica Neue",Helvetica,Arial,"Lucida Grande",sans-serif;font-size: 14px;font-weight: 300;height: 32px;line-height: 32px;margin: 0;padding: 0 25.6px;text-align: center;text-decoration: none;text-shadow: 0 1px 1px white;vertical-align: middle;}.button:hover {background: -moz-linear-gradient(center top , #ffffff, #dcdcdc) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button:active {background: none repeat scroll 0 0 #eeeeee;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) inset, 0 1px 0 white;color: #bbbbbb;text-shadow: 0 1px 0 rgba(255, 255, 255, 0.4);}input.button, button.button {cursor: pointer;height: 34px;width: max-content;}.button-block {display: block;width: 100%;margin: 10px 0px;}.button-other{width: 48%;margin: 10px 5px;}.button.disabled, .button.disabled:hover, .button.disabled:active, input.button:disabled, button.button:disabled {background: none repeat scroll 0 0 #eee;border: 1px solid #dddddd;box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);color: #ccc;cursor: default;text-shadow: 0 1px 1px white;}.button-wrap {background: -moz-linear-gradient(center top , #e3e3e3, #f2f2f2) repeat scroll 0 0 rgba(0, 0, 0, 0);border-radius: 200px;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) inset;display: inline-block;padding: 10px;}.button-rounded {border-radius: 3px;}.button-pill {border-radius: 50px;}.button-circle {border-radius: 240px;border-width: 4px;box-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) inset, 0 1px 2px rgba(0, 0, 0, 0.2);font-size: 18px;height: 120px;line-height: 120px;padding: 0;width: 120px;}.button-primary {background: -moz-linear-gradient(center top , #00b5e5, #008db2) repeat scroll 0 0 #00a1cb;border-color: #007998;color: white;text-shadow: 0 -1px 1px rgba(0, 40, 50, 0.35);}.button-primary:hover {background: -moz-linear-gradient(center top , #00c9fe, #008db2) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button-primary:active {background: none repeat scroll 0 0 #1495b7;color: #005065;}.button-action {background: -moz-linear-gradient(center top , #8fcf00, #6b9c00) repeat scroll 0 0 #7db500;border-color: #5a8200;color: white;text-shadow: 0 -1px 1px rgba(19, 28, 0, 0.35);}.button-action:hover {background: -moz-linear-gradient(center top , #a0e800, #6b9c00) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button-action:active {background: none repeat scroll 0 0 #76a312;color: #374f00;}.button-highlight {background: -moz-linear-gradient(center top , #fa9915, #d87e04) repeat scroll 0 0 #f18d05;border-color: #bf7004;color: white;text-shadow: 0 -1px 1px rgba(91, 53, 2, 0.35);}.button-highlight:hover {background: -moz-linear-gradient(center top , #fba42e, #d87e04) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button-highlight:active {background: none repeat scroll 0 0 #d8891e;color: #8d5303;}.button-caution {background: -moz-linear-gradient(center top , #e8543f, #d9331a) repeat scroll 0 0 #e54028;border-color: #c22d18;color: white;text-shadow: 0 -1px 1px rgba(103, 24, 13, 0.35);}.button-caution:hover {background: -moz-linear-gradient(center top , #eb6855, #d9331a) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button-caution:active {background: none repeat scroll 0 0 #cd5240;color: #952312;}.button-royal {background: -moz-linear-gradient(center top , #99389f, #752a79) repeat scroll 0 0 #87318c;border-color: #632466;color: white;text-shadow: 0 -1px 1px rgba(26, 9, 27, 0.35);}.button-royal:hover {background: -moz-linear-gradient(center top , #ab3eb2, #752a79) repeat scroll 0 0 rgba(0, 0, 0, 0);}.button-royal:active {background: none repeat scroll 0 0 #764479;color: #3e1740;}.button-flat {background: none repeat scroll 0 0 #e9e9e9;border: medium none;box-shadow: none;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat:hover {background: none repeat scroll 0 0 #fbfbfb;}.button-flat:active {background: none repeat scroll 0 0 #eeeeee;color: #bbbbbb;}.button-flat.disabled {box-shadow: none;}.button-flat-primary {background: none repeat scroll 0 0 #62a8d1;border: medium none;box-shadow: none;color: white;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat-primary:hover {background: none repeat scroll 0 0 #4f99c6;}.button-flat-primary:active {background: none repeat scroll 0 0 #1495b7;color: #00647f;}.button-flat-primary.disabled {box-shadow: none;}.button-flat-action {background: none repeat scroll 0 0 #7db500;border: medium none;box-shadow: none;color: white;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat-action:hover {background: none repeat scroll 0 0 #8fcf00;}.button-flat-action:active {background: none repeat scroll 0 0 #76a312;color: #486900;}.button-flat-action.disabled {box-shadow: none;}.button-flat-highlight {background: none repeat scroll 0 0 #f18d05;border: medium none;box-shadow: none;color: white;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat-highlight:hover {background: none repeat scroll 0 0 #fa9915;}.button-flat-highlight:active {background: none repeat scroll 0 0 #d8891e;color: #a66103;}.button-flat-highlight.disabled {box-shadow: none;}.button-flat-caution {background: none repeat scroll 0 0 #e54028;border: medium none;box-shadow: none;color: white;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat-caution:hover {background: none repeat scroll 0 0 #e8543f;}.button-flat-caution:active {background: none repeat scroll 0 0 #cd5240;color: #ac2815;}.button-flat-caution.disabled {box-shadow: none;}.button-flat-royal {background: none repeat scroll 0 0 #87318c;border: medium none;box-shadow: none;color: white;text-shadow: none;transition-duration: 0.3s;transition-property: background, color;}.button-flat-royal:hover {background: none repeat scroll 0 0 #99389f;}.button-flat-royal:active {background: none repeat scroll 0 0 #764479;color: #501d53;}.button-flat-royal.disabled {box-shadow: none;}.button-large {font-size: 19px;height: 38.4px;line-height: 38.4px;padding: 0 30.72px;}input.button-large, button.button-large {height: 40.4px;}.button-small {font-size: 12px;height: 25.6px;line-height: 25.6px;padding: 0 20.48px;}input.button-small, button.button-small {height: 27.6px;}.button-tiny {font-size: 11px;height: 22.4px;line-height: 22.4px;padding: 0 17.92px;}input.button-tiny, button.button-tiny {height: 24.4px;}.button.glow {animation-duration: 3s;animation-iteration-count: infinite;animation-name: glowing;}.button.glow:active {animation-name: none;box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) inset, 0 1px 0 white;}