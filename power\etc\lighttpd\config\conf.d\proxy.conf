#######################################################################
##
##  Proxy Module 
## --------------- 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModProxy
##
server.modules += ( "mod_proxy" )

##
## a value between 0 and 65535 to set the debug-level in the Proxy module.
## Currently only 0 and 1 are used. Use 1 to enable some debug output, 0 to
## disable it. 
##
#proxy.debug = 1

##  
## might be one of 'hash', 'round-robin' or 'fair' (default).
##  
#proxy.balance = "fair"
  
##
## Handle all jsp requests via *************
##
#proxy.server = ( ".jsp" =>
#                 ( "tomcat" =>
#                   (
#                     "host" => "*************",
#                     "port" => 80
#                   )
#                 )
#               )

##
#######################################################################
