//   当前选择的业务对象
var selectBoId = Cookies.get("pagelist");

/*setTimeout(function(){
	BusinessObject.GetVersionObject();
}, 2000);*/

var  version_vmodel = avalon.define({
	$id:'version_info',
	init_finished:0,
	is_devinfo_empty:0,
	version_infos:[],
	get_version_value:function(siddata){
		if (siddata.value == "val_invalid") {
			return mainvalue.i18nkeyword.value_invalid;
		} else {
			if (typeof(siddata.convention)=='undefined' || mainvalue.getconventionlength(siddata.convention) <=0) {
				return siddata.value + siddata.unit;
			} else {
				if (siddata.value in siddata.convention) {
					return siddata.convention[siddata.value];
				} else {
					return mainvalue.i18nkeyword.value_invalid;
				}
			}
		}
	}
});



function version_value_refresh(time) {
	// var paraval = [{"bo_id":selectBoId.toString()}];
	let boid = selectBoId.toString();
	if(selectBoId.toString().includes("-")){
		boid = boid.split("-")[1];
	}
	var paraval = [{"bo_id":boid}];
	var req_get = {data:{objectid:"devinfo",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},refresh:time,success:version_value_refresh_succ};
	request.clearRequest(req_get);
	request.addRequest([req_get]);
	
	function version_value_refresh_succ(d,r) {
		if (d.result == 'ok') {
			classsify_version(d.data);
		}else{
			version_vmodel.is_devinfo_empty = 1;
		}
	}
	
	function classsify_version(data) {
		var datanum =  data.length;
		version_vmodel.is_devinfo_empty = (datanum == 0)? 1:0;
		var array = new Array(datanum);
		for (var i in data) {
			var sigtype = calSidToDevType(BigInt(data[i].sid));
			if (sigtype.sigType == 2) {  // 数字量带有取值约定时需要额外处理
				var convention = transformToJson(data[i].convention);
				data[i].convention = convention;
			}
			array[i] = data[i];
		}
		version_vmodel.version_infos = array;
	}
}

version_value_refresh(60 * 2); // 两分钟刷新一次
