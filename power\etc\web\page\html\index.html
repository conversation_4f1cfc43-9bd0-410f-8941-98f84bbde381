<!DOCTYPE html>
<html>    
    <head>
      <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        
        <div class="main-container container-fluid" ms-controller="homepage">
             <!--#include virtual="/page/html/prompt.html" -->
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <!-- 可关闭提示 -->
                <div id="def_pswd_tip" style="display:none;margin-top:10px;" class="alert alert-block .alert-danger">
                    <button type="button" class="close" data-dismiss="alert">
                        <i class="icon-remove"></i>
                    </button>
                    <i class="icon-warning-sign  icon-animated-bell"></i>
                    {{i18nkeyword.default_pswd_tip}}
                </div>
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.site_overview}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div id="page-content">
                    <div style="margin-bottom: 30px;">
                        <div class="infobox infobox-pink" style="width: auto; margin: 0px 0px 0px 20px;border: none;">
                            <div class="infobox-icon">
                                <i class="icon-bolt"></i>
                            </div>
                            <div class="infobox-data">
                                <span class="infobox-data-number">{{battery_group_status}}</span>
                                <span class="infobox-content">{{i18nkeyword.battery_group_info.batt_manage_sta}}</span>
                            </div>
                        </div>
                        <div class="infobox infobox-blue" style="width: auto; margin: 0px 0px 0px 20px;border: none;">
                            <div class="infobox-icon">
                                <i class="icon-laptop"></i>
                            </div>
                            <div class="infobox-data">
                                <span class="infobox-data-number">{{csu_scan_info.csu_version}}</span>
                                <div class="infobox-content">{{i18nkeyword.info_scan.csu_version}}</div>
                            </div>
                        </div>
                        <div id="env_info" class="infobox infobox-green" style="width: auto; margin: 0px 0px 0px 20px;border: none;">
                            <div class="infobox-icon">
                                <i class="icon-leaf"></i>
                            </div>
                            <div class="infobox-data">
                                <span class="infobox-data-number">{{i18nkeyword.env_scan_info.temp}}:{{env_scan_info.temperature}}</span>
                                <span class="infobox-data-number">{{i18nkeyword.env_scan_info.humidity}}:{{env_scan_info.humidity}}</span>
                            </div>
                        </div>
                        <!-- <div class="infobox infobox-blue" style="margin: 0px 0px 0px 20px;border: none;">
                            <div class="infobox-icon">
                                <i class="icon-dashboard"></i>
                            </div>
                            <div class="infobox-data">
                                <span class="infobox-data-number">CPU:{{csu_scan_info.cpu_rate}}%</span>
                                <span class="infobox-data-number">{{i18nkeyword.info_scan.mem_rate}}:{{csu_scan_info.mem_rate}}%</span>
                            </div>
                        </div> -->
                    </div>
                    <div id="plot">
                        <canvas id="plot_canvas" width = "1500px" height = "550px"></canvas>
                    </div>
                    <div id="box" style="width: auto;height: auto;display: none; background:rgba(170,170,170,0.8);border:1px solid rgb(170,170,170);position: absolute;">
                        <p style="color: black;" ms-html = '@dev_info'></p>
                    </div>
                </div>
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
    </body>
    <script src="/page/js/homepage.js"></script>
</html>