// by xiaoshengxian
function HeartBeat() {
	/************************普通心跳消息处理函数******************************/
	var auth_log = 0;
	var auth_lost_alert_tag = false;
	function heart_beat_success(d,r){
		var sessionval = Cookies.get('PMSA_SESSION_ID');
        if (d.result != 'ok') {
            auth_log++;
        }
        if (auth_log > 2) {
            gotopage("login.html");
			return;
        }
		if (d.objectid === 'heart_beat' && d.result === 'ok') {
			if (d.data[0].userlevel == 0) {  //表示没有登陆，可能由于监控断电造成
				gotopage("login.html");
				return;
			}
			if(d.data[0].userlevel > 0){
				auth_log = 0;
				//  setCookieTime('PMSA_SESSION_ID',sessionval,30);
			}else{
				auth_log++;
				if (auth_log > 1){
					auth_log = 0;
					gotopage("login.html");
					if (!auth_lost_alert_tag && !logout_atction_tag) {
						auth_lost_alert_tag = true;
						alert(mainvalue.i18nkeyword.common.lost_auth + " " + mainvalue.i18nkeyword.common.login_again);
					}
				}
			}
		}
		return;
	}
	/************************升级心跳消息处理函数******************************/
	var heartcount = 0;
	var uptimemax = 10*60/2;  //两秒一次共10分钟，默认超时时间
	var is_up_ok;
	var is_tip_alert = false;
	var is_ssl_tip_alert = false;
	var wrong_time = 0;
	function checksyshandler(d, r) {
		if (fileupload_finished_tag) {
			is_up_ok = true;   //  文件上传完毕
			if (update_tip_show_tag) {
				showDataInit();
			} else {
				updateheart.stop();
				normalheart.start();
			}
		}
		heartcount = heartcount + 1;
		if (heartcount >= uptimemax * 3) {
			alert(mainvalue.i18nkeyword.operate_overtime);
			heartcount = 0;
			uptimemax = 10*60/2;
			hideDataInit();
			gotopage('login.html');
		}
		if (d.result == 'ok' && parseInt(d.data[0].userlevel) < 1) {
			update_status_get();
		}
	}
	function upheartfailed(d,r){
		is_up_ok = false;
		is_tip_alert = false;
		heartcount = heartcount + 1;
		let type_uptimemax = uptimemax;
		if (upload_type === "sysall") {
			type_uptimemax = Math.round(uptimemax * 1.5);
		}
		if (heartcount >= type_uptimemax) {
			if (upload_type === "ssl") {
				if (!is_ssl_tip_alert) {
					is_ssl_tip_alert = true;
					alert(mainvalue.i18nkeyword.system.refresh_page_tip);
				}
			} else {
				alert(mainvalue.i18nkeyword.operate_overtime);
				heartcount = 0;
				wrong_time = 0;
				uptimemax = 10*60/2;
				hideDataInit();
				gotopage('login.html');
			}
		}
	}

	function update_status_get(){
		var para = [{'type':'5'}];  //升级状态查询发送的心跳
		var Rq = {data:{objectid:"heart_beat",type:"attr_get",paraval:JSON.stringify(para)},success:update_status_success};
		request.clearRequest(Rq);
		request.addRequest([Rq]);
	}

	function update_status_success(d,r){
		if(d.result == 'ok'){
			update_status = d.data[0]['update_status'];
			if(update_status == 1){		//处于失败状态
				wrong_time = wrong_time + 1;
				if(wrong_time >= 90){		//判断90次（3分钟）即认为失败
					console.log("update_status: failure");
					if (!is_tip_alert) {
						console.log("alert update_failure");
						alert(mainvalue.i18nkeyword.update_failure);
						is_tip_alert = true;
					}
					wrong_time = 0;
					hideDataInit();
					gotopage('login.html');
				}
			}
			if(update_status == 0){		//处于成功状态
				console.log("update_status: success");
				if (!is_tip_alert) {
					console.log("alert update_success");
					alert(mainvalue.i18nkeyword.update_success);
					is_tip_alert = true;
				}
				wrong_time = 0;
				hideDataInit();
				gotopage('login.html');
			}
		}
	}

	/************************导入心跳消息处理函数******************************/
	function export_checksyshandler(d,r) {
		heartcount = heartcount + 1;
		if (heartcount >= uptimemax) {
			alert(mainvalue.i18nkeyword.operate_overtime);
			heartcount = 0;
			uptimemax = 10*60/2;
			hideDataInit();
			gotopage('login.html');
		}
		if (d.result == 'ok' && parseInt(d.data[0].userlevel) < 1) {
			export_status_get();
		}
	}

	function export_status_get(){
		var para = [{'type':'6'}];  //参数导入状态查询发送的心跳
		var Rq = {data:{objectid:"heart_beat",type:"attr_get",paraval:JSON.stringify(para)},success:export_status_success};
		request.clearRequest(Rq);
		request.addRequest([Rq]);
	}

	function export_status_success(d,r){
		if(d.result == 'ok'){
			update_status = d.data[0]['update_status'];
			if(update_status == 1){		//处于失败状态
				wrong_time = wrong_time + 1;
				if(wrong_time >= 10){		//判断10次（20s）即认为失败
					if (!is_tip_alert) {
						alert(mainvalue.i18nkeyword.system.import_failed);
						is_tip_alert = true;
					}
					wrong_time = 0;
					hideDataInit();
					gotopage('login.html');
				}
			}
			if(update_status == 0){		//处于成功状态
				if (!is_tip_alert) {
					alert(mainvalue.i18nkeyword.system.import_success);
					is_tip_alert = true;
				}
				wrong_time = 0;
				hideDataInit();
				gotopage('login.html');
			}
		}
	}

	return {
		Normal:function(){
			var para = [{'type':'1'}]; //正常情况发送的心跳
			var heartbeatRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify(para)},refresh:10,success:heart_beat_success};
			return {
				start:function() {
					request.clearRequest(heartbeatRq);
					request.addRequest([heartbeatRq]);
				},
				stop:function() {
					request.clearRequest(heartbeatRq);
				}
			};
		},
		Update:function(){
			var para = [{'type':'2'}]; //升级情况发送的心跳
			var heartbeatRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify(para)},refresh:2,success:checksyshandler,error:upheartfailed};
			return {
				start:function() {
					request.clearRequest(heartbeatRq);
					request.addRequest([heartbeatRq]);
				},
				stop:function() {
					request.clearRequest(heartbeatRq);
				}
			};
		},
		Reboot:function(){
			var para = [{'type':'3'}];
			var heartbeatRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify(para)},refresh:5,success:function(d, r){
				if (request.getDataResult(d) == "ok") {
					request.clearRequest(r);
					setTimeout(function(){gotopage("login.html");}, 10000);
				}
			}};
			return {
				start:function(){
					request.clearRequest(heartbeatRq);
					request.addRequest([heartbeatRq]);
				},
				stop:function() {
					request.clearRequest(heartbeatRq);
				}
			};
		},
		Para_config_import:function(){
			var para = [{'type':'4'}];
			var heartbeatRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify(para)},refresh:2,success:export_checksyshandler,error:upheartfailed};
			return {
				start:function(){
					request.clearRequest(heartbeatRq);
					request.addRequest([heartbeatRq]);
				},
				stop:function() {
					request.clearRequest(heartbeatRq);
				}
			};
		}
	} ; //各子类结束
}  // 心跳类结束

var heart = new HeartBeat();
var normalheart = new heart.Normal();
var updateheart = new heart.Update();
var rebooteheart = new heart.Reboot();
var importheart = new heart.Para_config_import();

normalheart.start();