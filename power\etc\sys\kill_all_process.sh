#!/bin/sh
# yang.an

# dash not support () array
BIN_DIR=/root/power/bin
for proc in `ls $BIN_DIR`; do
    pkill $proc
done
pkill lighttpd
pkill sshd

SH_DIR=/root/power/etc/sys
for sh in `ls $SH_DIR`; do
    if [ "$sh" = "kill_all_process.sh" ] ;then
        echo "exclude kill_all_process.sh"
        continue
    fi

    if [ "$sh" = "update_all.sh" ] ;then
        echo "exclude update_all.sh"
        continue
    fi
    
    if [ "${sh##*.}"  = "sh" ] ;then
        pkill $sh
        echo "pkill $sh"
    fi
done

sleep 5
function remove_sharm_memory()
{
    MODE=$1
    counter=0
    mid=`ipcs -$MODE | awk "{ print \\$2 }"`
    for m in $mid
    do
      counter=`expr $counter + 1`
      if [ $counter -gt "2" ];then
      echo $counter
      ipcrm -$MODE $m
      fi
    done
}

echo "remove shm..." && remove_sharm_memory m
echo "remove que..." && remove_sharm_memory q
echo "remove sem..." && remove_sharm_memory s

