﻿<?xml version="1.0" encoding="utf-8"?>
<config>
  <cfgobject id="plat.snmp" name="SNMP Settings" class="Site Config">
    <para name="SNMP Read Community">Wig+zp1v+V6oZx9RcxALmA==</para>
    <para name="SNMP Write Community">VJzn6chGaRy3Nn/G2PvYSw==</para>
    <para name="SNMP Notification Mode">0</para>
    <para name="SNMP V3 Notification Name">user</para>
    <para name="SNMP V3 Notification Severity Level">0</para>
    <para name="Authentication Protocol">0</para>
    <para name="Authentication Key"></para>
    <para name="Privacy Protocol">0</para>
    <para name="Privacy Key"></para>
    <para name="SNMP Enable">0</para>
    <para name="SNMP Community Strong Passwd Enable">1</para>
	<para name="SNMP trap V3 Strong Passwd Enable">1</para>
	<para name="SNMP V3 User Strong Passwd Enable">1</para>
  </cfgobject>
  <cfgobject id="plat.snmp_v3user" name="SNMP V3 User" class="Site Config">
    <row id="plat.snmp_v3user0">
      <para name="SNMP User Name">zteuser</para>
      <para name="SNMP Authentication Protocol">1</para>
      <para name="SNMP Authentication Key">vpyjOEsuOwch2brq2GjtBA==</para>
      <para name="SNMP Privacy Protocol">1</para>
      <para name="SNMP Privacy Key">VJzn6chGaRy3Nn/G2PvYSw==</para>
      <para name="SNMP Permission Scope">1</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.snmp_manager" name="SNMP Manager" class="Site Config">
    <row id="plat.snmp_manager0">
      <para name="SNMP Manager Name">Manager 1</para>
      <para name="SNMP Manager IP">127.0.0.1</para>
      <para name="SNMP Notification Port">162</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.com" name="Serial Configure Information" class="Site Config">
    <row id="plat.COM0">
      <para name="Serial Name">COM0</para>
      <para name="Serial NO.">0</para>
      <para name="Device Name">/dev/ttyS2</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
    <row id="plat.COM1">
      <para name="Serial Name">COM1</para>
      <para name="Serial NO.">1</para>
      <para name="Device Name">/dev/ttyS0</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
    <row id="plat.COM2">
      <para name="Serial Name">COM2</para>
      <para name="Serial NO.">2</para>
      <para name="Device Name">/dev/ttyS1</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
    <row id="plat.COM3">
      <para name="Serial Name">COM3</para>
      <para name="Serial NO.">3</para>
      <para name="Device Name">/dev/ttyS4</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
    <row id="plat.COM1_1">
      <para name="Serial Name">COM1</para>
      <para name="Serial NO.">1</para>
      <para name="Device Name">/dev/ttyS0</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">2</para>
    </row>
    <row id="plat.COM2_1">
      <para name="Serial Name">COM2</para>
      <para name="Serial NO.">2</para>
      <para name="Device Name">/dev/ttyS1</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">2</para>
    </row>
    <row id="plat.COM1_2">
      <para name="Serial Name">COM1</para>
      <para name="Serial NO.">1</para>
      <para name="Device Name">/dev/ttyS0</para>
      <para name="Baud Rate">7</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
     <row id="plat.COM3_1">
      <para name="Serial Name">COM3</para>
      <para name="Serial NO.">3</para>
      <para name="Device Name">/dev/ttyS4</para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">2</para>
    </row>
    <row id="plat.COM_FB100B3_UPDATE">
      <para name="Serial Name"></para>
      <para name="Serial NO."></para>
      <para name="Device Name"></para>
      <para name="Baud Rate">3</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
    <row id="plat.ACMU_FONT_LIBRARY_UPDATE">
      <para name="Serial Name"></para>
      <para name="Serial NO."></para>
      <para name="Device Name"></para>
      <para name="Baud Rate">4</para>
      <para name="Data Bits">8</para>
      <para name="Stop Bits">1</para>
      <para name="Parity Bits">0</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.can" name="CAN Configure Information" class="Factory Config">
    <row id="plat.can0">
      <para name="CAN Name">CAN0</para>
      <para name="CAN NO.">0</para>
      <para name="Device Name">can0</para>
      <para name="Baud Rate">500000</para>
      <para name="Can ID">2415925760</para>
      <para name="Can Mask">2415935360</para>
    </row>
    <row id="plat.can1">
      <para name="CAN Name">CAN1</para>
      <para name="CAN NO.">1</para>
      <para name="Device Name">can1</para>
      <para name="Baud Rate">500000</para>
      <para name="Can ID">2148073472</para>
      <para name="Can Mask">2148073472</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.ip_link" name="IP Link Configure Information" class="Site Config">
    <row id="plat.ip_link1">
       <para name="IP link name">ip_link_1104_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
       <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link2">
       <para name="IP link name">ip_link_1104_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link3">
       <para name="IP link name">ip_link_1104_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link7">
       <para name="IP link name">ip_link_1363_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link8">
       <para name="IP link name">ip_link_1363_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link9">
       <para name="IP link name">ip_link_1363_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link13">
       <para name="IP link name">ip_link_apptest_1104_data_nms</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link14">
       <para name="IP link name">ip_link_sm_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">24522</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link15">
       <para name="IP link name">ip_link_sm_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">24522</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link16">
       <para name="IP link name">ip_link_sm_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">24522</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link20">
       <para name="IP link name">ip_link_power_sm_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">25522</para>
	   <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link21">
       <para name="IP link name">ip_link_power_sm_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">25522</para>
	   <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link22">
       <para name="IP link name">ip_link_power_sm_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">25522</para>
	   <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link23">
       <para name="IP link name">ip_link_north_download</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
	   <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link24">
       <para name="IP link name">ip_link_a_inter_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link25">
       <para name="IP link name">ip_link_a_inter_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link26">
       <para name="IP link name">ip_link_a_inter_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link27">
       <para name="IP link name">ip_link_north_download_client</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
	   <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link28">
       <para name="IP link name">ip_link_gc_modbus_data_nms1</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link29">
       <para name="IP link name">ip_link_gc_modbus_data_nms2</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
    <row id="plat.ip_link30">
       <para name="IP link name">ip_link_gc_modbus_data_nms3</para>
       <para name="transmission protocol">0</para>
       <para name="communication role">0</para>
       <para name="IP address">0.0.0.0</para>
       <para name="port">0</para>
       <para name="SSH Status">0</para>
	   <para name="SSH UserName"></para>
       <para name="SSH Passwd"></para>
    </row>
  </cfgobject>
  <cfgobject id="plat.ai.factory_config" name="AI Channel Configure" class="Factory Config">
	<row id="plat.battvolt1">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_X2_VB1</para>
      <para name="Channel Type">0</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">23</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">3</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">60</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">60</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb001010010001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.battvolt2">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_X2_VB2</para>
      <para name="Channel Type">0</para>
      <para name="Channel Type NO.">1</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">24</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">3</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">60</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">60</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb002010010001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.battvolt3">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_X2_VB3</para>
      <para name="Channel Type">0</para>
      <para name="Channel Type NO.">2</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">25</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">3</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">60</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">60</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb003010010001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.battvolt4">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_X2_VB4</para>
      <para name="Channel Type">0</para>
      <para name="Channel Type NO.">3</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">26</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">3</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">60</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">60</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb004010010001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.fanspeed1">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_X14_FAN</para>
      <para name="Channel Type">5</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">11</para>
      <para name="AdPrecision">1</para>
      <para name="BaseVoltage">1</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">20000</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0x9001010040001</para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
	</row>
    <row id="plat.fanspeed2">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_X15_FAN</para>
      <para name="Channel Type">5</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">12</para>
      <para name="AdPrecision">1</para>
      <para name="BaseVoltage">1</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">20000</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0x9001010040002</para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
	</row>
  </cfgobject>
  <cfgobject id="plat.ai.site_config" name="AI Channel Configure" class="Site Config">
    <row id="plat.battmidvolt2">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_J16_DI6</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">21</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">29</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3.000000</para>
      <para name="Maximum Sample Value">3.000000</para>
      <para name="Mininum Sample Value">0.000000</para>
      <para name="Maximum Sensor Data">60.000000</para>
      <para name="Mininum Sensor Data">0.000000</para>
      <para name="Maximum Analog Data">60.000000</para>
      <para name="Mininum Analog Data">0.000000</para>
      <para name="Offset">0.000000</para>
      <para name="Slope">1.000000</para>
      <para name="Default Status">1</para>
      <para name="SID">0x1001020020006</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020006</para>
    </row>
    <row id="plat.battmidvolt3">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_J16_DI7</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">22</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">30</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3.000000</para>
      <para name="Maximum Sample Value">3.000000</para>
      <para name="Mininum Sample Value">0.000000</para>
      <para name="Maximum Sensor Data">60.000000</para>
      <para name="Mininum Sensor Data">0.000000</para>
      <para name="Maximum Analog Data">60.000000</para>
      <para name="Mininum Analog Data">0.000000</para>
      <para name="Offset">0.000000</para>
      <para name="Slope">1.000000</para>
      <para name="Default Status">1</para>
      <para name="SID">0x1001020020007</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020007</para>
    </row>
    <row id="plat.battmidvolt4">
      <para name="Board ID">IDDB</para>
      <para name="Board Type">1</para>
      <para name="Channel Name">SPB_J16_DI8</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">23</para>
      <para name="Major Channel NO.">4</para>
      <para name="Minor Channel NO.">31</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3.000000</para>
      <para name="Maximum Sample Value">3.000000</para>
      <para name="Mininum Sample Value">0.000000</para>
      <para name="Maximum Sensor Data">60.000000</para>
      <para name="Mininum Sensor Data">0.000000</para>
      <para name="Maximum Analog Data">60.000000</para>
      <para name="Mininum Analog Data">0.000000</para>
      <para name="Offset">0.000000</para>
      <para name="Slope">1.000000</para>
      <para name="Default Status">1</para>
      <para name="SID">0x1001020020008</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board2</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020008</para>
    </row>
    <row id="plat.humity">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J15_HUM</para>
      <para name="Channel Type">4</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">10</para>
      <para name="AdPrecision">1</para>
      <para name="BaseVoltage">1</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">100</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0x9001010020001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
	</row>
	<row id="plat.referencevolt">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">reference volt 5V</para>
      <para name="Channel Type">0</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">0</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">0</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID"></para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
	</row>
	<row id="plat.batt1temp">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J4_T1</para>
      <para name="Channel Type">3</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">1</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">100</para>
      <para name="Mininum Analog Data">-40</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb001010040001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.batt2temp">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J4_T2</para>
      <para name="Channel Type">3</para>
      <para name="Channel Type NO.">1</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">2</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">100</para>
      <para name="Mininum Analog Data">-40</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb002010040001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.batt3temp">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J13_T3</para>
      <para name="Channel Type">3</para>
      <para name="Channel Type NO.">2</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">3</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">100</para>
      <para name="Mininum Analog Data">-40</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0xb003010040001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
    <row id="plat.envtemp">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J12_T4</para>
      <para name="Channel Type">3</para>
      <para name="Channel Type NO.">3</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">4</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">100</para>
      <para name="Mininum Analog Data">-40</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0x9001010010001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0</para>
    </row>
	<row id="plat.smoke">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J3_SMK</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">0</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">5</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">0</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">0</para>
      <para name="SID">0x9001020010001</para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0</para>
    </row>
	<row id="plat.Inrelay3Status">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J2_DI3</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">3</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">6</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3.000000</para>
      <para name="Maximum Sample Value">0.000000</para>
      <para name="Mininum Sample Value">0.000000</para>
      <para name="Maximum Sensor Data">0.000000</para>
      <para name="Mininum Sensor Data">0.000000</para>
      <para name="Maximum Analog Data">0.000000</para>
      <para name="Mininum Analog Data">0.000000</para>
      <para name="Offset">0.000000</para>
      <para name="Slope">1.000000</para>
      <para name="Default Status">0</para>
      <para name="SID">0x1001020020003</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020003</para>
    </row>
    <row id="plat.Inrelay4Status">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J2_DI4</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">4</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">7</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3.000000</para>
      <para name="Maximum Sample Value">0.000000</para>
      <para name="Mininum Sample Value">0.000000</para>
      <para name="Maximum Sensor Data">0.000000</para>
      <para name="Mininum Sensor Data">0.000000</para>
      <para name="Maximum Analog Data">0.000000</para>
      <para name="Mininum Analog Data">0.000000</para>
      <para name="Offset">0.000000</para>
      <para name="Slope">1.000000</para>
      <para name="Default Status">0</para>
      <para name="SID">0x90010300d0001</para>
      <para name="DisplayMask">0</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">0</para>
      <para name="Stat SID">0x1001020020004</para>
    </row>
	<row id="plat.MagneticDoor">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J3_DOOR</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">1</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">8</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">0</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">1</para>
      <para name="SID">0x9001020030001</para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0</para>
    </row>
	<row id="plat.Water">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J14_WAT</para>
      <para name="Channel Type">2</para>
      <para name="Channel Type NO.">2</para>
      <para name="Major Channel NO.">0</para>
      <para name="Minor Channel NO.">9</para>
      <para name="AdPrecision">4095</para>
      <para name="BaseVoltage">3</para>
      <para name="Maximum Sample Value">0</para>
      <para name="Mininum Sample Value">0</para>
      <para name="Maximum Sensor Data">0</para>
      <para name="Mininum Sensor Data">0</para>
      <para name="Maximum Analog Data">0</para>
      <para name="Mininum Analog Data">0</para>
      <para name="Offset">0</para>
      <para name="Slope">1</para>
      <para name="Default Status">1</para>
      <para name="SID">0x9001020020001</para>
      <para name="DisplayMask">1</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.di.factory_config" name="DI Channel Configure" class="Factory Config"/>
  <cfgobject id="plat.di.site_config" name="DI Channel Configure" class="Site Config">
    <row id="plat.Inrelay1Status">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J2_DI1</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">0</para>
      <para name="Default Status">1</para>
      <para name="SID">0x1001020020001</para>
      <para name="Channel Alias"/>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020001</para>
    </row>
    <row id="plat.Inrelay2Status">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">SPB_J2_DI2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">1</para>
      <para name="Default Status">1</para>
      <para name="SID">0x1001020020002</para>
      <para name="Channel Alias"></para>
	  <para name="Related Device ID">plat.smart_board1</para>
	  <para name="Preset Status">1</para>
      <para name="Stat SID">0x1001020020002</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.do.factory_config" name="DO Channel Configure" class="Factory Config">
    <row id="plat.RunLed">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">Run Led</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">0</para>
      <para name="Channel NO.">0</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
	  <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.AlarmLed">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">Alarm Led</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">0</para>
      <para name="Channel NO.">1</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.Buzz">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">Buzz</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">0</para>
      <para name="Channel NO.">2</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_d1">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X8_LOAD1A</para>
      <para name="Ctrl Mode">2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">0</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.download1</para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_d2">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X8_LOAD2A</para>
      <para name="Ctrl Mode">2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">1</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.download2</para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_d3">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X8_LOAD3A</para>
      <para name="Ctrl Mode">2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">2</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.download3</para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.WakeupTurnoff">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">Wakeup Turnoff</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">0</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
	<row id="plat.relay_d4">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X8_L4A</para>
      <para name="Ctrl Mode">2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">3</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.download4</para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
	<row id="plat.relay_d5">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X8_L5A</para>
      <para name="Ctrl Mode">2</para>
      <para name="Channel Type">1</para>
      <para name="Channel NO.">4</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.download5</para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
	<row id="plat.sim_switch">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X1_SIM_SWITCH</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">9</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
	<row id="plat.sim_status">
      <para name="Board ID">CORE</para>
      <para name="Board Type">0</para>
      <para name="Channel Name">SPB_X1_SIM_STATUS</para>
      <para name="Ctrl Mode">0</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">10</para>
      <para name="SID"></para>
      <para name="Related Device ID"></para>
      <para name="Sps Cmd ID"></para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.do.site_config" name="DO Channel Configure" class="Site Config">
  <row id="plat.relay_out1">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO1</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">1</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out2">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO2</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">2</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out3">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO3</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">3</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out4">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO4</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">4</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out5">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO5</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">5</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out6">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO6</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">6</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out7">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO7</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">7</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
    <row id="plat.relay_out8">
      <para name="Board ID">UIB</para>
      <para name="Board Type">2</para>
      <para name="Channel Name">DO8</para>
      <para name="Ctrl Mode">1</para>
      <para name="Channel Type">2</para>
      <para name="Channel NO.">8</para>
      <para name="SID"></para>
      <para name="Related Device ID">plat.smart_board1</para>
      <para name="Sps Cmd ID">ctrl do output</para>
      <para name="Default Status">1</para>
      <para name="Preset Status">1</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.apconnection" name="AP Connection" class="Site Config">
    <para name="AP Name">RT2860AP</para>
    <para name="AP Password">PVfQPmLYjaZ60jiRKjpv+A==</para>
    <para name="AP Strong Passwd Enable">1</para>
  </cfgobject>
  <cfgobject id="plat.wiredconnection" name="Wired Connection" class="Site Config">
    <para name="Default Router">1</para>
    <para name="IP Allocation Mode">0</para>
    <para name="IP Address">***********</para>
    <para name="Subnet Mask">*************</para>
    <para name="Gateway">***********</para>
  </cfgobject>
  <cfgobject id="plat.wiredconnectionv6" name="Wired Connection V6" class="Site Config">
    <para name="IPv6 Address"></para>
  </cfgobject>
  <cfgobject id="plat.wirelessconnection" name="Wireless Connection" class="Site Config">
    <para name="Default Router">0</para>
    <para name="APN"></para>
    <para name="Dial Code"></para>
    <para name="User Name"></para>
    <para name="Password"></para>
	<para name="Backup APN"></para>
    <para name="Backup Dial Code"></para>
    <para name="Backup User Name"></para>
    <para name="Backup Password"></para>
    <para name="No Manager Mode"></para>
  </cfgobject>
  <cfgobject id="plat.wirelessnetdevs" name="Wireless Net Devices" class="Site Config">
    <row id="plat.wirelessnetdevs0">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0120</para>
      <para name="Modem Device"></para>
      <para name="SMS Device"></para>
      <para name="Net Name">0079</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs1">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">2000</para>
      <para name="Modem Device"></para>
      <para name="SMS Device"></para>
      <para name="Net Name">0117</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs2">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0117</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device">/dev/ttyUSB1</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs3">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0016</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device">/dev/ttyUSB1</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs4">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0079</para>
      <para name="Modem Device">/dev/ttyUSB0</para>
      <para name="SMS Device">/dev/ttyUSB2</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs5">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">ffeb</para>
      <para name="Modem Device">/dev/ttyUSB1</para>
      <para name="SMS Device">/dev/ttyUSB2</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs6">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">1300</para>
      <para name="Modem Device">/dev/ttyUSB0</para>
      <para name="SMS Device"></para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs7">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">ffed</para>
      <para name="Modem Device">/dev/ttyUSB0</para>
      <para name="SMS Device"></para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs8">
      <para name="Vendor ID">1c9e</para>
      <para name="Product ID">9603</para>
      <para name="Modem Device">/dev/ttyUSB0</para>
      <para name="SMS Device"></para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs9">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">2003</para>
      <para name="Modem Device">/dev/ttyUSB3</para>
      <para name="SMS Device"></para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs10">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0144</para>
      <para name="Modem Device">/dev/ttyUSB4</para>
      <para name="SMS Device">/dev/ttyUSB3</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs11">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">0396</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device">/dev/ttyUSB0</para>
      <para name="Net Name">usb0</para>
      <para name="Dial Cmd">1</para>
    </row>
    <row id="plat.wirelessnetdevs12">
      <para name="Vendor ID">1c9e</para>
      <para name="Product ID">9b05</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device"></para>
      <para name="Net Name">usb0</para>
      <para name="Dial Cmd">2</para>
    </row>
    <row id="plat.wirelessnetdevs13">
      <para name="Vendor ID">19d2</para>
      <para name="Product ID">1476</para>
      <para name="Modem Device">/dev/ttyUSB1</para>
      <para name="SMS Device">/dev/ttyUSB2</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs14">
      <para name="Vendor ID">2c7c</para>
      <para name="Product ID">0125</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device"></para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs15">
      <para name="Vendor ID">305a</para>
      <para name="Product ID">1406</para>
      <para name="Modem Device">/dev/ttyUSB1</para>
      <para name="SMS Device">/dev/ttyUSB2</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
    <row id="plat.wirelessnetdevs16">
      <para name="Vendor ID">328e</para>
      <para name="Product ID">1145</para>
      <para name="Modem Device">/dev/ttyUSB1</para>
      <para name="SMS Device">/dev/ttyUSB1</para>
      <para name="Net Name">usb1</para>
      <para name="Dial Cmd">4</para>
    </row>
    <row id="plat.wirelessnetdevs17">
      <para name="Vendor ID">1e0e</para>
      <para name="Product ID">9011</para>
      <para name="Modem Device">/dev/ttyUSB1</para>
      <para name="SMS Device">/dev/ttyUSB1</para>
      <para name="Net Name">usb1</para>
      <para name="Dial Cmd">3</para>
    </row>
    <row id="plat.wirelessnetdevs18">
      <para name="Vendor ID">2c7c</para>
      <para name="Product ID">0121</para>
      <para name="Modem Device">/dev/ttyUSB2</para>
      <para name="SMS Device">/dev/ttyUSB3</para>
      <para name="Net Name">ppp0</para>
      <para name="Dial Cmd">0</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.ECMstatus" name="ECM Status" class="Factory Config">
    <para name="status">0</para>
    <para name="ECM Ip"/>
  </cfgobject>
  <cfgobject id="plat.b_interface" name="B Interface Connection" class="Site Config">
	<para name="Operators">3</para>
	<para name="FSU ID"></para>
	<para name="Login user"></para>
	<para name="Login Password"></para>
	<para name="SC IP Address">***********</para>
	<para name="SC Port">8080</para>
    <para name="Inform IP Address">***********</para>
	<para name="Inform Port">8080</para>
	<para name="FSU Port">8080</para>
    <para name="POWER ID"></para>
    <para name="FBBMS ID1"></para>
    <para name="FBBMS ID2"></para>
    <para name="DOOR ID"></para>
	<para name="Humiture Sensor ID"></para>
	<para name="Smog Sensor ID"></para>
	<para name="Flood Sensor ID"></para>
	<para name="SC Heartbeat Interval">90</para>
	<para name="FTP User">Power</para>
	<para name="FTP Password"></para>
  </cfgobject>
  <cfgobject id="plat.VPNconnection" name="VPN Connection" class="Site Config">
    <para name="Default Router">0</para>
    <para name="Physical Connection Type">1</para>
    <para name="VPN Type">0</para>
    <para name="Server Ip">***********</para>
    <para name="User Name"></para>
    <para name="Password"></para>
    <para name="Pre-shared Key">3</para>
    <para name="IPSec User Certificate">user</para>
    <para name="IPSec CA Certificate">user</para>
    <para name="IPSec Server Certificate">22</para>
    <para name="MPPE">0</para>
  </cfgobject>
  <cfgobject id="plat.routesettings" name="Route Settings Configure Information" class="Site Config">
    <row id="plat.wiredroute">
      <para name="Connection ID">plat.wiredconnection</para>
      <para name="Network Dest">***********</para>
      <para name="Network Mask">*************</para>
    </row>
	<row id="plat.wiredroute1">
      <para name="Connection ID">plat.wiredconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wiredroute2">
      <para name="Connection ID">plat.wiredconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wiredroute3">
      <para name="Connection ID">plat.wiredconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wiredroute4">
      <para name="Connection ID">plat.wiredconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
    <row id="plat.wirelessroute">
      <para name="Connection ID">plat.wirelessconnection</para>
      <para name="Network Dest">***********</para>
      <para name="Network Mask">*************</para>
    </row>
	<row id="plat.wirelessroute1">
      <para name="Connection ID">plat.wirelessconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wirelessroute2">
      <para name="Connection ID">plat.wirelessconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wirelessroute3">
      <para name="Connection ID">plat.wirelessconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.wirelessroute4">
      <para name="Connection ID">plat.wirelessconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
    <row id="plat.VPNroute">
      <para name="Connection ID">plat.VPNconnection</para>
      <para name="Network Dest">***********</para>
      <para name="Network Mask">*************</para>
    </row>
	<row id="plat.VPNroute1">
      <para name="Connection ID">plat.VPNconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.VPNroute2">
      <para name="Connection ID">plat.VPNconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.VPNroute3">
      <para name="Connection ID">plat.VPNconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
	<row id="plat.VPNroute4">
      <para name="Connection ID">plat.VPNconnection</para>
      <para name="Network Dest"></para>
      <para name="Network Mask"></para>
    </row>
  </cfgobject>
  <cfgobject id="plat.wiredconnection_sp" name="Wired Connection Slave Params" class="Site Config">
    <para name="Cut Over Mode">0</para>
    <para name="North Manager IP">127.0.0.1</para>
    <para name="IP Allocation Mode">0</para>
    <para name="IP Address">***********</para>
    <para name="Subnet Mask">*************</para>
    <para name="Gateway">***********</para>
    <para name="Apply Time">22</para>
  </cfgobject>
  <cfgobject id="plat.northinterfaceset" name="Northbound Interface Settings" class="Site Config">
    <para name="Northbound Interface Type">0</para>
  </cfgobject>
  <cfgobject id="plat.netconstatus" name="Net Connection Status" class="Factory Config">
    <row id="plat.wiredconstatus">
      <para name="Connection Name">wiredCon</para>
      <para name="Connection Status">1</para>
      <para name="Interface Name">eth0</para>
      <para name="IP Address">***********</para>
      <para name="Subnet Mask">*************</para>
      <para name="Gateway">***********</para>
      <para name="IPv6 Address"></para>
      <para name="Local Link IPv6 Address"></para>
    </row>
    <row id="plat.wiredv6constatus">
      <para name="Connection Name">wiredv6Con</para>
      <para name="Connection Status">1</para>
      <para name="Interface Name">eth0</para>
      <para name="IP Address"></para>
      <para name="Subnet Mask"></para>
      <para name="Gateway"></para>
      <para name="IPv6 Address"></para>
      <para name="Local Link IPv6 Address"></para>
    </row>
    <row id="plat.wirelessconstatus">
      <para name="Connection Name">wirelessCon</para>
      <para name="Connection Status">0</para>
      <para name="Interface Name">eth0</para>
      <para name="IP Address">***********</para>
      <para name="Subnet Mask">*************</para>
      <para name="Gateway">***********</para>
      <para name="IPv6 Address"></para>
      <para name="Local Link IPv6 Address"></para>
    </row>
    <row id="plat.VPNconstatus">
      <para name="Connection Name">vpnCon</para>
      <para name="Connection Status">0</para>
      <para name="Interface Name">eth1</para>
      <para name="IP Address">***********</para>
      <para name="Subnet Mask">*************</para>
      <para name="Gateway">***********</para>
      <para name="IPv6 Address"></para>
      <para name="Local Link IPv6 Address"></para>
    </row>
  </cfgobject>
  <cfgobject id="plat.sps_listen" name="sps listen configure" class="Factory Config"/>
  <cfgobject id="plat.device" name="device configure" class="Factory Config"/>
  <cfgobject id="plat.smart_board" name="smart extend board configure" class="Factory Config">
    <row id="plat.smart_board1">
      <para name="smart board name">uib</para>
      <para name="sps device type id">pdt.uib_zte_rs485_protobuf</para>
      <para name="link type id">plat.com</para>
      <para name="link inst id">plat.COM0</para>
      <para name="comm address">2</para>
    </row>
    <row id="plat.smart_board2">
      <para name="smart board name">cpb</para>
      <para name="sps device type id">pdt.cpb_zte_rs485_protobuf</para>
      <para name="link type id">plat.com</para>
      <para name="link inst id">plat.COM0</para>
      <para name="comm address">1</para>
    </row>
  </cfgobject>
  <cfgobject id="pdt.bmus" name="bmus configure" class="Factory Config">
    <para name="sps device type id">pdt.bmu_zte_can15</para>
    <para name="link type id">plat.can</para>
    <para name="link inst id">plat.can0</para>
    <para name="bmu number">4</para>
    <para name="online bat number">2</para>
    <para name="offline bat number">2</para>
    <para name="statistic unplug bat number">2</para>
    <para name="normal unplug bat number">2</para>
  </cfgobject>
  <cfgobject id="pdt.optimizers" name="optimizers configure" class="Factory Config">
    <para name="sps device type id">pdt.optimizer_zte_can15</para>
    <para name="link type id">plat.can</para>
    <para name="link inst id">plat.can0</para>
    <para name="optimizer number">4</para>
    <para name="online bat number">2</para>
    <para name="offline bat number">2</para>
    <para name="statistic unplug bat number">2</para>
    <para name="normal unplug bat number">2</para>
  </cfgobject>
  <cfgobject id="pdt.can_sensor" name="can sensor configure" class="Factory Config">
    <para name="sps device type id">pdt.sensor_dev_can</para>
    <para name="link type id">plat.can</para>
    <para name="link inst id">plat.can1</para>
    <para name="can sensor number">1</para>
    <para name="online bat number">1</para>
    <para name="offline bat number">1</para>
    <para name="statistic unplug bat number">1</para>
    <para name="normal unplug bat number">1</para>
  </cfgobject>
  <cfgobject id="pdt.pcs" name="pcs configure" class="Factory Config">
    <para name="sps device type id">pdt.pcs_zte_can</para>
    <para name="link type id">plat.can</para>
    <para name="link inst id">plat.can1</para>
    <para name="can pcs number">1</para>
    <para name="online bat number">1</para>
    <para name="offline bat number">1</para>
    <para name="statistic unplug bat number">1</para>
    <para name="normal unplug bat number">1</para>
  </cfgobject>
  <cfgobject id="plat.local_addr" name="local link address configure" class="Site Config">
    <row id="plat.local_addr1">
      <para name="link inst id">plat.COM0</para>
	  <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr2">
      <para name="link inst id">plat.COM1</para>
	  <para name="protocol id">plat.s1104_client</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr3">
      <para name="link inst id">plat.COM2</para>
	  <para name="protocol id">plat.s1104_client</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr4">
      <para name="link inst id">plat.can0</para>
	  <para name="protocol id">plat.can15</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr5">
      <para name="link inst id">plat.ip_link</para>
	  <para name="protocol id">plat.s1104_client</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr6">
      <para name="link inst id">plat.ip_link_server</para>
	  <para name="protocol id">plat.s1104_client</para>
      <para name="comm address">1</para>
    </row>
	<row id="plat.local_addr7">
      <para name="link inst id">plat.ip_link</para>
	  <para name="protocol id">plat.download</para>
      <para name="comm address">1</para>
    </row>
	<row id="plat.local_addr8">
      <para name="link inst id">plat.COM1</para>
	  <para name="protocol id">plat.download</para>
      <para name="comm address">0</para>
    </row>
	<row id="plat.local_addr9">
      <para name="link inst id">plat.COM2</para>
	  <para name="protocol id">plat.download</para>
      <para name="comm address">0</para>
    </row>
	<row id="plat.local_addr10">
      <para name="link inst id">plat.COM0</para>
	  <para name="protocol id">plat.sm_update</para>
      <para name="comm address">0</para>
	</row>
    <row id="plat.local_addr11">
      <para name="link inst id">plat.COM1</para>
	  <para name="protocol id">plat.comm_sm_slave</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr12">
      <para name="link inst id">plat.ip_link</para>
      <para name="protocol id">plat.comm_sm_slave</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr13">
      <para name="link inst id">plat.ip_link_server</para>
      <para name="protocol id">plat.comm_sm_slave</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr14">
      <para name="link inst id">plat.COM_FB100B3_UPDATE</para>
      <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
	  <row id="plat.local_addr15">
      <para name="link inst id">plat.COM1</para>
	  <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr16">
      <para name="link inst id">plat.COM2</para>
	  <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr17">
      <para name="link inst id">plat.COM3</para>
      <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
  	<row id="plat.local_addr18">
      <para name="link inst id">plat.COM3</para>
	  <para name="protocol id">plat.s1104_client</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr19">
      <para name="link inst id">plat.COM3</para>
	  <para name="protocol id">plat.download</para>
      <para name="comm address">0</para>
    </row>
	<row id="plat.local_addr20">
      <para name="link inst id">plat.COM2</para>
	  <para name="protocol id">plat.comm_sm_slave</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr21">
      <para name="link inst id">plat.COM3</para>
	  <para name="protocol id">plat.comm_sm_slave</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr22">
      <para name="link inst id">plat.COM1</para>
	  <para name="protocol id">plat.modbus_rtu_server</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr23">
      <para name="link inst id">plat.ACMU_FONT_LIBRARY_UPDATE</para>
      <para name="protocol id">plat.rs485</para>
      <para name="comm address">1</para>
    </row>
    <row id="plat.local_addr24">
      <para name="link inst id">plat.can1</para>
	  <para name="protocol id">plat.can_sensor</para>
      <para name="comm address">1</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.local_devtype_code" name="local device type code" class="Factory Config">
    <row id="plat.local_devtype_code1">
      <para name="protocol id">plat.rs485</para>
      <para name="device type code">52</para>
    </row>
    <row id="plat.local_devtype_code2">
      <para name="protocol id">plat.can15</para>
      <para name="device type code">52</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.language_category" name="Language Category Configure Information" class="Site Config">
    <row id="plat.en_US">
      <para name="Language Name">English</para>
      <para name="Language Encode Info">en_US.UTF-8</para>
      <para name="Default Language">1</para>
    </row>
    <row id="plat.zh_CN">
      <para name="Language Name">中文简体</para>
      <para name="Language Encode Info">zh_CN.UTF-8</para>
      <para name="Default Language">0</para>
    </row>
	<row id="plat.es_ES">
      <para name="Language Name">Español</para>
      <para name="Language Encode Info">es_ES.UTF-8</para>
      <para name="Default Language">0</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.parser_fidinfo" name="parser field id infomation" class="Site Config">
    <row id="plat.parser_fidinfo1">
      <para name="field id">plat.sps.device1.table1.field1</para>
      <para name="signal id">0x7001020010001</para>
    </row>
  </cfgobject>
  <cfgobject id="plat.buzz_ctrl" name="buzz ctrl" class="Site Config">
    <para name="ctrl switch">0</para>
    <para name="ctrl period">0</para>
  </cfgobject>
  <cfgobject id="plat.system_time" name="system time" class="Site Config">
    <para name="timezone select">8</para>
    <para name="Daylight Saving Time Enable">0</para>
  </cfgobject>
  <cfgobject id="plat.acq_2dev" name="acquisition bind to device inst" class="Site Config"/>
  <cfgobject id="plat.outrelay_ctrl" name="out relay ctrl" class="Site Config">
     <para name="ctrl out relay">0</para>
  </cfgobject>
  <cfgobject id="plat.history_num" name="history num" class="Factory Config">
    <para name="history data num">50000</para>
    <para name="history alarm num">5000</para>
    <para name="history event num">5000</para>
  </cfgobject>
  <cfgobject id="plat.loginpara" name="login para" class="Factory Config">
    <para  name="Max wrong times">5</para>
  </cfgobject>
  <cfgobject id="plat.mqtt" name="MQTT Settings" class="Site Config">
	<para name="Broker IP">127.0.0.1</para>
	<para name="Broker Port">1883</para>
	<para name="MQTT Proxy Server Addressing Mode">0</para>
	<para name="MQTT Proxy Server IP">127.0.0.1</para>
	<para name="MQTT Proxy Server Port">8883</para>
	<para name="MQTT Username">PC_POWER</para>
	<para name="MQTT Password">LGbHPbVfP50QRCOI/EySjQ==</para>
	<para name="MQTT Proxy Server Domain Name" />
	<para name="MQTT DNS IP">127.0.0.1</para>
	<para name="MQTT SSL Security Enable">1</para>
	<para name="MQTT SSL Authentication Mode">0</para>
	<para name="MQTT Data Publish Period">120</para>
	<para name="MQTT Publish Theme">zabbix/pst</para>
  </cfgobject>
  <cfgobject id="pdt.sps_dev_type" name="sps device type" class="Factory Config">
     <row id="pdt.smr_zte_can15">
       <para name="sps device type id">pdt.smr_zte_can15</para>
       <para name="device type name">zte can15 smr</para>
       <para name="device type alias">smr</para>
       <para name="sps protocol id">plat.can15</para>
       <para name="location">1</para>
     </row>
     <row id="pdt.bmu_zte_can15">
       <para name="sps device type id">pdt.bmu_zte_can15</para>
       <para name="device type name">zte can15 bmu</para>
       <para name="device type alias">bmu</para>
       <para name="sps protocol id">plat.can15</para>
       <para name="location">1</para>
     </row>
	 <row id="pdt.optimizer_zte_can15">
       <para name="sps device type id">pdt.optimizer_zte_can15</para>
       <para name="device type name">zte can15 bmu</para>
       <para name="device type alias">optimizer</para>
       <para name="sps protocol id">plat.can15</para>
       <para name="location">1</para>
     </row>
     <row id="pdt.sensor_dev_can">
       <para name="sps device type id">pdt.sensor_dev_can</para>
       <para name="device type name">dev can sensor</para>
       <para name="device type alias">can sensor</para>
       <para name="sps protocol id">plat.can_sensor</para>
       <para name="location">1</para>
     </row>
	 <row id="pdt.pcs_zte_can">
       <para name="sps device type id">pdt.pcs_zte_can</para>
       <para name="device type name">zte can pcs</para>
       <para name="device type alias">pcs</para>
       <para name="sps protocol id">plat.can_sensor</para>
       <para name="location">1</para>
     </row>
     <row id="north_unit_zte_1104_v20_compat">
       <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
       <para name="device type name">zte 1104 2.0 north unit</para>
       <para name="device type alias">1104 NMS</para>
       <para name="sps protocol id">plat.s1104_client</para>
       <para name="location">0</para>
     </row>
     <row id="north_unit_zte_1363">
       <para name="sps device type id">north_unit_zte_1363</para>
       <para name="device type name">zte 1363 north unit</para>
       <para name="device type alias">1363 NMS</para>
       <para name="sps protocol id">plat.s1104_client</para>
       <para name="location">0</para>
     </row>
     <row id="north_unit_zte_comm_sm">
       <para name="sps device type id">north_unit_zte_comm_sm</para>
       <para name="device type name">zte sm north unit</para>
       <para name="device type alias">sm NMS</para>
       <para name="sps protocol id">plat.comm_sm_slave</para>
       <para name="location">0</para>
     </row>
     <row id="north_unit_zte_power_sm">
       <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
       <para name="device type name">zte comm sm va0 north unit</para>
       <para name="device type alias">power_sm NMS</para>
       <para name="sps protocol id">plat.comm_sm_slave</para>
       <para name="location">0</para>
     </row>
     <row id="north_unit_zte_a_inter">
       <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
       <para name="device type name">zte 1363 1.2 north unit</para>
       <para name="device type alias">a_inter NMS</para>
       <para name="sps protocol id">plat.s1104_client</para>
       <para name="location">0</para>
     </row>
     <row id="modbus_rtu_north_device">
       <para name="sps device type id">pdt.modbus_rtu_north_device</para>
       <para name="device type name">zte modbus rtu north device</para>
       <para name="device type alias">modbus rtu north device NMS</para>
       <para name="sps protocol id">plat.modbus_rtu_server</para>
       <para name="location">0</para>
     </row>
  </cfgobject>
  <cfgobject id="plat.north_protocol" name="North Protocol" class="Site Config"/>
  <cfgobject id="plat.sps_north" name="sps north device configure" class="Site Config"/>
  <cfgobject id="pdt.lcd_contrast" name="lcd contrast configure" class="Site Config">
    <para name="lcd contrast">45</para>
  </cfgobject>
<cfgobject id="pdt.power_subrack" name="Power Subrack" class="Factory Config">
    <para name="Power Subrack Type">Power Subrack</para>
    <para name="Power Subrack Code">123246131000</para>
    <para name="Power Subrack Name">ZXDU98 B301 V6.0R40(18.220P3.C.LL.12514)Embedded Power Subrack</para>
    <para name="Rate Output Capacity">300</para>
    <para name="Subrack AC Power Supply">0</para>
    <para name="AC Power Supply Optional">1</para>
    <para name="Power Module Slot Num">4</para>
    <para name="DCLP Type">1</para>
    <para name="DCLP Alarm Status">1</para>
    <para name="LLVD1 Config">1</para>
    <para name="LLVD2 Config">1</para>
    <para name="BLVD Config">0</para>
    <para name="Load Shunt">200A/25mV</para>
    <para name="UIB Model">SPB</para>
    <para name="Temp Sensor Type">1</para>
    <para name="CIB Model">SPB</para>
    <para name="F01 Det Channel App Conf">1</para>
    <para name="F02 Det Channel App Conf">2</para>
    <para name="F03 Det Channel App Conf">3</para>
    <para name="F04 Det Channel App Conf">4</para>
    <para name="F05 Det Channel App Conf">5</para>
    <para name="F06 Det Channel App Conf">6</para>
    <para name="F07 Det Channel App Conf">7</para>
    <para name="F08 Det Channel App Conf">8</para>
    <para name="F09 Det Channel App Conf">1</para>
    <para name="F10 Det Channel App Conf">1</para>
    <para name="F11 Det Channel App Conf">2</para>
    <para name="F12 Det Channel App Conf">2</para>
    <para name="Power System Name">ZXDU98 B301 V6.0</para>
    <para name="SMR Curr Share Mode">1</para>
    <para name="Res DC In Shunt"/>
    <para name="Subrack Res Integer1">0</para>
    <para name="Subrack Res Integer2">0</para>
    <para name="Subrack Res Integer3">1</para>
    <para name="Subrack Res Integer4">0</para>
    <para name="Subrack Res Integer5">50</para>
    <para name="Subrack Res Integer6">0</para>
    <para name="Subrack Res Integer7">0</para>
    <para name="Subrack Res Integer8">0</para>
    <para name="Subrack Res Integer9">0</para>
    <para name="Subrack Res Integer10">0</para>
    <para name="Subrack Res Integer11">0</para>
    <para name="Subrack Res Integer12">0</para>
    <para name="Subrack Res Integer13">0</para>
    <para name="Subrack Res Integer14">0</para>
    <para name="Subrack Res Integer15">0</para>
    <para name="Subrack Res Integer16">0</para>
    <para name="Subrack Res String">0</para>
  </cfgobject>
  <cfgobject id="pdt.batt_input_asm" name="Battery Input Assemble" class="Factory Config">
    <para name="Batt Input Asm Type">Battery Input Assemble</para>
    <para name="Batt Input Asm Code"/>
    <para name="Batt Input Asm Name"/>
    <para name="Battery Shunt">200A/25mV</para>
    <para name="Batt1 Shunt1 Config">1</para>
    <para name="Batt1 Shunt2 Config">0</para>
    <para name="Batt1 Shunt3 Config">0</para>
    <para name="Batt1 Shunt4 Config">0</para>
    <para name="Batt2 Shunt1 Config">1</para>
    <para name="Batt2 Shunt2 Config">0</para>
    <para name="Batt2 Shunt3 Config">0</para>
    <para name="Batt2 Shunt4 Config">0</para>
    <para name="Batt3 Shunt1 Config">1</para>
    <para name="Batt3 Shunt2 Config">0</para>
    <para name="Batt3 Shunt3 Config">0</para>
    <para name="Batt3 Shunt4 Config">0</para>
    <para name="Batt4 Shunt1 Config">1</para>
    <para name="Batt4 Shunt2 Config">0</para>
    <para name="Batt4 Shunt3 Config">0</para>
    <para name="Batt4 Shunt4 Config">0</para>
    <para name="Batt Input Res Integer1">0</para>
    <para name="Batt Input Res Integer2">0</para>
    <para name="Batt Input Res Integer3">0</para>
    <para name="Batt Input Res Integer4">0</para>
  </cfgobject>
  <cfgobject id="pdt.ac_input_asm" name="AC Input Assemble" class="Factory Config">
    <para name="AC Input Asm Type">AC Input</para>
    <para name="AC Input Asm Code"/>
    <para name="AC Input Asm Name"/>
    <para name="AC Input Switch Det">0</para>
    <para name="AC SPD Power Supply">0</para>
	<para name="AC SPD Level">2</para>
	<para name="AC SPD Det">1</para>
	<para name="AC Output Switch Det">0</para>
	<para name="ACTM Config">0</para>
	<para name="AC Input Res Integer1">0</para>
	<para name="AC Input Res Integer2">0</para>
	<para name="AC Input Res Integer3">0</para>
	<para name="AC Input Res Integer4">0</para>
  </cfgobject>
  <cfgobject id="pdt.outdoor_cabi" name="OutDoor Cabinet" class="Factory Config"/>
  <cfgobject id="plat.acq_unit" name="acquisition unit configure" class="Site Config"/>
  <cfgobject id="plat.device_type" name="device type" class="Factory Config"/>
  <cfgobject id="plat.acq_2sid" name="acquisition bind to signal inst" class="Site Config">
    <row id="plat.acq_2sid0">
       <para name="acquisition inst id">COM1.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM1.devtype_48.1.get blm ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid1">
       <para name="acquisition inst id">COM2.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM2.devtype_48.1.get blm ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid2">
       <para name="acquisition inst id">COM3.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM3.devtype_48.1.get blm ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid3">
       <para name="acquisition inst id">COM1.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM1.devtype_-48.1.get yada ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid4">
       <para name="acquisition inst id">COM2.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM2.devtype_-48.1.get yada ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid5">
       <para name="acquisition inst id">COM3.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM3.devtype_-48.1.get yada ext tmp hum.1</para>
       <para name="signal id">0x9001010030004</para>
    </row>
    <row id="plat.acq_2sid6">
       <para name="acquisition inst id">COM1.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM1.devtype_48.1.get blm ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
    <row id="plat.acq_2sid7">
       <para name="acquisition inst id">COM2.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM2.devtype_48.1.get blm ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
    <row id="plat.acq_2sid8">
       <para name="acquisition inst id">COM3.devtype_48.1</para>
       <para name="command id">get blm ext tmp hum</para>
       <para name="field id">COM3.devtype_48.1.get blm ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
    <row id="plat.acq_2sid9">
       <para name="acquisition inst id">COM1.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM1.devtype_-48.1.get yada ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
    <row id="plat.acq_2sid10">
       <para name="acquisition inst id">COM2.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM2.devtype_-48.1.get yada ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
    <row id="plat.acq_2sid11">
       <para name="acquisition inst id">COM3.devtype_-48.1</para>
       <para name="command id">get yada ext tmp hum</para>
       <para name="field id">COM3.devtype_-48.1.get yada ext tmp hum.2</para>
       <para name="signal id">0x9001010050001</para>
    </row>
  </cfgobject>
  <cfgobject id="pdt.com_nms" name="nms on com link" class="Site Config">
     <row id="pdt.com_nms1">
       <para name="dev name">com_1104_nms</para>
       <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id"></para>
     </row>
     <row id="pdt.com_nms2">
       <para name="dev name">com_1363_nms</para>
       <para name="sps device type id">north_unit_zte_1363_v22</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id"></para>
     </row>
     <row id="pdt.com_nms3">
       <para name="dev name">com_apptest_1104_nms</para>
       <para name="sps device type id">pdt.apptest_qtp_zte_1104_v20</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id">pdt.com_nms1</para>
    </row>
    <row id="pdt.com_nms4">
       <para name="dev name">com_csu_download_nms</para>
       <para name="sps device type id">pdt.download</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id">pdt.com_nms1</para>
    </row>
    <row id="pdt.com_nms5">
       <para name="dev name">com_csu_download_1363_nms</para>
       <para name="sps device type id">pdt.download</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id">pdt.com_nms2</para>
    </row>
    <row id="pdt.com_nms6">
       <para name="dev name">com_comm_sm_nms</para>
       <para name="sps device type id">north_unit_zte_comm_sm_v10</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id"></para>
    </row>
    <row id="pdt.com_nms7">
       <para name="dev name">com_power_sm_nms</para>
       <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id"></para>
    </row>
	<row id="pdt.com_nms8">
       <para name="dev name">com_a_inter_1363_nms</para>
       <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">0</para>
       <para name="share com with nms id"></para>
    </row>
    <row id="pdt.com_nms9">
       <para name="dev name">com_great_power_lcd_nms</para>
       <para name="sps device type id">pdt.modbus_rtu_north_device</para>
       <para name="link type id">plat.com</para>
       <para name="link inst id">plat.COM1</para>
       <para name="comm address">1</para>
       <para name="share com with nms id"></para>
    </row>
  </cfgobject>
  <cfgobject id="pdt.north_download_ip_comm" name="north download ip comm configure" class="Site Config">
    <para name="csu role">1</para>
    <para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">1234</para>
    <para name="inform port">3000</para>
    <para name="SSH Status">0</para>
  </cfgobject>
  <cfgobject id="pdt.ip_north_download_nms" name="north download on ip link" class="Site Config">
	<row id="pdt.ip_north_download_nms1">
       <para name="dev name">ip_north_download_data_nms1</para>
       <para name="sps device type id">pdt.download</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link23</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_north_download_nms2">
       <para name="dev name">ip_north_download_data_nms2</para>
       <para name="sps device type id">pdt.download</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link27</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>	
  </cfgobject>
  <cfgobject id="pdt.1104_nms_ip_comm" name="1104 nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
    <para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">4000</para>
    <para name="inform port">3000</para>
    <para name="SSH Status">0</para>
  </cfgobject>
  <cfgobject id="pdt.ip_1104_nms" name="1104 nms on ip link" class="Site Config">
	<row id="pdt.ip_1104_nms1">
       <para name="dev name">ip_1104_data_nms1</para>
       <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link1</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_1104_nms2">
       <para name="dev name">ip_1104_data_nms2</para>
       <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link2</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_1104_nms3">
       <para name="dev name">ip_1104_data_nms3</para>
       <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link3</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_1104_nms7">
       <para name="dev name">ip_apptest_1104_data_nms</para>
       <para name="sps device type id">pdt.apptest_qtp_zte_1104_v20</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link13</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
  <cfgobject id="pdt.1363_nms_ip_comm" name="1363 nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
	<para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">4000</para>
    <para name="inform port">3000</para>
    <para name="SSH Status">0</para>
  </cfgobject>
  <cfgobject id="pdt.ip_1363_nms" name="1363 nms on ip link" class="Site Config">
	<row id="pdt.ip_1363_nms1">
       <para name="dev name">ip_1363_data_nms1</para>
       <para name="sps device type id">north_unit_zte_1363_v22</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link7</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_1363_nms2">
       <para name="dev name">ip_1363_data_nms2</para>
       <para name="sps device type id">north_unit_zte_1363_v22</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link8</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_1363_nms3">
       <para name="dev name">ip_1363_data_nms3</para>
       <para name="sps device type id">north_unit_zte_1363_v22</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link9</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
  <cfgobject id="pdt.sm_nms_ip_comm" name="sm nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
	<para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">5500</para>
    <para name="inform port">3000</para>
	<para name="SSH Status">1</para>
  </cfgobject>
  <cfgobject id="pdt.ip_sm_nms" name="sm nms on ip link" class="Site Config">
	<row id="pdt.ip_sm_nms1">
       <para name="dev name">ip_sm_data_nms1</para>
       <para name="sps device type id">north_unit_zte_comm_sm_v10</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link14</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_sm_nms2">
       <para name="dev name">ip_sm_data_nms2</para>
       <para name="sps device type id">north_unit_zte_comm_sm_v10</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link15</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_sm_nms3">
       <para name="dev name">ip_sm_data_nms3</para>
       <para name="sps device type id">north_unit_zte_comm_sm_v10</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link16</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
  <cfgobject id="pdt.power_sm_nms_ip_comm" name="power_sm nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
    <para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">5500</para>
    <para name="inform port">3000</para>
	<para name="SSH Status">1</para>
  </cfgobject>
  <cfgobject id="pdt.ip_power_sm_nms" name="power_sm nms on ip link" class="Site Config">
	<row id="pdt.ip_power_sm_nms1">
       <para name="dev name">ip_power_sm_data_nms1</para>
       <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link20</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_power_sm_nms2">
       <para name="dev name">ip_power_sm_data_nms2</para>
       <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link21</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_power_sm_nms3">
       <para name="dev name">ip_power_sm_data_nms3</para>
       <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link22</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
   <cfgobject id="pdt.a_inter_nms_ip_comm" name="a inter nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
	<para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">4000</para>
    <para name="inform port">3000</para>
    <para name="SSH Status">0</para>
  </cfgobject>
  <cfgobject id="pdt.ip_a_inter_nms" name="a inter nms on ip link" class="Site Config">
	<row id="pdt.ip_a_inter_nms1">
       <para name="dev name">ip_a_inter_data_nms1</para>
       <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link24</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_a_inter_nms2">
       <para name="dev name">ip_a_inter_data_nms2</para>
       <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link25</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_a_inter_nms3">
       <para name="dev name">ip_a_inter_data_nms3</para>
       <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link26</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
  <cfgobject id="pdt.gc_modbus_tcp_nms_ip_comm" name="gc modbus tcp nms ip comm configure" class="Site Config">
    <para name="csu role">1</para>
	<para name="transmission protocol">0</para>
    <para name="listen ip">0.0.0.0</para>
    <para name="listen port">502</para>
    <para name="inform port">3000</para>
    <para name="SSH Status">0</para>
  </cfgobject>
  <cfgobject id="pdt.ip_gc_modbus_tcp_nms" name="gc modbus tcp nms on ip link" class="Site Config">
	<row id="pdt.ip_gc_modbus_tcp_nms1">
       <para name="dev name">ip_gc_modbus_tcp_data_nms1</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link27</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms2">
       <para name="dev name">ip_gc_modbus_tcp_data_nms2</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link28</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms3">
       <para name="dev name">ip_gc_modbus_tcp_data_nms3</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms4">
       <para name="dev name">ip_gc_modbus_tcp_data_nms4</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms5">
       <para name="dev name">ip_gc_modbus_tcp_data_nms5</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms6">
       <para name="dev name">ip_gc_modbus_tcp_data_nms6</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms7">
       <para name="dev name">ip_gc_modbus_tcp_data_nms7</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms8">
       <para name="dev name">ip_gc_modbus_tcp_data_nms8</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
	<row id="pdt.ip_gc_modbus_tcp_nms9">
       <para name="dev name">ip_gc_modbus_tcp_data_nms9</para>
       <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
       <para name="link type id">plat.ip_link</para>
       <para name="link inst id">plat.ip_link29</para>
       <para name="comm address">0</para>
	   <para name="nms type">data</para>
       <para name="union nms id"></para>
	</row>
  </cfgobject>
  <cfgobject id="pdt_sps_north_protocol" name="sps north protocol" class="Site Config">
    <row id="pdt_sps_north_protocol1">
      <para name="protocol alias">north 1104 compat</para>
      <para name="sps device type id">north_unit_zte_1104_v20_compat</para>
    </row>
    <row id="pdt_sps_north_protocol2">
      <para name="protocol alias">north 1363</para>
      <para name="sps device type id">north_unit_zte_1363_v22</para>
    </row>
    <row id="pdt_sps_north_protocol3">
      <para name="protocol alias">north comm sm</para>
      <para name="sps device type id">north_unit_zte_comm_sm_v10</para>
    </row>
    <row id="pdt_sps_north_protocol4">
      <para name="protocol alias">north power sm</para>
      <para name="sps device type id">north_unit_zte_comm_sm_va0</para>
    </row>
     <row id="pdt_sps_north_protocol5">
      <para name="protocol alias">north iron tower a inter</para>
      <para name="sps device type id">pdt.iron_tower_a_inter_1363_v12</para>
    </row>
    <row id="pdt_sps_north_protocol6">
      <para name="protocol alias">gc modbus tcp</para>
      <para name="sps device type id">gc_north_unit_zte_modbus_tcp</para>
    </row>
    <row id="pdt_sps_north_protocol7">
      <para name="protocol alias">modbus rtu north device</para>
      <para name="sps device type id">pdt.modbus_rtu_north_device</para>
    </row>
 </cfgobject>
  <cfgobject id="pdt.com_sdev" name="south device on com link" class="Site Config">
    <row id="pdt.com_sdev1">
    <para name="sps device type id">pdt.dr_zte_modbus_rtu</para>
    <para name="link type id">plat.com</para>
    <para name="link inst ids">plat.COM3;plat.COM2;plat.COM1</para>
    <para name="dev number">1</para>
    <para name="dev base addr">2</para>
    <para name="dev custom addr"/>
  </row>
  <row id="pdt.com_sdev2">
    <para name="sps device type id">pdt.tf_zte_modbus_rtu</para>
    <para name="link type id">plat.com</para>
    <para name="link inst ids">plat.COM3;plat.COM2;plat.COM1</para>
    <para name="dev number">1</para>
    <para name="dev base addr">3</para>
    <para name="dev custom addr"/>
  </row>
  <row id="pdt.com_sdev3">
    <para name="sps device type id">pdt.fbbms_zte_1363_v22</para>
    <para name="link type id">plat.com</para>
    <para name="link inst ids">plat.COM3;plat.COM2;plat.COM1</para>
    <para name="dev number">32</para>
    <para name="dev base addr">1</para>
    <para name="dev custom addr"/>
  </row>
  <row id="pdt.com_sdev4">
    <para name="sps device type id">pdt.sd_dr_zte_modbus_rtu</para>
    <para name="link type id">plat.com</para>
    <para name="link inst ids">plat.COM3;plat.COM2;plat.COM1</para>
    <para name="dev number">1</para>
    <para name="dev base addr">1</para>
    <para name="dev custom addr"/>
  </row>
  <row id="pdt.com_sdev5">
    <para name="sps device type id">pdt.pcs_zte_modbus_rtu</para>
    <para name="link type id">plat.com</para>
    <para name="link inst ids">plat.COM3;plat.COM2;plat.COM1</para>
    <para name="dev number">1</para>
    <para name="dev base addr">20</para>
    <para name="dev custom addr"/>
  </row>
  </cfgobject>
  <cfgobject id="plat.radius" name="RADIUS" class="Site Config">
    <para name="RADIUS Status">0</para>
    <para name="RADIUS Primary Server IP Address"></para>
    <para name="RADIUS Backup Server IP Address"></para>
    <para name="RADIUS Server Authentication Port">0</para>
    <para name="RADIUS Secret"></para>
    <para name="Expiration Time">10</para>
    <para name="Retry Times">1</para>
    <para name="Private Key Status">0</para>
    <para name="Private Key Password"></para>
  </cfgobject>
  <cfgobject id="pdt.custom_func" name="CUSTOM" class="Factory Config">
    <para name="custom slow curr limiting">0</para>
    <para name="custom csu power">0</para>
    <para name="custom volt limit gen power">0</para>
    <para name="custom smr name">0</para>
  </cfgobject>
  <cfgobject id="plat.service_config" name="service config" class="Site Config">
  <row id="plat.service_config1">
    <para name="service name">pdt.n1104</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
  <row id="plat.service_config2">
    <para name="service name">pdt.n1363</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
  <row id="plat.service_config3">
    <para name="service name">pdt.sm</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
  <row id="plat.service_config4">
    <para name="service name">pdt.power_sm</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
  <row id="plat.service_config5">
    <para name="service name">plat.snmp</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
   <row id="plat.service_config6">
    <para name="service name">pdt.na_inter</para>
    <para name="wired priority">1</para>
    <para name="wireless priority">2</para>
    <para name="vpn priority">0</para>
    <para name="link detection mechanism">1</para>
  </row>
  </cfgobject>
  <cfgobject id="pdt.plc_enbale_status" name="PLC Function Enable Status Config" class="Factory Config">
    <para name="PLC Function Enable Status">1</para>
  </cfgobject>
  <cfgobject id="pdt.plc_group" name="PLC Group Config" class="Factory Config"/>
  <cfgobject id="pdt.plc_sentence" name="PLC Sentence Config" class="Factory Config"/>
  <cfgobject id="pdt.remote_update_old_naming" name="remote update old naming" class="Factory Config">
    <row id="powernew">
	  <para name="device type">1</para>
      <para name="keyword1">power</para>
	  <para name="keyword2">new</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">powernew.tar.gz</para>
    </row>
    <row id="all_update">
      <para name="device type">1</para>
      <para name="keyword1">all</para>
	  <para name="keyword2">update</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">all_update.tar.gz</para>
    </row>
    <row id="ZXDC48_FB_BMS">
      <para name="device type">23</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">FB_BMS</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDC48_FB_BMS.tar.gz</para>
    </row>
	<row id="ZXDC48_B1_BMS">
      <para name="device type">23</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">B1_BMS</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDC48_B1_BMS.tar.gz</para>
    </row>
	<row id="ZXDC48_B1_BDCU">
      <para name="device type">23</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">BDCU</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDC48_B1_BDCU.tar.gz</para>
    </row>
	<row id="ZXDUPAV5.0BDCU">
      <para name="device type">23</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">BDCU</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDUPAV5.0BDCU.tar.gz</para>
    </row>
	<row id="ZXD3000PFC181107">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">3000</para>
	  <para name="keyword3">PFC</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD3000V6.0PFC181107.tar.gz</para>
    </row>
    <row id="ZXD3000DC181107">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">3000</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD3000V6.0DC181107.tar.gz</para>
    </row>
	<row id="ZXD3000">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">3000</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD3000V6.0.tar.gz</para>
    </row>
	<row id="ZXD4000PFC211118">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">4000</para>
	  <para name="keyword3">PFC</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0PFC211118.tar.gz</para>
    </row>
    <row id="ZXD4000DC211118">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">4000</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0DC211118.tar.gz</para>
    </row>
	<row id="ZXD4000">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">4000</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0.tar.gz</para>
    </row>
	<row id="ZXDSWMB7Z-63-A0">
      <para name="device type">1045</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">SWMB</para>
	  <para name="keyword3">63-A0</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDSWMB7Z-63-A0.tar.gz</para>
    </row>
	<row id="ZXDSWMB7Z-125-A0">
      <para name="device type">1046</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">SWMB</para>
	  <para name="keyword3">125-A0</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDSWMB7Z-125-A0.tar.gz</para>
    </row>
	<row id="ZXDSWMB7Z-63">
      <para name="device type">44</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">SWMB</para>
	  <para name="keyword3">63</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDSWMB7Z-63.tar.gz</para>
    </row>
	<row id="ZXDSWMB7Z-125">
      <para name="device type">1044</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">SWMB</para>
	  <para name="keyword3">125</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDSWMB7Z-125.tar.gz</para>
    </row>
    <row id="ZXDSWKSiS2-ZC-63DC">
      <para name="device type">44</para>
      <para name="keyword1">ZXD</para>
      <para name="keyword2">SW</para>
      <para name="keyword3">63</para>
      <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDSWKSiS2-ZC-63DC.tar.gz</para>
    </row>
    <row id="ZXDSWKSiS2-ZC-125DC">
      <para name="device type">44</para>
      <para name="keyword1">ZXD</para>
      <para name="keyword2">SW</para>
      <para name="keyword3">125</para>
      <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDSWKSiS2-ZC-125DC.tar.gz</para>
    </row>
	<row id="ZXDAEMBUpdateFile">
      <para name="device type">-30</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">AEMB</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDAEMBUpdateFile.tar.gz</para>
    </row>
	<row id="ZXDDCMU001">
      <para name="device type">34</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">DCMU</para>
	  <para name="keyword3">001</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDDCMU001.tar.gz</para>
    </row>
	<row id="DCMU001">
      <para name="device type">34</para>
      <para name="keyword1">DCMU</para>
	  <para name="keyword2">001</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">DCMU001.tar.gz</para>
    </row>
	<row id="ZXDSDUUpdateFile">
      <para name="device type">-34</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">SDU</para>
	  <para name="keyword3">UpdateFile</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDSDUUpdateFile.tar.gz</para>
    </row>
	<row id="ZXDT02PUV3.0DC180119">
      <para name="device type">16</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">PU</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name"><EMAIL></para>
    </row>
	<row id="ZXDPFCDC">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0PFCDC.tar.gz</para>
    </row>
	<row id="ZXDPFC211118">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0PFC211118.tar.gz</para>
    </row>
    <row id="ZXDDC211118">
      <para name="device type">7</para>
      <para name="keyword1">ZXD</para>
	  <para name="keyword2">DC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXD4000V6.0DC211118.tar.gz</para>
    </row>
	<row id="ZXEPS_EBD48200_SPCU">
      <para name="device type">35</para>
      <para name="keyword1">ZXEPS</para>
	  <para name="keyword2">EBD</para>
	  <para name="keyword3">SPCU</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXEPS_EBD48200_SPCU01_V2.01.tar.gz</para>
    </row>
	<row id="ZXEPSS4810A220331">
      <para name="device type">36</para>
      <para name="keyword1">ZXEPS</para>
	  <para name="keyword2">S4810A</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXEPSS4810A220331.tar.gz</para>
    </row>
	<row id="ZXDU58_W121_FCTL">
	  <para name="device type">61</para>
	  <para name="keyword1">ZXD</para>
	  <para name="keyword2">W121</para>
	  <para name="keyword3">FCTL</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDU58_W121_FCTL.tar.gz</para>
	</row>
	<row id="ZXDT22_SF01_FCTL">
	  <para name="device type">1061</para>
	  <para name="keyword1">ZXD</para>
	  <para name="keyword2">SF01</para>
	  <para name="keyword3">FCTL</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDT22_SF01_FCTL_V1.tar.gz</para>
	</row>
	<row id="ZXDACMUAPPUpdate">
	  <para name="device type">66</para>
	  <para name="keyword1">ZXD</para>
	  <para name="keyword2">ACMU</para>
	  <para name="keyword3">APP</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDACMUAPPUpdate.tar.gz</para>
	</row>
	<row id="ZXDACMUCCLIBUpdate">
	  <para name="device type">66</para>
	  <para name="keyword1">ZXD</para>
	  <para name="keyword2">ACMU</para>
	  <para name="keyword3">CCLIB</para>
	  <para name="suffix">.tar.gz</para>
	  <para name="full name">ZXDACMUCCLIBUpdate.tar.gz</para>
	</row>
	<row id="UIB_app">
      <para name="device type">-1</para>
      <para name="keyword1">UIB</para>
	  <para name="keyword2">app</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.bin</para>
	  <para name="full name">UIB_app.bin</para>
    </row>
	<row id="IDDB_app">
      <para name="device type">-2</para>
      <para name="keyword1">IDDB</para>
	  <para name="keyword2">app</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.bin</para>
	  <para name="full name">IDDB_app.bin</para>
    </row>
  </cfgobject>
  <cfgobject id="pdt.remote_update_new_naming" name="remote update new naming" class="Factory Config">
    <row id="BMS@V1.00.03.05@BDCU@V1.0B5">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BMS</para>
	  <para name="keyword3">BDCU</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BMS@220524@V1.00.03.05@BDCU@<EMAIL></para>
    </row>
	<row id="BMS@V1.00.04.00@BDCU@V1.0B5">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BMS</para>
	  <para name="keyword3">BDCU</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BMS@221124@V1.00.04.00@BDCU@<EMAIL></para>
    </row>
	<row id="FB100B3@BMS@V1.00.03.05">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BMS</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BMS@<EMAIL></para>
    </row>
	<row id="FB100B3@BDCU@V1.0B5">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BDCU</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BDCU@<EMAIL></para>
    </row>
	<row id="FB100B3@BMS@V1.00.02.03">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BMS</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BMS@<EMAIL></para>
    </row>
	<row id="FB100B3@BDCU@V1.0B4">
      <para name="device type">23</para>
      <para name="keyword1">FB100B3</para>
	  <para name="keyword2">BDCU</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDFBBMS@FB100B3@BDCU@<EMAIL></para>
    </row>
	<row id="NFB@FB100C2@P@V1.X">
      <para name="device type">33</para>
      <para name="keyword1">NFB</para>
	  <para name="keyword2">FB100C2</para>
	  <para name="keyword3">P</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXD@NFB@FB100C2@P@<EMAIL></para>
    </row>
	<row id="NFB@FB100C2@C@V1.X">
      <para name="device type">33</para>
      <para name="keyword1">NFB</para>
	  <para name="keyword2">FB100C2</para>
	  <para name="keyword3">C</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXD@NFB@FB100C2@C@<EMAIL></para>
    </row>
	<row id="NFB@FB100C2@M@V1.1.4">
      <para name="device type">33</para>
      <para name="keyword1">NFB</para>
	  <para name="keyword2">FB100C2</para>
	  <para name="keyword3">M</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXD@NFB@FB100C2@M@<EMAIL></para>
    </row>
    <row id="NFBBMS@R311@V1.00.01.02">
      <para name="device type">33</para>
      <para name="keyword1">NFBBMS</para>
      <para name="keyword2">R311</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXD@NFBBMS@R311@<EMAIL></para>
    </row>
	<row id="ZXD3000@PFC@DC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD3000V6.0@PFC@181107@V1.01@DC@<EMAIL></para>
    </row>
	<row id="ZXD4000@PFC@DC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3">DC</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD4000V6.0@PFC@211118@V1.01@DC@<EMAIL></para>
    </row>
	<row id="ZXD3000@PFC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD3000V6.0@PFC@<EMAIL></para>
    </row>
    <row id="ZXD3000@DC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">DC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD3000V6.0@DC@<EMAIL></para>
    </row>
	<row id="ZXD4000@PFC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">PFC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD4000V6.0@PFC@<EMAIL></para>
    </row>
    <row id="ZXD4000@DC@V1.01">
      <para name="device type">7</para>
      <para name="keyword1">SMR</para>
	  <para name="keyword2">DC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SMR@ZXD4000V6.0@DC@<EMAIL></para>
    </row>
	<row id="ZXDU98@AEMB@V1.00.00.01">
      <para name="device type">-30</para>
      <para name="keyword1">ZXDU98</para>
	  <para name="keyword2">AEMB</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">ZXDU98@B601@<EMAIL></para>
    </row>
	<row id="ZXDT02PUV3.0@DC@V1.20">
      <para name="device type">16</para>
      <para name="keyword1">PU</para>
	  <para name="keyword2">DC</para>
	  <para name="keyword3"></para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">PU@ZXDT02PUV3.0@DC@<EMAIL></para>
    </row>
	<row id="ZXEPSS4810A@V1.02">
      <para name="device type">36</para>
      <para name="keyword1">SPU</para>
	  <para name="keyword2">ZXEPS</para>
	  <para name="keyword3">S4810A</para>
	  <para name="suffix">.tar.gz</para>
      <para name="full name">SPU@<EMAIL></para>
    </row>
  </cfgobject>
  <cfgobject class="Factory Config" id="plat.dmu" name="dmu">
      <para name="dmu type">2</para>
  </cfgobject>
</config>
