var vmodel = avalon.define({
	$id:'history',
	tab_list:'hisalarm',
	init_time:{},
	historyalarm_time:{"start_time":"","end_time":""},
	historyalarm_counts:"10",

	historydata_time:{"start_time":"","end_time":""},
	historydata_counts:"10",
	historydata_selectdevsid:"0",
	historydata_selectsigsid:"0",
	historydata_sigsidvalue:[],

	historyevent_time:{"start_time":"","end_time":""},
	historyevent_type:"0",
	historyevent_counts:"10",

	smrevent_time:{"start_time":"","end_time":""},
	smrevent_counts:"10",

	statistic_record_types:[],
	statistic_record_time:{"start_time":"","end_time":""},
	statistic_record_counts:"10",
	statistic_records:[],
	statistic_indexs:[],
	statistic_records_num:[],
	statistic_records_type:"-1",
	record_details:{},
	record_details_scan:"",
	is_statistic_empty:0,

	export_hisalarm_passwd:"",    //导出历史记录密码
	export_hisdata_passwd:"",     //导出历史数据密码
	export_hisevent_passwd:"",    //导出操作记录密码
	record_export_passwd:"",      //导出事件记录密码

	record_show:function(index) {
		if(typeof(index) == "undefined") {
			index = vmodel.statistic_records.length -1;
		}
		if (vmodel.statistic_records && vmodel.statistic_records.length > 0) {
			vmodel.record_details = vmodel.statistic_records[index];
			var rec_type_name = vmodel.statistic_record_types[vmodel.statistic_records_type*1].name;
			vmodel.record_details_scan = rec_type_name + (index + 1) + "#";
			return true;
		}
		return false;
	},
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
    }
});

//  获取设备列表
get_dev_list_all();

/************历史信息起止时间默认30天**************/
function get_start_end_time(timestr) {
	var now_time_str = timestr;
	var date = new Date(now_time_str);
	var startTime,endTime;

	var endDate = date;
	endTime=timeToStr(endDate);
	date.setDate(date.getDate()-30);
	var startDate = date;
	startTime=timeToStr(startDate);
	return {"start_time":startTime,"end_time":endTime};
}

function showHisInfoDate(){
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_init_time_succ};
	request.addRequest([gettimeRq]);

	function get_init_time_succ(d,r) {
		if (d.result == "ok") {
			var now_time_str = d.data[0].time;
			vmodel.init_time = get_start_end_time(now_time_str);
			get_init_time();
		}
	}
}
showHisInfoDate();

function get_init_time() {
	//初始化时间选择空间的显示内容，终止时间为浏览器当前时间，起始时间为该时间的前30天
	var initTime = vmodel.init_time;
	vmodel.historyalarm_time.start_time = initTime.start_time;
	vmodel.historyalarm_time.end_time = initTime.end_time;

	vmodel.historydata_time.start_time = initTime.start_time;
	vmodel.historydata_time.end_time = initTime.end_time;

	vmodel.historyevent_time.start_time = initTime.start_time;
	vmodel.historyevent_time.end_time = initTime.end_time;

	vmodel.statistic_record_time.start_time = initTime.start_time;
	vmodel.statistic_record_time.end_time = initTime.end_time;

	vmodel.smrevent_time.start_time = initTime.start_time;
	vmodel.smrevent_time.end_time = initTime.end_time;
}

//校验日期时间是否合法或者日期时间范围是否有效
function checkDateTime(obj) {
	var reg = /^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
	var valid = $(obj).val() == "" || reg.test($(obj).val());

	if(valid) {
		if(vmodel.tab_list == "hisalarm") {
			if(vmodel.historyalarm_time.end_time != "" && vmodel.historyalarm_time.start_time > vmodel.historyalarm_time.end_time) {
				valid = false;
			}
		}
		else if(vmodel.tab_list == "hisdata") {
			if(vmodel.historydata_time.end_time != "" && vmodel.historydata_time.start_time > vmodel.historydata_time.end_time) {
				valid = false;
			}
		}
		else {
			if(vmodel.historyevent_time.end_time != "" && vmodel.historyevent_time.start_time > vmodel.historyevent_time.end_time) {
				valid = false;
			}
		}	
	}

	if(!valid) {
		alert(mainvalue.i18nkeyword.system.date_not_correct);
		$(obj).val("");
	}
}

//历史告警相关操作
//查询历史告警
function doAlarmsearch(){
	showDataInit();
	vmodel.historyalarm_time.start_time = $('#alarm_starttext').val();
	vmodel.historyalarm_time.end_time = $('#alarm_endtext').val();
	var alarmSearch = [{"start_time":vmodel.historyalarm_time.start_time,"end_time":vmodel.historyalarm_time.end_time}];
	request.addRequest([{data:{objectid:"history_alarm",type:"list",paranum:"0",paraval:JSON.stringify(alarmSearch)},success:getHisAlarmList}]);
}

function getHisAlarmList(d,r){
	if(d.result!=="ok"||(d.datanum===0)){
		hideDataInit();
		d.data = [{"counts":"0"}];
	}
	mainvalue.paperattr.totalnum = d.data[0].counts;
	storeTotalNum();
	mainvalue.paperattr.currentpage = "1";
	mainvalue.paperattr.id = d.objectid;
	mainvalue.paperattr.listcom = JSON.parse(r.data.paraval)[0];
	mainvalue.paperattr.counts = vmodel.historyalarm_counts;
	var listdata = JSON.parse(r.data.paraval)[0];
	// var offset = d.data[0].counts - mainvalue.paperattr.counts +1;
	var offset = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	if(offset <= 0){
		listdata.offset = "1";
		listdata.counts =  d.data[0].counts;
		
	}
	else{
		listdata.offset =offset.toString();
		// listdata.counts = mainvalue.paperattr.counts;
		listdata.counts = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	}
	mainvalue.paperattr.getcom = listdata;
	var value = {};
	$.extend( true, value, mainvalue.paperattr );
	addvalue(mainvalue.pagerattrall,[value],"id");
	seachdata(mainvalue.paperattr.id);
}

//历史事件相关操作
//查询历史事件
function doEventsearch(){
	showDataInit();
	vmodel.historyevent_time.start_time = $('#event_starttext').val();
	vmodel.historyevent_time.end_time = $('#event_endtext').val();
	vmodel.historyevent_type = $('#hiseventtype').val();
	var eventSearch = [{"start_time":vmodel.historyevent_time.start_time,"end_time":vmodel.historyevent_time.end_time, "hisevent_type":vmodel.historyevent_type}];
	request.addRequest([{data:{objectid:"history_event",type:"list",paranum:"0",paraval:JSON.stringify(eventSearch)},success:getHisEventList}]);
}

function getHisEventList(d,r){
	if(d.result!=="ok"||(d.datanum===0)){
		hideDataInit();
		d.data = [{"counts":"0"}];
	}
	mainvalue.paperattr.totalnum = d.data[0].counts;
	storeTotalNum();
	mainvalue.paperattr.currentpage = "1";
	mainvalue.paperattr.id = d.objectid;
	mainvalue.paperattr.listcom = JSON.parse(r.data.paraval)[0];
	mainvalue.paperattr.counts = vmodel.historyevent_counts;
	var listdata = JSON.parse(r.data.paraval)[0];
	// var offset = d.data[0].counts - mainvalue.paperattr.counts +1;
	var offset = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	if(offset <= 0){
		listdata.offset = "1";
		listdata.counts =  d.data[0].counts;
	}
	else{
		listdata.offset =offset.toString();
		// listdata.counts = mainvalue.paperattr.counts;
		listdata.counts = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;

	}
	listdata['hisevent_type'] = vmodel.historyevent_type;

	mainvalue.paperattr.getcom = listdata;
	var value = {};
	$.extend( true, value, mainvalue.paperattr );
	addvalue(mainvalue.pagerattrall,[value],"id");
	seachdata(mainvalue.paperattr.id);
}



//统计记录相关操作
function init_record_types(obj) {
	function get_types_succ(d,r) {
		if (d.result == "ok") {
			vmodel.statistic_record_types = d.data;
		}
	}
	request.addRequest([{data:{objectid:"records",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_types_succ}]);
}
init_record_types();

function show_record_detail(obj){
	var show_tag = vmodel.record_show();
	if (show_tag) {
		$("#record_detail").show();
	} else {
		$("#record_detail").hide();
	}
}

function record_sort_compare(property) {    //  根据时间进行降序排列，后产生的数据排在前面
	return function(in1,in2) {
		var d1= in1[property];
		var d2= in2[property];
		var rslt = (new Date(d1.replace(/-/g,"\/"))) < (new Date(d2.replace(/-/g,"\/")))?1:-1;
		return rslt;
	}
}

function record_sort(rec_data) {
	var array = rec_data.sort(record_sort_compare('start_time'));
	return array;
}

function doRecordsearch(){
	$("#export_record").hide();
	$("#record_detail").hide();
	$("#query_record").show();
	if (vmodel.statistic_records) {
		vmodel.statistic_records.clear();
	}
	showDataInit();
	vmodel.statistic_record_time.start_time = "";
	vmodel.statistic_record_time.end_time =   "";
	vmodel.records_type = vmodel.statistic_records_type;
	if (vmodel.records_type < 0) {
		vmodel.is_statistic_empty = 0;
		vmodel.statistic_indexs.clear();
		hideDataInit();
		return;
	}
	vmodel.is_statistic_empty = 0;
	vmodel.statistic_indexs.clear();
	var eventSearch = [{"start_time":vmodel.statistic_record_time.start_time,"end_time":vmodel.statistic_record_time.end_time, "record_type":vmodel.records_type}];
	request.addRequest([{data:{objectid:"records",type:"list",paranum:"0",paraval:JSON.stringify(eventSearch)},success:getStaticRecordList}]);
}

function getStaticRecordList(d,r){
	if(d.result!=="ok"||(d.datanum===0)){
		hideDataInit();
		d.data = [{"counts":"0"}];
		return;
	}
	if (d.data[0].counts <= 0) {
		hideDataInit();
		vmodel.is_statistic_empty = 1;
		return;
	}
	var para = JSON.parse(r.data.paraval)[0];
	para["offset"] = "1";
	para["counts"] = d.data[0].counts;
	request.addRequest([{data:{objectid:"records",type:"val_get",paranum:"0",paraval:JSON.stringify([para])},success:getStaticRecordValue}]);

}

function getStaticRecordValue(d,r) {
	hideDataInit();
	//vmodel.statistic_records = record_sort(d.data);
	var index_array = [];
	for (var i in d.data) {
		index_array.push(d.data[i].end_time);
	}
	vmodel.statistic_indexs = index_array;
	vmodel.statistic_records = d.data;
	show_record_detail(vmodel.statistic_indexs.length - 1);
}

//历史数据相关操作
vmodel.$watch("historydata_selectdevsid", function(a) {
	var sidData = calSidToDevType(BigInt(vmodel.historydata_selectdevsid));
	vmodel.historydata_sigsidvalue.removeAll();
	vmodel.historydata_selectsigsid=vmodel.historydata_selectdevsid;
	if((vmodel.historydata_selectdevsid!=="0")&&(sidData.devType>0)){
		var signal_para = JSON.stringify([{"sid":vmodel.historydata_selectdevsid}]);
		var hisdata_sid_val = {data:{objectid:"history_data_sids",type:"val_get",paranum:"1",paraval:signal_para},success:getHisDataSidData};
		request.addRequest([hisdata_sid_val]);
	}
});

vmodel.$watch("historydata_selectsigsid", function(a) {
	if(vmodel.historydata_selectsigsid==="0"){
		vmodel.historydata_selectsigsid=vmodel.historydata_selectdevsid;
	}
});


function getHisDataSidData(d,r){
	if (d.result == "ok") {
		addvalue(vmodel.historydata_sigsidvalue,d.data,"sid");
	}
}

function doHisDatasearch(){
	showDataInit();
	vmodel.historydata_time.start_time = $('#data_starttext').val();
	vmodel.historydata_time.end_time = $('#data_endtext').val();
	var hisDataSearch = [{"start_time":vmodel.historydata_time.start_time,"end_time":vmodel.historydata_time.end_time,"sid":vmodel.historydata_selectsigsid}];
	request.addRequest([{data:{objectid:"history_data",type:"list",paranum:"0",paraval:JSON.stringify(hisDataSearch)},success:getHisDataList}]);
}

function getHisDataList(d,r){
	if(d.result!=="ok"||(d.datanum===0)){
		hideDataInit();
		d.data = [{"counts":"0"}];
	}
	mainvalue.paperattr.totalnum = d.data[0].counts;
	storeTotalNum();
	mainvalue.paperattr.currentpage = "1";
	mainvalue.paperattr.id = d.objectid;
	mainvalue.paperattr.listcom = JSON.parse(r.data.paraval)[0];
	mainvalue.paperattr.counts = vmodel.historydata_counts;
	var listdata = JSON.parse(r.data.paraval)[0];
	// var offset = d.data[0].counts - mainvalue.paperattr.counts +1;
	var offset = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	if(offset <= 0){
		listdata.offset = "1";
		listdata.counts =  d.data[0].counts;
	}
	else{
		listdata.offset =offset.toString();
		// listdata.counts = mainvalue.paperattr.counts;
		listdata.counts = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	}
	mainvalue.paperattr.getcom = listdata;
	var value = {};
	$.extend( true, value, mainvalue.paperattr );
	addvalue(mainvalue.pagerattrall,[value],"id");
	seachdata(mainvalue.paperattr.id);
}

function history_download_handler(d, r) {
    hideDataInit();
	if (d.result == "ok" && d.data.length > 0) {
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
	if (d.objectid == "export_hisalarm") {
		vmodel.export_hisalarm_passwd = "";
	} else if (d.objectid == "export_hisdata") {
		vmodel.export_hisdata_passwd = "";
	} else if (d.objectid == "export_hisevent") {
		vmodel.export_hisevent_passwd = "";
	} else if (d.objectid == "record_export") {
		vmodel.record_export_passwd = "";
	}
}

function pswd_strong_check(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

function check_string_length(str) {
    str = str.trim();
    if (getStrLeng_UTF8(str) > 31) {
        return false;
    }
    return true;
}

function doDL_hisdata() {
	if ($("input[name='export_hisdata_box']").prop("checked")) {
		var password = vmodel.export_hisdata_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.export_hisdata_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.export_hisdata_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	showDataInit();
	var para = [{"encrypswd":password}];
	var q = {data:{objectid:"export_hisdata",type:"val_get",paraval:JSON.stringify(para)},success:history_download_handler};
	request.addRequest([q]);
}


function doDL_hisevent() {
	if ($("input[name='export_hisevent_box']").prop("checked")) {
		var password = vmodel.export_hisevent_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.export_hisevent_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.export_hisevent_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	showDataInit();
	var para = [{"encrypswd":password}];
	var q = {data:{objectid:"export_hisevent",type:"val_get",paraval:JSON.stringify(para)},success:history_download_handler};
	request.addRequest([q]);
}


function doDL_hisalarm() {
	if ($("input[name='export_hisalarm_box']").prop("checked")) {
		var password = vmodel.export_hisalarm_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.export_hisalarm_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.export_hisalarm_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	showDataInit();
	var para = [{"encrypswd":password}];
	var q = {data:{objectid:"export_hisalarm",type:"val_get",paraval:JSON.stringify(para)},success:history_download_handler};
	request.addRequest([q]);
}


function doDL_report() {
	if (vmodel.statistic_records_type < 0) {
		return;
	}
	if ($("input[name='record_export_box']").prop("checked")) {
		var password = vmodel.record_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.record_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.record_export_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	showDataInit();
	var para = {"record_type":vmodel.statistic_records_type, "encrypswd":password};
	var q = {data:{objectid:"record_export",type:"val_get",paraval:JSON.stringify([para])},success:history_download_handler};
	request.addRequest([q]);
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "hisalarm" || tab == "hisdata" || tab == "hisevent" || tab == "statistic_record") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "hisalarm";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();
////////////////////////////***加密密码框是否展示***///////////////////////////////////
function whether_to_encrypt(obj) {
	if ($(obj).is(":checked")) {
		$(obj).next().removeAttr("disabled");
	} else {
		$(obj).next().attr("disabled","disabled");
		$(obj).next().val("");
	}
}
