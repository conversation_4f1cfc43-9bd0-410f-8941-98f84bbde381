﻿/************************用于处理CSU升级及SSL证书升级*********************************/
var fileupload_finished_tag = 0;   //   文件上传完成标记
var update_tip_show_tag = 1;       //   升级开始转动进度条显示标记
let updatesys_tip_action = -1;      //   对系统升级提示采取的动作
var upload_type = "";               //   上传类型
var file_uploaded = 0;           // 文件已上传的量

function onprogress(evt){
    //侦查附件上传情况
    //通过事件对象侦查
	//该匿名函数表达式大概0.05-0.1秒执行一次
	var loaded = evt.loaded > file_uploaded ? evt.loaded : file_uploaded;
	file_uploaded = loaded;
    var tot = evt.total;
    var per = Math.floor(100*loaded/tot);  //文件已经上传的百分比
    var son =  document.getElementById('progressBar');
    son.innerHTML = per+"%";
    son.style.width=per+"%";
    if (per > 99) {   // 文件上传完成
		fileupload_finished_tag = 1;
		file_uploaded = 0;
        updateheart.start();
        $("#progressHide").css('display',"none");
    } else {
        fileupload_finished_tag = 0;
    }
}
var upload_options = {
    //提交表单之前做的验证
    beforeSubmit:function(){
    },
    //服务器返回结果处理
    success:function(data){
    },
    //进度条的监听器
    xhr: function(){
        var xhr = $.ajaxSettings.xhr();
        if(onprogress && xhr.upload) {
            xhr.upload.addEventListener("progress" , onprogress, false);
            return xhr;
        }
    }
};
/****************************************************************************/

var vmodel = avalon.define({
	$id:'system',
	tab_list:'sysmaintain',
	statistic_record_types:[],       // 统计记录类型
	allchecked:true,
	nonechecked:false,
	buttonflag:0,
	csu_updata_flag:0,     //  CSU升级按钮标记符（用于限制文件上传频率）

	bak_para_url:"",     //  备份参数路径
	bak_para_name:"",     //  备份参数名称
	para_paraval:[],      //  参数导入传参

	para_export_passwd:"",   //  参数导出密码
	para_import_passwd:"",   //  参数导入密码
	para_back_passwd:"",     //  参数备份密码
	all_export_passwd:"",    //  一键导出密码
	eeprom_export_passwd:"", //  eeprom导出密码

	spcupara_export_passwd:"",     // spcu参数导出密码
	spcupara_import_passwd:"",     // spcu参数导入密码
	spcupara_paraval:[],      //  参数导入传参
	spcubuttonflag:0,
	spcupara_verify_value:[],

	del_historysql:'allsql',

	subdev_packets_uploaded:{},  //  已上传的子设备升级包

	slave_device_type:'1',
	cand_upload_file_objid:"smr_paket",
	cand_update_event_list:[],   //  CAN设备升级的历史事件

	slave_sboard_type:1,
	sboard_upload_file_objid:"uib_update",
	sboard_alert_flag:1,                  // 子板升级弹窗标记
	uib_update_now_no_process:0.0,        // uib进度，*100之后的结果
	iddb_update_now_no_process:0.0,       // iddb进度，*100之后的结果

    smr_cfg_tag:0,
	smr_update_mode:2,    //升级模式 全部 单台 多台
	smr_update_type:0,    //升级类型 全部 前级 后级
	smr_update_no_min:0,  // 地址起始值
	smr_update_no_max:1,  // 地址结束值
	smr_info_ids:[],      // 存储list结果
	smr_update_now_no:0,  // 正在升级
	smr_update_last_no_process:0.0,     // 上一次获取的升级进度，*100之后的结果
	smr_update_now_no_process:0.0,     // 当前获取的升级进度，*100之后的结果
	smr_update_state_datas:[{}],
	smr_update_total_count : 0,   // 需要升级的总台数
	smr_update_success_count:0,
	smr_update_failed_count:0,
	smr_update_state_class:['badge-grey','badge-warning','badge-success','badge-pink'],

	getsmrnodivdisplay:function(){
		return vmodel.smr_update_mode == 1 ? "inline":"none";
	},
	getsmrnominmaxdivdisplay:function(){
		return vmodel.smr_update_mode == 2 ? "inline":"none";
	},
	getsmrupdateclass:function(state) {
		if (state<0 || state > 4) {
			return "";
		}
		return vmodel.smr_update_state_class[state];
	},
	smr_update_mode_change:function() {
		vmodel.smr_update_no_max = vmodel.smr_update_total_count;
		vmodel.smr_update_no_min = 1;
	},
    smr_no_to_string:function(smr_no) {
        var str = PrefixInteger(smr_no, 2);
        return str;
    },
	change_list_cand_update_state_datas:function(selectId) {
		clear_smr_request();
		clear_pu_request();
		clear_spcu_request();
		clear_spu_request();

		let selected = $("#"+selectId).val().toString();
		if (selected === '1') {
			request.addRequest([listSmrUpdateStateDatas]);
		} else if (selected === '8') {
			request.addRequest([listPuUpdateStateDatas]);
		} else if (selected === '9') {
			request.addRequest([listspcuUpdateStateDatas]);
		} else if (selected === '10') {
			request.addRequest([listspcu]);
		}
	},

	south_device_type:23,   //设备编号
	south_upload_para:"",
	south_upload_file_objid:"south_paket",
	cur_comd_code:"0",  // 当前升级的设备
	fbbms_cfg_tag:0,
	nfbbms_cfg_tag:0,
	ssw_direct_cfg_tag:0,
	acem_cfg_tag:0,
	sddu_cfg_tag:0,
	fctl_cfg_tag:0,
	acmu_cfg_tag:0,
	south_update_mode:2,    //升级模式 全部 单台 多台
	south_update_type:1,    //升级类型 前级 后级
	south_update_no_min:0,  // 地址起始值
	south_update_no_max:1,  // 地址结束值
	south_info_ids:[],      // 存储list结果
	south_update_now_no:0,  // 正在升级
	south_update_now_no_process:0.0,    // 进度，*100之后的结果
	south_update_state_datas:[{}],
	south_update_total_count : 0,       // 需要升级的总台数
	south_update_success_count:0,
	south_update_failed_count:0,
	south_update_event_list:[],     	//  南向设备升级的历史事件
	south_update_state_class:['badge-grey','badge-warning','badge-success','badge-pink'],
	para_verify_value:[],
	para_err_type:0,	// 参数导入错误文件类型，1表示参数文件错误，2表示配置文件错误
	show_more_info_tip:0,
	sddu_no_for_ssw:0,
	ssw_update_no_min:0,
	ssw_update_no_max:0,
	is_south_sub_dev:false,
	ssw_num_per_sddu:0,
	sddu_num:0,
	ssw_nos:[],
	sddu_nos:[],
	south_sub_update_state_datas:{},
	sddu_status_ssw_updating:{},
	sddu_update_model:1,     //SDDU型号 1:DCMU 2:SMB

	getsouthnodivdisplay:function(){
		return vmodel.south_update_mode == 1 && vmodel.south_device_type != '-30' ? "inline":"none";
	},
	getsouthnominmaxdivdisplay:function(){
		return vmodel.south_update_mode == 2 && vmodel.south_device_type != '-30' && vmodel.south_device_type != '66' ? "inline":"none";
	},
	getsouthupdateclass:function(state) {
		if (state<0 || state > 4) {
			return "";
		}
		return vmodel.south_update_state_class[state];
	},
	south_no_to_string:function(south_no) {
        var str = PrefixInteger(south_no, 2);
        return str;
	},
	change_list_south_update_state_datas:function(selectId) {
		let selected = $("#"+selectId).val();
		if (selected.toString() == "41") {
			vmodel.is_south_sub_dev = true;
			request.clearRequest(listsouthUpdateStateDatas);
			getsouthSubUpdateStateDatas = {data:{objectid:"south_sub_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"dev_code":"41"}])},success:get_south_sub_update_state_datas_succ, refresh:3};
			request.addRequest([getsouthSubUpdateStateDatas]);
			/* ... */
		} else {
			vmodel.is_south_sub_dev = false;
			request.clearRequest(listsouthUpdateStateDatas);
			request.clearRequest(getsouthSubUpdateStateDatas);
			let paras = [{"dev_code":selected.toString()}];
			if (selected.toString() == '34') {
				if (vmodel.sddu_update_model == '2') {
					paras[0]["dev_code"] = '-34';   // SDDU的SMB型号特殊处理成单独的设备
				} else {
					paras[0]["dev_code"] = '34';
				}
			} else {
				paras[0]["dev_code"] = selected.toString(); //刷新数据，防止切换设备类型过快导致下发设备码未及时刷新
			}
			listsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify(paras)},success:get_south_update_state_datas,refresh:3};
			request.addRequest([listsouthUpdateStateDatas]);
		}
	},
	change_sddu_update_model:function(selectId) {
		let selected = $("#"+selectId).val();
		vmodel.is_south_sub_dev = false;
		request.clearRequest(listsouthUpdateStateDatas);
		let paras = [{"dev_code":"34"}];
		if (selected.toString() == '2') {
			paras[0]["dev_code"] = '-34';   // SDDU的SMB型号特殊处理成单独的设备
		}
		listsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify(paras)},success:get_south_update_state_datas,refresh:3};
		request.addRequest([listsouthUpdateStateDatas]);
	},

	pu_cfg_tag:0,
	pu_update_mode:2,    //升级模式 全部 单台 多台
	pu_update_type:2,    //升级类型 全部 前级 后级
	pu_update_no_min:0,  // 地址起始值
	pu_update_no_max:1,  // 地址结束值
	pu_info_ids:[],      // 存储list结果
	pu_update_now_no:0,  // 正在升级
	pu_update_now_no_process:0.0,     // 进度，*100之后的结果
	pu_update_state_datas:[{}],
	pu_update_total_count : 0,   // 需要升级的总台数
	pu_update_success_count:0,
	pu_update_failed_count:0,
	pu_update_state_class:['badge-grey','badge-warning','badge-success','badge-pink'],

	getpunodivdisplay:function(){
		return vmodel.pu_update_mode == 1 ? "inline":"none";
	},
	getpunominmaxdivdisplay:function(){
		return vmodel.pu_update_mode == 2 ? "inline":"none";
	},
	getpuupdateclass:function(state) {
		if (state<0 || state > 4) {
			return "";
		}
		return vmodel.smr_update_state_class[state];
	},
	pu_update_mode_change:function() {
		vmodel.pu_update_no_max = vmodel.pu_update_total_count;
		vmodel.pu_update_no_min = 1;
	},

	spcu_cfg_tag:0,
	spcu_update_mode:2,    //升级模式 全部 单台 多台
	spcu_update_type:2,    //升级类型 全部 前级 后级
	spcu_update_no_min:0,  // 地址起始值
	spcu_update_no_max:1,  // 地址结束值
	spcu_info_ids:[],      // 存储list结果
	spcu_update_now_no:0,  // 正在升级
	spcu_update_now_no_process:0.0,     // 进度，*100之后的结果
	spcu_update_state_datas:[{}],
	spcu_update_total_count : 0,   // 需要升级的总台数
	spcu_update_success_count:0,
	spcu_update_failed_count:0,
	spcu_update_state_class:['badge-grey','badge-warning','badge-success','badge-pink'],

	getspcunodivdisplay:function(){
		return vmodel.spcu_update_mode == 1 ? "inline":"none";
	},
	getspcunominmaxdivdisplay:function(){
		return vmodel.spcu_update_mode == 2 ? "inline":"none";
	},
	getspcuupdateclass:function(state) {
		if (state<0 || state > 4) {
			return "";
		}
		return vmodel.spcu_update_state_class[state];
	},
	spcu_update_mode_change:function() {
		vmodel.spcu_update_no_max = vmodel.spcu_update_total_count;
		vmodel.spcu_update_no_min = 1;
	},

	spu_cfg_tag:0,
	spu_update_mode:1,    //升级模式 全部 单台
	spu_update_type:2,    //升级类型 全部 前级 后级
	spcu_no_for_spu_update:0, // SPU所属SPCU
	spu_update_no_min:0,  // 地址起始值
	spu_update_no_max:1,  // 地址结束值
	spu_update_now_no:0,  // 正在升级
	spu_update_state_datas:[],
	spu_update_cfg_set_times:0,
	spu_num_per_spcu:0,
	spcu_num:0,
	spu_nos:[],
	spu_update_state_class:['badge-grey','badge-warning','badge-success','badge-pink'],

	getspunodivdisplay:function(){
		return vmodel.spu_update_mode == 1 ? "inline":"none";
	},
	getspuupdateclass:function(info) {
		if (info.phase_two_rst < 0 || info.phase_two_rst > 3) {
			return "";
		}
		return vmodel.spu_update_state_class[info.phase_two_rst];
	},
	reload_no:function() {
		vmodel.smr_update_no_max = vmodel.smr_update_no_max;
		vmodel.smr_update_no_min = vmodel.smr_update_no_min;
	},
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
        send_request_by_tab(tab);
    },
});

function send_request_by_tab(tab){
	switch(tab){
		case "slavemaintain":
			send_cand_request();
			clear_request_by_flag(["sboard", "south", "para_bak"]);
			break;
		case "southmaintain":
			send_south_request();
			clear_request_by_flag(["cand", "sboard","para_bak"])
			break;
		case "sboardupdate":
			send_sboard_request();
			clear_request_by_flag(["cand", "south", "para_bak"])
			break;
		case "paraMaintain":
			send_para_bak_request();
			clear_request_by_flag(["cand", "sboard","south"])
			break;
		default:
			clear_request_by_flag(["cand", "sboard","south", "para_bak"])
			break;
	}
}

function clear_request_by_flag(arr){
	var map = {
			"cand":clear_cand_request,
			"sboard":clear_sboard_request,
			"south":clear_south_request,
			"para_bak":clear_para_bak_request
			};
	for(var i=0;i<arr.length;i++){
		if(map[arr[i]]){
			map[arr[i]]();
		}
	}
}

function init_cand_cfg_tag(obj) {
    var getCandCfg = {data:{objectid:"cand_type_cfg",type:"val_get",paraval:JSON.stringify([{}])},success:get_cand_cfg_tag_succ};
    request.addRequest([getCandCfg]);

    function get_cand_cfg_tag_succ(d,r) {
        if (d.result == 'ok') {
            vmodel.smr_cfg_tag = d.data[0].cand_type_tag[0];
			vmodel.pu_cfg_tag = d.data[0].cand_type_tag[1];
			vmodel.spcu_cfg_tag = d.data[0].cand_type_tag[2];
			vmodel.spu_cfg_tag = d.data[0].cand_type_tag[2];
        }
        if (vmodel.smr_cfg_tag == '1') {
            vmodel.slave_device_type = '1';
            return;
        }
        if (vmodel.pu_cfg_tag == '1') {
            vmodel.slave_device_type = '8';
            return;
		}
		if (vmodel.spcu_cfg_tag == '1') {
			vmodel.slave_device_type = '9';
			return;
		}
		if (vmodel.spu_cfg_tag == '1') {
			vmodel.slave_device_type = '10';
			return;
		}
    }
}

function init_comd_cfg_tag(obj) {
    var getCandCfg = {data:{objectid:"comd_type_cfg",type:"val_get",paraval:JSON.stringify([{}])},success:get_comd_cfg_tag_succ};
    request.addRequest([getCandCfg]);

    function get_comd_cfg_tag_succ(d,r) {
        if (d.result == 'ok') {
            vmodel.fbbms_cfg_tag = d.data[0].comd_type_tag[0];

			// vmodel.nfbbms_cfg_tag = d.data[0].comd_type_tag[2];
			vmodel.ssw_direct_cfg_tag = d.data[0].comd_type_tag[3];
			vmodel.acem_cfg_tag = '1';
			vmodel.sddu_cfg_tag = d.data[0].comd_type_tag[5];
			vmodel.fctl_cfg_tag = d.data[0].comd_type_tag[6];
			vmodel.acmu_cfg_tag = d.data[0].comd_type_tag[7];
        }
		// 不在位也要显示NFBBMS升级页面
		vmodel.nfbbms_cfg_tag = '1';
        if (vmodel.fbbms_cfg_tag == '1') {
            vmodel.south_device_type = 23;
        } else if (vmodel.nfbbms_cfg_tag == '1') {
			vmodel.south_device_type = 33;
		} else if (vmodel.ssw_direct_cfg_tag == '1') {
			vmodel.south_device_type = 44;
		} else if (vmodel.acem_cfg_tag == '1') {
            vmodel.south_device_type = -30;
        } else if (vmodel.sddu_cfg_tag == '1') {
			vmodel.south_device_type = 34;
		} else if (vmodel.fctl_cfg_tag == '1') {
			vmodel.south_device_type = 61;
		} else if (vmodel.acmu_cfg_tag == '1') {
			vmodel.south_device_type = 66;
		} else {
			return;
		}
		let paras = [{"dev_code":(vmodel.south_device_type).toString()}];
		if (vmodel.south_device_type == '34' && vmodel.sddu_update_model == '2') {
			paras[0]["dev_code"] = '-34'  // SDDU的SMB型号特殊处理成单独的设备
		}
		listsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify(paras)},success:get_south_update_state_datas,refresh:3};
		request.addRequest([listsouthUpdateStateDatas]);
    }
}

function init_send_request(){//F5刷新或初次进入页面
	send_request_by_tab(vmodel.tab_list);
}

setTimeout(function(){
	init_cand_cfg_tag();
	init_comd_cfg_tag();
	init_send_request();
}, 0);


//   获取统计记录类型
function init_record_types(obj) {
	function get_types_succ(d,r) {
		if (d.result == "ok") {
			vmodel.statistic_record_types = d.data;
		}
	}
	request.addRequest([{data:{objectid:"records",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_types_succ}]);
}
init_record_types();

/********************************************整流器升级相关操作****************************************************************************************************/
//  获取整流器升级配置
var getSmrUpdateCfg = {data:{objectid:"smr_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_smr_update_cfg};
//  获取整流器升级状态信息
var listSmrUpdateStateDatas = {data:{objectid:"smr_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:get_smr_update_state_datas,refresh:3};
//  获取进度条信息
var getSmrUpdateNowProcess = {data:{objectid:"smr_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":vmodel.smr_update_now_no.toString()}])},success:get_smr_update_now_process_succ,refresh:2};

function hide_smr_update_process(obj) {
	$("#smr_update_progress").css("display","none");
}

function show_smr_update_process(obj) {
	$("#smr_update_progress").css("display","block");
}

function get_smr_update_now_process_succ(d,r) {
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].update_process!= '') {
			process = d.data[0].update_process * 1.0 * 100;
			if (!isNaN(process)) {
				// 上一次进度与当前进度进行比较，取较大值显示，防止进度条回退
				vmodel.smr_update_now_no_process = parseInt(process) > vmodel.smr_update_last_no_process ? parseInt(process) : vmodel.smr_update_last_no_process;
				// 升级类型为前级和后级情况下，考虑前级升级完成到后级升级开始的进度条切换
				if (vmodel.smr_update_type == 0 && vmodel.smr_update_now_no_process >= 90 && parseInt(process) <= 10) {
					vmodel.smr_update_now_no_process = parseInt(process);
				}
				vmodel.smr_update_last_no_process = vmodel.smr_update_now_no_process;
				if (vmodel.smr_update_now_no_process >= 99) {
					vmodel.smr_update_now_no_process = 100;
					vmodel.smr_update_last_no_process = 0;
					setTimeout(show_smr_update_process, 1000);
				}
			}
		}
	}
}

function get_smr_update_cfg(d,r) {
	if (d.result == 'ok') {
		vmodel.smr_update_mode = d.data[0].update_mode;
		vmodel.smr_update_no_min = d.data[0].index_min;
		vmodel.smr_update_no_max = d.data[0].index_max;
		vmodel.smr_update_type = d.data[0].update_type;
		vmodel.smr_update_now_no = d.data[0].update_doing;
	}
}
// request.addRequest([getSmrUpdateCfg]);


function set_smr_update_cfg_succ(d,r) {
    if (d.result == 'ok') {
        request.addRequest([getSmrUpdateCfg]);
    } else {
        popupTipsDiv($("#time_failurealert"), 3000);
    }
}

function set_smr_update_cfg(action) {
	var mode =   vmodel.smr_update_mode;
	var no_min = vmodel.smr_update_no_min;
	var no_max = vmodel.smr_update_no_max;
	var type = vmodel.smr_update_type;
	if (mode==0) {
        no_min = 0;
		no_max = no_min;
	}
    if (mode==1) {
		no_max = no_min;
	}
    if (no_min<0 || no_max < 0) {
		if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
    if (no_max*1 < no_min*1) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
	var para = [{
			"update_mode":	mode.toString(),
			"index_min":	no_min.toString(),
			"index_max":	no_max.toString(),
			"update_type":	type.toString(),
			"action":action.toString()
	}];
	if(action == 0){
		popupTipsDiv($("#south_update_warn_tip"), 5000);
	}
	var setSmrUpdateCfg = {data:{objectid:"smr_update_cfg",type:"val_set",paranum:"0",paraval:JSON.stringify(para)},success:set_smr_update_cfg_succ};
	request.addRequest([setSmrUpdateCfg]);
}

// request.addRequest([listSmrUpdateStateDatas]);

function get_smr_update_state_datas(d,r) {
	if (d.result == 'ok') {
		var paras = d.data;
		addvalue(vmodel.smr_info_ids,d.data,"instid");
		getSmrUpdateStateDatas = {data:{objectid:"smr_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify(paras)},success:get_smr_update_state_datas_succ};
		request.addRequest([getSmrUpdateStateDatas]);
	}
}

function get_smr_update_state_datas_succ(d,r) {
	if (d.result == 'ok') {
		var datas = JSON.parse(JSON.stringify(vmodel.smr_update_state_datas));  // 保留一份之前的升级状态数据
		vmodel.smr_update_state_datas = d.data;
		var num =  d.datanum;
		vmodel.smr_update_total_count = num;
		var i = 0;
		for (i = 0; i < num; i++) {
			if (d.data[i].update_rst == 1) {   // 正在升级中
				vmodel.smr_update_now_no = d.data[i].smr_no;
				if (vmodel.smr_update_now_no !=0) {
					if (Object.keys(datas[0]).length == 0 || datas[i].update_rst != 1) {    // 第一次识别到升级状态变更为升级中
						vmodel.smr_update_last_no_process = 0;
					}
					show_smr_update_process();
					request.clearRequest(getSmrUpdateNowProcess);
					getSmrUpdateNowProcess = {data:{objectid:"smr_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":vmodel.smr_update_now_no.toString()}])},success:get_smr_update_now_process_succ,refresh:2};
					request.addRequest([getSmrUpdateNowProcess]);
				}
				break;
			}
		}
		if (i == num) {   // 无升级的整流器
			hide_smr_update_process();
			vmodel.smr_update_now_no = 0;
		}
	}
}

var getCandUpdateLogs = {data:{objectid:"smr_update_logs",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_cand_update_logs_succ, refresh:5};
function get_cand_update_logs_succ(d,r) {
	if (d.result === 'ok') {
		vmodel.cand_update_event_list = d.data;
	}
}

function send_cand_request(){
	if (vmodel.slave_device_type === '1') {
		send_smr_request();
	} else if (vmodel.slave_device_type === '8') {
		send_pu_request();
	} else if (vmodel.slave_device_type === '9') {
		send_spcu_request();
	} else if (vmodel.slave_device_type === '10') {
		send_spu_request();
	}
	request.addRequest([getCandUpdateLogs]);
}

function clear_cand_request(){
	if (vmodel.slave_device_type === '1') {
		clear_smr_request();
	} else if (vmodel.slave_device_type === '8') {
		clear_pu_request();
	} else if (vmodel.slave_device_type === '9') {
		clear_spcu_request();
	} else if (vmodel.slave_device_type === '10') {
		clear_spu_request();
	}
	request.clearRequest(getCandUpdateLogs);
}

function send_smr_request(){
	request.addRequest([getSmrUpdateCfg]);
	request.addRequest([listSmrUpdateStateDatas]);
}

function clear_smr_request(){
	request.clearRequest(getSmrUpdateCfg);
	request.clearRequest(listSmrUpdateStateDatas);
	let getSmrUpdateStateDatas = {data:{objectid:"smr_update_info",type:"val_get"}};
	request.clearRequest(getSmrUpdateStateDatas);
	request.clearRequest(getSmrUpdateNowProcess);
}

vmodel.$watch("smr_update_mode", function(a) {
    vmodel.smr_update_no_min = -1;
    vmodel.smr_update_no_min = -1;
});

/********************************************整流器升级相关接口结束****************************************************************************************************/

/********************************************PU升级相关操作****************************************************************************************************/
//  获取PU升级配置
var getPuUpdateCfg = {data:{objectid:"pu_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_pu_update_cfg};
//  获取PU升级状态信息
var listPuUpdateStateDatas = {data:{objectid:"pu_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:get_pu_update_state_datas,refresh:3};
//  获取进度条信息
var getPuUpdateNowProcess = {data:{objectid:"pu_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":vmodel.pu_update_now_no.toString()}])},success:get_pu_update_now_process_succ,refresh:2};

function hide_pu_update_process(obj) {
	$("#pu_update_progress").css("display","none");
}

function show_pu_update_process(obj) {
	$("#pu_update_progress").css("display","block");
}

function get_pu_update_now_process_succ(d,r) {
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].update_process!= '') {
			process = d.data[0].update_process * 1.0 * 100;
			if (!isNaN(process)) {
				vmodel.pu_update_now_no_process = parseInt(process);
				if (vmodel.pu_update_now_no_process >= 99) {
					vmodel.pu_update_now_no_process = 100;
					setTimeout(show_pu_update_process, 1000);
				}
			}
		}
	}
}

function get_pu_update_cfg(d,r) {
	if (d.result == 'ok') {
		vmodel.pu_update_mode = d.data[0].update_mode;
		vmodel.pu_update_no_min = d.data[0].index_min;
		vmodel.pu_update_no_max = d.data[0].index_max;
		vmodel.pu_update_type = d.data[0].update_type;
		vmodel.pu_update_now_no = d.data[0].update_doing;
	}
}
// request.addRequest([getPuUpdateCfg]);


function set_pu_update_cfg_succ(d,r) {
    if (d.result == "ok") {
        request.addRequest([getPuUpdateCfg]);
    } else {
        popupTipsDiv($("#time_failurealert"), 3000);
    }
}

function set_pu_update_cfg(action) {
	var mode =   vmodel.pu_update_mode;
	var no_min = vmodel.pu_update_no_min;
	var no_max = vmodel.pu_update_no_max;
	var type =     2;    //vmodel.pu_update_type;
	if (mode==0) {
        no_min = 0;
		no_max = no_min;
	}
    if (mode==1) {
		no_max = no_min;
	}
    if (no_min<0 || no_max < 0) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
    if (no_max*1 < no_min*1) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
	var para = [{
			"update_mode":	mode.toString(),
			"index_min":	no_min.toString(),
			"index_max":	no_max.toString(),
			"update_type":	type.toString(),
			"action":action.toString()
	}];
	if(action == 0){
		popupTipsDiv($("#south_update_warn_tip"), 5000);
	}
	var setPuUpdateCfg = {data:{objectid:"pu_update_cfg",type:"val_set",paranum:"0",paraval:JSON.stringify(para)},success:set_pu_update_cfg_succ};
	request.addRequest([setPuUpdateCfg]);
}

// request.addRequest([listPuUpdateStateDatas]);

function get_pu_update_state_datas(d,r) {
	if (d.result == 'ok') {
		var paras = d.data;
		addvalue(vmodel.pu_info_ids,d.data,"instid");
		getPuUpdateStateDatas = {data:{objectid:"pu_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify(paras)},success:get_pu_update_state_datas_succ};
		request.addRequest([getPuUpdateStateDatas]);
	}
}

function get_pu_update_state_datas_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.pu_update_state_datas = d.data;
		var num =  d.datanum;
		vmodel.pu_update_total_count = num;
		var i = 0;
		for (i = 0; i < num; i++) {
			if (d.data[i].update_rst == 1) {   // 正在升级中
				vmodel.pu_update_now_no = d.data[i].pu_no;
				if (vmodel.pu_update_now_no !=0) {
					show_pu_update_process();
					request.clearRequest(getPuUpdateNowProcess);
					getPuUpdateNowProcess = {data:{objectid:"pu_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":vmodel.pu_update_now_no.toString()}])},success:get_pu_update_now_process_succ,refresh:2};
					request.addRequest([getPuUpdateNowProcess]);
				}
				break;
			}
		}
		if (i == num) {   // 无升级的PU
			hide_pu_update_process();
			vmodel.pu_update_now_no = 0;
		}
	}
}

function send_pu_request(){
	request.addRequest([getPuUpdateCfg]);
	request.addRequest([listPuUpdateStateDatas]);
}

function clear_pu_request(){
	request.clearRequest(getPuUpdateCfg);
	request.clearRequest(listPuUpdateStateDatas);
	let getPuUpdateStateDatasAndNowProcess = {data:{objectid:"pu_update_info",type:"val_get"}};
	request.clearRequest(getPuUpdateStateDatasAndNowProcess);
}

vmodel.$watch("pu_update_mode", function(a) {
    vmodel.pu_update_no_min = -1;
    vmodel.pu_update_no_min = -1;
});

/********************************************PU升级相关接口结束****************************************************************************************************/

/********************************************SPCU升级相关操作****************************************************************************************************/
//  获取SPCU升级配置
var getspcuUpdateCfg = {data:{objectid:"spcu_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_spcu_update_cfg};
//  获取SPCU升级状态信息
var listspcuUpdateStateDatas = {data:{objectid:"spcu_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:get_spcu_update_state_datas,refresh:3};

function hide_spcu_update_process(obj) {
	$("#spcu_update_progress").css("display","none");
}

function show_spcu_update_process(obj) {
	$("#spcu_update_progress").css("display","block");
}

function get_spcu_update_cfg(d,r) {
	if (d.result == 'ok') {
		vmodel.spcu_update_mode = d.data[0].update_mode;
		vmodel.spcu_update_no_min = d.data[0].index_min;
		vmodel.spcu_update_no_max = d.data[0].index_max;
		vmodel.spcu_update_type = d.data[0].update_type;
		vmodel.spcu_update_now_no = d.data[0].update_doing;
	}
}
// request.addRequest([getspcuUpdateCfg]);


function set_spcu_update_cfg_succ(d,r) {
    if (d.result == "ok") {
        request.addRequest([getspcuUpdateCfg]);
    } else {
        popupTipsDiv($("#time_failurealert"), 3000);
    }
}

function set_spcu_update_cfg(action) {
	var mode =   vmodel.spcu_update_mode;
	var no_min = vmodel.spcu_update_no_min;
	var no_max = vmodel.spcu_update_no_max;
	var type =     2;    //vmodel.spcu_update_type;
	if (mode==0) {
        no_min = 0;
		no_max = no_min;
	}
    if (mode==1) {
		no_max = no_min;
	}
    if (no_min<0 || no_max < 0) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
    if (no_max*1 < no_min*1) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
	var para = [{
			"update_mode":	mode.toString(),
			"index_min":	no_min.toString(),
			"index_max":	no_max.toString(),
			"update_type":	type.toString(),
			"action":action.toString()
	}];
	var setspcuUpdateCfg = {data:{objectid:"spcu_update_cfg",type:"val_set",paranum:"0",paraval:JSON.stringify(para)},success:set_spcu_update_cfg_succ};
	request.addRequest([setspcuUpdateCfg]);
}

// request.addRequest([listspcuUpdateStateDatas]);

function get_spcu_update_state_datas(d,r) {
	if (d.result == 'ok') {
		var paras = d.data;
		addvalue(vmodel.spcu_info_ids,d.data,"instid");
		getspcuUpdateStateDatas = {data:{objectid:"spcu_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify(paras)},success:get_spcu_update_state_datas_succ};
		request.addRequest([getspcuUpdateStateDatas]);
	}
}

function get_spcu_update_state_datas_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.spcu_update_state_datas = d.data;
		let num =  d.datanum;
		vmodel.spcu_update_total_count = num;
		let i = 0;
		for (i = 0; i < num; i++) {
			if (d.data[i].update_rst == 1) {   // 正在升级中
				vmodel.spcu_update_now_no = d.data[i].spcu_no;
				if (vmodel.spcu_update_now_no !=0) {
					show_spcu_update_process();
					if (d.data[i].update_process!= '') {
						let process = d.data[i].update_process * 1.0 * 100;
						if (!isNaN(process)) {
							vmodel.spcu_update_now_no_process = parseInt(process);
							if (vmodel.spcu_update_now_no_process >= 99) {
								vmodel.spcu_update_now_no_process = 100;
								setTimeout(show_spcu_update_process, 1000);
							}
						}
					}
				}
				break;
			}
		}
		if (i == num) {   // 无升级的SPCU
			hide_spcu_update_process();
			vmodel.spcu_update_now_no = 0;
		}
	}
}

function send_spcu_request(){
	request.addRequest([getspcuUpdateCfg]);
	request.addRequest([listspcuUpdateStateDatas]);
}

function clear_spcu_request(){
	request.clearRequest(getspcuUpdateCfg);
	request.clearRequest(listspcuUpdateStateDatas);
	let setspcuUpdateCfg = {data:{objectid:"spcu_update_cfg",type:"val_set"}};//中间请求清理
	request.clearRequest(setspcuUpdateCfg);
	let getspcuUpdateStateDatas = {data:{objectid:"spcu_update_info",type:"val_get"}};
	request.clearRequest(getspcuUpdateStateDatas);
}

vmodel.$watch("spcu_update_mode", function(a) {
    vmodel.spcu_update_no_min = -1;
    vmodel.spcu_update_no_min = -1;
});

/********************************************SPCU升级相关接口结束****************************************************************************************************/

/********************************************SPU升级相关操作****************************************************************************************************/
//  获取SPU升级配置
var getspuUpdateCfg = {data:{objectid:"spu_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_spu_update_cfg};

function hide_spu_update_process(obj) {
	$("#spu_update_progress").css("display","none");
	$("#spu_update_progress_phase").css("display","none");
}

function show_spu_update_process(obj) {
	$("#spu_update_progress").css("display","block");
	$("#spu_update_progress_phase").css("display","block");
}

function object_copy(obj) {
	var tmpobj = JSON.stringify(obj);
	var newobj = JSON.parse(tmpobj);
	return newobj;
}

function list_spcu_success(d,r) {
	vmodel.spcu_num = d.data.length;
	request.addRequest([listspu]);
}

function list_spu_success(d,r) {
	vmodel.spu_num_per_spcu = d.data.length;
	vmodel.spu_nos = Array.from(Array(vmodel.spu_num_per_spcu), (v,k)=>k+1);
	vmodel.spu_update_state_datas = new Array(vmodel.spcu_num);
	for (let i = 0; i < vmodel.spcu_num; i++) {
		vmodel.spu_update_state_datas[i] = new Array(vmodel.spu_num_per_spcu);
		let one_spu_update_state = {phase_one_rst:'0', phase_one_progress:'0', phase_two_rst:'0', phase_two_progress:'0', status:'0', progress:'0', phase:""};
		for (let j = 0; j < vmodel.spu_num_per_spcu; j++) {
			vmodel.spu_update_state_datas[i][j] = $.extend(true, {}, one_spu_update_state);
		}
	}
	vmodel.spu_update_state_datas = object_copy(vmodel.spu_update_state_datas);  // 刷新界面显示
	request.addRequest([getspuUpdateTransFile]);
	request.addRequest([getspuUpdateStateDatas]);
}
function get_spu_update_transfile_state(d,r) {
	if (d.result == 'ok') {
		for(let i = 0; i < vmodel.spcu_update_state_datas.length; i++) {
			for (let j = 0; j < vmodel.spu_num_per_spcu; j++) {
				vmodel.spu_update_state_datas[i][j].phase_one_rst = d.data[i].update_rst;
				vmodel.spu_update_state_datas[i][j].phase_one_progress = d.data[i].update_process;
				if (vmodel.spu_update_state_datas[i][j].phase_one_rst == '1') {
					vmodel.spu_update_state_datas[i] = vmodel.spu_update_state_datas[i].map((item) => {
						item.status = '1';
						item.progress = parseInt(vmodel.spu_update_state_datas[i][j].phase_one_progress * 1.0 * 100);
						item.phase = mainvalue.i18nkeyword.system.upgrade_file_trans_prog;
						return item;
					});
					break;
				}
				// spcu[i]没有传输升级文件，且下属spu中没有正在升级中的
				if ((j == vmodel.spu_num_per_spcu - 1) && !(()=>{
					for (let one_spu of vmodel.spu_update_state_datas[i])
						if (one_spu.phase_two_rst == '1')
							return true
					return false;
				})()) {
					vmodel.spu_update_state_datas[i][0].status = '0';
				}
			}
		}
		return;
	}
}
function get_spu_update_state_datas(d,r) {
	if (d.result == 'ok') {
		for (let i = 0; i < vmodel.spcu_num; i++) {
			let spu_updating = false;
			for (let j = 0; j < vmodel.spu_num_per_spcu; j++) {
				vmodel.spu_update_state_datas[i][j].phase_two_rst = d.data[i * vmodel.spu_num_per_spcu + j].status;
				vmodel.spu_update_state_datas[i][j].phase_two_progress = d.data[i * vmodel.spu_num_per_spcu + j].progress;
				if (vmodel.spu_update_state_datas[i][j].phase_two_rst == '1') {
					spu_updating = true;
					vmodel.spu_update_state_datas[i] = vmodel.spu_update_state_datas[i].map((item) => {
						item.status = '1';
						item.progress = parseInt(vmodel.spu_update_state_datas[i][j].phase_two_progress);
						item.phase =  mainvalue.i18nkeyword.system.spu_upgrade_prog;
						return item;
					});
					continue;
				}
				// spcu[i]下没有正在升级的spu, 且没有传输升级文件
				if (j == vmodel.spu_num_per_spcu - 1 && !spu_updating && vmodel.spu_update_state_datas[i][0].phase_one_rst != '1') {
					vmodel.spu_update_state_datas[i][0].status = d.data[i * vmodel.spu_num_per_spcu].status;
				}
			}
		}
	}
}

function get_spu_update_cfg(d,r) {
	if (d.result == 'ok') {
		vmodel.spu_update_mode = d.data[0].update_mode;
		vmodel.spu_update_no_min = d.data[0].index_min;
		vmodel.spu_update_no_max = d.data[0].index_max;
		vmodel.spu_update_type = d.data[0].update_type;
		vmodel.spu_update_now_no = d.data[0].update_doing;
	}
}
// request.addRequest([getspuUpdateCfg]);

var listspu = {data:{objectid:"spu_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:list_spu_success};
var listspcu = {data:{objectid:"spcu_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:list_spcu_success};
// request.addRequest([listspcu]);
var getspuUpdateStateDatas = {data:{objectid:"spu_update_info_v2",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_spu_update_state_datas,refresh:5};
var getspuUpdateTransFile = {data:{objectid:"spu_update_info_v2",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_spu_update_transfile_state,refresh:5};

function set_spu_update_cfg_succ(d,r) {
    if (d.result == "ok") {
		request.addRequest([getspuUpdateCfg]);
    } else {
        popupTipsDiv($("#time_failurealert"), 3000);
    }
}

function set_spu_update_cfg(action) {
	var mode =   vmodel.spu_update_mode;
	let spcu_no = vmodel.spcu_no_for_spu_update;
	var no_min = (spcu_no-1)*8 + vmodel.spu_update_no_min*1;
	var no_max = vmodel.spu_update_no_max;
	var type =     2;    //vmodel.spu_update_type;
	if (mode==0) {
		if (spcu_no == vmodel.spcu_update_state_datas.length + 1) {
			no_min = 1;
			no_max = vmodel.spcu_update_state_datas.length * vmodel.spu_num_per_spcu;
		} else {
			no_min = (spcu_no - 1) * 8 + 1;
			no_max = spcu_no * 8;
		}
	}
    if (mode==1) {
		no_max = no_min;
	}
    if (no_min<0 || no_max < 0) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
    if (no_max*1 < no_min*1) {
        if(action == 0) {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip);
		}
		else {
			alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
		}
        return;
    }
	var para = [{
			"update_mode":	mode.toString(),
			"index_min":	no_min.toString(),
			"index_max":	no_max.toString(),
			"update_type":	type.toString(),
			"action":action.toString()
	}];

	for (let i = 0; i < vmodel.spcu_num; i++) {
		for (let j = 0; j < vmodel.spu_num_per_spcu; j++) {
			if (vmodel.spu_update_state_datas[i][j].phase_two_rst == '1') {
				if (vmodel.spu_update_cfg_set_times < 10) {
					vmodel.spu_update_cfg_set_times++;
					return;
				}
			}
		}
	}
	vmodel.spu_update_cfg_set_times = 0;
	var setspuUpdateCfg = {data:{objectid:"spu_update_cfg",type:"val_set",paranum:"0",paraval:JSON.stringify(para)},success:set_spu_update_cfg_succ};
	request.addRequest([setspuUpdateCfg]);
}

function send_spu_request(){
	request.addRequest([getspuUpdateCfg]);
	request.addRequest([listspcu]);
}

function clear_spu_request(){
	request.clearRequest(getspuUpdateCfg);
	request.clearRequest(listspcu);
	request.clearRequest(listspu);
	request.clearRequest(getspuUpdateTransFile);
	request.clearRequest(getspuUpdateStateDatas);
}

vmodel.$watch("spu_update_mode", function(a) {
    vmodel.spu_update_no_min = -1;
    vmodel.spu_update_no_min = -1;
});

/********************************************SPU升级相关接口结束****************************************************************************************************/

/********************************************南向设备升级相关操作****************************************************************************************************/
//  获取南向设备升级配置
var getsouthUpdateCfg = {data:{objectid:"south_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{"dev_code":vmodel.cur_comd_code.toString()}])},success:get_south_update_cfg};
//  获取南向设备升级状态信息
var listsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:get_south_update_state_datas,refresh:3};
//  获取进度条信息
var getsouthUpdateNowProcess = {data:{objectid:"south_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":vmodel.south_update_now_no.toString()}])},success:get_south_update_now_process_succ,refresh:2};

var getsouthSubUpdateStateDatas = {data:{objectid:"south_sub_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_south_sub_update_state_datas_succ, refresh:3};

var listSdduNum = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{"dev_code":"34"}])},success:list_sddu_num_succ};
var listSswNum = {data:{objectid:"south_update_info",type:"list",paranum:"0",paraval:JSON.stringify([{"dev_code":"41"}])},success:list_ssw_num_succ};
request.addRequest([listSdduNum]);
request.addRequest([listSswNum]);

function hide_south_update_process(obj) {
	$("#south_update_progress").css("display","none");
}

function show_south_update_process(obj) {
	$("#south_update_progress").css("display","block");
}

function list_sddu_num_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.sddu_num = d.datanum;
		vmodel.sddu_nos = Array.from(Array(vmodel.sddu_num), (v,k)=>k + 1);
		vmodel.sddu_nos[2] = NaN; // SDDU_3只用于升扩展电池，屏蔽扩展空开的数据显示
	}
}

function list_ssw_num_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.ssw_num_per_sddu = d.datanum;
		vmodel.ssw_nos = Array.from(Array(vmodel.ssw_num_per_sddu), (v,k)=>k + 1);
	}
}

function get_south_update_now_process_succ(d,r) {
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].update_process!= '') {
			process = d.data[0].update_process * 1.0 * 100;
			if (!isNaN(process)) {
				vmodel.south_update_now_no_process = parseInt(process);
				if (vmodel.south_update_now_no_process >= 99) {
					vmodel.south_update_now_no_process = 100;
					setTimeout(show_south_update_process, 1000);
				}
			}
		}
	}
}

// 设备下发升级时获取升级配置信息并保存，以便刷新后能重新显示升级选项
function get_south_update_cfg(d,r) {
	if (d.result == 'ok') {
		vmodel.cur_comd_code = d.data[0].dev_code;
		vmodel.south_update_mode = d.data[0].update_mode;
		vmodel.south_update_no_min = d.data[0].index_min;
		vmodel.south_update_no_max = d.data[0].index_max;
		vmodel.south_update_type = d.data[0].update_type;
		vmodel.south_update_now_no = d.data[0].update_doing;
		if(vmodel.cur_comd_code == '-34') {
			vmodel.sddu_update_model = 2; // 保存sddu的选择型号
		}
	}
}
// request.addRequest([getsouthUpdateCfg]);


function set_south_update_cfg_succ(d,r) {
    if (d.result == 'ok') {
        getsouthUpdateCfg = {data:{objectid:"south_update_cfg",type:"val_get",paranum:"0",paraval:JSON.stringify([{"dev_code":vmodel.cur_comd_code.toString()}])},success:get_south_update_cfg};
        request.addRequest([getsouthUpdateCfg]);
    } else {
        popupTipsDiv($("#time_failurealert"), 3000);
    }
}

function set_south_update_cfg(action) {
	var no_min = vmodel.south_update_no_min;
	var no_max = vmodel.south_update_no_max;

	var mode = vmodel.south_update_mode;
	if (vmodel.south_device_type == '-30') {
		mode = 0;  // 升级模式 全部
	} else if (vmodel.south_device_type == '66' && mode == 2) {
		mode = 0;
	}

	var type = vmodel.south_update_type;  // FBBMS 升级类型 前级或后级
	if (vmodel.south_device_type == '44' || vmodel.south_device_type == '41' || vmodel.south_device_type == '34') {
		type = 2;  // SSW_DIRECT SSW_SDDU SDDU(含SMB) 升级类型 后级
	}
	if (vmodel.south_device_type == '33' || vmodel.south_device_type == '-30' || vmodel.south_device_type == '61') {
		type = 1;  // NFBBMS ACEM FCTL 升级类型 前级
	}
	let is_sub_dev = false;
	if (vmodel.south_device_type == 41) {
		is_sub_dev = true;
	}
	if (vmodel.south_device_type == '34' && vmodel.sddu_update_model == '2') {
		vmodel.cur_comd_code = '-34';   // SDDU的SMB型号特殊处理成单独的设备
	} else {
		vmodel.cur_comd_code = vmodel.south_device_type;
	}
	if (is_sub_dev) {
		let sddu_num = vmodel.sddu_nos.length;
		let ssw_num = vmodel.ssw_nos.length;
		// 页面呈现会屏蔽不接入扩展空开的SDDU，实际下发升级序列号暂时还是按照全量计算，后续继续扩展SDDU时评估是否仍满足
		if (mode == 0) {
			if (vmodel.sddu_no_for_ssw == vmodel.sddu_nos[vmodel.sddu_nos.length-1]+1) {
				no_min = 1;
				no_max = sddu_num * ssw_num;
			} else {
				no_min = parseInt(vmodel.sddu_no_for_ssw - 1) * ssw_num + 1;
				no_max = parseInt(vmodel.sddu_no_for_ssw) * ssw_num;
			}
		}
		if (mode == 1) {
			no_min = parseInt(vmodel.sddu_no_for_ssw - 1) * ssw_num + parseInt(vmodel.ssw_update_no_min);
			no_max = no_min;
		}
		if (mode == 2) {
			no_min = parseInt(vmodel.sddu_no_for_ssw - 1) * ssw_num + parseInt(vmodel.ssw_update_no_min);
			no_max = parseInt(vmodel.sddu_no_for_ssw - 1) * ssw_num + parseInt(vmodel.ssw_update_no_max);
		}
		if (no_min<0 || no_max < 0) {
			if(action == 0) {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip);
			}
			else {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
			}
			return;
		}
		if (no_max*1 < no_min*1) {
			if(action == 0) {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip);
			}
			else {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
			}
			return;
		}
	} else {
		if (mode==0) {
			no_min = 0;
			no_max = no_min;
		}
		if (mode==1) {
			no_max = no_min;
		}
		if (no_min<0 || no_max < 0) {
			if(action == 0) {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip);
			}
			else {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
			}
			return;
		}
		if (no_max*1 < no_min*1) {
			if(action == 0) {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip);
			}
			else {
				alert(mainvalue.i18nkeyword.system.start_end_error_tip_v2);
			}
			return;
		}
	}
	var para = [{
			"update_mode":	mode.toString(),
			"index_min":	no_min.toString(),
			"index_max":	no_max.toString(),
			"update_type":	type.toString(),
			"action":action.toString(),
			"dev_code":    vmodel.cur_comd_code.toString(),
	}];
	if(action == 0){
		popupTipsDiv($("#south_update_warn_tip"), 5000);
	}
	var setsouthUpdateCfg = {data:{objectid:"south_update_cfg",type:"val_set",paranum:"0",paraval:JSON.stringify(para)},success:set_south_update_cfg_succ};
	request.addRequest([setsouthUpdateCfg]);
}

// request.addRequest([listsouthUpdateStateDatas]);

function get_south_update_state_datas(d,r) {
	if (d.result == 'ok') {
		var instids = d.data;
		addvalue(vmodel.south_info_ids,d.data,"instid");
		let dev_code = "";
		if (vmodel.south_device_type == "41") {
			return;
		} else {
			dev_code = vmodel.south_device_type.toString();
		}
		if (vmodel.south_device_type == '34' && vmodel.sddu_update_model == '2') {
			dev_code = '-34';    // SDDU的SMB型号特殊处理成单独的设备
		}
		let paras = [{"dev_code":dev_code, "instids":instids}];
		getsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify(paras)},success:get_south_update_state_datas_succ};
		request.addRequest([getsouthUpdateStateDatas]);
	}
}

function get_south_update_state_datas_succ(d,r) {
	if (d.result == 'ok') {
		for (let i = 0; i < d.datanum; ++i) {
			d.data[i].south_no = (i + 1).toString();
		}
		vmodel.south_update_state_datas = d.data
		var num =  d.datanum;
		vmodel.south_update_total_count = num;
		var i = 0;
		for (i = 0; i < num; i++) {
			if (d.data[i].update_rst == 1) {   // 正在升级中
				vmodel.south_update_now_no = d.data[i].south_no;
				if (vmodel.south_update_now_no !=0) {
					show_south_update_process();
					request.clearRequest(getsouthUpdateNowProcess);
					let paras = [{"dev_code":vmodel.south_device_type.toString(), "instids":[{"instid":vmodel.south_update_now_no.toString()}]}];
					if (vmodel.south_device_type == '34' && vmodel.sddu_update_model == '2') {
						paras[0]["dev_code"] = '-34'  // SDDU的SMB型号特殊处理成单独的设备
					}
					getsouthUpdateNowProcess = {data:{objectid:"south_update_info",type:"val_get",paranum:"0",paraval:JSON.stringify(paras)},success:get_south_update_now_process_succ,refresh:2};
					request.addRequest([getsouthUpdateNowProcess]);
				}
				break;
			}
		}
		if (i == num) {   // 无升级的南向设备
			hide_south_update_process();
			vmodel.south_update_now_no = 0;
		}
	} else {
		// DCMU和SMB属于镜像设备，当全部接入DCMU或SMB时，另一个设备实例则在升级状态机中被删除，进而获取设备实例信息失败，无法正常刷新
		if (vmodel.south_device_type == '34') {
			var data = {"south_no":'0', "update_process":0, "update_rst":0};
			vmodel.south_update_state_datas = [];
			for (let i = 0; i < 2; ++i) {
				data.south_no = (i + 1).toString();
				vmodel.south_update_state_datas.push(data);  // 对于SDDU设备，如果获取设备实例信息失败，手动覆盖为未升级的设备实例数据
			}
			hide_south_update_process();
		}
	}
}

function get_south_sub_update_state_datas_succ(d,r) {
	if (d.result == 'ok') {
		let ssw_blocks = new Array(vmodel.sddu_num);
		let ssw_states = [];
		let one_ssw_not_updating = {"status":'0', "progress":0};
		for (let i = 0; i < vmodel.sddu_num; i++) {
			ssw_states.push(one_ssw_not_updating); // 先初始化空值，再判断是否屏蔽，后续sddu_num继续扩展也能正确显示
			if (i == 2) continue;  // SDDU_3只用于升扩展电池，屏蔽扩展空开的数据显示
			ssw_blocks[i] = d.data.slice(i * vmodel.ssw_num_per_sddu, (i+1) * vmodel.ssw_num_per_sddu);
			ssw_blocks[i].forEach((item)=>{
				if (item.update_rst == '1') {
					let one_ssw_updating = {"status":'1', "progress":parseInt(item.update_process*100)};
					ssw_states[i] = one_ssw_updating;
					return;
				}
			})
		}
		vmodel.south_sub_update_state_datas = {};
		vmodel.south_sub_update_state_datas = $.extend(true, {}, ssw_blocks);
		vmodel.sddu_status_ssw_updating = {};
		vmodel.sddu_status_ssw_updating = ssw_states;
	}
}

var getsouthUpdateLogs = {data:{objectid:"south_update_logs",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:get_south_update_logs_succ, refresh:5};
// request.addRequest([getsouthUpdateLogs]);
function get_south_update_logs_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.south_update_event_list = d.data;
	}
}

function send_south_request(){
	request.addRequest([getsouthUpdateCfg]);
	request.addRequest([listsouthUpdateStateDatas]);
	request.addRequest([getsouthUpdateLogs]);
}

function clear_south_request(){
	request.clearRequest(getsouthUpdateCfg);
	request.clearRequest(listsouthUpdateStateDatas);
	request.clearRequest(getsouthUpdateLogs);
	request.clearRequest(getsouthSubUpdateStateDatas);

	let setsouthUpdateCfg = {data:{objectid:"south_update_cfg",type:"val_set"}};
	request.clearRequest(setsouthUpdateCfg);
	let getsouthUpdateStateDatas = {data:{objectid:"south_update_info",type:"val_get"}};
	request.clearRequest(getsouthUpdateStateDatas);
	request.clearRequest(getsouthUpdateNowProcess);

}

vmodel.$watch("south_update_mode", function(a) {
    vmodel.south_update_no_min = -1;
    vmodel.south_update_no_max = -1;
});

/********************************************南向设备升级相关接口结束****************************************************************************************************/
function del_success(d,r) {
	if (d.result ==="ok") {
		popupTipsDiv($("#time_successalert"), 2000);
	} else {
		popupTipsDiv($("#time_failurealert"), 2000);
	}
}

function delHisRecord(){
	if(confirm(mainvalue.i18nkeyword.delete_confirm)){
		if (checkNumber(vmodel.del_historysql)) {    //  如果为数字则表明此传入参数为记录类型
			var del_reak_record = {data:{objectid:"records",type:"inst_delete",paranum:"1",paraval:JSON.stringify([{"record_type":vmodel.del_historysql}])},success:del_success};
			request.addRequest([del_reak_record]);
		} else {
			var gettimeRq = {data:{objectid:"del_hisrecord",type:"inst_delete",paranum:"1",paraval:JSON.stringify([{'sqlname':vmodel.del_historysql}])},success:del_success};
			request.addRequest([gettimeRq]);
		}
	}
}


function uploadsmr(obj) {
	if($("#id-slave-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#id-slave-file").val().endsWith(".tar.gz") == false) {
		$("#id-slave-file~.remove").trigger("click");
		popupTipsDiv($("#tar_failurealert"), 3000);
		return false;
	}
	$("#up_slave_bt").attr("disabled", "disabled");
	if (vmodel.slave_device_type == '1') {
		vmodel.cand_upload_file_objid = "smr_paket";
	} else if (vmodel.slave_device_type == '8') {
		vmodel.cand_upload_file_objid = "pu_paket";
	} else if (vmodel.slave_device_type == '9') {
		vmodel.cand_upload_file_objid = "spcu_paket";
	} else if (vmodel.slave_device_type == '10') {
		vmodel.cand_upload_file_objid = "spu_paket";
	}
	var alertTask = new AlertTask();
	$("#sysUploading").show();
	alertTask.dismiss("csualert");
	showDataInit();
	$("#slave_up").ajaxSubmit(check_slave_packet_upload_result);
	return false;   //必须返回false否则将跳转到新界面
}

function check_slave_packet_upload_result(d,r) {
	result = JSON.parse(d);
	if (result.result == 'ok') {
		popupTipsDiv($("#time_successalert"), 3000);
	} else {
		popupTipsDiv($("#time_failurealert"), 3000);
	}
	hideDataInit();
	$("#id-slave-file~.remove").trigger("click");
	setTimeout(function () {
		$("#up_slave_bt").attr("disabled", false);
	}, 5000);
	list_subdev_packets_uploaded();
}

function uploadsouth(obj) {
	let paras = [{}];
	if($("#id-south-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#id-south-file").val().endsWith(".tar.gz") == false) {
		$("#id-south-file~.remove").trigger("click");
		popupTipsDiv($("#tar_failurealert"), 3000);
		return false;
	}
	$("#up_south_bt").attr("disabled", "disabled");
	if (vmodel.south_device_type == '23' || vmodel.south_device_type == '33' || vmodel.south_device_type == '44' || vmodel.south_device_type == '-30' || vmodel.south_device_type == '34' || vmodel.south_device_type == '41' || vmodel.south_device_type == '61' || vmodel.south_device_type == '66') {
		vmodel.south_upload_file_objid = "south_paket";
		if (vmodel.south_device_type == '34' && vmodel.sddu_update_model == '2') {
			paras = [{"dev_code": '-34'}];    // SDDU的SMB型号特殊处理成单独的设备
		} else {
			paras = [{"dev_code":(vmodel.south_device_type.toString())}];
		}
		vmodel.south_upload_para = JSON.stringify(paras);
	}
	var alertTask = new AlertTask();
	$("#sysUploading").show();
	alertTask.dismiss("csualert");
	showDataInit();
	$("#south_up").ajaxSubmit(check_south_packet_upload_result);
	return false;   //必须返回false否则将跳转到新界面
}

function check_south_packet_upload_result(d,r) {
	result = JSON.parse(d);
	if (result.result == 'ok') {
		popupTipsDiv($("#time_successalert"), 3000);
	} else {
		popupTipsDiv($("#time_failurealert"), 3000);
	}
	hideDataInit();
	$("#id-south-file~.remove").trigger("click");
	setTimeout(function () {
		$("#up_south_bt").attr("disabled", false);
	}, 5000);
	list_subdev_packets_uploaded();
}

if (userLevel >= 1){
	//$("#sysmaintain").show();
	$("#sys_reset").show();
	$("#rebootbt").show();
	$("#facResetbt").hide();
	// $("#paratab").show();
	$("#cutovertab").show();
	$("#para_down").show();
}
if (userLevel >= 2) {
	$("#sys_up").show();
	$("#sys_all_up").show();
    $("#ssl_up").show();
	$("#uib_up").show();
	$("#iddb_up").show();
	$("#para_up").show();
	$("#config_up").show();
	$("#del_hisinfo").show();
	$("#upparauser").val(userName);
	$("#upsysuser").val(userName);
	$("#delhisevent").show();
	$("#facResetbt").show();

	if(Cookies.get("uploadpack") == 1) {
		Cookies.remove("uploadpack");
		$("#statustab").removeClass("active");
		$("#sysStatus").removeClass("active");
		$("#sysDoStatus").removeClass("active");
		$("#sysDiStatus").removeClass("active");
		$("#sysmaintain").addClass("active");
		$("#maintain").addClass("active");
		var alertTask = new AlertTask();
		//alertTask.stay("uploadwaitingalert");
		$("#sysUpwaiting").show();
		$("#pageLock").show();
		showFleshtime(false);
		request.addRequest([checksysRq]);
	}
}


/********************升级流程*****************************/
function hide_the_csu_submit_button() {
	switch(vmodel.csu_updata_flag){
		case 1:  //CSU升级
			setTimeout(function () {
				$("#up_sys_bt").attr("disabled", false);
			}, 5000);
			$("#id-sys-file~.remove").trigger("click");
			break;
		case 2:  //CSU整体升级
			setTimeout(function () {
				$("#up_sys_all_bt").attr("disabled", false);
			}, 5000);
			$("#id-sys-all-file~.remove").trigger("click");
			break;
		case 3:  //证书升级
			setTimeout(function () {
				$("#up_ssl_bt").attr("disabled", false);
			}, 5000);
			$("#id-ssl-file~.remove").trigger("click");
			break;
		default:
			break;
	}
	vmodel.csu_updata_flag = 0;
}

function check_update_result(d,r) {
	var result;
	hide_the_csu_submit_button();
	if (typeof(d)=="undefined") {
		update_tip_show_tag = 0;
		hideDataInit();
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	} else {
		result = JSON.parse(d);
	}
	if(result.result == 'error') {
		update_tip_show_tag = 0;
		hideDataInit();
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	} else {
		showDataInit();
		normalheart.stop();
		updateheart.start();
		return true;
	}
}

function check_before_update_result(d,r) {
	let proc_up_req = {data:{}};
	update_tip_show_tag = 0;                    //转圈可以被显示
	hideDataInit();
	if (vmodel.csu_updata_flag == 1) {
		proc_up_req = {data:{objectid:"update_v2",type:"val_set",paraval:JSON.stringify([{}])},success:check_confirm_update_result};
	}
	if (vmodel.csu_updata_flag == 2) {
		proc_up_req = {data:{objectid:"update_v2",type:"inst_add",paraval:JSON.stringify([{}])},success:check_confirm_update_result};
	}
	hide_the_csu_submit_button();
	if (typeof(d)=="undefined") {
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	}
	let result = JSON.parse(d);
	if(result.result == 'error') {
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	}

	let message;
	if (result.data[0].sta == '0') {
		proc_up_req.data.paraval = JSON.stringify([{"action": "0"}]);
		update_tip_show_tag = 1;
		$('#prompt_tip').text(mainvalue.i18nkeyword.update_loading);
		request.addRequest([proc_up_req]);
		return;
	} else if (result.data[0].sta == '2') {
		message = mainvalue.i18nkeyword.system.uploadsys_filesys_check_tips;
	} else {
		message = mainvalue.i18nkeyword.system.uploadsys_list_check_tips;
	}
	const a = confirm(message);
	if (a == true) {
		updatesys_tip_action = '0';
		update_tip_show_tag = 1;
		$('#prompt_tip').text(mainvalue.i18nkeyword.update_loading);
	} else {
		updatesys_tip_action = '-1';
		update_tip_show_tag = 1;
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
	}
	proc_up_req.data.paraval = JSON.stringify([{"action": updatesys_tip_action}]);
	request.addRequest([proc_up_req]);
	return;
}

function check_confirm_update_result(d,r) {
	if (typeof(d)=="undefined") {
		update_tip_show_tag = 0;
		hideDataInit();
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	}

	if(d.result == 'error') {
		update_tip_show_tag = 0;
		hideDataInit();
		if (updatesys_tip_action == '-1') {
			return false;
		}
		alert(mainvalue.i18nkeyword.update_failure);
		normalheart.start();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		return false;
	} else {
		showDataInit();
		normalheart.stop();
		updateheart.start();
		return true;
	}
}

function uploadsys() {
	if($("#id-sys-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	let filename = $("#id-sys-file").val().split("\\").pop();
	if(filename !== "powernew.tar.gz" && filename !== "powernewdefault.tar.gz") {
		$("#id-sys-file~.remove").trigger("click");
		popupTipsDiv($("#upsys_name_err"), 3000);
		return false;
	}
	let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable", filename:filename}])},success:get_uploadsys_cert_enable_success};
	request.addRequest([req]);
	return false;   //必须返回false否则将跳转到新界面
}

function get_uploadsys_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	let paraval = JSON.parse(r.data.paraval)[0]; 
	if(d.data[0].seco_cert_enable == 1 && paraval.filename == "powernewdefault.tar.gz"){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = uploadsys_succ;
		$('#appModal').modal('show');
	}else{
		uploadsys_succ();
	}
}

function uploadsysmul() {
	let req = {data:{objectid:"mid_up",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable"}])},success:get_default_reset_cert_enable_success};
	request.addRequest([req]);
}


function get_uploadsys_cert_enable_success_mul(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
}

function uploadsys_succ(){
	$("#up_sys_bt").attr("disabled", "disabled");
	vmodel.csu_updata_flag = 1;
	$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
	upload_options.success = check_before_update_result;
	$("#sys_up").ajaxSubmit(upload_options);
	$("#sys_up_mul").ajaxSubmit(upload_options);
	$("#progressHide").css('display', 'block'); //显示
	update_tip_show_tag = 1;                    //转圈可以被显示
	upload_type = "sys";
}

function uploadsysall() {
	if($("#id-sys-all-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	let filename = $("#id-sys-all-file").val().split("\\").pop();
	if(filename !== "all_update.tar.gz" && filename !== "all_update_default.tar.gz") {
		$("#id-sys-all-file~.remove").trigger("click");
		popupTipsDiv($("#upsysall_name_err"), 3000);
		return false;
	}
	
	let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable", filename:filename}])},success:get_uploadsysall_cert_enable_success};
	request.addRequest([req]);
	return false;   //必须返回false否则将跳转到新界面
}

function get_uploadsysall_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	let paraval = JSON.parse(r.data.paraval)[0]; 
	if(d.data[0].seco_cert_enable == 1 && paraval.filename == "all_update_default.tar.gz"){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = uploadall_succ;
		$('#appModal').modal('show');
	}else{
		uploadall_succ();
	}
}

function uploadall_succ(){
	$("#up_sys_all_bt").attr("disabled", "disabled");
	vmodel.csu_updata_flag = 2;
	$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
	upload_options.success = check_before_update_result;
	$("#sys_all_up").ajaxSubmit(upload_options);
	$("#progressHide").css('display', 'block'); //显示
	update_tip_show_tag = 1;                    //转圈可以被显示
	upload_type = "sysall";
}

function uploadssl() {
	if($("#id-ssl-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	let filename = $("#id-ssl-file").val().split("\\").pop();
	if(filename !== "sslnew.zip" && filename !== "sslnew.tar.gz") {
		$("#id-ssl-file~.remove").trigger("click");
		popupTipsDiv($("#upssl_name_err"), 3000);
		return false;
	}
	$("#up_ssl_bt").attr("disabled", "disabled");
	vmodel.csu_updata_flag = 3;
	$('#prompt_tip').text(mainvalue.i18nkeyword.update_loading);
	showDataInit();
	upload_options.success = check_update_result;
	$("#ssl_up").ajaxSubmit(upload_options);
	// $("#progressHide").css('display', 'block'); //显示
	update_tip_show_tag = 1;                    //转圈可以被显示
	updateheart.start();
	upload_type = "ssl";
	return false;   //必须返回false否则将跳转到新界面
}

////////////////////////////***导入功能代码***////////////////////////////////////////
function pswd_strong_check(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

function check_string_length(str) {
    str = str.trim();
    if (getStrLeng_UTF8(str) > 31) {
        return false;
    }
    return true;
}

function disableBT(flag, tag) {
	if (flag) {
		if (tag == '1') {
			$("#dl_tip_all").show();
			$("#dl_data").attr("disabled", "disabled");
		} else {
			$("#dl_tip_para").show();
			$("#dl_para").attr("disabled", "disabled");
		}
	} else {
		if (tag == '1') {
			$("#dl_tip_all").hide();
			$("#dl_data").removeAttr("disabled");
		} else {
			$("#dl_tip_para").hide();
			$("#dl_para").removeAttr("disabled");
		}
	}
}

function doDL_para() {
	if ($("input[name='export_para_box']").prop("checked")) {
		var password = vmodel.para_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.para_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.para_export_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	disableBT(true, 2);
	var paras = [{"encrypswd":password}];
	var q = {data:{objectid:"paramaintain_v2",type:"val_get",paraval:JSON.stringify(paras)},success:paraexphandler};
	request.addRequest([q]);
}

function paraexphandler(d, r) {
    if (d.result == "ok" && d.data.length > 0) {
		if (d.data[0].exportresult === "error") {
			alert(mainvalue.i18nkeyword.system.export_failed);
		}
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
	disableBT(false);
	if (d.objectid == 'export_all') {
		disableBT(false, 1);
		vmodel.all_export_passwd = "";
	} else if (d.objectid == 'paramaintain_v2') {
		disableBT(false, 2);
		vmodel.para_export_passwd = "";
	}
}

////////////////////////////***一键导出功能代码***////////////////////////////////////////
function do_export_all() {
	if ($("input[name='export_all_box']").prop("checked")) {
		var password = vmodel.all_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.all_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.all_export_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	disableBT(true, 1);
	var para = [{"encrypswd":password}];
	var q = {data:{objectid:"export_all",type:"val_get",paraval:JSON.stringify(para)},success:paraexphandler};
	request.addRequest([q]);
}

////////////////////////////***一键导出代码结束***////////////////////////////////////////

////////////////////////////***EEPROM导出功能代码***////////////////////////////////////////
function disableBT_eeprom(flag) {
	if (flag) {
		$("#dl_tip_eeprom").show();
		$("#dl_eeprom").attr("disabled", "disabled");
	} else {
		$("#dl_tip_eeprom").hide();
		$("#dl_eeprom").removeAttr("disabled");
	}
}

function paraexphandler_eeprom(d, r) {
    if (d.result == "ok" && d.data.length > 0) {
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
	disableBT_eeprom(false);
	vmodel.eeprom_export_passwd = "";
}

function do_export_eeprom() {
	if ($("input[name='export_eeprom_box']").prop("checked")) {
		var password = vmodel.eeprom_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.eeprom_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.eeprom_export_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	disableBT_eeprom(true);
	var para = [{"encrypswd":password}];
	var q = {data:{objectid:"export_eeprom",type:"val_get",paraval:JSON.stringify(para)},success:paraexphandler_eeprom};
	request.addRequest([q]);
}

////////////////////////////***EEPROM导出代码结束***////////////////////////////////////////

function check_paraup_result(d,r) {
	setTimeout(function () {
		$("#up_para_bt").attr("disabled", false);
	}, 5000);
	vmodel.para_import_passwd = "";
	$("#id-para-file~.remove").trigger("click");
	if (typeof(d)=="undefined" || typeof(r)=="undefined") {
		hideDataInit();
		normalheart.start();
		alert(mainvalue.i18nkeyword.operate_failure);
		return false;
	} else {
		var result = JSON.parse(d);
		var para_import = {data:{objectid:"paramaintain_v3",type:"val_set",paraval:vmodel.para_paraval},success:import_paraup_result};

		var paraval = JSON.parse(vmodel.para_paraval);	
		paraval[0].action = "1";  //用于二次场景中取消释放文件锁
		var para_import_v2 = {data:{objectid:"paramaintain_v3",type:"val_set",paraval:JSON.stringify(paraval)},success:import_paraup_result};
	}
	if(result.result === 'error'|| result.objectid ==="para_inspect_er") {
		hideDataInit();
		normalheart.start();
		vmodel.para_verify_value = [];
		vmodel.para_verify_value = result.data;
		vmodel.para_err_type = 0;
		vmodel.show_more_info_tip = 0;
		if (vmodel.para_verify_value.length != 0) {
			if (result.data[0].hasOwnProperty("para verify")) {
				vmodel.para_err_type = 1;
			} else {
				vmodel.para_err_type = 2;
			}
			if (vmodel.para_verify_value.length > 5) {
				vmodel.show_more_info_tip = 1;
			} else {
				vmodel.show_more_info_tip = 0;
			}
		}
		alert(mainvalue.i18nkeyword.abnormal_data_upload);
		return false;
	}

	if (result.data[0]['change_para'].length > 0) {  //有场景配置参数，需要二次确认，弹出确认框
		var change_para = result.data[0]['change_para'].replace(/\n/g, '<br>');
		$.confirm({
			title: mainvalue.i18nkeyword.para_confirm_title,
			content: mainvalue.i18nkeyword.para_confirm_content + "<br>" + change_para ,
			boxWidth: '30%',
			useBootstrap: false,
			autoClose: 'close|60000',
			buttons: {
				confirm: {
					text: mainvalue.i18nkeyword.seco_cert.confirm,
					btnClass: 'btn-info',
					action: function () {
						request.addRequest([para_import]);
					}
				},
				close: {
					text: mainvalue.i18nkeyword.seco_cert.close,
					action: function () {
						request.addRequest([para_import_v2]);
					}
				}
			}
		});
	}
	else {   //无场景配置参数，且校验通过，直接返回成功，等待导入重启
		return true;
	}
}

function import_paraup_result(d,r) {
	if (d.result != 'ok') {
		hideDataInit();
		normalheart.start();
		alert(mainvalue.i18nkeyword.operate_failure);
	}
}

function para_verify_succ(d, r) {
	if (d.result != 'ok') {
		return;
	}
	vmodel.para_verify_value = [];
	vmodel.para_verify_value = d.data;
	if (vmodel.para_verify_value.length != 0) {
		alert(mainvalue.i18nkeyword.abnormal_data_upload);
	}
}

function uploadpara() {
	//getParaVerify();
	var upload_file_path = $("#id-para-file").val();
	if(upload_file_path == "" || vmodel.buttonflag == 1){
		return false;
	}
	if(upload_file_path.indexOf(".zip") == -1) {
		$("#id-para-file~.remove").trigger("click");
		popupTipsDiv($("#zip_failurealert"), 3000);
		return false;
	}
	// 增加导入加密功能
	if ($("input[name='up_para_box']").prop("checked")) {
		var password = vmodel.para_import_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.para_import_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	// 增加action属性，用于二次确认场景中区分是否需要取消释放文件锁
	var paras = [{"encrypswd":password, "action":"0"}];
	vmodel.para_paraval = JSON.stringify(paras);
	if(!confirm(mainvalue.i18nkeyword.operate_reboot_confirm)) {
		return false;
	}

	$("#up_para_bt").attr("disabled", "disabled");
	showDataInit();
	normalheart.stop();
	importheart.start();
	// 先进行参数校验，判断是否需要用户二次确认进行导入
	$("#para_up").ajaxSubmit(check_paraup_result);
	return false;   //必须返回false否则将跳转到新界面
}

function change_butonflag() {
	vmodel.buttonflag = 0;
}

function download_abnormal_data() {
	vmodel.buttonflag = 1;
	download_file_by_url(vmodel.para_verify_value[0].url,vmodel.para_verify_value[0].filename);
}

/**************SPCU参数维护开始*************/
function disableBT_spcu(flag) {
	if (flag) {
		$("#dl_tip_spcupara").show();
		$("#dl_spcupara").attr("disabled", "disabled");
	} else {
		$("#dl_tip_spcupara").hide();
		$("#dl_spcupara").removeAttr("disabled");
	}
}

function paraexphandler_spcu(d, r) {
    if (d.result == "ok" && d.data.length > 0) {
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
	disableBT_spcu(false);
	vmodel.spcupara_export_passwd = "";
}

function doDL_spcupara() {
	let password = '';
	if ($("input[name='export_spcupara_box']").prop("checked")) {
		password = vmodel.spcupara_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.spcupara_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.spcupara_export_passwd = "";
			return false;
		}
	} else {
		password = "";
	}
	disableBT_spcu(true);

	let paras = [{"encrypswd":password}];
	let q = {data:{objectid:"paramaintain_spcu",type:"val_get",paraval:JSON.stringify(paras)},success:paraexphandler_spcu};
	request.addRequest([q]);
}


function change_butonflag_spcu() {
	vmodel.spcubuttonflag = 0;
}

function uploadspcupara() {
	//getParaVerify();
	let password = '';
	let upload_file_path = $("#id-spcupara-file").val();
	if(upload_file_path == "" || vmodel.spcubuttonflag == 1){
		return false;
	}
	if(upload_file_path.indexOf(".zip") == -1) {
		$("#id-spcupara-file~.remove").trigger("click");
		popupTipsDiv($("#zip_failurealert"), 3000);
		return false;
	}
	// 增加导入加密功能
	if ($("input[name='up_spcupara_box']").prop("checked")) {
		password = vmodel.spcupara_import_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.spcupara_import_passwd = "";
			return false;
		}
	} else {
		password = "";
	}
	let paras = [{"encrypswd":password}];
	vmodel.spcupara_paraval = JSON.stringify(paras);
	// if(!confirm(mainvalue.i18nkeyword.operate_reboot_confirm)) {
	// 	return false;
	// }
	$("#up_spcupara_bt").attr("disabled", "disabled");
	showDataInit();
	normalheart.stop();
	importheart.start();
	$("#spcupara_up").ajaxSubmit(check_spcuparaup_result);
	return false;   //必须返回false否则将跳转到新界面
}

function check_spcuparaup_result(d,r) {
	let result;
	setTimeout(function () {
		$("#up_spcupara_bt").attr("disabled", false);
	}, 5000);
	vmodel.spcupara_import_passwd = "";
	$("#id-spcupara-file~.remove").trigger("click");
	if (typeof(d)=="undefined") {
		hideDataInit();
		normalheart.start();
		// alert(mainvalue.i18nkeyword.update_failure);
		mainvalue.controlsuccess = "failure";
		return false;
	} else {
		result = JSON.parse(d);
	}
	if(result.result === 'error'|| result.objectid ==="para_inspect_er") {
		hideDataInit();
		normalheart.start();
		vmodel.spcupara_verify_value = [];
		vmodel.spcupara_verify_value = result.data;
		//if (vmodel.para_verify_value.length != 0) {
		alert(mainvalue.i18nkeyword.abnormal_data_upload);
		//}
		return false;
	} else {
		//alert(mainvalue.i18nkeyword.system.upload_success);
		mainvalue.controlsuccess = "success";
		hideDataInit();
		normalheart.start();
		return true;
	}
}

function download_abnormal_data_spcu() {
	vmodel.spcubuttonflag = 1;
	download_file_by_url(vmodel.spcupara_verify_value[0].url,vmodel.spcupara_verify_value[0].filename);
}
/**************SPCU参数维护结束*************/

function check_configup_result(d,r) {
	var result;
	setTimeout(function () {
		$("#up_config_bt").attr("disabled", false);
	}, 5000);
	$("#id-config-file~.remove").trigger("click");
	if (typeof(d)=="undefined") {
		alert(mainvalue.i18nkeyword.update_failure);
		return false;
	} else {
		result = JSON.parse(d);
	}
	if(result.result === 'error') {
		alert(mainvalue.i18nkeyword.system.upload_failed);
		hideDataInit();
		importheart.stop();
		return false;
	} else {
		hideDataInit();
		return true;
	}
}

function uploadconfig() {
	if($("#id-config-file").val() == ""){
		return false;
	}
	if(!confirm(mainvalue.i18nkeyword.operate_reboot_confirm)) {
		return false;
	}
	$("#up_config_bt").attr("disabled", "disabled");
	showDataInit();
	normalheart.stop();
	importheart.start();
	$("#config_up").ajaxSubmit(check_configup_result);
	return false;   //必须返回false否则将跳转到新界面
}

/********************系统复位*****************************/
function devReboot(){
	if(confirm(mainvalue.i18nkeyword.reboot_confirm)){
		doReboot();
		setTimeout(function(){
			var para = [{'type':'3'}];
			rebooteheart.start();
		}, 10000);
	}
}
function doReboot(){
	var reason = 1;
	$("#dl_url").hide();
	$("div[id^='tipReboot']").hide();
	$('#prompt_tip').html("<strong>"+mainvalue.i18nkeyword.jumptologinpage+"...</strong>");
	showDataInit();
	var Rq = {data:{objectid:"reboot",type:"val_set",paraval:JSON.stringify([{'reboot':'1'}])}, bindid:undefined, refresh:undefined, success:rebootSuccessHandler, error:undefined, extdata:undefined};
	request.addRequest([Rq]);
	Cookies.set('user', '', { expires: -1, "path":"/power" });
	normalheart.stop();
	setTimeout(function(){
		var connRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify([{'type':'4'}])}, bindid:undefined, refresh:5, success:rebootConnHandler, error:undefined, extdata:undefined};
		request.addRequest([connRq]);
	}, 10000);
}
function rebootSuccessHandler(d, r){
	if (request.getDataResult(d) == "error") {
		hideDataInit();
		$('#prompt_tip').text(mainvalue.i18nkeyword.data_loading);
		popupTipsDiv($("#time_failurealert"), 1000);
	} else {
		request.clearRequest(r);
		gotopage('login.html');
	}
}

function rebootConnHandler(d, r) {
	if (request.getDataResult(d) != "error") {
		request.clearRequest(r);
		showDataInit();
	}
}


/**************恢复出厂设置*************/
function doFactoryReset(){
	if(confirm(mainvalue.i18nkeyword.system.factory_reset_influence + " " + mainvalue.i18nkeyword.system.factory_reset_confirm)){
		let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable"}])},success:get_factory_reset_cert_enable_success};
		request.addRequest([req]);
	}
}

function get_factory_reset_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	if(d.data[0].seco_cert_enable == 1){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = factory_reset;
		$('#appModal').modal('show');
	}else{
		factory_reset();
	}
}

function factory_reset(){
	var Rq = {data:{objectid:"restore_factory_set",type:"val_set",paraval:JSON.stringify([{'reboot':'3'}])}, success:factoryResetHandler, error:undefined, extdata:undefined};
	request.addRequest([Rq]);
	$('#prompt_tip').html("<strong>"+mainvalue.i18nkeyword.system.factory_reset_now+"...</strong>");
	showDataInit();
	normalheart.stop();
	Cookies.set('user', '', { expires: -1, "path":"/power" });   //清除当前账户的用户名
	setTimeout(function(){
		var connRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify([{'type':'4'}])}, bindid:undefined, refresh:5, success:rebootSuccessHandler, error:undefined, extdata:undefined};
		request.addRequest([connRq]);
	}, 10*1000);
}

function factoryResetHandler(d, r){
	if (request.getDataResult(d) != "ok") {
		popupTipsDiv($("#time_failurealert"), 1000);
		hideDataInit();
	} else {
		normalheart.stop();
		showDataInit();
		setTimeout(function(){
			var connRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify([{'type':'4'}])}, bindid:undefined, refresh:5, success:rebootConnHandler, error:undefined, extdata:undefined};
			request.addRequest([connRq]);
		}, 5000);
	}
}

/**************强制恢复默认值设置*************/
function dodefaultReset(){
	if(confirm(mainvalue.i18nkeyword.system.default_reset_influence + " " + mainvalue.i18nkeyword.system.default_reset_confirm)){
		let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable"}])},success:get_default_reset_cert_enable_success};
		request.addRequest([req]);
	}
}

function get_default_reset_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	if(d.data[0].seco_cert_enable == 1){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = default_reset_succ;
		$('#appModal').modal('show');
	}else{
		default_reset_succ();
	}
}

function default_reset_succ(){
	var Rq = {data:{objectid:"default_factory_set",type:"val_set",paraval:JSON.stringify([{'reboot':'3'}])}, success:defaultResetHandler, error:undefined, extdata:undefined};
	request.addRequest([Rq]);
	$('#prompt_tip').html("<strong>"+mainvalue.i18nkeyword.system.default_reset_now+"...</strong>");
	showDataInit();
	normalheart.stop();
	Cookies.set('user', '', { expires: -1, "path":"/power" });   //清除当前账户的用户名
	setTimeout(function(){
		var connRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify([{'type':'4'}])}, bindid:undefined, refresh:5, success:rebootSuccessHandler, error:undefined, extdata:undefined};
		request.addRequest([connRq]);
	}, 10*1000);
}

function defaultResetHandler(d, r){
	if (request.getDataResult(d) != "ok") {
		popupTipsDiv($("#time_failurealert"), 1000);
		hideDataInit();
	} else {
		normalheart.stop();
		showDataInit();
		setTimeout(function(){
			var connRq = {data:{objectid:"heart_beat",type:"val_get",paraval:JSON.stringify([{'type':'4'}])}, bindid:undefined, refresh:5, success:rebootConnHandler, error:undefined, extdata:undefined};
			request.addRequest([connRq]);
		}, 5000);
	}
}

/**************参数备份与恢复*************/
function para_bak(obj) {
	var Rq = {data:{objectid:"parabakrecover_v2",type:"inst_add",paraval:JSON.stringify([{}])}, success:para_bak_succ};
	request.addRequest([Rq]);
	showDataInit();
}

function para_bak_succ(d,r) {
	para_bak_file_list();
	hideDataInit();
	if (d.result == "error") {
		popupTipsDiv($("#time_failurealert"), 2000);
	} else {
		popupTipsDiv($("#time_successalert"), 2000);
	}
}


var operat_succ_tag = true;
function para_recover_succ(d,r) {
	if (d.result == 'error') {
		normalheart.start();
		operat_succ_tag = false;
		hideDataInit();
		popupTipsDiv($("#time_failurealert"), 1000);
		return;
	} else if (d.result == 'ok') {
		operat_succ_tag = true;
		setTimeout(function() {reboot_process()}, 3000); // 重启心跳保证在后端进程开始重启后才开始监听，否则过早开启会在网络断开时跳转至登录页面导致无法访问
	}
}

function reboot_process() {
	if (!operat_succ_tag) {
		return;
	}
	$('#prompt_tip').html("<strong>"+mainvalue.i18nkeyword.jumptologinpage+"...</strong>");
	showDataInit();
	normalheart.stop();
	rebooteheart.start();
}

function para_recover(rec_type) {
	var type = rec_type || 0;
	var para = {"id":type.toString()};
	var Rq;
	let recover_confirm = true;
	if (type == 0) {
		Rq = {data:{objectid:"parabakrecover_v2",type:"val_set",paraval:JSON.stringify([para])}, success:para_recover_succ, error:reboot_process};
		recover_confirm = confirm(mainvalue.i18nkeyword.system.para_recover_influence + " " + mainvalue.i18nkeyword.system.para_recover_confirm);
		if (recover_confirm) {
			request.clearRequest(Rq);
			request.addRequest([Rq]);
			showDataInit();
			normalheart.stop();
			// setTimeout(function() {reboot_process()}, 6*1000);
		}
	} else {
		recover_confirm = confirm(mainvalue.i18nkeyword.system.recover_default_para_influence + " " + mainvalue.i18nkeyword.system.recover_default_para_confirm);
		if (recover_confirm) {
			let seco_cert_req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable"}])},success:get_para_recoverl_cert_enable_success};
			request.addRequest([seco_cert_req]);
		}
	}
}

function get_para_recoverl_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	if(d.data[0].seco_cert_enable == 1){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = para_recover_default;
		$('#appModal').modal('show');
	}else{
		para_recover_default();
	}
}

function para_recover_default(){
	let req = {data:{objectid:"parabakrecover_v2",type:"attr_set",paraval:JSON.stringify([{"id":"1"}])}, success:para_recover_succ, error:reboot_process};
	request.clearRequest(req);
	request.addRequest([req]);
	showDataInit();
	normalheart.stop();
	// setTimeout(function() {reboot_process()}, 6*1000);
}

function para_bak_file_list(obj) {
	var Rq = {data:{objectid:"parabakrecover_v2",type:"list",paraval:JSON.stringify([])}, refresh:10, success:para_bak_file_list_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);

	function para_bak_file_list_succ(d,r) {
		if (d.result == "ok" && d.datanum > 0) {
			vmodel.bak_para_name = d.data[0].filename;
			$("#bak_para_name_val").html(vmodel.bak_para_name);
		}
	}
}
// para_bak_file_list();

function send_para_bak_request(){
	para_bak_file_list();
}

function clear_para_bak_request(){
	var Rq = {data:{objectid:"parabakrecover_v2",type:"list"}};
	request.clearRequest(Rq);
}

function para_bak_file_export(obj) {
	if ($("input[name='back_para_box']").prop("checked")) {
		var password = vmodel.para_back_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip_v2);
			vmodel.para_back_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.para_back_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	var paras = [{"encrypswd":password}];
	var Rq = {data:{objectid:"parabakrecover_v2",type:"val_get",paraval:JSON.stringify(paras)}, success:para_bak_file_exprt_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);

	function para_bak_file_exprt_succ(d,r) {
		if (d.result == "ok" && d.datanum > 0) {
			vmodel.bak_para_url = d.data[0].fileurl;
			vmodel.bak_para_name = d.data[0].filename;
			$("#bak_para_name_val").html(vmodel.bak_para_name);
            download_file_by_url(vmodel.bak_para_url, vmodel.bak_para_name);
		} else {
			alert(mainvalue.i18nkeyword.system.export_failed+","+mainvalue.i18nkeyword.later_try);
			para_bak_file_list();
		}
		vmodel.para_back_passwd = "";
	}
}


/********************子板升级流程*****************************/

function hide_uib_update_process(obj) {
	$("#uib_update_progress").css("display","none");
}

function show_uib_update_process(obj) {
	$("#uib_update_progress").css("display","block");
}

var getUIBUpdateNowProcess = {data:{objectid:"uib_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])}};
//getUIBUpdateNowProcess = {data:{objectid:"uib_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_uib_update_now_process_succ,refresh:2};
//request.addRequest([getUIBUpdateNowProcess]);
var getUIBUpdateNowProcess1 = {data:{objectid:"uib_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_uib_update_now_process_succ1};
// request.addRequest([getUIBUpdateNowProcess1]);

var getiddbBUpdateNowProcess = {data:{objectid:"iddb_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])}};;
var getiddbUpdateNowProcess1 = {data:{objectid:"iddb_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_iddb_update_now_process_succ1};
// request.addRequest([getiddbUpdateNowProcess1]);

function send_sboard_request(){
	request.addRequest([getUIBUpdateNowProcess1]);
	request.addRequest([getiddbUpdateNowProcess1]);
}

function clear_sboard_request(){
	request.clearRequest(getUIBUpdateNowProcess1);
	request.clearRequest(getiddbUpdateNowProcess1);
}

function get_uib_update_now_process_succ1(d,r){
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].result == "updating" && d.data[0].progress!= '') {
			$("#up_sboard_bt").attr("disabled", "disabled");
			vmodel.slave_sboard_type = 1;
			show_uib_update_process();
			process = d.data[0].progress * 1.0;
			if (!isNaN(process)) {
				vmodel.uib_update_now_no_process = parseInt(process);
				request.clearRequest(getUIBUpdateNowProcess1);
				getUIBUpdateNowProcess = {data:{objectid:"uib_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_uib_update_now_process_succ,refresh:2};
				request.addRequest([getUIBUpdateNowProcess]);
			}
		}
	}
}

function check_sboard_update_result(d,r) {
	var result;
	$("#id-sboard-file~.remove").trigger("click");
	if (typeof(d)=="undefined") {
		alert(mainvalue.i18nkeyword.update_failure);
		setTimeout(function () {
			$("#up_sboard_bt").attr("disabled", false);
		}, 5000);
		return false;
	} else {
		result = JSON.parse(d);
	}
	if(result.result == 'error') {
		alert(mainvalue.i18nkeyword.update_failure);
		setTimeout(function () {
			$("#up_sboard_bt").attr("disabled", false);
		}, 5000);
		return false;
	} else {
		if(result.objectid == 'uib_update'){
			show_uib_update_process();
			vmodel.slave_sboard_type = 1;
			getUIBUpdateNowProcess = {data:{objectid:"uib_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_uib_update_now_process_succ,refresh:2};
			request.addRequest([getUIBUpdateNowProcess]);
			return true;
		} else {
			show_iddb_update_process();
			vmodel.slave_sboard_type = 2;
			getiddbUpdateNowProcess = {data:{objectid:"iddb_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_iddb_update_now_process_succ,refresh:2};
			request.addRequest([getiddbUpdateNowProcess]);
			return true;
		}
	}
}


function get_uib_update_now_process_succ(d,r) {
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].result == "updating" && d.data[0].progress!= '') {
			process = d.data[0].progress * 1.0;
			if (!isNaN(process)) {
				vmodel.uib_update_now_no_process = parseInt(process);
			}
		} else if(d.data[0].result == "update_success"){
			process = 100;
			hide_uib_update_process();
			setTimeout(function () {
				$("#up_sboard_bt").attr("disabled", false);
			}, 5000);
			$(".remove").trigger("click");
			request.clearRequest(getUIBUpdateNowProcess);
			if (vmodel.sboard_alert_flag == '1') {
				alert(mainvalue.i18nkeyword.update_success);
				vmodel.sboard_alert_flag = 0;
			}
		} else {
			if (vmodel.sboard_alert_flag == '1') {
				alert(mainvalue.i18nkeyword.update_failure);
				vmodel.sboard_alert_flag = 0;
			}
			hide_uib_update_process();
			request.clearRequest(getUIBUpdateNowProcess);
			setTimeout(function () {
				$("#up_sboard_bt").attr("disabled", false);
			}, 5000);
			$(".remove").trigger("click");
		}
	}
}

function uploadsboard() {
	if($("#id-sboard-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#id-sboard-file").val().indexOf(".bin") == -1) {
		$("#id-sboard-file~.remove").trigger("click");
		popupTipsDiv($("#bin_failurealert"), 3000);
		return false;
	}
	vmodel.sboard_alert_flag = 1;      // 初始化子板升级弹窗标记
	$("#up_sboard_bt").attr("disabled", "disabled");
	if (vmodel.slave_sboard_type == '1') {
		vmodel.sboard_upload_file_objid = "uib_update";
	} else if (vmodel.slave_sboard_type == '2') {
		vmodel.sboard_upload_file_objid = "iddb_update";
	}
	$("#sboard_up").ajaxSubmit(check_sboard_update_result);
	return false;   //必须返回false否则将跳转到新界面
}


function hide_iddb_update_process(obj) {
	$("#iddb_update_progress").css("display","none");
}

function show_iddb_update_process(obj) {
	$("#iddb_update_progress").css("display","block");
}


function get_iddb_update_now_process_succ1(d,r){
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].result == "updating" && d.data[0].progress!= '') {
			$("#up_sboard_bt").attr("disabled", "disabled");
			vmodel.slave_sboard_type = 2;
			show_iddb_update_process();
			process = d.data[0].progress * 1.0;
			if (!isNaN(process)) {
				vmodel.iddb_update_now_no_process = parseInt(process);
				request.clearRequest(getiddbUpdateNowProcess1);
				getiddbUpdateNowProcess = {data:{objectid:"iddb_update",type:"attr_set",paranum:"0",paraval:JSON.stringify([{}])},success:get_iddb_update_now_process_succ,refresh:2};
				request.addRequest([getiddbUpdateNowProcess]);
			}
		}
	}
}

function get_iddb_update_now_process_succ(d,r) {
	if (d.result == 'ok' && d.datanum == 1) {
		var process = 0.0;
		if (d.data[0].result == "updating" && d.data[0].progress!= '') {
			process = d.data[0].progress * 1.0;
			if (!isNaN(process)) {
				vmodel.iddb_update_now_no_process = parseInt(process);
			}
		} else if(d.data[0].result == "update_success"){
			process = 100;
			hide_iddb_update_process();
			setTimeout(function () {
				$("#up_sboard_bt").attr("disabled", false);
			}, 5000);
			$(".remove").trigger("click");
			request.clearRequest(getiddbUpdateNowProcess);
			if (vmodel.sboard_alert_flag == '1') {
				alert(mainvalue.i18nkeyword.update_success);
				vmodel.sboard_alert_flag = 0;
			}
		} else {
			if (vmodel.sboard_alert_flag == '1') {
				alert(mainvalue.i18nkeyword.update_failure);
				vmodel.sboard_alert_flag = 0;
			}
			hide_iddb_update_process();
			request.clearRequest(getiddbUpdateNowProcess);
			setTimeout(function () {
				$("#up_sboard_bt").attr("disabled", false);
			}, 5000);
			$(".remove").trigger("click");
		}
	}
}



////////////////////////////***一键导出证书***////////////////////////////////////////

function do_ssl_downfile() {
	disableBT(true);
	$("#ssl_up").attr("disabled","disabled");
	var paras = [{}];
	var q = {data:{objectid:"ssl_download",type:"val_get",paraval:JSON.stringify(paras)},success:sslexphandler};
	request.addRequest([q]);
}

function sslexphandler(d, r) {
    if (d.result == "ok" && d.data.length > 0) {
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
    disableBT(false);
}

////////////////////////////页面多语言跳转////////////////////////////////////////
function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "sysmaintain" || tab == "slavemaintain" || tab == "southmaintain" || tab == "sboardupdate" || tab == "paraMaintain") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "sysmaintain";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();
////////////////////////////***加密密码框是否展示***///////////////////////////////////
function whether_to_encrypt(obj) {
	if ($(obj).is(":checked")) {
		$(obj).next().removeAttr("disabled");
	} else {
		$(obj).next().attr("disabled","disabled");
		$(obj).next().val("");
	}
}

////////////////////////////***导出按钮布局***////////////////////////////////////////
function page_show() {
	if (language == "zh_CN.UTF-8") {
		$("#dl_data").width(311);
		$("#dl_eeprom").width(311);
		$("#dl_para").width(305);
		$("#dl_spcupara").width(305);
	} else {
		$("#dl_data").width(411);
		$("#dl_eeprom").width(411);
		$("#dl_para").width(406);
		$("#dl_spcupara").width(406);
	}
}
$(document).ready(function(){
	page_show();
})


function list_subdev_packets_uploaded() {
	let	listSubdevPacketsUploaded = {data:{objectid:"update_packets",type:"list",paranum:"0",paraval:JSON.stringify([{}])},success:get_uploaded_subdev_packets_list};
	request.addRequest([listSubdevPacketsUploaded]);
}

function get_uploaded_subdev_packets_list(d,r) {
	let tmp = d.data[0];
	for (i in tmp) {
		tmp[i] = tmp[i].toString();
	}

	tmp.fctl_packet_name = (tmp.fctl_w121_packet_name ? tmp.fctl_w121_packet_name + '\n' : "") +
						tmp.fctl_sf01_packet_name;
	tmp.fctl_packet_name = tmp.fctl_packet_name ? tmp.fctl_packet_name.replace(/,/g, '\n') : tmp.fctl_packet_name;

	tmp.smr_packet_name = tmp.smr_packet_name ? tmp.smr_packet_name.replace(/,/g, '\n') : tmp.smr_packet_name;
	tmp.pu_packet_name = tmp.pu_packet_name ? tmp.pu_packet_name.replace(/,/g, '\n') : tmp.pu_packet_name;
	tmp.spcu_packet_name = tmp.spcu_packet_name ? tmp.spcu_packet_name.replace(/,/g, '\n') : tmp.spcu_packet_name;
	tmp.spu_packet_name = tmp.spu_packet_name ? tmp.spu_packet_name.replace(/,/g, '\n') : tmp.spu_packet_name;
	tmp.fbbms_packet_name = tmp.fbbms_packet_name ? tmp.fbbms_packet_name.replace(/,/g, '\n') : tmp.fbbms_packet_name;
	tmp.nfbbms_packet_name = tmp.nfbbms_packet_name ? tmp.nfbbms_packet_name.replace(/,/g, '\n') : tmp.nfbbms_packet_name;
	tmp.aemb_packet_name = tmp.aemb_packet_name ? tmp.aemb_packet_name.replace(/,/g, '\n') : tmp.aemb_packet_name;
	tmp.sddu_packet_name = tmp.sddu_packet_name ? tmp.sddu_packet_name.replace(/,/g, '\n') : tmp.sddu_packet_name;
	tmp.smb_packet_name = tmp.smb_packet_name ? tmp.smb_packet_name.replace(/,/g, '\n') : tmp.smb_packet_name;
	tmp.ssw_packet_name = tmp.ssw_packet_name ? tmp.ssw_packet_name.replace(/,/g, '\n') : tmp.ssw_packet_name;
	tmp.acmu_packet_name = tmp.acmu_packet_name ? tmp.acmu_packet_name.replace(/,/g, '\n') : tmp.acmu_packet_name;
	vmodel.subdev_packets_uploaded = tmp;
}

list_subdev_packets_uploaded();