#!/bin/sh
#
if [ "$1" = "" ]; then
	DEV=/dev/ttyUSB2
else
	DEV=$1
fi

if [ "$2" = "" ]; then
	APN=ctnet
else
	APN=$2
fi

if [ "$5" = "" ]; then
	DIALNUM=*99#
else
	DIALNUM=$5
fi

if [ "$6" = "" ]; then
	ID=102
else
	ID=$6
fi

if [ "$7" = "" ]; then
	CON=wirelessCon
else
	CON=$7
fi

#
# Export them so that they will be available at 'ppp-on-dialer' time.
export APN
export DIALNUM
#
# Initiate the connection
# 
if [ "$APN" = "ctnet" ]; then
	exec /root/power/bin/pppd protocol-mgr-id $ID con-name $CON \
			$DEV user $3 password $4 call ctnetCFG
else
	exec /root/power/bin/pppd protocol-mgr-id $ID con-name $CON \
			$DEV user $3 password $4 call pppdCFG
fi

	

	
