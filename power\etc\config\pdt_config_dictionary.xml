<?xml version="1.0" encoding="utf-8"?>
<config>
	<cfgobject id="pdt.smrs" name="smrs configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="smr_num" name="srm number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>smr number</full_name>
			<short_name>smr num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.bmus" name="bmus configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="bmu_num" name="bmu number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>bmu number</full_name>
			<short_name>bmu num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.optimizers" name="optimizers configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="optimizer_num" name="optimizer number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>optimizer number</full_name>
			<short_name>optimizer num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.can_sensor" name="can sensor configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="can_sensor_num" name="can sensor number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>can sensor number</full_name>
			<short_name>can sensor num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.pcs" name="pcs configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="can_pcs_num" name="can pcs number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>can sensor number</full_name>
			<short_name>can sensor num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>1</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>1</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>1</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>1</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.pus" name="pus configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="pu_num" name="pu number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>pu number</full_name>
			<short_name>pu num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.spcus" name="spcus configure" type="single">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="spcu_num" name="spcu number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>spcu number</full_name>
			<short_name>spcu num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="online_bat_num" name="online bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>online bat number</full_name>
			<short_name>online bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="offline_bat_num" name="offline bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>offline bat number</full_name>
			<short_name>offline bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="sta_unplug_bat_num" name="statistic unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>statistic unplug bat number</full_name>
			<short_name>sta unplug bat num</short_name>
			<convention></convention>
			<default>4</default>
			<visible>no</visible>
		</para>
		<para id="nor_unplug_bat_num" name="normal unplug bat number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>normal unplug bat number</full_name>
			<short_name>nor unplug bat num</short_name>
			<convention></convention>
			<default>2</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.lcd_contrast" name="lcd contrast configure" type="single">
		<para id="lcd_contrast_haha" name="lcd contrast" type="int">
			<min>0</min>
			<max>100</max>
			<precision></precision>
			<step></step>
			<full_name>lcd contrast</full_name>
			<short_name>lcd contrast</short_name>
			<convention></convention>
			<default>45</default>
			<visible>yes</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.power_subrack" name="Power Subrack" type="single">
		<para id="subrack_type" name="Power Subrack Type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Power Subrack Type</full_name>
			<short_name>Power Subrack Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="subrack_code" name="Power Subrack Code" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Power Subrack Code</full_name>
			<short_name>Power Subrack Code</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="subrack_code_name" name="Power Subrack Name" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Power Subrack Name</full_name>
			<short_name>Power Subrack Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="rate_cap" name="Rate Output Capacity" type="int">
			<min>15</min>
			<max>5000</max>
			<precision></precision>
			<step></step>
			<full_name>Rate Output Capacity</full_name>
			<short_name>Rate Output Capacity</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ac_power_supply" name="Subrack AC Power Supply" type="int">
			<min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack AC Power Supply</full_name>
			<short_name>Subrack AC Power Supply</short_name>
			<convention>0:L1L2L3N-220V,1:L1L2L3-110V,2:L1N-220V,3:L1L2-110V</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="optional" name="AC Power Supply Optional" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>AC Power Supply Optional</full_name>
			<short_name>AC Power Supply Optional</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="slot_num" name="Power Module Slot Num" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Power Module Slot Number</full_name>
			<short_name>Power Module Slot Number</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="dclp_type" name="DCLP Type" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>DCLP Type</full_name>
			<short_name>DCLP Type</short_name>
			<convention>0:DLPB,1:DCSPD</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="dclp_alm_st" name="DCLP Alarm Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>DCLP Alarm Status</full_name>
			<short_name>DCLP Alarm Status</short_name>
			<convention>0:Close,1:Break</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="llvd1_conf" name="LLVD1 Config" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>LLVD1 Config</full_name>
			<short_name>LLVD1 Config</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="llvd2_conf" name="LLVD2 Config" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>LLVD2 Config</full_name>
			<short_name>LLVD2 Config</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="blvd_conf" name="BLVD Config" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>BLVD Config</full_name>
			<short_name>BLVD Config</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="load_shunt" name="Load Shunt" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Load Shunt</full_name>
			<short_name>Load Shunt</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="uib_model" name="UIB Model" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>User Interface Board Model</full_name>
			<short_name>User Interface Board Model</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="temp_sen_type" name="Temp Sensor Type" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Temperature Sensor Type</full_name>
			<short_name>Temperature Sensor Type</short_name>
			<convention>0:AD590,1:NTC</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="cib_model" name="CIB Model" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>CSU interface board Model</full_name>
			<short_name>CSU interface board Model</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="f01_det_ch_app_conf" name="F01 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F01 Detect Channel AppList Config</full_name>
			<short_name>F01 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f02_det_ch_app_conf" name="F02 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F02 Detect Channel AppList Config</full_name>
			<short_name>F02 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f03_det_ch_app_conf" name="F03 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F03 Detect Channel AppList Config</full_name>
			<short_name>F03 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f04_det_ch_app_conf" name="F04 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F04 Detect Channel AppList Config</full_name>
			<short_name>F04 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f05_det_ch_app_conf" name="F05 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F05 Detect Channel AppList Config</full_name>
			<short_name>F05 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f06_det_ch_app_conf" name="F06 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F06 Detect Channel AppList Config</full_name>
			<short_name>F06 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f07_det_ch_app_conf" name="F07 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F07 Detect Channel AppList Config</full_name>
			<short_name>F07 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f08_det_ch_app_conf" name="F08 Det Channel App Conf" type="int">
			<min>0</min>
			<max>37</max>
			<precision></precision>
			<step></step>
			<full_name>F08 Detect Channel AppList Config</full_name>
			<short_name>F08 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Loop-1,2:LLVD1 Loop-2,3:LLVD1 Loop-3,4:LLVD1 Loop-4,5:LLVD2 Loop-1,6:LLVD2 Loop-2,7:LLVD2 Loop-3,8:LLVD2 Loop-4,9:BLVD Loop-1,10:BLVD Loop-2,11:BLVD Loop-3,12:BLVD Loop-4,13:LLVD1 Load Module-1,14:LLVD1 Load Module-2,15:LLVD1 Load Module-3,16:BLVD Load Module-1,17:LLVD2 Load Module-1,18:TLLVD1 Loop-1,19:TLLVD1 Loop-2,20:TLLVD1 Loop-3,21:TLLVD1 Loop-4,22:TLLVD2 Loop-1,23:TLLVD2 Loop-2,24:TLLVD2 Loop-3,25:TLLVD2 Loop-4,26:TBLVD Loop-1,27:TBLVD Loop-2,28:TBLVD Loop-3,29:TBLVD Loop-4,30:DC Loop Status-1,31:DC Loop Status-2,32:DC Loop Status-3,33:DC Loop Status-4,34:DC Loop Status-5,35:DC Loop Status-6,36:DC Loop Status-7,37:DC Loop Status-8</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="f09_det_ch_app_conf" name="F09 Det Channel App Conf" type="int">
			<min>0</min>
			<max>18</max>
			<precision></precision>
			<step></step>
			<full_name>F09 Detect Channel AppList Config</full_name>
			<short_name>F09 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Extend Loop,2:LLVD2 Extend Loop,3:BLVD Extend Loop,4:DC Ext.Loop,5:TLLVD1 Loop-1,6:TLLVD1 Loop-2,7:TLLVD1 Loop-3,8:TLLVD1 Loop-4,9:TLLVD2 Loop-1,10:TLLVD2 Loop-2,11:TLLVD2 Loop-3,12:TLLVD2 Loop-4,13:TBLVD Loop-1,14:TBLVD Loop-2,15:TBLVD Loop-3,16:TBLVD Loop-4,17:Common LLVD2 Loop,18:Common BLVD Loop</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="f10_det_ch_app_conf" name="F10 Det Channel App Conf" type="int">
			<min>0</min>
			<max>18</max>
			<precision></precision>
			<step></step>
			<full_name>F10 Detect Channel AppList Config</full_name>
			<short_name>F10 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Extend Loop,2:LLVD2 Extend Loop,3:BLVD Extend Loop,4:DC Ext.Loop,5:TLLVD1 Loop-1,6:TLLVD1 Loop-2,7:TLLVD1 Loop-3,8:TLLVD1 Loop-4,9:TLLVD2 Loop-1,10:TLLVD2 Loop-2,11:TLLVD2 Loop-3,12:TLLVD2 Loop-4,13:TBLVD Loop-1,14:TBLVD Loop-2,15:TBLVD Loop-3,16:TBLVD Loop-4,17:Common LLVD2 Loop,18:Common BLVD Loop</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="f11_det_ch_app_conf" name="F11 Det Channel App Conf" type="int">
			<min>0</min>
			<max>18</max>
			<precision></precision>
			<step></step>
			<full_name>F11 Detect Channel AppList Config</full_name>
			<short_name>F11 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Extend Loop,2:LLVD2 Extend Loop,3:BLVD Extend Loop,4:DC Ext.Loop,5:TLLVD1 Loop-1,6:TLLVD1 Loop-2,7:TLLVD1 Loop-3,8:TLLVD1 Loop-4,9:TLLVD2 Loop-1,10:TLLVD2 Loop-2,11:TLLVD2 Loop-3,12:TLLVD2 Loop-4,13:TBLVD Loop-1,14:TBLVD Loop-2,15:TBLVD Loop-3,16:TBLVD Loop-4,17:Common LLVD2 Loop,18:Common BLVD Loop</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="f12_det_ch_app_conf" name="F12 Det Channel App Conf" type="int">
			<min>0</min>
			<max>18</max>
			<precision></precision>
			<step></step>
			<full_name>F12 Detect Channel AppList Config</full_name>
			<short_name>F12 Det Channel App Conf</short_name>
			<convention>0:Null,1:LLVD1 Extend Loop,2:LLVD2 Extend Loop,3:BLVD Extend Loop,4:DC Ext.Loop,5:TLLVD1 Loop-1,6:TLLVD1 Loop-2,7:TLLVD1 Loop-3,8:TLLVD1 Loop-4,9:TLLVD2 Loop-1,10:TLLVD2 Loop-2,11:TLLVD2 Loop-3,12:TLLVD2 Loop-4,13:TBLVD Loop-1,14:TBLVD Loop-2,15:TBLVD Loop-3,16:TBLVD Loop-4,17:Common LLVD2 Loop,18:Common BLVD Loop</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="sys_name" name="Power System Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Power System Name</full_name>
			<short_name>Power System Name</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="curr_share_mode" name="SMR Curr Share Mode" type="int">
			<min>1</min>
			<max>2</max>
			<precision></precision>
			<step></step>
			<full_name>SMR Current Share Mode</full_name>
			<short_name>SMR Curr Share Mode</short_name>
			<convention>1:Master Slave Curr Share,2:Packet Curr Share</convention>
			<visible>NO</visible>
			<default>1</default>
		</para>
		<para id="res_dc_in_shunt" name="Res DC In Shunt" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Reserve DC Input Shunt</full_name>
			<short_name>Res DC In Shunt</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="_res_int1_of_subrack" name="Subrack Res Integer1" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-1</full_name>
			<short_name>Subrack Reserve Integer-1</short_name>
			<convention>0:Common Scene,1:Shared Cobuild Scene</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int2_of_subrack" name="Subrack Res Integer2" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-2</full_name>
			<short_name>Subrack Reserve Integer-2</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int3_of_subrack" name="Subrack Res Integer3" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-3</full_name>
			<short_name>Subrack Reserve Integer-3</short_name>
			<convention>0:Exist,1:Null</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int4_of_subrack" name="Subrack Res Integer4" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-4</full_name>
			<short_name>Subrack Reserve Integer-4</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int5_of_subrack" name="Subrack Res Integer5" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-5</full_name>
			<short_name>Subrack Reserve Integer-5</short_name>
			<convention>0:0,50:50,75:75</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int6_of_subrack" name="Subrack Res Integer6" type="int">
			<min>0</min>
			<max>7</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-6</full_name>
			<short_name>Subrack Reserve Integer-6</short_name>
			<convention>0:No Config,1:Indoor Power System,2:Outdoor Power System,3:Wall Mounted Power System,4:Embedded Power System,5:Central office Power System,6:Additional Photovoltaic Power System,7:PAD Power System</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int7_of_subrack" name="Subrack Res Integer7" type="int">
			<min>0</min>
			<max>69</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-7</full_name>
			<short_name>Subrack Reserve Integer-7</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int8_of_subrack" name="Subrack Res Integer8" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-8</full_name>
			<short_name>Subrack Reserve Integer-8</short_name>
			<convention>0:Default,1:Phase_Special,2:Two_Acin_and_Special_Phase</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int9_of_subrack" name="Subrack Res Integer9" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-9</full_name>
			<short_name>Subrack Reserve Integer-9</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int10_of_subrack" name="Subrack Res Integer10" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-10</full_name>
			<short_name>Subrack Reserve Integer-10</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int11_of_subrack" name="Subrack Res Integer11" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-11</full_name>
			<short_name>Subrack Reserve Integer-11</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int12_of_subrack" name="Subrack Res Integer12" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-12</full_name>
			<short_name>Subrack Reserve Integer-12</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int13_of_subrack" name="Subrack Res Integer13" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-13</full_name>
			<short_name>Subrack Reserve Integer-13</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int14_of_subrack" name="Subrack Res Integer14" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-14</full_name>
			<short_name>Subrack Reserve Integer-14</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int15_of_subrack" name="Subrack Res Integer15" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-15</full_name>
			<short_name>Subrack Reserve Integer-15</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int16_of_subrack" name="Subrack Res Integer16" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve Integer-16</full_name>
			<short_name>Subrack Reserve Integer-16</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_str_of_subrack" name="Subrack Res String" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subrack Reserve String</full_name>
			<short_name>Subrack Reserve String</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.batt_input_asm" name="Battery Input Assemble" type="single">
		<para id="batt_asm_type" name="Batt Input Asm Type" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Battery Input Assemble Type</full_name>
			<short_name>Batt Input Asm Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt_asm_code" name="Batt Input Asm Code" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Battery Input Assemble Code</full_name>
			<short_name>Batt Input Asm Code</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt_asm_name" name="Batt Input Asm Name" type="string64">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Battery Input Assemble Name</full_name>
			<short_name>Batt Input Asm Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt_shunt" name="Battery Shunt" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Battery Shunt</full_name>
			<short_name>Battery Shunt</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="batt1_shunt1_conf" name="Batt1 Shunt1 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-1 Shunt-1 Config</full_name>
			<short_name>Batt-1 Shunt-1 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt1_shunt2_conf" name="Batt1 Shunt2 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-1 Shunt-2 Config</full_name>
			<short_name>Batt-1 Shunt-2 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt1_shunt3_conf" name="Batt1 Shunt3 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-1 Shunt-3 Config</full_name>
			<short_name>Batt-1 Shunt-3 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt1_shunt4_conf" name="Batt1 Shunt4 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-1 Shunt-4 Config</full_name>
			<short_name>Batt-1 Shunt-4 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="batt2_shunt1_conf" name="Batt2 Shunt1 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-2 Shunt-1 Config</full_name>
			<short_name>Batt-2 Shunt-1 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt2_shunt2_conf" name="Batt2 Shunt2 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-2 Shunt-2 Config</full_name>
			<short_name>Batt-2 Shunt-2 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt2_shunt3_conf" name="Batt2 Shunt3 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-2 Shunt-3 Config</full_name>
			<short_name>Batt-2 Shunt-3 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt2_shunt4_conf" name="Batt2 Shunt4 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-2 Shunt-4 Config</full_name>
			<short_name>Batt-2 Shunt-4 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt3_shunt1_conf" name="Batt3 Shunt1 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-3 Shunt-1 Config</full_name>
			<short_name>Batt-3 Shunt-1 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt3_shunt2_conf" name="Batt3 Shunt2 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-3 Shunt-2 Config</full_name>
			<short_name>Batt-3 Shunt-2 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt3_shunt3_conf" name="Batt3 Shunt3 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-3 Shunt-3 Config</full_name>
			<short_name>Batt-3 Shunt-3 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt3_shunt4_conf" name="Batt3 Shunt4 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-3 Shunt-4 Config</full_name>
			<short_name>Batt-3 Shunt-4 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt4_shunt1_conf" name="Batt4 Shunt1 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-4 Shunt-1 Config</full_name>
			<short_name>Batt-4 Shunt-1 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt4_shunt2_conf" name="Batt4 Shunt2 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-4 Shunt-2 Config</full_name>
			<short_name>Batt-4 Shunt-2 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt4_shunt3_conf" name="Batt4 Shunt3 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-4 Shunt-3 Config</full_name>
			<short_name>Batt-4 Shunt-3 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="batt4_shunt4_conf" name="Batt4 Shunt4 Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Battery-4 Shunt-4 Config</full_name>
			<short_name>Batt-4 Shunt-4 Conf</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default></default>
		</para>	
		<para id="_res_int1_of_batt_input" name="Batt Input Res Integer1" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Batt Input Reserve Integer-1</full_name>
			<short_name>Batt Input Res Integer-1</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int2_of_batt_input" name="Batt Input Res Integer2" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Batt Input Reserve Integer-2</full_name>
			<short_name>Batt Input Res Integer-2</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int3_of_batt_input" name="Batt Input Res Integer3" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Batt Input Reserve Integer-3</full_name>
			<short_name>Batt Input Res Integer-3</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int4_of_batt_input" name="Batt Input Res Integer4" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Batt Input Reserve Integer-4</full_name>
			<short_name>Batt Input Res Integer-4</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.ac_input_asm" name="AC Input Assemble" type="single">
	    <para id="ac_asm_type" name="AC Input Asm Type" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Assemble Type</full_name>
			<short_name>AC Input Asm Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="ac_asm_code" name="AC Input Asm Code" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Assemble Code</full_name>
			<short_name>AC Input Asm Code</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="ac_asm_name" name="AC Input Asm Name" type="string64">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Assemble Name</full_name>
			<short_name>AC Input Asm Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="ac_in_sw_det" name="AC Input Switch Det" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Switch Detect</full_name>
			<short_name>AC Input Switch Det</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ac_spd_supply" name="AC SPD Power Supply" type="int">
		    <min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>AC SPD Power Supply</full_name>
			<short_name>AC SPD Power Supply</short_name>
			<convention>0:L1L2L3N-220V,1:L1L2L3-110V,2:L1N-220V,3:L1L2-110V</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ac_spd_level" name="AC SPD Level" type="int">
		    <min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>AC SPD Level</full_name>
			<short_name>AC SPD Level</short_name>
			<convention>0:B,1:B+C,2:C,3:NULL</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="ac_spd_det" name="AC SPD Det" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>AC SPD Detect</full_name>
			<short_name>AC SPD Det</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ac_out_sw_det" name="AC Output Switch Det" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>AC Output Switch Detect</full_name>
			<short_name>AC Output Switch Det</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ac_tm_conf" name="ACTM Config" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>AC Transmitter Config</full_name>
			<short_name>ACTM Config</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="_res_int1_of_ac_input" name="AC Input Res Integer1" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Res Integer-1</full_name>
			<short_name>AC Input Res Integer-1</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int2_of_ac_input" name="AC Input Res Integer2" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Res Integer-2</full_name>
			<short_name>AC Input Res Integer-2</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int3_of_ac_input" name="AC Input Res Integer3" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Res Integer-3</full_name>
			<short_name>AC Input Res Integer-3</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int4_of_ac_input" name="AC Input Res Integer4" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>AC Input Res Integer-4</full_name>
			<short_name>AC Input Res Integer-4</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.outdoor_cabi" name="OutDoor Cabinet" type="single">
		<para id="out_cabi_type" name="OutDoor Cabinet Type" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>OutDoor Cabinet Type</full_name>
			<short_name>OutDoor Cabinet Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="out_cabi_code" name="OutDoor Cabinet Code" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>OutDoor Cabinet Code</full_name>
			<short_name>OutDoor Cabinet Code</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="out_cabi_name" name="OutDoor Cabinet Name" type="string64">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>OutDoor Cabinet Name</full_name>
			<short_name>OutDoor Cabinet Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="door_det" name="Door Detect" type="int">
		    <min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Door Detect</full_name>
			<short_name>Door Detect</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="compart_type1" name="Compartment Type1" type="int">
		    <min>0</min>
			<max>5</max>
			<precision></precision>
			<step></step>
			<full_name>Compartment Type-1</full_name>
			<short_name>Compartment Type-1</short_name>
			<convention>0:Power cabinet,1:Equipment cabinet,2:Battery cabinet,3:Integrated Cabinet-power,4:Integrated Cabinet-battery,5:Integrated Cabinet-equipment</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="temp_ctl_u_type1" name="Temp Ctl Unit Type1" type="int"> 
		    <min>0</min>
			<max>7</max>
			<precision></precision>
			<step></step>
			<full_name>Temper Control Unit Type-1</full_name>
			<short_name>Temp Ctl Unit Type-1</short_name>
			<convention>0:FAN,1:HEX,2:DC Aircon,3:TEC Aircon,4:NULL,5:FAN+DC Aircon,6:Dual DC Aircon,7:FAN+AC Aircon</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="temp_ctl_u_spec1" name="Temp Ctl Unit Spec1" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Temper Control Unit Spec-1</full_name>
			<short_name>Temp Ctl Unit Spec-1</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fctl_sys_name1" name="FCTL Board Model1" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FCTL Board Model-1</full_name>
			<short_name>FCTL Board Model-1</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fctl_sys_ver1" name="FCTL Software Ver1" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FCTL Software Version-1</full_name>
			<short_name>FCTL Software Version-1</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="compart_type2" name="Compartment Type2" type="int">
		    <min>0</min>
			<max>5</max>
			<precision></precision>
			<step></step>
			<full_name>Compartment Type-2</full_name>
			<short_name>Compartment Type-2</short_name>
			<convention>0:Power cabinet,1:Equipment cabinet,2:Battery cabinet,3:Integrated Cabinet-power,4:Integrated Cabinet-battery,5:Integrated Cabinet-equipment</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="temp_ctl_u_type2" name="Temp Ctl Unit Type2" type="int">
		    <min>0</min>
			<max>7</max>
			<precision></precision>
			<step></step>
			<full_name>Temper Control Unit Type-2</full_name>
			<short_name>Temp Ctl Unit Type-2</short_name>
			<convention>0:FAN,1:HEX,2:DC Aircon,3:TEC Aircon,4:NULL,5:FAN+DC Aircon,6:Dual DC Aircon,7:FAN+AC Aircon</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="temp_ctl_u_spec2" name="Temp Ctl Unit Spec2" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Temper Control Unit Spec-2</full_name>
			<short_name>Temp Ctl Unit Spec-2</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fctl_sys_name2" name="FCTL Board Model2" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FCTL Board Model-2</full_name>
			<short_name>FCTL Board Model-2</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fctl_sys_ver2" name="FCTL Software Ver2" type="string32">
		    <min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FCTL Software Version-2</full_name>
			<short_name>FCTL Software Version-2</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="_res_int1_of_outdoor_cabi" name="Outdoor Cabi Res Integer1" type="int">
			<min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-1</full_name>
			<short_name>Outdoor Cabi Res Integer-1</short_name>
			<convention>0:Null,1:Heater-Uncontrolled,2:Heating film,3:Heater-Controlled</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int2_of_outdoor_cabi" name="Outdoor Cabi Res Integer2" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-2</full_name>
			<short_name>Outdoor Cabi Res Integer-2</short_name>
			<convention>0:Null,1:Exist</convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int3_of_outdoor_cabi" name="Outdoor Cabi Res Integer3" type="int">
			<min>0</min>
			<max>2</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-3</full_name>
			<short_name>Outdoor Cabi Res Integer-3</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int4_of_outdoor_cabi" name="Outdoor Cabi Res Integer4" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-4</full_name>
			<short_name>Outdoor Cabi Res Integer-4</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int5_of_outdoor_cabi" name="Outdoor Cabi Res Integer5" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-5</full_name>
			<short_name>Outdoor Cabi Res Integer-5</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int6_of_outdoor_cabi" name="Outdoor Cabi Res Integer6" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-6</full_name>
			<short_name>Outdoor Cabi Res Integer-6</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int7_of_outdoor_cabi" name="Outdoor Cabi Res Integer7" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-7</full_name>
			<short_name>Outdoor Cabi Res Integer-7</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int8_of_outdoor_cabi" name="Outdoor Cabi Res Integer8" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-8</full_name>
			<short_name>Outdoor Cabi Res Integer-8</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int9_of_outdoor_cabi" name="Outdoor Cabi Res Integer9" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-9</full_name>
			<short_name>Outdoor Cabi Res Integer-9</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int10_of_outdoor_cabi" name="Outdoor Cabi Res Integer10" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-10</full_name>
			<short_name>Outdoor Cabi Res Integer-10</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int11_of_outdoor_cabi" name="Outdoor Cabi Res Integer11" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-11</full_name>
			<short_name>Outdoor Cabi Res Integer-11</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int12_of_outdoor_cabi" name="Outdoor Cabi Res Integer12" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-12</full_name>
			<short_name>Outdoor Cabi Res Integer-12</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int13_of_outdoor_cabi" name="Outdoor Cabi Res Integer13" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-13</full_name>
			<short_name>Outdoor Cabi Res Integer-13</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int14_of_outdoor_cabi" name="Outdoor Cabi Res Integer14" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-14</full_name>
			<short_name>Outdoor Cabi Res Integer-14</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int15_of_outdoor_cabi" name="Outdoor Cabi Res Integer15" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-15</full_name>
			<short_name>Outdoor Cabi Res Integer-15</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int16_of_outdoor_cabi" name="Outdoor Cabi Res Integer16" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-16</full_name>
			<short_name>Outdoor Cabi Res Integer-16</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int17_of_outdoor_cabi" name="Outdoor Cabi Res Integer17" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-17</full_name>
			<short_name>Outdoor Cabi Res Integer-17</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int18_of_outdoor_cabi" name="Outdoor Cabi Res Integer18" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-18</full_name>
			<short_name>Outdoor Cabi Res Integer-18</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int19_of_outdoor_cabi" name="Outdoor Cabi Res Integer19" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-19</full_name>
			<short_name>Outdoor Cabi Res Integer-19</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int20_of_outdoor_cabi" name="Outdoor Cabi Res Integer20" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-20</full_name>
			<short_name>Outdoor Cabi Res Integer-20</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int21_of_outdoor_cabi" name="Outdoor Cabi Res Integer21" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-21</full_name>
			<short_name>Outdoor Cabi Res Integer-21</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int22_of_outdoor_cabi" name="Outdoor Cabi Res Integer22" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-22</full_name>
			<short_name>Outdoor Cabi Res Integer-22</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int23_of_outdoor_cabi" name="Outdoor Cabi Res Integer23" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-23</full_name>
			<short_name>Outdoor Cabi Res Integer-23</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_int24_of_outdoor_cabi" name="Outdoor Cabi Res Integer24" type="int">
			<min>0</min>
			<max>0</max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res Integer-24</full_name>
			<short_name>Outdoor Cabi Res Integer-24</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default>0</default>
		</para>
		<para id="_res_str_of_outdoor_cabi" name="Outdoor Cabi Res String" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Outdoor Cabi Res String</full_name>
			<short_name>Outdoor Cabi Res String</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.sps_dev_type" name="sps device type" type="multi">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="dev_type_name" name="device type name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type name</full_name>
			<short_name>dev type name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="dev_type_alias" name="device type alias" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type alias</full_name>
			<short_name>dev type alias</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_protocol_id" name="sps protocol id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps protocol id</full_name>
			<short_name>sps protocol id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="location" name="location" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>location</full_name>
			<short_name>location</short_name>
			<convention>0:north,1:south,2:both</convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.com_nms" name="nms on com link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="share_com_with_nms_id" name="share com with nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>share com with nms id</full_name>
			<short_name>share com with nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.north_download_ip_comm" name="north download ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>no</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
            <visible>no</visible>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>1234</default>
			<visible>no</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>no</visible>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>0</default>
            <visible>no</visible>
        </para>
	</cfgobject>
	<cfgobject id="pdt.1104_nms_ip_comm" name="1104 nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>4000</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="pdt.1363_nms_ip_comm" name="1363 nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>4000</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="pdt.sm_nms_ip_comm" name="sm nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>4000</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
		<para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="pdt.power_sm_nms_ip_comm" name="power_sm nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>4000</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
		<para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="pdt.a_inter_nms_ip_comm" name="a inter nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>4000</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="pdt.gc_modbus_tcp_nms_ip_comm" name="gc modbus tcp nms ip comm configure" type="single">
		<para id="csu_role" name="csu role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>csu role</full_name>
			<short_name>csu role</short_name>
			<convention>0:as client,1:as server</convention>
			<default>1</default>
			<visible>yes</visible>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="listen_ip" name="listen ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen ip address</full_name>
			<short_name>listen ip address</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="listen_port" name="listen port" type="int">
			<min>1</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>listen port</full_name>
			<short_name>listen port</short_name>
			<convention></convention>
			<default>502</default>
			<visible>yes</visible>
		</para>
		<para id="inform_port" name="inform port" type="int">
			<min>1025</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>inform port</full_name>
			<short_name>inform port</short_name>
			<convention></convention>
			<default>3000</default>
			<visible>yes</visible>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
    <cfgobject id="pdt.ip_north_download_nms" name="north download on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.ip_1104_nms" name="1104 nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.ip_1363_nms" name="1363 nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.ip_sm_nms" name="sm nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.ip_power_sm_nms" name="power_sm nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
    <cfgobject id="pdt.ip_a_inter_nms" name="a inter nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.ip_gc_modbus_tcp_nms" name="gc modbus tcp nms on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="type" name="nms type" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>nms type</full_name>
			<short_name>nms type</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="union_nms_id" name="union nms id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>union nms id</full_name>
			<short_name>union nms id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.ip_loc_gc_modbus_tcp" name="loc gc modbus tcp on ip link" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt_sps_north_protocol" name="sps north protocol" type="multi">
		<para id="protocol_alias" name="protocol alias" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps north protocol name</full_name>
			<short_name>protocol name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.com_sdev" name="south device on com link" type="multi">
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_ids" name="link inst ids" type="string256">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id array</full_name>
			<short_name>link inst id array</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="dev_num" name="dev number" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>south device number</full_name>
			<short_name>south dev num</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="dev_base_addr" name="dev base addr" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>south device base address</full_name>
			<short_name>south dev base addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="dev_custom_addr" name="dev custom addr" type="string256">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>south device custom address</full_name>
			<short_name>south dev custom addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="pdt.custom_func" name="CUSTOM" type="single">
		<para id="slow_curr_limit" name="custom slow curr limiting" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>custom slow curr limiting</full_name>
			<short_name>slow curr limiting</short_name>
			<convention>0:disable,1:enable</convention>
			<default>0</default>
			<visible>no</visible>
		</para>
		<para id="csu_power" name="custom csu power" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>custom csu power</full_name>
			<short_name>csu power</short_name>
			<convention>0:disable,1:enable</convention>
			<default>0</default>
			<visible>no</visible>
		</para>
		<para id="volt_limit_gen_power" name="custom volt limit gen power" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>custom volt limit gen power</full_name>
			<short_name>volt limit gen power</short_name>
			<convention>0:disable,1:enable</convention>
			<default>0</default>
			<visible>no</visible>
		</para>		
		<para id="custom_smr_name" name="custom smr name" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>custom smr name</full_name>
			<short_name>custom smr name</short_name>
			<convention>0:disable,1:enable</convention>
			<default>0</default>
			<visible>no</visible>
		</para>		
	</cfgobject>
	<cfgobject id="pdt.plc_enbale_status" name="PLC Function Enable Status Config" type="single">
		<para id="enable_status" name="PLC Function Enable Status" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>PLC Function Enable Status</full_name>
			<short_name>PLC Function Enable Status</short_name>
			<convention>0:disable,1:enable</convention>
			<default>0</default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.plc_group" name="PLC Group Config" type="multi">
		<para id="group_no" name="Group No" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group No</full_name>
			<short_name>Group No</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="description" name="Group Description" type="string256">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group Description</full_name>
			<short_name>Group Description</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="output_type" name="Group Output Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group Output Type</full_name>
			<short_name>Group Output Type</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="output_sid" name="Group Output Sid" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group Output Sid</full_name>
			<short_name>Group Output Sid</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="output_alias" name="Group Output Alias" type="string256">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group Output Alias</full_name>
			<short_name>Group Output Alias</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="status" name="Group Status" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Group Status</full_name>
			<short_name>Group Status</short_name>
			<convention>0:unactive,1:active,2:invalid</convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.plc_sentence" name="PLC Sentence Config" type="multi">
		<para id="group_no" name="Group No" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>PLC Function Group Number</full_name>
			<short_name>PLC Function Group No</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="pre_operand_type" name="Preposition Operand Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>PLC Sentence Preposition Operand Type</full_name>
			<short_name>PLC Sentence Preposition Operand Type</short_name>
			<convention>0:sid,1:register,2:constant </convention>
			<default></default>
		</para>
		<para id="pre_operand_value" name="Preposition Operand Value" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>PLC Sentence Preposition Operand Value</full_name>
			<short_name>PLC Sentence Preposition Operand Value</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="mid_operator" name="Middle Operator" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>PLC Sentence Middle Operator</full_name>
			<short_name>PLC Sentence Middle Operator</short_name>
			<convention>0:and,1:or,2:not,3:gt,4:lt,5:eq,6:neq,7:par</convention>
			<default></default>
		</para>
		<para id="operator_extra_attr" name="Operator Extra Attr" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Operator Extra Attr</full_name>
			<short_name>Operator Extra Attr</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="pos_operand_type" name="Postposition Operand Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Postposition Operand Type</full_name>
			<short_name>Postposition Operand Type</short_name>
			<convention>0:sid,1:register,2:constant</convention>
			<default></default>
		</para>
		<para id="pos_operand_value" name="Postposition Operand Value" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Postposition Operand Value</full_name>
			<short_name>Postposition Operand Value</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="output_type" name="Output Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Output Type</full_name>
			<short_name>Output Type</short_name>
			<convention>0:sid,1:register,2:do</convention>
			<default></default>
		</para>
		<para id="output_value" name="Output Value" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Output Value</full_name>
			<short_name>Output Value</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="status" name="Output Default Status" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Output Default Status</full_name>
			<short_name>Output Default Status</short_name>
			<convention>0:reset,1:set</convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.remote_update_old_naming" name="remote update old naming" type="multi">
		<para id="dev_type" name="device type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type</full_name>
			<short_name>device type</short_name>
			<convention>1:CSU,7:SMR,16:PU,23:FBBMS,33:NFBBMS,34:SDDU,35:SPCU,36:SPU,41:SDDU_SSW_63,1041:SDDU_SSW_125,44:SSW_63,1044:SSW_125,1045:ssw_63_A0,1046:ssw_125_A0,-30:AEMB,-1:UIB,-2:IDDB,-34:SMB,61:W121_FCTL,1061:SF01_FCTL,66:ACMU</convention>
			<default></default>
		</para>
		<para id="keyword1" name="keyword1" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword1</full_name>
			<short_name>keyword1</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="keyword2" name="keyword2" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword2</full_name>
			<short_name>keyword2</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="keyword3" name="keyword3" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword3</full_name>
			<short_name>keyword3</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="suffix" name="suffix" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>suffix</full_name>
			<short_name>suffix</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="full_name" name="full name" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ful name</full_name>
			<short_name>full name</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.remote_update_new_naming" name="remote update new naming" type="multi">
		<para id="dev_type" name="device type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type</full_name>
			<short_name>device type</short_name>
			<convention>1:CSU,7:SMR,16:PU,23:FBBMS,33:NFBBMS,34:SDDU,35:SPCU,36:SPU,41:SDDU_SSW_63,1041:SDDU_SSW_125,44:SSW_63,1044:SSW_125,1045:ssw_63_A0,1046:ssw_125_A0,-30:AEMB,-1:UIB,-2:IDDB,-34:SMB,61:W121_FCTL,1061:SF01_FCTL,66:ACMU</convention>
			<default></default>
		</para>
		<para id="keyword1" name="keyword1" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword1</full_name>
			<short_name>keyword1</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="keyword2" name="keyword2" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword2</full_name>
			<short_name>keyword2</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="keyword3" name="keyword3" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>keyword3</full_name>
			<short_name>keyword3</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="suffix" name="suffix" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>suffix</full_name>
			<short_name>suffix</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="full_name" name="full name" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ful name</full_name>
			<short_name>full name</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="pdt.edu_ntp_config" name="edu ntp config" type="multi">
		<para id="ntp_ip" name="ipv4 address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ipv4 address</full_name>
			<short_name>ipv4 address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="ntp_port" name="ipv4 port" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ipv4 port</full_name>
			<short_name>ipv4 port</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="ntp_syn_period" name="ipv4 syn period" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ipv4 syn period</full_name>
			<short_name>ipv4 syn period</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
</config>
