#######################################################################
##
## /etc/lighttpd/lighttpd.conf
##
## check /etc/lighttpd/conf.d/*.conf for the configuration of modules.
##
#######################################################################

#######################################################################
##
## Some Variable definition which will make chrooting easier.
##
## if you add a variable here. Add the corresponding variable in the
## chroot example aswell.
##
#var.state_dir   = "/opt/web/lighttpd"

##var.log_root    = "/var/log/lighttpd"
var.server_root = "/root/power/etc/web/"
var.home_dir    = "/var"
var.conf_dir    = "/root/power/etc/lighttpd/config"

## 
## run the server chrooted.
## 
## This requires root permissions during startup.
##
## If you run Chrooted set the the variables to directories relative to
## the chroot dir.
##
## example chroot configuration:
## 
#var.log_root    = "/logs"
#var.server_root = "/"
#var.state_dir   = "/run"
#var.home_dir    = "/lib/lighttpd"
#var.vhosts_dir  = "/vhosts"
#var.conf_dir    = "/etc"
#
#server.chroot   = "/srv/www"

##
## Some additional variables to make the configuration easier
##

##
## Base directory for all virtual hosts
##
## used in:
## conf.d/evhost.conf
## conf.d/simple_vhost.conf
## vhosts.d/vhosts.template
##
#var.vhosts_dir  = server_root + "/vhosts"

##
## Cache for mod_compress
##
## used in:
## conf.d/compress.conf
##
#var.cache_dir   = server_root + "/cache"

##
## Base directory for sockets.
##
## used in:
## conf.d/fastcgi.conf
## conf.d/scgi.conf
##
var.socket_dir  = home_dir + "/sockets"

##
#######################################################################

#######################################################################
##
## Load the modules.
include "modules.conf"

##
#######################################################################

#######################################################################
##
##  Basic Configuration
## ---------------------
##
##
## Use IPv6?
##
server.use-ipv6 = "enable"
server.set-v6only = "disable"
#server.port = 443
$SERVER["socket"] == ":443" {
    ssl.engine = "enable"
	ssl.honor-cipher-order = "enable"
	ssl.use-sslv2 = "disable" 
	ssl.use-sslv3 = "disable" 
	ssl.use-compression = "disable"
	ssl.pemfile = "/mnt/data/work/web/ssl/lighttpd.pem"
	ssl.ca-file = "/mnt/data/work/web/ssl/lighttpd.crt"
	ssl.dh-file = "/mnt/data/work/web/ssl/dh2048.pem"
	ssl.ec-curve = "secp384r1"
	##ssl.cipher-list = "HIGH:!aNULL:!eNULL:!NULL:!EXPORT:!DES:!MD5:!PSK:!RC2:!RC4:!DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA"
	#ssl.cipher-list = "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH"
	ssl.cipher-list = "ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:!DHE-RSA-AES128-GCM-SHA256:!DHE-DSS-AES128-GCM-SHA256:!kEDH+AESGCM:!ECDHE-RSA-AES128-SHA256:!ECDHE-ECDSA-AES128-SHA256:!ECDHE-ECDSA-AES128-SHA:!ECDHE-RSA-AES256-SHA384:!ECDHE-ECDSA-AES256-SHA384:!ECDHE-ECDSA-AES256-SHA:!DHE-RSA-AES128-SHA256:!DHE-DSS-AES128-SHA256:!DHE-RSA-AES256-SHA256:!DHE-DSS-AES256-SHA:!AES128-GCM-SHA256:!AES256-GCM-SHA384:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA"
	setenv.add-response-header = ("Strict-Transport-Security" => "max-age=63072000; includeSubDomains; preload", "X-Frame-Options" => "DENY", "X-Content-Type-Options" => "nosniff")
}
#$SERVER["socket"] == "[::]:442" {
#    accesslog.filename = server_root + "/log/ipv6.access.log"
#	ssl.engine = "enable"
#	ssl.pemfile = "/root/esmu/etc/lighttpd/ssl/lighttpd.pem"
#   ssl.ca-file = "/root/esmu/etc/lighttpd/ssl/lighttpd.crt"
#}

##
## bind to a specific IP
##
#server.bind = "localhost"

##
## Run as a different username/groupname.
## This requires root permissions during startup. 
##
#server.username  = "lighttpd"
#server.groupname = "lighttpd"

## 
## enable core files.
##
#server.core-files = "disable"

##
## Document root
##
server.document-root = var.server_root

##
## The value for the "Server:" response field.
##
## It would be nice to keep it at "lighttpd".
##
server.tag = "zte_power"

##
## store a pid file
##
#server.pid-file = state_dir + "/lighttpd.pid"

##
#######################################################################

#######################################################################
##
##  Logging Options
## ------------------
##
## all logging options can be overwritten per vhost.
##
## Path to the error log file
##
##server.errorlog             = log_root + "/error.log"

##
## If you want to log to syslog you have to unset the 
## server.errorlog setting and uncomment the next line.
##
#server.errorlog-use-syslog = "enable"

##
## Access log config
## 
#include "conf.d/access_log.conf"

##
## The debug options are moved into their own file.
## see conf.d/debug.conf for various options for request debugging.
##
#include "conf.d/debug.conf"

##
#######################################################################

#######################################################################
##
##  Tuning/Performance
## --------------------
##
## corresponding documentation:
## http://www.lighttpd.net/documentation/performance.html
##
## set the event-handler (read the performance section in the manual)
##
## possible options on linux are:
##
## select
## poll
## linux-sysepoll
##
## linux-sysepoll is recommended on kernel 2.6.
##
server.event-handler = "linux-sysepoll"

##
## The basic network interface for all platforms at the syscalls read()
## and write(). Every modern OS provides its own syscall to help network
## servers transfer files as fast as possible 
##
## sendfile       - is recommended for small files.
## writev         - is recommended for sending many large files
##
#server.network-backend = "sendfile"

##
## As lighttpd is a single-threaded server, its main resource limit is
## the number of file descriptors, which is set to 1024 by default (on
## most systems).
##
## If you are running a high-traffic site you might want to increase this
## limit by setting server.max-fds.
##
## Changing this setting requires root permissions on startup. see
## server.username/server.groupname.
##
## By default lighttpd would not change the operation system default.
## But setting it to 2048 is a better default for busy servers.
##
server.max-fds = 1024

##
## listen-backlog is the size of the listen() backlog queue requested when
## the lighttpd server ask the kernel to listen() on the provided network
## address.  Clients attempting to connect() to the server enter the listen()
## backlog queue and wait for the lighttpd server to accept() the connection.
##
## The out-of-box default on many operating systems is 128 and is identified
## as SOMAXCONN.  This can be tuned on many operating systems.  (On Linux,
## cat /proc/sys/net/core/somaxconn)  Requesting a size larger than operating
## system limit will be silently reduced to the limit by the operating system.
##
## When there are too many connection attempts waiting for the server to
## accept() new connections, the listen backlog queue fills and the kernel
## rejects additional connection attempts.  This can be useful as an
## indication to an upstream load balancer that the server is busy, and
## possibly overloaded.  In that case, configure a smaller limit for
## server.listen-backlog.  On the other hand, configure a larger limit to be
## able to handle bursts of new connections, but only do so up to an amount
## that the server can keep up with responding in a reasonable amount of
## time.  Otherwise, clients may abandon the connection attempts and the
## server will waste resources servicing abandoned connections.
##
## It is best to leave this setting at its default unless you have modelled
## your traffic and tested that changing this benefits your traffic patterns.
##
## Default: 1024
##
#server.listen-backlog = 128

##
## Stat() call caching.
##
## lighttpd can utilize FAM/Gamin to cache stat call.
##
## possible values are:
## disable, simple or fam.
##
server.stat-cache-engine = "simple"

##
## Fine tuning for the request handling
##
## max-connections == max-fds/2 (maybe /3)
## means the other file handles are used for fastcgi/files
##
server.max-connections = 512

##
## How many seconds to keep a keep-alive connection open,
## until we consider it idle. 
##
## Default: 5
##
#server.max-keep-alive-idle = 5

##
## How many keep-alive requests until closing the connection.
##
## Default: 16
##
#server.max-keep-alive-requests = 16

##
## Maximum size of a request in kilobytes.
## By default it is unlimited (0).
##
## Uploads to your server cant be larger than this value.
##
#server.max-request-size = 40960

##
## Time to read from a socket before we consider it idle.
##
## Default: 60
##
server.max-read-idle = 60

##
## Time to write to a socket before we consider it idle.
##
## Default: 360
##
server.max-write-idle = 60

##
##  Traffic Shaping 
## -----------------
##
## see /usr/share/doc/lighttpd/traffic-shaping.txt
##
## Values are in kilobyte per second.
##
## Keep in mind that a limit below 32kB/s might actually limit the
## traffic to 32kB/s. This is caused by the size of the TCP send
## buffer. 
##
## per server:
##
#server.kbytes-per-second = 128

##
## per connection:
##
#connection.kbytes-per-second = 32

##
#######################################################################

#######################################################################
##
##  Filename/File handling
## ------------------------

##
## files to check for if .../ is requested
## index-file.names            = ( "index.php", "index.rb", "index.html",
##                                 "index.htm", "default.htm" )
##
index-file.names += (
  "index.xhtml", "index.html", "index.htm", "default.htm", "index.php"
)

##
## deny access the file-extensions
##
## ~    is for backupfiles from vi, emacs, joe, ...
## .inc is often used for code includes which should in general not be part
##      of the document-root
url.access-deny             = ( "~", ".inc" )

##
## disable range requests for pdf files
## workaround for a bug in the Acrobat Reader plugin.
##
#$HTTP["url"] =~ "\.pdf$" {
#  server.range-requests = "disable"
#}

##
## url handling modules (rewrite, redirect)
##
#url.rewrite                = ( "^/$"             => "/server-status" )
#url.redirect               = ( "^/wishlist/(.+)" => "http://www.example.com/$1" )

##
## both rewrite/redirect support back reference to regex conditional using %n
##
#$HTTP["host"] =~ "^www\.(.*)" {
#  url.redirect            = ( "^/(.*)" => "http://%1/$1" )
#}
   
$HTTP["scheme"] == "http" {
    # capture vhost name with regex conditiona -> %0 in redirect pattern
    # must be the most inner block to the redirect rule
    $HTTP["host"] =~ ".*" {
        url.redirect = (".*" => "https://%0$0")
    }
}

##
## which extensions should not be handle via static-file transfer
##
## .php, .pl, .fcgi are most often handled by mod_fastcgi or mod_cgi
##
#static-file.exclude-extensions = ( ".php", ".pl", ".fcgi", ".scgi" )

##
## error-handler for all status 400-599
##
#server.error-handler       = "/error-handler.html"
#server.error-handler       = "/error-handler.php"

##
## error-handler for status 404
##
#server.error-handler-404   = "/error-handler.html"
#server.error-handler-404   = "/error-handler.php"

##
## Format: <errorfile-prefix><status-code>.html
## -> ..../status-404.html for 'File not found'
##
#server.errorfile-prefix    = "/srv/www/htdocs/errors/status-"

##
## mimetype mapping
##
include "conf.d/mime.conf"

##
## directory listing configuration
##
include "conf.d/dirlisting.conf"

##
## Should lighttpd follow symlinks?
## 
server.follow-symlink = "enable"

##
## force all filenames to be lowercase?
##
#server.force-lowercase-filenames = "disable"

##
## defaults to /var/tmp as we assume it is a local harddisk
##
server.upload-dirs = ( "/mnt/data" )

##
#######################################################################


#######################################################################
##
##  SSL Support
## ------------- 
##
## To enable SSL for the whole server you have to provide a valid
## certificate and have to enable the SSL engine.::
##
##   ssl.engine = "enable"
##   ssl.pemfile = "/path/to/server.pem"
##
## The HTTPS protocol does not allow you to use name-based virtual
## hosting with SSL. If you want to run multiple SSL servers with
## one lighttpd instance you must use IP-based virtual hosting: ::
##
## Mitigate CVE-2009-3555 by disabling client triggered renegotation
## This is enabled by default.
##
## IMPORTANT: this setting can only be used in the global scope.
## It does *not* work inside conditionals
##
     ssl.disable-client-renegotiation = "enable"
##
##   $SERVER["socket"] == "********:443" {
##     ssl.engine                  = "enable"
##     ssl.pemfile                 = "/etc/ssl/private/www.example.com.pem"
##     #
##     # (Following SSL/TLS Deployment Best Practices 1.3 / 17 September 2013 from:
##     # https://www.ssllabs.com/projects/best-practices/index.html)
##     # - BEAST is considered mitigaed on client side now, and new weaknesses have been found in RC4,
##     #   so it is strongly advised to disable RC4 ciphers (HIGH doesn't include RC4)
##     # - It is recommended to disable 3DES too (although disabling RC4 and 3DES breaks IE6+8 on Windows XP,
##     #   so you might want to support 3DES for now - just remove the '!3DES' parts below).
##     # - The examples below prefer ciphersuites with "Forward Secrecy" (and ECDHE over DHE (alias EDH)), remove '+kEDH +kRSA'
##     #   if you don't want that.
##     # - SRP and PSK are not supported anyway, excluding those ('!kSRP !kPSK') just keeps the list smaller (easier to review)
##     # Check your cipher list with: openssl ciphers -v '...' (use single quotes as your shell won't like ! in double quotes)
##     #
##     # If you know you have RSA keys (standard), you can use:
##     ssl.cipher-list             = "aRSA+HIGH !3DES +kEDH +kRSA !kSRP !kPSK"
##     # The more generic version (without the restriction to RSA keys) is
##     # ssl.cipher-list           = "HIGH !aNULL !3DES +kEDH +kRSA !kSRP !kPSK"
##     #
##     # Make the server prefer the order of the server side cipher suite instead of the client suite.
##     # This option is enabled by default, but only used if ssl.cipher-list is set.
##     #
##     # ssl.honor-cipher-order = "enable"
##     #
##     server.name                 = "www.example.com"
##
##     server.document-root        = "/srv/www/vhosts/example.com/www/"
##   }
##

## If you have a .crt and a .key file, cat them together into a
## single PEM file:
## $ cat /etc/ssl/private/lighttpd.key /etc/ssl/certs/lighttpd.crt \
##   > /etc/ssl/private/lighttpd.pem
##
#ssl.pemfile = "/etc/ssl/private/lighttpd.pem"

##
## optionally pass the CA certificate here.
##
##
#ssl.ca-file = ""

##
#######################################################################

#######################################################################
##
## custom includes like vhosts.
##
#include "conf.d/config.conf"
#include_shell "cat /etc/lighttpd/vhosts.d/*.conf"
##
#######################################################################
evasive.max-conns-per-ip = 16