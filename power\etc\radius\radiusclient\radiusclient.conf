# General settings

# specify which authentication comes first respectively which
# authentication is used. possible values are: "radius" and "local".
# if you specify "radius,local" then the RADIUS server is asked
# first then the local one. if only one keyword is specified only
# this server is asked.
auth_order	radius,local

# maximum login tries a user has
login_tries	4

# timeout for all login tries
# if this time is exceeded the user is kicked out
login_timeout	60

# name of the nologin file which when it exists disables logins.
# it may be extended by the ttyname which will result in
# a terminal specific lock (e.g. /etc/nologin.ttyS2 will disable
# logins on /dev/ttyS2)
nologin /etc/nologin

# name of the issue file. it's only display when no username is passed
# on the radlogin command line
issue	/root/gerrit/ZXDUPA-PMSA-V3/platform/power/etc/radius/etc/radiusclient/issue

# RADIUS settings

# RADIUS server to use for authentication requests. this config
# item can appear more then one time. if multiple servers are
# defined they are tried in a round robin fashion if one
# server is not answering.
# optionally you can specify a the port number on which is remote
# RADIUS listens separated by a colon from the hostname. if
# no port is specified /etc/services is consulted of the radius
# service. if this fails also a compiled in default is used.
authserver 	************* #localhost

# RADIUS server to use for accouting requests. All that I
# said for authserver applies, too. 
#
acctserver 	************* #localhost

# file holding shared secrets used for the communication
# between the RADIUS client and server
servers		/mnt/radius/etc/radiusclient/servers

# dictionary of allowed attributes and values
# just like in the normal RADIUS distributions
dictionary 	/root/gerrit/ZXDUPA-PMSA-V3/platform/power/etc/radius/etc/radiusclient/dictionary

# program to call for a RADIUS authenticated login
login_radius	/root/gerrit/ZXDUPA-PMSA-V3/platform/power/etc/radius/sbin/login.radius

# file which holds sequence number for communication with the
# RADIUS server
seqfile		/var/run/radius.seq

# file which specifies mapping between ttyname and NAS-Port attribute
mapfile		/root/gerrit/ZXDUPA-PMSA-V3/platform/power/etc/radius/etc/radiusclient/port-id-map

# default authentication realm to append to all usernames if no
# realm was explicitly specified by the user
# the radiusd directly form Livingston doesnt use any realms, so leave
# it blank then
default_realm

# time to wait for a reply from the RADIUS server
radius_timeout	10

# resend request this many times before trying the next server
radius_retries	3

# The length of time in seconds that we skip a nonresponsive RADIUS
# server for transaction requests.  Server(s) being in the "dead" state
# are tried only after all other non-dead servers have been tried and
# failed or timeouted.  The deadtime interval starts when the server
# does not respond to an authentication/accounting request transmissions. 
# When the interval expires, the "dead" server would be re-tried again,
# and if it's still down then it will be considered "dead" for another
# such interval and so on. This option is no-op if there is only one
# server in the list. Set to 0 in order to disable the feature.
radius_deadtime	0

# local address from which radius packets have to be sent
bindaddr *

# LOCAL settings

# program to execute for local login
# it must support the -f flag for preauthenticated login
login_local	/bin/login
