<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
        <style>
            .user-dis{
                display: none;
            }
        </style>
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="northnet">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.northnet.north_net_para}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>

                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                            <!--PAGE CONTENT BEGINS-->
                            <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'wired' == tab_list?'active':''" ms-click="tabChange('wired')">
                                        <a data-toggle="tab" href="#wired">
                                            <i class="blue icon-cog bigger-110"></i>
                                            {{i18nkeyword.northnet.wired_connection}}
                                        </a>
                                    </li>
                                    <li ms-class="'wireless' == tab_list?'active':''" ms-click="tabChange('wireless')">
                                        <a data-toggle="tab" href="#wireless">
                                            <i class="blue icon-cog bigger-110"></i>
                                            {{i18nkeyword.northnet.wireless_connection}}
                                        </a>
                                    </li>
                                    <li ms-class="'vpn' == tab_list?'active':''" ms-click="tabChange('vpn')">
                                        <a data-toggle="tab" href="#vpn">
                                            <i class="blue icon-cog bigger-110"></i>
                                            {{i18nkeyword.northnet.vpn_configuration}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div id="wired" class="tab-pane" ms-class="'wired' == tab_list?'active':''">
                                    	<div class="tabbable tabs-left" id="wiredtable">
                                            <ul class="nav nav-tabs">
                                                <li ms-class="'ipv4_data' == tab_list2?'active':''" ms-click="tabChange2('ipv4_data')">
                                                    <a data-toggle="tab" href="#ipv4_data">
                                                        <i class="pink icon-flag bigger-110">
                                                        </i>
                                                        IPv4
                                                    </a>
                                                </li>
                                                <li ms-class="'ipv6_data' == tab_list2?'active':''" ms-click="tabChange2('ipv6_data')">
                                                    <a data-toggle="tab" href="#ipv6_data">
                                                        <i class="blue icon-fire bigger-110">
                                                        </i>
                                                        IPv6
                                                    </a>
                                                </li>
                                            </ul>
                                            <div class="tab-content">
                                                <div class="tab-pane" id ="ipv4_data" ms-class="'ipv4_data' == tab_list2?'active':''">
                                                    <div class="widget-box" style="margin-bottom:5px;" id="wireddata">
                                                        <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                            <h5 class="lighter fontbold">
                                                                <i class="icon-cog"></i>
                                                                {{i18nkeyword.northnet.net_para_config}}
                                                            </h5>
                                                            <div class="widget-toolbar pull-right">
                                                                <a>
                                                                    <i class="icon-chevron-up"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                            <div class="widget-body-inner" style="display: block;">
                                                                <div class="widget-main no-padding">
                                                                    <table class="table table-striped table-bordered table-hover">
                                                                        <tr ms-for = "(WiredAttrindex,WiredAttr) in northnet_WiredAttrData" ms-if="northnet_show[WiredAttrindex].show == 1">
                                                                            <td style="width: 40%;">{{WiredAttr.full_name}}</td>
                                                                            <td ms-if="getconventionlength(WiredAttr.convention)>0">
                                                                                <select ms-duplex="northnet_WiredValueData[0][WiredAttr.name]" ms-attr="{name:@WiredAttr.name}" ms-change="changeshow">
                                                                                        <option ms-for="(index,name) in WiredAttr.convention" ms-attr="{value:index}">{{name}}</option>
                                                                                </select>
                                                                            </td>
                                                                            <td ms-if="getconventionlength(WiredAttr.convention)===0">
                                                                                <input class="form-control input-medium pull-left" ms-duplex="northnet_WiredValueData[0][WiredAttr.name]">
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <button class="button button-small button-flat-primary" style="margin-bottom:5px;" onClick="setWiredData()" id="setWireddataBT"><i class="icon-pencil bigger-100"></i>
                                                    {{i18nkeyword.set}}
                                                    </button>
                                                    <div class="widget-box" style="margin-bottom:5px;" id="wiredroute">
                                                        <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                            <h5 class="lighter fontbold">
                                                                <i class="icon-cog"></i>
                                                                {{i18nkeyword.northnet.static_route_config}}
                                                            </h5>
                                                            <div class="widget-toolbar pull-right">
                                                                <a>
                                                                    <i class="icon-chevron-up"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                            <div class="widget-body-inner" style="display: block;">
                                                                <div class="widget-main no-padding">
                                                                    <table id="wiredroute_cfg" class="table table-striped table-bordered table-hover">
                                                                        <thead>
                                                                        <tr>
                                                                            <th>{{i18nkeyword.northnet.nerwork_dest}}</th>
                                                                            <th>{{i18nkeyword.northnet.network_mask}}</th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <!--ms-for:(i,wiredroutedata) in northnet_WiredRouteData-->
                                                                            <tr>
                                                                                <td style="width: 40%;">
                                                                                    <input class="form-control input-medium pull-left" ms-duplex="wiredroutedata['Network Dest']" />
                                                                                </td>
                                                                                <td>
                                                                                    <input class="form-control input-medium pull-left" ms-duplex="wiredroutedata['Network Mask']" />
                                                                                </td>
                                                                            </tr>
                                                                        <!--ms-for-end:-->
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div style="padding-right:50px;display:inline">
                                                        <button class="button button-small button-flat-primary"  onclick="set_wiredroute_value()"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}</button>
                                                    </div>
                                                </div>
                                                <div class="tab-pane" id ="ipv6_data" ms-class="'ipv6_data' == tab_list2?'active':''">
                                                    <div class="widget-box" style="margin-bottom:5px;" id="wiredv6data">
                                                        <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                            <h5 class="lighter fontbold">
                                                                <i class="icon-cog"></i>
                                                                {{i18nkeyword.northnet.net_para_config}}
                                                            </h5>
                                                            <div class="widget-toolbar pull-right">
                                                                <a>
                                                                    <i class="icon-chevron-up"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                            <div class="widget-body-inner" style="display: block;">
                                                                <div class="widget-main no-padding">
                                                                    <table class="table table-striped table-bordered table-hover">
                                                                        <tr ms-for = "(WiredAttrindex,WiredAttr) in northnet_Wiredv6AttrData" ms-if="northnet_show[WiredAttrindex].show == 1">
                                                                            <td>{{WiredAttr.full_name}}</td>
                                                                            <td ms-if="getconventionlength(WiredAttr.convention)>0">
                                                                                <select style="width:240px" ms-duplex="northnet_Wiredv6ValueData[0][WiredAttr.name]" ms-attr="{name:@WiredAttr.name}" ms-change="changeshow">
                                                                                        <option ms-for="(index,name) in WiredAttr.convention" ms-attr="{value:index}">{{name}}</option>
                                                                                </select>
                                                                            </td>
                                                                            <td ms-if="getconventionlength(WiredAttr.convention)===0">
                                                                                <input class="form-control input-medium pull-left" style="width:240px" ms-duplex="northnet_Wiredv6ValueData[0][WiredAttr.name]">
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <button class="button button-small button-flat-primary" onClick="setWiredv6Data()" id="setWireddataBT"><i class="icon-pencil bigger-100"></i>
                                                    {{i18nkeyword.set}}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="wireless" class="tab-pane" ms-class="'wireless' == tab_list?'active':''">
                                        <div class="tab-pane" id="wirelesstable">
                                            <div class="widget-box" style="margin-bottom:5px;" id="wirelessdata">
                                                <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                    <h5 class="lighter fontbold">
                                                        <i class="icon-cog"></i>
                                                        {{i18nkeyword.northnet.net_para_config}}
                                                    </h5>
                                                    <div class="widget-toolbar pull-right">
                                                        <a>
                                                            <i class="icon-chevron-up"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                    <div class="widget-body-inner" style="display: block;">
                                                        <div class="widget-main no-padding">
                                                            <table class="table table-striped table-bordered table-hover">
                                                                <tr ms-for = "(WirelessAttrindex,WirelessAttr) in northnet_WirelessAttrData">
                                                                    <td style="width: 40%;" ms-visible="WirelessAttr.visible && WirelessAttr.visible!='no'">{{WirelessAttr.full_name}}</td>
                                                                    <td ms-if="getconventionlength(WirelessAttr.convention)>0 && WirelessAttr.visible && WirelessAttr.visible!='no'">
                                                                        <select ms-duplex="northnet_WirelessValueData[0][WirelessAttr.name]">
                                                                                <option ms-for="(index,name) in WirelessAttr.convention" ms-attr="{value:index}">{{name}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="getconventionlength(WirelessAttr.convention)===0 && WirelessAttr.visible && WirelessAttr.visible!='no' && WirelessAttr.id !='password' && WirelessAttr.id !='backuppassword'">
                                                                        <input class="form-control input-medium pull-left" ms-duplex="northnet_WirelessValueData[0][WirelessAttr.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                                    </td>
                                                                    <td ms-if="getconventionlength(WirelessAttr.convention)===0 && WirelessAttr.visible && WirelessAttr.visible!='no' && ( WirelessAttr.id =='password' || WirelessAttr.id =='backuppassword')">
                                                                        <input type="Password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-duplex="northnet_WirelessValueData[0][WirelessAttr.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button class="button button-small button-flat-primary" style="margin-bottom:5px;" onClick="setWirelessData()" id="setWirelessdataBT"><i class="icon-pencil bigger-100"></i>
                                                {{i18nkeyword.set}}
                                            </button>
                                            <div class="widget-box" style="margin-bottom:5px;" id="wirelessroute">
                                                <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                    <h5 class="lighter fontbold">
                                                        <i class="icon-cog"></i>
                                                        {{i18nkeyword.northnet.static_route_config}}
                                                    </h5>
                                                    <div class="widget-toolbar pull-right">
                                                        <a>
                                                            <i class="icon-chevron-up"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                    <div class="widget-body-inner" style="display: block;">
                                                        <div class="widget-main no-padding">
                                                            <table id="wirelessroute_cfg" class="table table-striped table-bordered table-hover">
                                                                <thead>
                                                                <tr>
                                                                    <th>{{i18nkeyword.northnet.nerwork_dest}}</th>
                                                                    <th>{{i18nkeyword.northnet.network_mask}}</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <!--ms-for:(i,wirelessroutedata) in northnet_WirelessRouteData-->
                                                                    <tr>
                                                                        <td style="width: 40%;">
                                                                            <input class="form-control input-medium pull-left" ms-duplex="wirelessroutedata['Network Dest']" />
                                                                        </td>
                                                                        <td>
                                                                            <input class="form-control input-medium pull-left" ms-duplex="wirelessroutedata['Network Mask']" />
                                                                        </td>
                                                                    </tr>
                                                                <!--ms-for-end:-->
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding-right:50px;display:inline">
                                                <button class="button button-small button-flat-primary"  onclick="set_wirelessroute_value()"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="vpn" class="tab-pane" ms-class="'vpn' == tab_list?'active':''">
                                        <div class="tab-pane" id="vpntable">
                                            <div class="widget-box" style="margin-bottom:5px;" id="vpndata">
                                                <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                    <h5 class="lighter fontbold">
                                                        <i class="icon-cog"></i>
                                                        {{i18nkeyword.northnet.net_para_config}}
                                                    </h5>
                                                    <div class="widget-toolbar pull-right">
                                                        <a>
                                                            <i class="icon-chevron-up"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                    <div class="widget-body-inner" style="display: block;">
                                                        <div class="widget-main no-padding">
                                                            <table class="table table-striped table-bordered table-hover">
                                                                <tr ms-for = "(VPNAttrindex,VPNAttr) in northnet_VPNAttrData">
                                                                    <td style="width: 40%;" ms-visible="VPNAttr.visible && VPNAttr.visible!='no'">{{VPNAttr.full_name}}</td>
                                                                    <td ms-if="getconventionlength(VPNAttr.convention)>0 && VPNAttr.visible && VPNAttr.visible!='no'">
                                                                        <select ms-duplex="northnet_VPNValueData[0][VPNAttr.name]" data-duplex-changed="changeVPNData(@VPNAttr.name, northnet_VPNValueData[0][VPNAttr.name])">
                                                                                <option ms-for="(index,name) in VPNAttr.convention" ms-attr="{value:index}">{{name}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="getconventionlength(VPNAttr.convention)===0 && VPNAttr.visible && VPNAttr.visible!='no' && VPNAttr.id !='password' && VPNAttr.id !='username'">
                                                                        <input class="form-control input-medium pull-left" ms-duplex="northnet_VPNValueData[0][VPNAttr.name]">
                                                                    </td>
                                                                    <td ms-if="getconventionlength(VPNAttr.convention)===0 && VPNAttr.visible && VPNAttr.visible!='no' && VPNAttr.id =='username'">
                                                                        <input class="form-control input-medium pull-left" ms-duplex="northnet_VPNValueData[0][VPNAttr.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                                    </td>
                                                                    <td ms-if="getconventionlength(VPNAttr.convention)===0 && VPNAttr.visible && VPNAttr.visible!='no' && VPNAttr.id =='password'">
                                                                        <input type="Password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-duplex="northnet_VPNValueData[0][VPNAttr.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button class="button button-small button-flat-primary" style="margin-bottom:5px;" onClick="setVPNData()" id="setVPNdataBT"><i class="icon-pencil bigger-100"></i>
                                                {{i18nkeyword.set}}
                                            </button>
                                            <div class="widget-box" style="margin-bottom:5px;" id="vpnroute">
                                                <div class="widget-header widget-header-flat widget-header-small" onClick="changeSlide(this)">
                                                    <h5 class="lighter fontbold">
                                                        <i class="icon-cog"></i>
                                                        {{i18nkeyword.northnet.static_route_config}}
                                                    </h5>
                                                    <div class="widget-toolbar pull-right">
                                                        <a>
                                                            <i class="icon-chevron-up"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="widget-body" style="overflow-y:auto;margin-bottom:5px">
                                                    <div class="widget-body-inner" style="display: block;">
                                                        <div class="widget-main no-padding">
                                                            <table id="VPNroute_cfg" class="table table-striped table-bordered table-hover">
                                                                <thead>
                                                                <tr>
                                                                    <th>{{i18nkeyword.northnet.nerwork_dest}}</th>
                                                                    <th>{{i18nkeyword.northnet.network_mask}}</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <!--ms-for:(i,VPNroutedata) in northnet_VPNRouteData-->
                                                                    <tr>
                                                                        <td style="width: 40%;">
                                                                            <input class="form-control input-medium pull-left" ms-duplex="VPNroutedata['Network Dest']" />
                                                                        </td>
                                                                        <td>
                                                                            <input class="form-control input-medium pull-left" ms-duplex="VPNroutedata['Network Mask']" />
                                                                        </td>
                                                                    </tr>
                                                                <!--ms-for-end:-->
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="padding-right:50px;display:inline">
                                                <button class="button button-small button-flat-primary"  onclick="set_VPNroute_value()"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!--/tabbable-->
                        </div><!--/span10-->
                            <!--PAGE CONTENT ENDS-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-content-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/page/js/config_northnet.js"></script>
</body>

</html>