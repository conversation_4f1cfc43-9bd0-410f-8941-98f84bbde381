﻿if( top.location != self.location ) top.location.href = self.location;

var logout_atction_tag = false;

Request = function () {
    String.prototype.replaceAllStr = function (str, replace) {
        str = str.replace("$", "[\$]");
        var reg = new RegExp(str, "g");
        return this.replace(reg, replace);
    };
    String.prototype.findSubstring = function (spF, spL) {
        var first = this.indexOfNocase(spF);
        var last = this.indexOfNocase(spL);
        if (first == -1 || last == -1) {
            return "";
        }
        return this.substring(first + spF.length, last);
    };
    String.prototype.indexOfNocase = function (string) {
        return this.toUpperCase().indexOf(string);
    };
    BindHTMLTemplate = function () {
        var bindSpF = "<TEMPLATE>";
        var bindSpL = "</TEMPLATE>";
        var itemSpF = "<BINDITEM>";
        var itemSpL = "</BINDITEM>";
        var childSpF = "<BINDCHILD>";
        var childSpL = "</BINDCHILD>";
        var childheadSpF = "<BINDCHILDHEAD>";
        var childheadSpL = "</BINDCHILDHEAD>";
        var childitemSpF = "<BINDCHILDITEM>";
        var childitemSpL = "</BINDCHILDITEM>";
        var nosubstrSpF = "<BINDNOSUBSTR>";
        var nosubstrSpL = "</BINDNOSUBSTR>";
        var substrSpF = "<BINDSUBSTR>";
        var substrSpL = "</BINDSUBSTR>";
        var substritemSpF = "<BINDSUBSTRITEM>";
        var substritemSpL = "</BINDSUBSTRITEM>";
        var MAX_INT_NUM = 110;
        var templates = {};
        var remainhtmls = {};
        return {
            request2data: function (data) {
				return JSON.parse(data);
			},
			getdataresult: function (data) {
				return dogetdataresult(data);
			},
			bindDataToElement: function (obj, data) {
				doBindDataToElement(obj, data);
			}
        };
        function doBindDataToElement(obj, data) {
            var html = templates[obj.attr("id")] === undefined ? obj.html().findSubstring(bindSpF, bindSpL) : templates[obj.attr("id")];
            templates[obj.attr("id")] = html;
            var remains = remainhtmls[obj.attr("id")] === undefined ? obj.html().replace(html, "") : remainhtmls[obj.attr("id")];
            remainhtmls[obj.attr("id")] = remains;
            var itemhtml = html.findSubstring(itemSpF, itemSpL); //clear
            var childhtml = html.findSubstring(childSpF, childSpL);
            var childheadhtml = childhtml.findSubstring(childheadSpF, childheadSpL); //clear
            var childitemhtml = childhtml.findSubstring(childitemSpF, childitemSpL); //clear
            var result = html.replace(itemSpF + itemhtml + itemSpL, "$replacement_");
            result = result.replace(childSpF + childhtml + childSpL, "");
            if (dogetdataresult(data) != "ok") {
                return;
            }
            var items = "";
            var itemsnum = 0;
            for (var item in data) {
                if (data[item].no !== undefined) {
                    itemsnum++;
                    if (data[item].children !== undefined) {
                        //data[item]["str0"] = data[item]["str0"].replace(/\d{1,}/, "n");
                        var childresult = childhtml.replace(childitemSpF + childitemhtml + childitemSpL, "$replacement_");
                        childresult = childresult.replace(childheadSpF + childheadhtml + childheadSpL, replaceItemAllValue(childheadhtml, data[item], item));
                        childresult = replaceItemAllValue(childresult, data[item], item);
                        var children = "";
                        for (var child in data[item].children) {
                            if (data[item].children[child].no !== undefined) {
                                var childtmp = replaceItemAllValue(childitemhtml, data[item].children[child], child);
                                childtmp = childtmp.replaceAllStr("$childindex_", child);
                                children += childtmp;
                            }
                        }
                        items += childresult.replace("$replacement_", children);
                    } else {
                        items += replaceItemAllValue(itemhtml, data[item], item);
                    }
                }
            }
            result = result.replace("$replacement_", items);
            if (itemsnum > 0) {
                obj.html(result);
            } else {
                obj.html(remains);
            }
        }
        function replaceItemAllValue(string, item, index) {
            var result = string;
            var hassubstr = false;
            var i = 0;
            result = result.replaceAllStr("$no_", item.no);
            result = result.replaceAllStr("$type_", item.type);
            result = result.replaceAllStr("$num_", item.num);
            result = result.replaceAllStr("$style_", item.style);
            result = result.replaceAllStr("$index_", parseInt(index) + 1);
            if (item.children !== undefined) {
                result = result.replaceAllStr("$childnum_", item.children.length - 1);
            } else {
                result = result.replaceAllStr("$childnum_", "");
            }
            for (i = 0; i < MAX_INT_NUM; i++) {
                if (item["int" + i] !== undefined) {
                    result = result.replaceAllStr("$int" + i + "_", item["int" + i]);
                } else {
                    result = result.replaceAllStr("$int" + i + "_", "");
                }
            }
            for (i = 0; i < MAX_INT_NUM; i++) {
                if (item["str" + i] !== undefined) {
                    if (item.int0 !== undefined && item.int0 !== 0) {
                        result = result.replaceAllStr("$str" + i + "_" + "[\$]ai_", item["str" + i] + "-" + item.int0 + "#");
                        result = result.replaceAllStr("$str" + i + "_", item["str" + i]);
                    } else {
                        result = result.replaceAllStr("$str" + i + "_" + "[\$]ai_", item["str" + i]);
                        result = result.replaceAllStr("$str" + i + "_", item["str" + i]);
                    }
                } else {
                    result = result.replaceAllStr("$str" + i + "_", "");
                }
            }
            if (item.substr0 !== undefined) {
                hassubstr = true;
            }
            if (!hassubstr) {
                result = result.replace(nosubstrSpF, "");
                result = result.replace(nosubstrSpL, "");
                var substrhtml = result.findSubstring(substrSpF, substrSpL); //clear
                result = result.replace(substrSpF + substrhtml + substrSpL, "");
            } else {
                var substritemhtml = result.findSubstring(substritemSpF, substritemSpL); //clear
                var substritem = "";
                var m = 1;
                while (item["substr" + m] !== undefined) {
                    var temp = substritemhtml.replaceAllStr("$substr_", item["substr" + m]);
                    temp = temp.replaceAllStr("$subint_", item["subint" + m]);
                    if (item["substr" + m] == item.substr0) {
                        temp = temp.replaceAllStr("$selected_", "selected");
                    } else {
                        temp = temp.replaceAllStr("$selected_", "");
                    }
                    substritem += temp;
                    m++;
                }
                result = result.replace(substritemSpF + substritemhtml + substritemSpL, substritem);
                var strhtml = result.findSubstring(nosubstrSpF, nosubstrSpL); //clear
                result = result.replace(nosubstrSpF + strhtml + nosubstrSpL, "");
                result = result.replace(substrSpF, "");
                result = result.replace(substrSpL, "");
            }
            return result;
        }
        function dogetdataresult(data) {
            if (data.result != "ok") {
                return undefined;
            }
            else return data.result;
        }
    };
    LoginRequest = function () {
        return {
            login: function (level, user, psw) {
                Cookies.set("user", user, {"path":"/power"});
                Cookies.set("psw", psw, {"path":"/power"});
                Cookies.set("level", level, {"path":"/power"});
			},
			getPassword: function () {
				return Cookies.get("psw");
			},
			getUsername: function () {
				return Cookies.get("user");
			},
			getLevel: function () {
				return Cookies.get("level");
			},
			refresh: function () {
				Cookies.remove("user");
				Cookies.remove("psw");
				Cookies.remove("level");
                Cookies.remove("PMSA_SESSION_ID");   // 清除sessionid
			}
        };
    };
    var MAX_BAD_ROUTINE = 5;
    var mainRequests ;
    var routineRequest = [];
    var routineCount = [];
    var badroutine = 0;
    var isdisconnected = false;
    var islostauth = false;
    var mainTimer;
    var language;
    var binder = new BindHTMLTemplate();
    var login = new LoginRequest();
    function clearRoutineRequest() {
        routineRequest = [];
        routineCount = [];
    }
    function addRoutineRequest(request) {
        if (request.refresh !== undefined && request.refresh > 0) {
            routineRequest.push(request);
            routineCount.push(0);
        }
        doAddRequest(request);
    }
    function routine() {
        for (var i in routineRequest) {
            if (routineRequest[i] !== undefined) {
                routineCount[i]++;
                if (routineCount[i] > routineRequest[i].refresh) {
                    routineCount[i] = 0;
                    doAddRequest(routineRequest[i]);
                }
            }
        }
    }
    function doAddRequest(request) {
        $.ajax({
            url: "/power/cgi-bin/main.fcgi?"+"cur_time=" + new Date().getTime(),
            data: request.data,
			method: "POST"
        }).done(function (backdata) {
            badroutine = 0;
            changenetworkworning(false);
            var extdata = binder.request2data(backdata);
            if (binder.getdataresult(extdata) == "ok") {
                if (request.extdata !== undefined) {
                    extdata = request.extdata(extdata);
                    if (extdata.length < 1 || extdata[extdata.length - 1].result === undefined) {
                        extdata.push({
                            result: "ok"
                        });
                    }
                }
            }
            if (request.success !== undefined) {
                if (extdata.data.length > 0 && extdata.data[0]['error_config'] !== undefined) {
                    alert( mainvalue.i18nkeyword.config_check_error + "\n" + extdata.data[0]['error_config']);
                }
                request.success(extdata, request);
            }
        }).fail(function () {
            badroutine++;
            if (request.error !== undefined) {
                request.error(request);
            }
            if (badroutine >= MAX_BAD_ROUTINE) {
                badroutine = MAX_BAD_ROUTINE;
                changenetworkworning(true);
            }
        });
    }
    function changenetworkworning(flag) {
        if (flag && !isdisconnected) {
            isdisconnected = true;
            var str = mainvalue.i18nkeyword.no_connection_tip;
            if (language === 0) {
                str = "设备通讯中断！";
            }
            $(".navbar-fixed-bottom").prepend("<div id='NETWORKWORNINGID' style='width=100%;background:#F00;text-align:center;color:#FFFFFF'>" + str + "</div>");
			Cookies.remove("devReboot");
			Cookies.remove("dl");
			if(Cookies.get("facreset") !== undefined){
				$("#facreseting").hide();
				Cookies.remove("facreset");
				alert(getLanStr(["恢复出厂设置成功，请在设备重启后用新IP登录。", "Successful factory reset, please login with the new IP after rebooting."]));
			}
        }
        if (!flag && isdisconnected) {
            isdisconnected = false;
            $("div[id^='rebootSuccess']").fadeOut();
			$("div[id^='tipReboot']").hide();
			$("#rebooting").hide();
			$("#dl_url").hide();
            $("#NETWORKWORNINGID").remove();
			$("#pageLock").hide();
			$("#sysUpwaiting").hide();
        }
    }
    function checkCookieFunctional() {
        Cookies.set("func", "qw", {"path":"/power"});
		if (Cookies.get("func") != "qw") {
			$(".navbar-fixed-top").prepend("<div style='width=100%;background:#F00;text-align:center;color:#FFFFFF'>请开启浏览器Cookie功能！ Please Enable Browser's Cookie!</div>");
		} else {
			Cookies.remove("func");
		}
	}
    return {
        logout: function () {
			login.refresh();
			logout_atction_tag = true;
            gotopage('login.html');
		},
		getUserLevel: function () {
			var level = login.getLevel();
			if (level === undefined || level === null) {
				level = 0;
			}
			return level;
		},
		getUsername: function () {
			var name = login.getUsername();
			if (name === undefined) {
				name = "Guest";
			}
			return name;
		},
		getPassword: function () {
			var psw = login.getPassword();
			if (psw === undefined) {
				psw = "";
			}
			return psw;
		},
		setLanguage: function (lan, page) {
			if (lan != language) {
				language = lan;
				Cookies.set("lan", language, {
					expires: 9999,
                    "path":"/power"
                    // path: '/'
				});
			}
		},
		getLanguage: function () {
			return Cookies.get("lan");
		},
		addMainRequest: function (requests) {
			checkCookieFunctional();
			if (mainRequests !== undefined) {
				return;
			}
			mainRequests = requests;
			clearRoutineRequest();
			language = Cookies.get("lan");
			for (var i in mainRequests) {
				addRoutineRequest(mainRequests[i]);
			}
			if (mainTimer === undefined) {
				mainTimer = setInterval(routine, 1000);
			}
		},
		clearMainRequest: function () {
			clearRoutineRequest();
			mainRequests = undefined;
		},
		addRequest: function (requests) {
			for (var i in requests) addRoutineRequest(requests[i]);
		},
		clearRequest: function (request) {
			for (var i in routineRequest) {
                if (typeof(routineRequest[i])=="undefined") {
                    continue;
                }
				if (cmp(routineRequest[i].data.objectid,request.data.objectid) &&
						cmp(routineRequest[i].data.type,request.data.type)) {    //   针对3.0平台，对象和类型相同即视为同一请求， by 肖胜贤 2018-12-15
					routineCount[i] = undefined;
					routineRequest[i] = undefined;
					//break;    //xiaoshengxian 2017-10-12
				}
			}
		},
		bindDataToElement: function (id, data) {
			if (data.length < 1 || data[data.length - 1].result === undefined) {
				data.push({
					result: "ok"
				});
			}
			binder.bindDataToElement($("#" + id), data);
		},
		getDataResult: function (data) {
			return binder.getdataresult(data);
		},
		redirect: function (htmlReq, id) {
			var user = login.getUsername();
			var pass = login.getPassword();
			var uStr = "";
			var pStr = "";
			var lStr = "";
			var idStr = "";
			language = Cookies.get("lan");
			// if (user !== undefined && user !== null && user !== "") {
			// 	uStr = "<input type='hidden' value='" + user + "' name='username'>";
			// }
			// if (pass !== undefined && pass !== null && pass !== "") {
			// 	pStr = "<input type='hidden' value='" + pass + "' name='password'>";
			// }
			// if (language !== undefined && language !== null && language !== "") {
			// 	lStr = "<input type='hidden' value='" + language + "' name='lan'>";
			// }
			// if (id !== undefined && id !== null && id !== "") {
			// 	idStr = "<input type='hidden' value='" + id + "' name='extpara'>";
			// }
			if (htmlReq !== undefined && htmlReq !== null && htmlReq !== "") {
				$("body").append("<form name='POSTFORM' id='POSTFORM' action='/power/page/"+htmlReq+"' method='post'></form>");
                document.forms.POSTFORM.submit();
			}
		}
    };
};
var request = new Request();
