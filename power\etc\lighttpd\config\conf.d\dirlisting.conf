#######################################################################
##
##  Dirlisting Module 
## ------------------- 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModDirlisting
##

##
## Enabled Directory listing
##
dir-listing.activate      = "disable"

##
## Hide dot files from the listing?
## By default they are listed.
##
dir-listing.hide-dotfiles = "disable" 

##
## list of regular expressions. Files that match any of the specified
## regular expressions will be excluded from directory listings.
##
dir-listing.exclude       = ( "~$" )

##
## set a encoding for the generated directory listing
##
## If you file-system is not using ASCII you have to set the encoding of
## the filenames as they are put into the HTML listing AS IS (with XML
## encoding)
##
dir-listing.encoding = "UTF-8"

##
## Specify the url to an optional CSS file. 
##
#dir-listing.external-css  = "/dirindex.css"

##
## Include HEADER.txt files above the directory listing. 
## You can disable showing the HEADER.txt in the listing. 
##
dir-listing.hide-header-file = "disable"
dir-listing.show-header = "disable"

##
## Include README.txt files above the directory listing. 
## You can disable showing the README.txt in the listing. 
##
dir-listing.hide-readme-file = "disable"
dir-listing.show-readme = "disable"

##
#######################################################################
