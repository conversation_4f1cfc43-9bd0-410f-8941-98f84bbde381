 <!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/prompt.html" -->
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="history">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.history.history_alarm}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
							<!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
								<ul class="nav nav-tabs" id="myTab">
									<li class="active">
										<a data-toggle="tab" href="#hisalarm">
											<i class="blue icon-warning-sign bigger-110">
											</i>
											{{i18nkeyword.history.history_alarm}}
										</a>
									</li>
								</ul>
								<div class="tab-content">
									<div id="hisalarm" class="tab-pane active">
										<table class="table">
                                            <tbody>
                                              <tr>
                                                <td>
													{{i18nkeyword.history.time_range}}
													<input id="alarm_starttext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyalarm_time.start_time" onfocus="laydate.render({elem:'#alarm_starttext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:'1970-01-01 00:00:00',max:$('#alarm_endtext').val(),ready:function(){$('#alarm_starttext')[0].eventHandler = false;},done:function(){$('#alarm_starttext').focus();$('#alarm_starttext').blur();}})" onblur="checkDateTime(this)"/>
													{{i18nkeyword.history.to}}
													<input id="alarm_endtext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyalarm_time.end_time" onfocus="laydate.render({elem:'#alarm_endtext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:$('#alarm_starttext').val(),ready:function(){$('#alarm_endtext')[0].eventHandler = false;},done:function(){$('#alarm_endtext').focus();$('#alarm_endtext').blur();}})" onblur="checkDateTime(this)"/>
                                                    {{i18nkeyword.history.lines_per_page}}
													<select ms-duplex="@historyalarm_counts" class="input-mini" id="alarmPerPage">
														<option value="10">10</option>
														<option value="20">20</option>
														<option value="100">100</option>
													</select>&nbsp;&nbsp;&nbsp;&nbsp;
													<button id="alarm_searchBT" class="button button-small button-flat-primary" onClick="doAlarmsearch()">
													{{i18nkeyword.history.search}}
													</button>
                                                </td>
                                              </tr>
                                            </tbody>
                                        </table>
										<table class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.history.num}}</th>
													<th>{{i18nkeyword.history.start_time}}</th>
													<th>{{i18nkeyword.history.end_time}}</th>
													<th>{{i18nkeyword.history.alarm_name}}</th>
													<th>{{i18nkeyword.history.alarm_level}}</th>
												</tr>
											</thead>
											<tbody id ="alarm_list">
												<tr ms-for="hisvalue in papervalue" ms-if="hisvalue.id == 'history_alarm'">
													<td>{{hisvalue.index}}</td>
													<td>{{hisvalue.start_time}}</td>
													<td>{{hisvalue.end_time}}</td>
													<td>{{hisvalue.full_name}}</td>
													<td>
													<center><span class="label arrowed arrowed-in arrowed-in-right" ms-class="@alarmlevelclass[hisvalue.level]">
                                                        {{@hisvalue.level_name}}</span>
		                                            </center>
		                                            </td>
												</tr>
											</tbody>
										</table>
										<div class="hr hr8 hr-double"></div>
										<div class="pagination" style="cursor:pointer;" ms-for="(i,paper) in pagerattrall" ms-if="pagerattrall[i].id=='history_alarm'" ms-html="pagerattrall[i].htmlvalue"></div>
									</div>
								</div>
                            </div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/history_inner_alarm.js"></script>
		<script src="/js/opensrc/laydate/laydate.js"></script>
    </body>

</html>