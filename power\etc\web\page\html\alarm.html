<!DOCTYPE html>
<html>    
    <head>
		<!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        
        <div class="main-container container-fluid" ms-controller="realalarm">
			<!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
                            {{i18nkeyword.realalarm.active_alarm}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>

                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
							<!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
								<div class="widget-header widget-header-flat widget-header-small">
									<h5 class="lighter">
										<i class="icon-warning-sign  icon-animated-bell"></i>
                                        {{i18nkeyword.realalarm.real_alarm}}
									</h5>
                                        {{i18nkeyword.total}}:
									<span class="badge badge-primary " id="realalarmnum">{{realalarmdata.length}}</span>
                				</div>
                				<div class="widget-body">
                                    <div class="widget-body-inner" style="display: block;">
                                        <div class="widget-main no-padding">
                                            <table class="table table-bordered table-striped table-hover">
                                                <thead class="thin-border-bottom">
                                                    <tr text-align:center="">
														<th>{{i18nkeyword.NO}}</th>
                                                        <th onclick='name_sort()'>{{i18nkeyword.realalarm.name}} <a id="name_sort" href="#" class="arrow"></th>
                                                        <th onclick='time_sort()'>{{i18nkeyword.realalarm.generated_time}} <a id="time_sort" href="#" class="arrow"></th>
                                                        <th onclick='level_sort()'>{{i18nkeyword.realalarm.level}} <a id="level_sort" href="#" class="arrow"></th>
                                                        <th>{{i18nkeyword.realalarm.dry_contact_output}}</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="realalarm">
                                                    <tr ms-for="(i,alarm) in realalarmdata">
														<td>{{realalarmdata.length-i}}</td>
		                                                <td>{{alarm.full_name}}</td>
		                                                <td>{{alarm.start_time}}</td>
		                                                <td>
		                                                <center><span class="label arrowed arrowed-in arrowed-in-right" ms-class="@alarmlevelclass[alarm.alm_level]" >
                                                        {{@alarm.level_name}}</span>
		                                                </center></td>
                                                        <td ms-if="alarm.alm_relay == '0' || !doconvention[alarm.alm_relay]">{{i18nkeyword.null}}</td>
		                                                <td ms-if="alarm.alm_relay != '0' && doconvention[alarm.alm_relay]">{{doconvention[alarm.alm_relay]}}</td>
                                                    </tr>
                                                </tbody>
											</table>
                                        </div>
                                    </div>
                                </div>								
						    </div>
                        </div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
                    </div><!--/span10-->
                </div><!--/row-fluid-->
            </div><!--/page-content-->         
        </div><!--/main-contain-->
        <!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
        <script src="/page/js/alarm.js"></script>
</body>

</html>