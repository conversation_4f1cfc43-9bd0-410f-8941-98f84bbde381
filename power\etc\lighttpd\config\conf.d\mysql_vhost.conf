#######################################################################
##
##  Virtual hosting with MySQL 
## ----------------------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModMySQLVhost
##
server.modules += ( "mod_mysql_vhost" )

## 
## Either set the the socket or host (and port)
##
## Local path to the mysql socket
##
#mysql-vhost.sock           = "/var/lib/mysql/mysql.sock"

##
## Host of the MySQL server. 
##
#mysql-vhost.hostname       = "localhost"

##
## Optional: port to use.
##
#mysql-vhost.port           = 3306

##
## Name of the database
##
mysql-vhost.db             = "lighttpd"

##
## The query to get the needed informations from the database.
##
## It doesnt matter how you name the fields the first field is always used
## as the document root.
##
mysql-vhost.sql            = "SELECT docroot FROM domains WHERE domain='?'"

##
#######################################################################
