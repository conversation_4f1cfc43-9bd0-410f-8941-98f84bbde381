var vmodel = avalon.define({
	$id:'radius',
	tab_list:'config_data',
	radiusAttrData:[],
	radiusValueData:[],
	hide_flag:0,
	getconventionlength : function(con) {
        var length = 0;
        for(var i in con){
        	length++;
        }
        return length;
    },
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
	},
	show_disallow_input:function(){
		return mainvalue.i18nkeyword.Disallow_input + " & ";
	},
});


radius_info = {
    //RADIUS参数管理
    HEAD:function(paraval,paranum) {
            var req = {data:{objectid:"plat.radius",type:"attr_get",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:get_radius_head_succ};
            request.addRequest([req]);

            //回调函数
            function get_radius_head_succ(d,r) {
                addvalue(vmodel.radiusAttrData,d.data,"name");
            }
    },
    GET:function(paraval,paranum) {
            var req = {data:{objectid:"plat.radius",type:"val_get",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:get_radius_succ};
            request.addRequest([req]);

            //回调函数
            function get_radius_succ(d,r) {
                addvalue(vmodel.radiusValueData,d.data,"instid");
            }
    },
    PUT:function(paraval,paranum) {
			var self = this;
			if (paraval[0]["RADIUS Secret"].indexOf("&") != -1 || paraval[0]["Private Key Password"].indexOf("&") != -1) {
				alert(mainvalue.i18nkeyword.Disallow_input + "&");
				radius_info.HEAD([],0);
				radius_info.GET([{"instid":""}],1);
				return;
			}
            var req = {data:{objectid:"plat.radius",type:"val_set",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:set_radius_succ};
            request.addRequest([req]);

            //回调函数
            function set_radius_succ(d,r) {
                if(d.result ==="ok"){
                    mainvalue.controlsuccess = "success";
                }
                else{
                    mainvalue.controlsuccess = "failure";
                }
                self.GET([{"instid":""}],1);
            }
    }
};


radius_info.HEAD([],0);
radius_info.GET([{"instid":""}],1);

function setRadiusData(obj) {
	let radius_secret = vmodel.radiusValueData[0]["RADIUS Secret"];
	let private_key_password = vmodel.radiusValueData[0]["Private Key Password"];
	let radius_secret_len = radius_secret.length;
	let private_key_password_len = private_key_password.length;
	if ((radius_secret != WEB_SPECIFY_STR && private_key_password != WEB_SPECIFY_STR) && (radius_secret_len > 32 || private_key_password_len >32)) {
		alert(mainvalue.i18nkeyword.radius.pswd_len_tip)
		radius_info.HEAD([],0);
		radius_info.GET([{"instid":""}],1);
		return ;
	}
    radius_info.PUT(vmodel.radiusValueData, 1);
}

function upload_ca_cert(obj) {
	if($("#ca-cert-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#ca-cert-file").val().split("\\").pop() !== "radius_rootca.pem") {
		$("#ca-cert-file~.remove").trigger("click");
		popupTipsDiv($("#upcacert_name_err"), 3000);
		return false;
	}
	$("#ca_cert_up_bt").attr("disabled", "disabled");
	vmodel.hide_flag = 1;
	$("#sysUploading").show();
	showDataInit();
	$("#ca_cert_up").ajaxSubmit(check_radius_cert_upload_result);
	return false;   //必须返回false否则将跳转到新界面
}

function upload_client_cert(obj) {
	if($("#client-cert-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#client-cert-file").val().split("\\").pop() !== "radius_user.pem") {
		$("#client-cert-file~.remove").trigger("click");
		popupTipsDiv($("#upclicert_name_err"), 3000);
		return false;
	}
	$("#client_cert_up_bt").attr("disabled", "disabled");
	vmodel.hide_flag = 2;
	$("#sysUploading").show();
	showDataInit();
	$("#client_cert_up").ajaxSubmit(check_radius_cert_upload_result);
	return false;   //必须返回false否则将跳转到新界面
}

function upload_private_key(obj) {
	if($("#private-key-file").val() == ""){
		popupTipsDiv($("#file_empty_tip"), 1000);
		return false;
	}
	if($("#private-key-file").val().split("\\").pop() !== "radius_userkey.pem") {
		$("#private-key-file~.remove").trigger("click");
		popupTipsDiv($("#upprikey_name_err"), 3000);
		return false;
	}
	$("#private_key_up_bt").attr("disabled", "disabled");
	vmodel.hide_flag = 3;
	$("#sysUploading").show();
	showDataInit();
	$("#private_key_up").ajaxSubmit(check_radius_cert_upload_result);
	return false;   //必须返回false否则将跳转到新界面
}

function hide_the_submit_button(){
	switch(vmodel.hide_flag){
		case 1:  //upload_ca_cert 证书
			setTimeout(function () {
				$("#ca_cert_up_bt").attr("disabled", false);
			}, 5000);
			$("#ca-cert-file~.remove").trigger("click");
			break;
		case 2:  //client_cert_up 证书
			setTimeout(function () {
				$("#client_cert_up_bt").attr("disabled", false);
			}, 5000);
			$("#client-cert-file~.remove").trigger("click");
			break;
		case 3:  //private_key_up 证书
			setTimeout(function () {
				$("#private_key_up_bt").attr("disabled", false);
			}, 5000);
			$("#private-key-file~.remove").trigger("click");
			break;
		default:
			break;
	}
	vmodel.hide_flag = 0;
}

function check_radius_cert_upload_result(d,r) {
	result = JSON.parse(d);
	if (result.result == 'ok') {
		popupTipsDiv($("#setSuccessalart"), 3000);
	} else {
		popupTipsDiv($("#setFailurealart"), 3000);
	}
	hideDataInit();
	hide_the_submit_button();
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "config_data" || tab == "cipher_data") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "config_data";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();