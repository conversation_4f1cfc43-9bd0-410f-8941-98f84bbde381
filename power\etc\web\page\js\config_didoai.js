var vmodel = avalon.define({
	$id:'didoai',
	tab_list:'diconfig',
	system_IOstate:[],      //  所有DI通道的状态
	system_DOstate:[],      //  所有DO通道的状态
	di_structuredata:[], //DI通用结构信息
	di_value:{},//当前选择的DI的Value信息
	di_selectexid:"",//当前选择的实例ID信息
	di_listid:[],//DI的实例ID信息
	di_instidvalue:[],//每个DI对应的Value信息
	di_relevance_signal_sid:"",    // DI关联SID
	di_relevance_device_sid:"",    // DI关联设备SID
	di_relevance_device_signals:[],  // 关联设备对应的所有信号
	ai_di_relevance_device_signals:[],  //存储AI_DI复用的所有信号
	di_all_device_signals:[],  // 所有DO的关联设备以及所有信号
	di_device_sid:[],  // 所有DO的关联设备
	di_select_flag:"0",
	di_changed_flag: "0",
	di_ai_instidvalue:[],//每个DI对应的Value信息（所有di，包含复用通道）
	di_title_show:"0", // di显示标记

	do_structuredata:[], //DO通用结构信息
	do_value:{},//当前选择的DO的Value信息
	do_selectexid:"",//当前选择的实例ID信息
	do_listid:[],//DO的实例ID信息
	do_instidvalue:[],//每个DO对应的Value信息
	do_relevance_signal_sid:"",    // DO关联SID
	do_relevance_device_sid:"",    // DO关联设备SID
	do_relevance_device_signals:[],  // 关联设备对应的所有信号
	do_all_device_signals:[],  // 所有DO的关联设备以及所有信号
	do_device_sid:[],  // 所有DO的关联设备
	do_title_show:"0",// do显示标记

	ai_structuredata:[], //AI通用结构信息
	ai_value:{},//当前选择的AI的Value信息
	ai_selectexid:"",//当前选择的实例ID信息
	ai_di_selectexid:"2",//当前选择的AI-DI复用的实例ID信息
	ai_pure_selectexid:"3",//当前选择的纯ID的实例ID信息
	ai_listid:[],//AI的实例ID信息
	ai_instidvalue:[],//每个AI对应的Value信息
	ai_relevance_signal_sid:"",    // AI关联SID
	ai_relevance_device_sid:"",    // AI关联设备SID
	ai_relevance_device_signals:[],  // 关联设备对应的所有满足信号
	ai_device_sid:[],  // AI温湿度通道的关联设备，筛选过无效的设备项
	ai_humid_device_sid:[],  //AI湿度通道的关联设备，根据静态表获得的可支持设备项
	ai_temp_device_sid:[],  //AI温度通道的关联设备，根据静态表获得的可支持设备项
	ai_analogdata_value:"",
	ai_title_show:"0",// ai显示标记

	temp_humid_value:{},                  //当前选择的温湿度传感器的Value信息
	temp_humid_selectexid:"1",    //当前选择的实例ID信息
	temp_humid_relevance_signal_sid:"",    // 温湿度传感器关联SID
	temp_humid_relevance_device_signals:[],  // 关联设备对应的所有满足信号
	temp_humid_analogdata_value:"",
	cur_ext_temp_sid:"",
	cur_ext_humid_sid:"",

	ai_show:function(attr,show,type){
		if(show !=="NO"){
			if((type==="2")&&((attr==="Offset")||(attr==="Slope")))
				return false;
			else if ((type!=="2")&&(attr==="Preset Status"))
				return false;
			else return true;
		}
		else return false;
	},
	getAISidVal:function(signalid){
		var value = "";
		for (var i in vmodel.ai_relevance_device_signals) {
			if (vmodel.ai_relevance_device_signals[i].sid == signalid) {
				if(vmodel.ai_relevance_device_signals[i].value == "val_invalid"){
					value = mainvalue.i18nkeyword.value_invalid;
				}
				else{
					value = vmodel.ai_relevance_device_signals[i].value + vmodel.ai_relevance_device_signals[i].unit;
				}
				break;
			}
		}
		return value;
	},
	getTempHumidSidVal:function(signalid){
		var value = "";
		for (var i in vmodel.temp_humid_relevance_device_signals) {
			if (vmodel.temp_humid_relevance_device_signals[i].sid == signalid) {
				if(vmodel.temp_humid_relevance_device_signals[i].value == "val_invalid"){
					value = mainvalue.i18nkeyword.value_invalid;
				}
				else{
					value = vmodel.temp_humid_relevance_device_signals[i].value + vmodel.temp_humid_relevance_device_signals[i].unit;
				}
				break;
			}
		}
		return value;
	},
	get_select_channel_state:function(channel_name) {
		for (var i in vmodel.system_IOstate) {
			if(vmodel.system_IOstate[i].full_name == channel_name) {
				return vmodel.system_IOstate[i].value;
			}
		}
		return 0;
	},
	get_select_do_channel_state:function(board_type,do_type,io_index){
		for (var i in vmodel.system_DOstate) {
			if (vmodel.system_DOstate[i].index ==String(io_index) &&
				vmodel.system_DOstate[i]['type'] == String(do_type) &&
				vmodel.system_DOstate[i].board_type ==String(board_type)) {
				return vmodel.system_DOstate[i].value;
			}
		}
		return "";
	},
	judge_inrelay:function(sid){    // 判断一个信号是否为输入干接点告警或状态
		sid = BigInt(sid);
		var sigtype = calSidToDevType(sid);
		if (sigtype.devType == "1" && sigtype.devSn == "1"
		&& ((sigtype.sigType == "3" && sigtype.devSigVar == "3")  // 输入干接点告警
			|| sigtype.sigType == "2" && sigtype.devSigVar == "2")  // 输入干接点状态
		){
			return true;
		}
		if (sid == ""){  // 关联信号为无时，可编辑别名用于输入干接点状态
			return true;
		}
		return false;
	},
	judge_aidi_channel:function(inst_id){    // 判断DI通道中属于输入干接点的通道
		for (let i in vmodel.di_ai_instidvalue) {
			if (inst_id == vmodel.di_ai_instidvalue[i].inst_id){
				return true;
			}
		}
		return false;
	},
	judge_do_sid_special:function(sid) {
		/*
		if (sid == '281544233123841' || sid == '0x1001020010001') {    //监控单元故障信号特殊处理
			return true;
		}*/
		return false;
	},
	judge_ai_sid_valid:function(sid) {
		for (var i in vmodel.ai_relevance_device_signals) {
			if (vmodel.ai_relevance_device_signals[i].sid == sid) {
				return true;
			}
		}
		return false;
	},
	judge_do_sid_valid:function(sid) {
		for (var i in vmodel.do_relevance_device_signals) {
			if (vmodel.do_relevance_device_signals[i].sid == sid) {
				return true;
			}
		}
		return false;
	},
	changeChannelData:function() {
		vmodel.ai_relevance_signal_sid = event.target.value.toString();
	},
	changeDoChannelData:function() {
		vmodel.do_relevance_signal_sid = event.target.value.toString();
	},
	changeTempHumidChannelData:function() {
		vmodel.temp_humid_relevance_signal_sid = event.target.value.toString();
	},
	temp_humid_judge:function(sid) {
		if (((vmodel.temp_humid_selectexid == '0')) && (sid != "2533343778635777")) {
			return false;
		} if ((vmodel.temp_humid_selectexid == '1') && (sid == "2533343778635777")) {
			return false;
		}
		return true;
	}
});

// AI温湿度通道所有关联设备以及所有信号
var ai_humid_all_device_signals = [{dev_sid : "2533343509872640", sid:"2533343778439169"},   // 系统运行环境:环境湿度
                                  {dev_sid : "13510867601588224", sid:"13510867870154753"}] // 温湿度传感器:温湿度传感器环境湿度
var ai_temp_all_device_signals = [{dev_sid : "2533343509872640", sid:"2533343778373633"},    // 系统运行环境:环境温度
                                  {dev_sid : "2533343509872640", sid:"2533343778504705"},    // 系统运行环境:扩展温度[1]
                                  {dev_sid : "2533343509872640", sid:"2533343778504706"},    // 系统运行环境:扩展温度[2]
                                  {dev_sid : "2533343509872640", sid:"2533343778504707"},    // 系统运行环境:扩展温度[3]
                                  {dev_sid : "2533343509872640", sid:"2533343778504708"},    // 系统运行环境:扩展温度[4]
                                  {dev_sid : "2533343509872640", sid:"2533343778504709"},    // 系统运行环境:扩展温度[5]
                                  {dev_sid : "2533343509872640", sid:"2533343778504710"},    // 系统运行环境:扩展温度[6]
                                  {dev_sid : "2533343509872640", sid:"2533343778504711"},    // 系统运行环境:扩展温度[7]
                                  {dev_sid : "2533343509872640", sid:"2533343778504712"},    // 系统运行环境:扩展温度[8]
                                  {dev_sid : "2533343509872640", sid:"2533343778701313"},    // 系统运行环境:柜外环境温度
                                  {dev_sid : "3096293463293952", sid:"3096293731991553"},    // 电池_1:电池温度
                                  {dev_sid : "3096362182770688", sid:"3096362451468289"},    // 电池_2:电池温度
                                  {dev_sid : "3096430902247424", sid:"3096431170945025"},    // 电池_3:电池温度
                                  {dev_sid : "3096499621724160", sid:"3096499890421761"},    // 电池_4:电池温度
                                  {dev_sid : "3096568341200896", sid:"3096568609898497"},    // 电池_5:电池温度
                                  {dev_sid : "3096637060677632", sid:"3096637329375233"},    // 电池_6:电池温度
                                  {dev_sid : "3096705780154368", sid:"3096706048851969"},    // 电池_7:电池温度
                                  {dev_sid : "3096774499631104", sid:"3096774768328705"},    // 电池_8:电池温度
                                  {dev_sid : "13510867601588224", sid:"13510867870089217"}]  // 温湿度传感器:温湿度传感器环境温度

// Ai温湿度通道的关联设备，筛选去除无效的设备项
var ai_humid_device_list = [];
var ai_temp_device_list = [];

//  获取设备列表
get_dev_list_all();

function get_selcet_channel_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	if (d.objectid == 'para_config_ai') {
		vmodel.ai_instidvalue = [];
		// 对通道顺序进行筛选排序，后续扩展优化
		let arr = d.data.filter(inst_info => inst_info['Channel Type'] == '3');
		arr.sort((a,b) => { return a['Channel Name'].split('_')[2].localeCompare(b['Channel Name'].split('_')[2]); });
		d.data = d.data.filter(inst_info => inst_info['Channel Type'] != '3');
		addvalue(vmodel.ai_instidvalue,d.data,"inst_id");
		addvalue(vmodel.ai_instidvalue,arr,"inst_id");

		var loopvar;
		for(loopvar = 0; loopvar < vmodel.ai_instidvalue.length; loopvar++){
			if(vmodel.ai_instidvalue[loopvar].inst_id == vmodel.ai_selectexid) {
				vmodel.ai_value = vmodel.ai_instidvalue[loopvar];
				break;
			}
		}
		if (vmodel.ai_value.SID!=="" && vmodel.ai_value.sid!=="") {
			var siddata = calSidToDevSid(vmodel.ai_value.sid);
			vmodel.ai_relevance_signal_sid = BigInt(vmodel.ai_value.sid).toString();
			vmodel.ai_relevance_device_sid = siddata.toString();
		} else {
			vmodel.ai_relevance_signal_sid = "";
			vmodel.ai_relevance_device_sid = "";
		}
	}
	if (d.objectid == 'para_config_di') {
		vmodel.di_instidvalue = [];
		addvalue(vmodel.di_instidvalue,d.data,"inst_id");
		var loopvar;
		for(loopvar = 0; loopvar < vmodel.di_instidvalue.length; loopvar++){
			if(vmodel.di_instidvalue[loopvar].inst_id == vmodel.di_selectexid) {
				vmodel.di_value = vmodel.di_instidvalue[loopvar];
				break;
			}
		}
		if (vmodel.di_value.SID!=="" && vmodel.di_value.sid!=="") {
			var siddata = calSidToDevSid(vmodel.di_value.sid);
			vmodel.di_relevance_signal_sid = BigInt(vmodel.di_value.sid).toString();
			vmodel.di_relevance_device_sid = siddata.toString();
		} else {
			vmodel.di_relevance_signal_sid = "";
			vmodel.di_relevance_device_sid = "";
		}
	}
	if (d.objectid == 'para_config_do') {
		vmodel.do_instidvalue = [];
		addvalue(vmodel.do_instidvalue,d.data,"inst_id");
		var loopvar;
		for(loopvar = 0; loopvar < vmodel.do_instidvalue.length; loopvar++){
			if(vmodel.do_instidvalue[loopvar].inst_id == vmodel.do_selectexid) {
				vmodel.do_value = vmodel.do_instidvalue[loopvar];
				break;
			}
		}
		if(vmodel.do_value.SID!=="" && vmodel.do_value.sid!==""){
			var siddata = calSidToDevSid(vmodel.do_value.sid);
			vmodel.do_relevance_signal_sid = BigInt(vmodel.do_value.sid).toString();
			vmodel.do_relevance_device_sid = siddata.toString();
		} else {
			vmodel.do_relevance_signal_sid = "";
			vmodel.do_relevance_device_sid = "";
		}
	}
}

function get_selcet_channel_info(objid) {
	vmodel.di_select_flag = "1";
	var Rq = {data:{objectid:objid,type:"val_get",paraval:JSON.stringify()},success:get_selcet_channel_info_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}


/*************************************获取DO状态*************************************************************/
function get_do_status_succ(d,r) {
	if (d.result == 'ok') {
		addvalue(vmodel.system_DOstate,d.data,"id");
	}
}

function get_do_status(board_type,do_type,io_index){
	var paraval = [{"board_type":String(board_type),"type":String(do_type),"index":String(io_index)}];
	var do_sta_req = {data:{objectid:"do_status",type:"val_get",paraval:JSON.stringify(paraval)}, success:get_do_status_succ};
	request.clearRequest(do_sta_req);
	request.addRequest([do_sta_req]);
}

function init_do_status(obj) {
	for (var i in obj) {
		get_do_status(obj[i]['Board Type'],obj[i]['Channel Type'],obj[i]['Channel NO.']);
	}
}

// 加载TAB标签是否显示请求（后续待完成接口）
// get_tab_display();

getDI_AIChannel();
get_di_allrelevancesid_info();
get_ai_di_relevancesid_info('0');

//  获取非复用的AI通道
get_all_channels_state();   // 获取非复用的AI通道

///////////////////////////////////******************DO相关操作：by xiaoshengxian***************************************//////////////
//获取DO结构信息
function getDOStructure(){
	var Rq = {data:{objectid:"plat.do",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listDOStructure};
	request.addRequest([Rq]);
}

function listDOStructure(d,r){
	addvalue(vmodel.do_structuredata,d.data,"name");
}

function getDOAllMsg() {
	var Rq = {data:{objectid:"para_config_do",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:getlistDOidname};
	request.addRequest([Rq]);
}

function getlistDOidname(d,r){
	vmodel.do_title_show = "1";
	if (d.result != "ok" || d.data.length == 0) {
		return;
	}
	init_do_status(d.data);
	vmodel.do_instidvalue = [];
	addvalue(vmodel.do_instidvalue,d.data,"inst_id");
	vmodel.do_selectexid = vmodel.do_instidvalue[0].inst_id;
	vmodel.do_value = vmodel.do_instidvalue[0];
	if(vmodel.do_value.SID!=="" && vmodel.do_value.sid!==""){
		var siddata = calSidToDevSid(vmodel.do_value.sid);
		vmodel.do_relevance_signal_sid = BigInt(vmodel.do_value.sid).toString();
		vmodel.do_relevance_device_sid = siddata.toString();
	} else {
		vmodel.do_relevance_signal_sid = "";
		vmodel.do_relevance_device_sid = "";
	}
}

function setDOValue(){
	if((vmodel.do_relevance_device_sid !=="")&&(vmodel.do_relevance_signal_sid ==="")){
		popupTipsDiv($("#setFailurealart"), 2000);
		get_selcet_channel_info("para_config_do");
		return;
	}
	if (!vmodel.judge_do_sid_valid(vmodel.do_relevance_signal_sid)&& (vmodel.do_relevance_device_sid !=="")) {
		popupTipsDiv($("#setFailurealart"), 2000);
		get_selcet_channel_info("para_config_do");
		return;
	}
	if(vmodel.do_relevance_signal_sid !=="" && vmodel.do_relevance_signal_sid !="NaN"){
		vmodel.do_value.SID = "0x"+BigInt(vmodel.do_relevance_signal_sid).toString(16);
	}
	else{
		vmodel.do_value.SID = "";
	}

	var Rq = {data:{objectid:"para_config_do",type:"val_set",paranum:"1",paraval:JSON.stringify([vmodel.do_value])},success:setDOSuccess};
	request.addRequest([Rq]);
}

function setDOSuccess(d,r){
	init_do_status();
	if(d.result ==="ok"){
		mainvalue.controlsuccess ="success";
		setTimeout(function(){
			get_selcet_channel_info("para_config_do");
		}, 3000);
	}
	else{
		mainvalue.controlsuccess ="failure";
		get_selcet_channel_info("para_config_do");
	}
}

//   获取DO关联信号的信息
function get_do_allrelevancesid_info() {
	var para = {"usage":"3"};
	var Rq = {data:{objectid:"signal_dido",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_do_allrelevancesid_succ};
	request.addRequest([Rq]);

	function get_do_allrelevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.do_all_device_signals.removeAll();
			addvalue(vmodel.do_all_device_signals,d.data,"sid");
			vmodel.do_device_sid.removeAll();
			addvalue(vmodel.do_device_sid,d.data,"dev_sid");
			get_do_relevancesid_info()
		}
	}
}

function get_do_relevancesid_info() {
	vmodel.do_relevance_device_signals.removeAll();
	for(var i in vmodel.do_all_device_signals){
		if(vmodel.do_all_device_signals[i].dev_sid == vmodel.do_relevance_device_sid){
			vmodel.do_relevance_device_signals.push(vmodel.do_all_device_signals[i]);
		}
	}
}

vmodel.$watch("do_selectexid", function(a) {
	for(var i in vmodel.do_instidvalue){
		if(vmodel.do_instidvalue[i].inst_id === vmodel.do_selectexid){
			vmodel.do_value = vmodel.do_instidvalue[i];
			vmodel.do_relevance_device_sid = '';
			if(vmodel.do_value.SID!=="" && vmodel.do_value.sid!==""){
				var siddata = calSidToDevSid(vmodel.do_value.sid);
				vmodel.do_relevance_signal_sid = BigInt(vmodel.do_value.sid).toString();
			 	vmodel.do_relevance_device_sid = siddata.toString();
			} else {
				vmodel.do_relevance_signal_sid = "";
				vmodel.do_relevance_device_sid = "";
			}
			break;
		}
	}
});

vmodel.$watch("do_relevance_device_sid", function(a) {
	vmodel.do_relevance_device_signals.removeAll();
	if(vmodel.do_relevance_device_sid!==""){
		get_do_relevancesid_info();
	}
	else{
		vmodel.do_relevance_signal_sid = "";
	}
});

function send_do_request(){
	init_do_status();
	get_do_allrelevancesid_info();
	getDOStructure();
	getDOAllMsg();
}

function clear_do_request(){
	var do_sta_req = {data:{objectid:"do_status",type:"val_get"}};
	request.clearRequest(do_sta_req);

	var Rq1 = {data:{objectid:"plat.do",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq1);

	var Rq2 = {data:{objectid:"para_config_do",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq2);
}
///////////////////////////////////******************DI相关操作：by 柳浪***************************************//////////////
//获取所有用作DI的通道
function getDI_AIChannel(obj){
	var Rq = {data:{objectid:"drycontact_ai_di",type:"val_get",paraval:JSON.stringify([])},success:get_ai_di_channel_info_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

function get_ai_di_channel_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	vmodel.di_ai_instidvalue.clear();
	addvalue(vmodel.di_ai_instidvalue, d.data, "inst_id");
}


//获取DI结构信息
function getDIStructure(){
	var Rq = {data:{objectid:"plat.di",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listDIStructure};
	request.addRequest([Rq]);
}

function listDIStructure(d,r){
	addvalue(vmodel.di_structuredata,d.data,"name");
}

function getDIAllMsg() {
	var Rq = {data:{objectid:"para_config_di",type:"val_get",paranum:"1",paraval:JSON.stringify()},success:getlistDIidname};
	request.addRequest([Rq]);
}

function getlistDIidname(d,r){
	vmodel.di_title_show = "1";
	if (d.result != "ok" || d.data.length == 0) {
		return;
	}
	vmodel.di_instidvalue = [];
	addvalue(vmodel.di_instidvalue,d.data,"inst_id");
	vmodel.di_selectexid = vmodel.di_instidvalue[0].inst_id;
	vmodel.di_value = vmodel.di_instidvalue[0];
	if (vmodel.di_value.SID!=="" && vmodel.di_value.sid!=="") {
		var siddata = calSidToDevSid(vmodel.di_value.sid);
		vmodel.di_relevance_signal_sid = BigInt(vmodel.di_value.sid).toString();
		vmodel.di_relevance_device_sid = siddata.toString();
	} else {
		vmodel.di_relevance_signal_sid = "";
		vmodel.di_relevance_device_sid = "";
	}
}


function check_alias_valid(str) {
    if (typeof(str) == 'undefined') {
        return false;
    }
    if (getStrLeng_UTF8(str) > 31) {
        return false;
    }
    var danger_char_list = ["/",";","&"," "];
    for (var i in danger_char_list) {
        if (checkStringStr(str, danger_char_list[i])) {
            return false;
        }
    }
    return true;
}


function setDIValue(){
	vmodel.di_select_flag = "0";
	if((vmodel.di_relevance_device_sid !=="")&&(vmodel.di_relevance_signal_sid ==="" ||vmodel.di_relevance_signal_sid ==="-1")){
		popupTipsDiv($("#setFailurealart"), 2000);
		get_selcet_channel_info("para_config_di");
		return;
	}
	if (vmodel.di_relevance_device_sid =="") {
		vmodel.di_relevance_signal_sid = "";
		vmodel.di_relevance_device_signals.removeAll();
	}
	if (typeof(vmodel.di_value['Channel Alias']) != 'undefined') {
		if (vmodel.judge_inrelay(vmodel.di_relevance_signal_sid) && !check_alias_valid(vmodel.di_value['Channel Alias'])){
			mainvalue.controlsuccess ="failure";
			get_selcet_channel_info("para_config_di");
			return;
		}
	}
	if(vmodel.di_relevance_signal_sid !=="" && vmodel.di_relevance_signal_sid !=="NaN"){
		vmodel.di_value.SID = "0x"+BigInt(vmodel.di_relevance_signal_sid).toString(16);
	}
	else{
		vmodel.di_value.SID = "";
	}

	var Rq = {data:{objectid:"para_config_di",type:"val_set",paranum:"1",paraval:JSON.stringify([vmodel.di_value])},success:setSuccess};
	request.addRequest([Rq]);
}

function setSuccess(d,r){
	if (d.result == "ok") {
		mainvalue.controlsuccess ="success";
	} else {
		mainvalue.controlsuccess ="failure";
		get_selcet_channel_info("para_config_di");
	}
}

//   获取DI关联信号的信息
function get_di_allrelevancesid_info() {
	var para = {"usage":"0"};
	var Rq = {data:{objectid:"signal_dido",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_di_allrelevancesid_succ};
	request.addRequest([Rq]);

	function get_di_allrelevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.di_all_device_signals.removeAll();
			addvalue(vmodel.di_all_device_signals,d.data,"sid");
			vmodel.di_device_sid.removeAll();
			addvalue(vmodel.di_device_sid,d.data,"dev_sid");
			get_di_relevancesid_info()
		}
	}
}


function get_di_relevancesid_info() {
	vmodel.di_relevance_device_signals.removeAll();
	for(var i in vmodel.di_all_device_signals){
		if(vmodel.di_all_device_signals[i].dev_sid == vmodel.di_relevance_device_sid){
			vmodel.di_relevance_device_signals.push(vmodel.di_all_device_signals[i])
		}
	}
	vmodel.di_select_flag = "0";
}

//   获取AI_DI复用关联信号的信息
function get_ai_di_relevancesid_info(usage) {
	var para = {"sid":vmodel.di_relevance_device_sid.toString(), "usage":usage};
	var Rq = {data:{objectid:"signal_aidido",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_ai_di_relevancesid_succ};
	request.addRequest([Rq]);

	function get_ai_di_relevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.ai_di_relevance_device_signals.removeAll();
			addvalue(vmodel.ai_di_relevance_device_signals,d.data,"sid");
		}
	}
}

function send_di_request(){
	getDIStructure();
	getDIAllMsg();
}

function clear_di_request(){
	var val_get_signal_di_do_req = {data:{objectid:"signal_dido",type:"val_get",paranum:"1"}};
	request.clearRequest(val_get_signal_di_do_req);

	var attr_get_di_req = {data:{objectid:"plat.di",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(attr_get_di_req);

	var val_get_di_req = {data:{objectid:"para_config_di",type:"val_get",paranum:"1",paraval:JSON.stringify()}};
	request.clearRequest(val_get_di_req);
}

vmodel.$watch("di_selectexid", function(a) {
	get_selcet_channel_info("para_config_di");
	for(var i in vmodel.di_instidvalue){
		if(vmodel.di_instidvalue[i].inst_id === vmodel.di_selectexid){
			vmodel.di_changed_flag = "1";
			vmodel.di_value = vmodel.di_instidvalue[i];
			vmodel.di_relevance_device_sid = '';
			if(vmodel.di_value.SID !=="" && vmodel.di_value.sid !==""){
				var siddata = calSidToDevSid(vmodel.di_value.sid);
				vmodel.di_relevance_signal_sid = BigInt((vmodel.di_value.sid)).toString();
				vmodel.di_relevance_device_sid = siddata.toString();
			} else {
				vmodel.di_relevance_signal_sid = "";
				vmodel.di_relevance_device_sid = "";
			}
			break;
		}
	}
});

vmodel.$watch("di_relevance_device_sid", function(a) {
	vmodel.di_relevance_device_signals.removeAll();
	if(vmodel.di_select_flag == "0" && vmodel.di_changed_flag == "0") {
		vmodel.di_relevance_signal_sid = "";
	}
	if(vmodel.di_relevance_device_sid!==""){
		get_di_relevancesid_info();
		get_ai_di_relevancesid_info("0");
		vmodel.di_changed_flag = "0";
	}
	else{
		vmodel.di_relevance_signal_sid = "";
		vmodel.ai_di_relevance_device_signals.removeAll();
		vmodel.ai_di_relevance_signal_sid = "";
	}
});


///////////////////////////////////******************AI相关操作：by 柳浪**************************************//////////////

function update_signal_info(d,r) {
	if (d.result == "ok") {
		refresh_value(vmodel.ai_devsidname, d.data);
		if(d.data[0].value == "val_invalid"){
			vmodel.ai_analogdata_value = mainvalue.i18nkeyword.value_invalid;
		}
		else{
			vmodel.ai_analogdata_value = d.data[0].value + d.data[0].unit;
		}
	}
}

function get_signal_info(paraval) {
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},refresh:3,success:update_signal_info};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}


function stop_ai_value_refresh(obj) {
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq);
}

//进一步筛选掉无效的设备项，获得AI温湿度通道有效的关联设备
function filter_ai_valid_device() {
	ai_humid_device_list = [];
	ai_temp_device_list = [];

	for(var i in vmodel.ai_humid_device_sid) {
		for(var j in mainvalue.devsid_listvalue) {
			if(vmodel.ai_humid_device_sid[i].dev_sid == mainvalue.devsid_listvalue[j].sid) {
				vmodel.ai_humid_device_sid[i].device_name = mainvalue.devsid_listvalue[j]["device name"];
				ai_humid_device_list.push(vmodel.ai_humid_device_sid[i]);
			}
		}
	}

	for(var i in vmodel.ai_temp_device_sid) {
		for(var j in mainvalue.devsid_listvalue) {
			if(vmodel.ai_temp_device_sid[i].dev_sid == mainvalue.devsid_listvalue[j].sid) {
				vmodel.ai_temp_device_sid[i].device_name = mainvalue.devsid_listvalue[j]["device name"];
				ai_temp_device_list.push(vmodel.ai_temp_device_sid[i]);
			}
		}
	}
}

function init_ai_temp_humid_device_sid() {
	vmodel.ai_humid_device_sid.removeAll();
	vmodel.ai_temp_device_sid.removeAll();
	
	//根据静态表筛选AI温湿度通道可支持的关联设备
	addvalue(vmodel.ai_humid_device_sid,ai_humid_all_device_signals,"dev_sid");
	addvalue(vmodel.ai_temp_device_sid,ai_temp_all_device_signals,"dev_sid");

	filter_ai_valid_device();
}

//获取AI结构信息
function getAIStructure(objid){
	var Rq = {data:{objectid:objid,type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listAIStructure};
	request.addRequest([Rq]);
}

function listAIStructure(d,r){
	addvalue(vmodel.ai_structuredata,d.data,"name");
}


function getAIAllMsg() {
	var Rq = {data:{objectid:"para_config_ai",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:getlistAIidname};
	request.addRequest([Rq]);
}

function getlistAIidname(d,r){
	vmodel.ai_title_show = "1";
	if (d.result != "ok" || d.data.length == 0) {
		return;
	}
	vmodel.ai_instidvalue = [];
	// 对通道顺序进行筛选排序，后续扩展优化
	let arr = d.data.filter(inst_info => inst_info['Channel Type'] == '3');
	arr.sort((a,b) => { return a['Channel Name'].split('_')[2].localeCompare(b['Channel Name'].split('_')[2]); });
	d.data = d.data.filter(inst_info => inst_info['Channel Type'] != '3');
	addvalue(vmodel.ai_instidvalue,d.data,"inst_id");
	addvalue(vmodel.ai_instidvalue,arr,"inst_id");

	vmodel.ai_pure_selectexid = vmodel.ai_instidvalue[0].inst_id;
	vmodel.ai_value = vmodel.ai_instidvalue[0];
	vmodel.ai_selectexid = vmodel.ai_pure_selectexid;
	if (vmodel.ai_value.SID!=="" && vmodel.ai_value.sid!=="") {
		var siddata = calSidToDevSid(vmodel.ai_value.sid);
		vmodel.ai_relevance_signal_sid = BigInt(vmodel.ai_value.sid).toString();
		vmodel.ai_relevance_device_sid = siddata.toString();
	} else {
		vmodel.ai_relevance_signal_sid = "";
		vmodel.ai_relevance_device_sid = "";
	}
}

function setAIValue(){
	if((vmodel.ai_relevance_device_sid !=="")&&(vmodel.ai_releaivance_signal_sid ==="" ||vmodel.ai_relevance_signal_sid ==="-1")){
		popupTipsDiv($("#setFailurealart"), 2000);
		get_selcet_channel_info("para_config_ai");
		return;
	}
	if (!vmodel.judge_ai_sid_valid(vmodel.ai_relevance_signal_sid) && (vmodel.ai_relevance_device_sid !=="")) {
		popupTipsDiv($("#setFailurealart"), 2000);
		get_selcet_channel_info("para_config_ai");
		return;
	}
	if (typeof(vmodel.ai_value['Channel Alias']) != 'undefined') {
		if (vmodel.judge_inrelay(vmodel.ai_relevance_signal_sid) && !check_alias_valid(vmodel.ai_value['Channel Alias'])){
			mainvalue.controlsuccess ="failure";
			vmodel.ai_value['Channel Alias'] = "";
			return;
		}
	}
	if(vmodel.ai_relevance_signal_sid !=="" && vmodel.ai_relevance_signal_sid !=="NaN"){
		vmodel.ai_value.SID = "0x"+BigInt(vmodel.ai_relevance_signal_sid).toString(16);
	}
	else{
		vmodel.ai_value.SID = "";
	}
	var Rq = {data:{objectid:"para_config_ai",type:"val_set",paranum:"1",paraval:JSON.stringify([vmodel.ai_value])},success:setAISuccess};
	request.addRequest([Rq]);
}

function setAISuccess(d,r){
	if(d.result ==="ok"){
		if (vmodel.ai_value.SID != "") {   //   标准AI通道
			var para = {"sid":vmodel.ai_relevance_signal_sid.toString(), "value":"", "unit":""};
			get_signal_info([para]);
		}
		mainvalue.controlsuccess ="success";
		setTimeout(function(){
			get_selcet_channel_info("para_config_ai");
		}, 3000);
	}
	else{
		mainvalue.controlsuccess ="failure";
		get_selcet_channel_info("para_config_ai");
	}
}

//   获取AI关联信号的信息
function get_ai_relevancesid_info(usage) {
	var para = {"sid":vmodel.ai_relevance_device_sid.toString(), "usage":usage};
	var Rq = {data:{objectid:"signal_aidido",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_ai_relevancesid_succ};
	request.addRequest([Rq]);

	function get_ai_relevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.ai_relevance_device_signals.removeAll();

			//根据静态表筛选AI温湿度通道关联设备对应的所有满足信号
			if(vmodel.ai_value['Class Type'] == "2") {
				if(vmodel.ai_selectexid == "plat.humity") {
					for(var i in ai_humid_all_device_signals) {
						if(ai_humid_all_device_signals[i].dev_sid == vmodel.ai_relevance_device_sid) {
							addvalue(vmodel.ai_relevance_device_signals,d.data.filter(sid_info => sid_info.sid == ai_humid_all_device_signals[i].sid),"sid");
						}
					}
				}
				else {
					for(var i in ai_temp_all_device_signals) {
						if(ai_temp_all_device_signals[i].dev_sid == vmodel.ai_relevance_device_sid) {
							addvalue(vmodel.ai_relevance_device_signals,d.data.filter(sid_info => sid_info.sid == ai_temp_all_device_signals[i].sid),"sid");
						}
					}
				}
			}
			else {
				d.data = d.data.filter(sid_info => sid_info.sid != '2533343778635777');
				addvalue(vmodel.ai_relevance_device_signals,d.data,"sid");
			}
			vmodel.ai_analogdata_value = vmodel.getAISidVal(vmodel.ai_relevance_signal_sid);
			if(vmodel.ai_analogdata_value != "") {
				// 开启值的刷新
				if (usage=='1' && vmodel.ai_relevance_signal_sid != "") {   // 刷新单纯AI通道显示值
					var para = {"sid":vmodel.ai_relevance_signal_sid.toString(), "value":"", "unit":""};
					get_signal_info([para]);
				}
			} else {
				if (vmodel.ai_value['Class Type'] != "2") {
					vmodel.ai_relevance_device_sid = '';
				}
			}
		} else {
			vmodel.ai_relevance_device_signals.removeAll();
			vmodel.ai_relevance_signal_sid = "";
			vmodel.ai_analogdata_value = "";
			stop_ai_value_refresh();
		}
	}
}

vmodel.$watch("ai_selectexid", function(a) {
	for(var i in vmodel.ai_instidvalue){
		if(vmodel.ai_instidvalue[i].inst_id === vmodel.ai_selectexid){
			vmodel.ai_value = vmodel.ai_instidvalue[i];
			vmodel.ai_relevance_device_sid = '';
			if(vmodel.ai_value.SID!=="" && vmodel.ai_value.sid!==""){
				var siddata = calSidToDevSid(vmodel.ai_value.sid);
				vmodel.ai_relevance_signal_sid = BigInt(vmodel.ai_value.sid).toString();
				vmodel.ai_relevance_device_sid = siddata.toString();
			} else {
				vmodel.ai_relevance_signal_sid = "";
				vmodel.ai_relevance_device_sid = "";
			}
			break;
		}
	}

	// 防止全局的有效设备列表可能因前后端交互时间长，导致初始化AI通道时没有获取到数据就进行了筛选处理
	if(ai_humid_device_list.length == 0 && ai_temp_device_list.length == 0) {
		filter_ai_valid_device();
	}

	// 获取AI温湿度通道的有效关联设备
	if(vmodel.ai_value['Class Type'] == "2") {
		vmodel.ai_device_sid.removeAll();
		if(vmodel.ai_selectexid == "plat.humity") {
			addvalue(vmodel.ai_device_sid,ai_humid_device_list,"dev_sid");
		}
		else {
			addvalue(vmodel.ai_device_sid,ai_temp_device_list,"dev_sid");
		}
	}

});

function send_ai_request(){
	init_ai_temp_humid_device_sid();
	get_ai_relevancesid_info('1');
	getAIStructure("plat.ai");
	getAIAllMsg();
}

function clear_ai_request(){
	var Rq1 = {data:{objectid:"signal",type:"val_get",paranum:"1"},refresh:3};
	request.clearRequest(Rq1);

	var Rq2 = {data:{objectid:"plat.ai",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq2);

	var Rq3 = {data:{objectid:"para_config_ai",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq3);
}

vmodel.$watch("ai_relevance_device_sid", function(a) {
	if (vmodel.ai_relevance_device_sid!=="") {
		get_ai_relevancesid_info('1');      //  获取AI相关信号量
	} else{
		vmodel.ai_relevance_device_signals.removeAll();
		vmodel.ai_relevance_signal_sid = "";
		vmodel.ai_analogdata_value = "";
		stop_ai_value_refresh();
	}
});

function update_temp_humid_signal_info(d,r) {
	if (d.result == "ok") {
		if(d.data[0].value == "val_invalid"){
			vmodel.temp_humid_analogdata_value = mainvalue.i18nkeyword.value_invalid;
		}
		else{
			vmodel.temp_humid_analogdata_value = d.data[0].value + d.data[0].unit;
		}
	} else {
		vmodel.temp_humid_analogdata_value  = "";
	}
}

function get_temp_humid_signal_info(paraval) {
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},refresh:3,success:update_temp_humid_signal_info};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

function stop_temp_humid_value_refresh(obj) {
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq);
}

function getTempHumidAllMsg() {
	var Rq = {data:{objectid:"temp_humid_cfg",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:getlistTempHumididname};
	request.addRequest([Rq]);
}

function getlistTempHumididname(d,r){
	if (d.result != "ok") {
		return;
	}
	vmodel.cur_ext_temp_sid = d.data[0]["cur_ext_tmp_sid"];
	vmodel.cur_ext_humid_sid = d.data[0]["cur_ext_hum_sid"];
	vmodel.temp_humid_relevance_signal_sid = vmodel.cur_ext_temp_sid;
	vmodel.temp_humid_selectexid = 1;

}

function setTempHumidValue(){
	if(vmodel.temp_humid_relevance_signal_sid ==="" || vmodel.temp_humid_relevance_signal_sid ==="NaN"){
		popupTipsDiv($("#setFailurealart"), 2000);
		return;
	}
	vmodel.temp_humid_value = "0x"+ BigInt(vmodel.temp_humid_relevance_signal_sid).toString(16);
	let para = {"channel" : (vmodel.temp_humid_selectexid).toString(), "sid":vmodel.temp_humid_value};
	var Rq = {data:{objectid:"temp_humid_cfg",type:"val_set",paranum:"1",paraval:JSON.stringify([para])},success:setTempHumidSuccess};
	request.addRequest([Rq]);
}

function setTempHumidSuccess(d,r){
	if(d.result ==="ok"){
		if (vmodel.temp_humid_value != "") {
			let para = {"sid":vmodel.temp_humid_relevance_signal_sid.toString(), "value":"", "unit":""};
			get_temp_humid_signal_info([para]);
		}
		mainvalue.controlsuccess ="success";
	}
	else{
		mainvalue.controlsuccess ="failure";
		if (vmodel.temp_humid_selectexid == "0") {
			vmodel.temp_humid_relevance_signal_sid = vmodel.cur_ext_humid_sid;
		} else if (vmodel.temp_humid_selectexid == "1") {
			vmodel.temp_humid_relevance_signal_sid = vmodel.cur_ext_temp_sid;
			let para = {"sid":vmodel.temp_humid_relevance_signal_sid.toString(), "value":"", "unit":""};
			get_temp_humid_signal_info([para]);
		}
	}
}
// 获取温湿度传感器通道关联信号的信息
function get_temp_humid_relevancesid_info(usage) {
	var para = {"sid":"2533343509872640", "usage":usage};
	var Rq = {data:{objectid:"signal_aidido",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_temp_humid_relevancesid_succ};
	request.addRequest([Rq]);

	function get_temp_humid_relevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.temp_humid_relevance_device_signals.removeAll();
			addvalue(vmodel.temp_humid_relevance_device_signals,d.data,"sid");
			vmodel.temp_humid_analogdata_value = vmodel.getTempHumidSidVal(vmodel.temp_humid_relevance_signal_sid);
			if(vmodel.temp_humid_analogdata_value != "") {
				// 开启值的刷新
				if (usage=='4' && vmodel.temp_humid_relevance_signal_sid != "") {
					var para = {"sid":vmodel.temp_humid_relevance_signal_sid.toString(), "value":"", "unit":""};
					get_temp_humid_signal_info([para]);
				}
			}
		} else {
			vmodel.temp_humid_relevance_device_signals.removeAll();
			vmodel.temp_humid_relevance_signal_sid = "";
			vmodel.temp_humid_analogdata_value = "";
			stop_temp_humid_value_refresh();
		}
	}
}

vmodel.$watch("temp_humid_selectexid", function(a) {
	if (vmodel.temp_humid_selectexid == "0") {
		vmodel.temp_humid_relevance_signal_sid = vmodel.cur_ext_humid_sid;
	} else {
		vmodel.temp_humid_relevance_signal_sid = vmodel.cur_ext_temp_sid;
	}
	if (vmodel.temp_humid_relevance_signal_sid != "0") {
		var para = {"sid":vmodel.temp_humid_relevance_signal_sid.toString(), "value":"", "unit":""};
		get_temp_humid_signal_info([para]);
	} else {
		stop_temp_humid_value_refresh();
		vmodel.temp_humid_analogdata_value = "";
	}
})

function send_temp_humid_request() {
	getTempHumidAllMsg();
	get_temp_humid_relevancesid_info('4');
}

function clear_temp_humid_request() {
	var Rq1 = {data:{objectid:"signal",type:"val_get",paranum:"1"},refresh:3};
	request.clearRequest(Rq1);

	var Rq2 = {data:{objectid:"temp_humid_cfg",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq2);
}

function get_all_channels_state(obj) {
	var getIOState = {data:{objectid:"di_do",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getIOState,refresh:3};
	request.clearRequest(getIOState);
	request.addRequest([getIOState]);

	function getIOState(d,r){
		addvalue(vmodel.system_IOstate,d.data,"full_name");
	}
}

function gotoaidipage(type, tab) {
	// 1-DI 2-AI 3-AI_DI 4-DO 5-温湿度
	vmodel.tab_list = tab;
	set_cookie_with_path("tab_list", vmodel.tab_list);
	switch(type*1){
		case 1:
			break;
		case 2:
			vmodel.ai_selectexid = vmodel.ai_pure_selectexid;
			break;
		case 3:
			break;
		default:
			break;
	}
	send_request_by_tab(tab);
}

function send_request_by_tab(tab){
	switch(tab){
		case "diconfig":
			send_di_request();
			clear_request_by_flag(["aiconfig", "doconfig", "temp_humid_config"]);
			break;
		case "aiconfig":
			send_ai_request();
			clear_request_by_flag(["diconfig", "doconfig", "temp_humid_config"]);
			break;
		case "doconfig":
			send_do_request();
			clear_request_by_flag(["diconfig", "aiconfig", "temp_humid_config"]);
			break;
		case "temp_humid_config":
			send_temp_humid_request();
			clear_request_by_flag(["diconfig", "aiconfig", "doconfig"]);
			break;
		default:
			clear_request_by_flag(["diconfig", "aiconfig", "doconfig", "temp_humid_config"]);
			break;
	}
}

function clear_request_by_flag(arr){
	var map = {
			"diconfig":clear_di_request,
			"aiconfig":clear_ai_request,
			"doconfig":clear_do_request,
			};
	for(var i=0;i<arr.length;i++){
		if(map[arr[i]]){
			map[arr[i]]();
		}
	}
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "diconfig" || tab == "aiconfig" || tab == "doconfig") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "diconfig";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();
send_request_by_tab(vmodel.tab_list);
