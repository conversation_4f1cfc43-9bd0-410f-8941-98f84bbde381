#######################################################################
##
## Corresponding documentation:
##
## http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModAccesslog
##
server.modules += ( "mod_accesslog" )

##
## Default access log.
##
accesslog.filename          = log_root + "/access.log"

##
## The default format produces CLF compatible output.
## For available parameters see access.txt 
##
#accesslog.format = "%h %l %u %t \"%r\" %b %>s \"%{User-Agent}i\" \"%{Referer}i\""

##
## If you want to log to syslog you have to unset the 
## accesslog.use-syslog setting and uncomment the next line.
##
#accesslog.use-syslog       = "enable"

#
#######################################################################
