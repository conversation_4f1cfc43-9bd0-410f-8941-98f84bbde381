var vmodel = avalon.define({
	$id:'dry_contact_info',
	tab_list:'diconfig',
	di_structuredata:[], //DI通用结构信息
    pure_di_instids:["plat.Inrelay1Status", "plat.Inrelay2Status"],   //  纯DI实例
	di_ai_instidvalue:[],//每个DI对应的Value信息（所有di，包含复用通道）
    di_ai_instids:[],
	di_instidvalue:[],//每个DI对应的Value信息（未关联信号）
	di_instidvalue_relevance_sid:[],//每个DI对应的Value信息(关联信号)
	ai_instidvalue:[],//每个AI对应的Value信息
	di_relevance_signal_sid:[],// DI关联SID
	di_relevance_device_sid:[],// DI关联信号对应设备信息
	di_defaultconfig_sid:[],
	convention:{},
	alm_levels:{},
	di_relevance_sid:[], //sid集合
	signal_done:false,
	di_title_show:"-1", // di标题显示标记

	num:[], //存储通道改变的索引
	donum:[],
	contact_changedidata:[],     // 存储变化的DI数据
	contact_changedodata:[],

	dev_changesiddata:[],     // 存储变化的数据
	dev_set_data:[],     //  存储设置的参数

	changeDevData: function(sidvalue,changekey){
		var changedata = [];
		var changeobject = {};
		vmodel.changeSig_tag = "true";
		changeobject.sid = sidvalue;
		changeobject[changekey] = event.target.value;
		changedata.push(changeobject);
		addvalue(this.dev_changesiddata,changedata,"sid");
	},

	changeChannelData: function(changeindex){
		if (vmodel.num.indexOf(changeindex) <= -1) {
			vmodel.num.push(changeindex);
		}
	},

	changeDoChannelData:function(changeindex){
		var changenum = [];
		if (vmodel.donum.indexOf(changeindex) <= -1) {
			changenum.push(changeindex);
			vmodel.donum.push(changenum);
		}
	},

	judge_inrelay:function(sid){    // 判断一个信号是否为输入干接点告警或状态
		sid = BigInt(sid);
		var sigtype = calSidToDevType(sid);
		if (sigtype.devType == "1" && sigtype.devSn == "1"
		&& ((sigtype.sigType == "3" && sigtype.devSigVar == "3")  // 输入干接点告警
			|| sigtype.sigType == "2" && sigtype.devSigVar == "2")  // 输入干接点状态
		){
			return true;
		}
		if (sid == ""){  // 关联信号为无时，可编辑别名用于输入干接点状态
			return true;
		}
		return false;
	},

	judge_alm_level:function(signal){   //判断一个信号是否含有告警级别
		if (signal.alm_level == null) {
			return false;
		} else {
			return true;
		}
	},

	judge_alm_relay:function(signal){   //判断一个信号是否含有输出干接点
		if (signal.alm_relay == null) {
			return false;
		} else {
			return true;
		}
	},
	tabChange : function(tab) {
		vmodel.tab_list = tab;
		set_cookie_with_path("tab_list", vmodel.tab_list);
	},

///////////////////////////////////******************DO相关参数***************************************//////////////

do_value:{},//当前选择的DO的Value信息
do_selectexid:"",//当前选择的实例ID信息
do_listid:[],//DO的实例ID信息
do_instidvalue:[],//每个DO对应的Value信息
do_relevance_signal_sid:"",    // DO关联SID
do_relevance_device_sid:"",    // DO关联设备SID
do_nonenumber:[], //存储无关联信号序号
do_namedata:[], //存在的对应关联信号
do_fullname:[],//关联信号数据
do_sids:[],
do_convention:{},
do_title_show:"-1", // do标题显示标记
});

function getDIStructure(){
	vmodel.changeSig_tag = "false";
	vmodel.change_tag = "false";
	var Rq = {data:{objectid:"plat.di",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listDIStructure};
	request.addRequest([Rq]);
}

function listDIStructure(d,r){
	if (d.result != 'ok') {
		return;
	}

	for (let i in d.data) {
		if (d.data[i].id == "presetstatus") {
			vmodel.convention = transformToJson(d.data[i].convention);
		}
	}

}

setTimeout(function(){
	getDIStructure();
	getAlmLeverStructure();
	//  获取DO信息
	listDoInstid();
}, 0);


//获取告警结构信息
function getAlmLeverStructure(){
	var Rq = {data:{objectid:"signal",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"sid":"281544501755905"}])},success:listAlmLeverStructure};
	request.addRequest([Rq]);
}

function listAlmLeverStructure(d,r){
	if (d.result != 'ok') {
		return;
	}
	vmodel.alm_levels = transformToJson(d.data[0].alm_level);
}


function get_di_channel_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	for (let i in d.data) {
		if (d.data[i].SID==""){
			addvalue(vmodel.di_instidvalue,d.data,"instid");
		} else {
			addvalue(vmodel.di_instidvalue_relevance_sid,d.data,"instid");
		}
	}
	//getAIChannel();
}

getDI_AIChannel();
function get_sid_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	addvalue(vmodel.di_relevance_signal_sid,d.data,"sid");
}

function get_device_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	addvalue(vmodel.di_relevance_device_sid,d.data,"sid");
}



//获取所有用作DI的通道
function getDI_AIChannel(obj){
	var Rq = {data:{objectid:"drycontact_ai_di",type:"val_get",paraval:JSON.stringify([])},success:get_ai_di_channel_info_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

function get_ai_di_channel_info_succ(d,r) {
	if (d.result != 'ok' || d.data.length == 0) {
		vmodel.di_title_show = 0;
		return;
	} else {
		vmodel.di_title_show = 1;
	}
	vmodel.di_ai_instidvalue.clear();
	addvalue(vmodel.di_ai_instidvalue, d.data, "inst_id");
}


function getSIDInfo(obj){
	var Rq = {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(vmodel.di_relevance_sid)},success:get_sid_info_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

function getDeviceInfo(){
	var Rq = {data:{objectid:"device",type:"attr_get",paraval:JSON.stringify(vmodel.di_relevance_sid)},success:get_device_info_succ}
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

//获取所有的sid集合

function get_di_relevance_sid(){
		var array = [];
		for (let i in vmodel.di_ai_instidvalue) {
			var para = {};
			var sid = BigInt('0x'+vmodel.di_ai_instidvalue[i].SID).toString();
			para["sid"] = sid;
			array.push(para);
		}
		vmodel.di_relevance_sid = array;
}

function check_alias_valid(str) {
    if (getStrLeng_UTF8(str) > 31) {
        return false;
    }
    var danger_char_list = ["/",";","&"," "];
    for (var i in danger_char_list) {
        if (checkStringStr(str, danger_char_list[i])) {
            return false;
        }
    }
    return true;
}

function setDIValue(){
	var paras_di = [];
	for (let i in vmodel.num) {
		var index = vmodel.num[i];
		paras_di.push(vmodel.di_ai_instidvalue[index]);
		if (!check_alias_valid(vmodel.di_ai_instidvalue[index]['Channel Alias'])){
			mainvalue.controlsuccess ="failure";
			vmodel.di_ai_instidvalue[index]['Channel Alias'] = "";
			getDI_AIChannel();
			return;
		}
	}
	vmodel.num.clear();
	vmodel.contact_changedidata = paras_di;

	if (vmodel.contact_changedidata.length > 0) {
		var Rq1 = {data:{objectid:"drycontact_ai_di",type:"val_set",paraval:JSON.stringify(vmodel.contact_changedidata)},success:setDISuccess};
		request.addRequest([Rq1]);
		vmodel.contact_changedidata=[];
	}

	function setDISuccess(d,r){
		if (d.result == "ok") {
			mainvalue.controlsuccess ="success";
		} else {
			mainvalue.controlsuccess ="failure";
			getDI_AIChannel();
		}
	}
}

function setContactData(){
	setDIValue();
}

///////////////////////////////////******************DO相关操作***************************************//////////////

function getAllDo(){
	var Rq = {data:{objectid:"parameter_do",type:"val_get",paraval:JSON.stringify([])},success:getlistDOidname};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}


function getlistDOidname(d,r){
	if (d.result != "ok" || d.data.length == 0) {
		vmodel.do_title_show = 0;
		return;
	} else {
		vmodel.do_title_show = 1;
	}
	vmodel.do_instidvalue = d.data;
}

getAllDo();

function getDoSids(){
	for(var i in vmodel.do_instidvalue){
		if (vmodel.do_instidvalue[i].SID=="" || vmodel.do_instidvalue[i].SID *1 <= 0){
			vmodel.do_nonenumber.push(i);
		} else {
			var sid = BigInt('0x'+vmodel.do_instidvalue[i].SID).toString();
			vmodel.do_sids.push(sid);
		}
	}

}

function getSignal() {
	var paras = [];
	for (var i in vmodel.do_sids) {
		var para = {};
		para["sid"]=vmodel.do_sids[i];
		paras.push(para);
	}
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify(paras)},success:getSignalSucc};
	request.addRequest([Rq]);
}


function getSignalSucc(d,r){
	if (d.result != "ok") {
		return;
	}
	vmodel.do_namedata = d.data;
	var arr = [];
	var len = vmodel.do_nonenumber.length + vmodel.do_namedata.length;  	// 合并数组
	for(var i=0;i<len;i++){
		if(i==vmodel.do_nonenumber[0]){
			vmodel.do_nonenumber.shift();
			arr.push(mainvalue.i18nkeyword.null);
		}else{
			arr.push(vmodel.do_namedata[0].full_name);
			vmodel.do_namedata.shift();
		}
	}
	vmodel.do_fullname =  arr;
}

function getDOStructure(){
	var Rq = {data:{objectid:"plat.do",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listDOStructure};
	request.addRequest([Rq]);
}
getDOStructure();

function listDOStructure(d,r){
	if (d.result != 'ok') {
		return;
	}
	for (let i in d.data) {
		if (d.data[i].id == "presetstatus") {
			vmodel.do_convention = transformToJson(d.data[i].convention);
		}
	}

}


function setPresetStatus(){
	var paras_do = [];
	for (let i in vmodel.donum) {
		var index = vmodel.donum[i];
		paras_do.push(vmodel.do_instidvalue[index]);
	}
	vmodel.donum.clear();
	vmodel.contact_changedodata = paras_do;
	if (vmodel.contact_changedodata.length > 0) {
		var Rq = {data:{objectid:"parameter_do",type:"val_set",paraval:JSON.stringify(vmodel.contact_changedodata)},success:setDOSuccess};
	    request.addRequest([Rq]);
	    vmodel.contact_changedodata=[];
	}
}

function setDOSuccess(d,r){
	if(d.result ==="ok"){
		mainvalue.controlsuccess ="success";
	}
	else{
		mainvalue.controlsuccess ="failure";
		getAllDo();
	}
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "diconfig" || tab == "doconfig") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "diconfig";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();