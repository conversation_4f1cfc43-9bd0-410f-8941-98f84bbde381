#!/bin/sh
#
# This script will be executed *after* all the other init scripts.
# You can put your own initialization stuff in here if you don't
# want to do the full Sys V style init stuff.

echo "roll back old packet"

APP=`ls /root/power/bin | sed -e 's/SysMonitor_run//g'`
top -b -d 1 -n 1 >> /tmp/log/message
echo "kill all process besides SysMonitor_run!"
killall -9 $APP
killall -9 lighttpd

echo "take default status lighttpd.crt, pem to running status dir!"
if [ ! -f /mnt/data/work/web/backup/lighttpd.crt ] ; then
    cp -f /root/power/etc/lighttpd/ssl/lighttpd.crt /mnt/data/work/web/backup/
    cp -f /root/power/etc/lighttpd/ssl/lighttpd.pem /mnt/data/work/web/backup/
fi
cp -f /mnt/data/work/web/backup/lighttpd.crt /mnt/data/work/web/ssl/
cp -f /mnt/data/work/web/backup/lighttpd.pem /mnt/data/work/web/ssl/

df -h >> /tmp/log/message
# top -b -d 1 -n 1 >> /tmp/log/message
ls -lh /mnt/data/app_backup/powernewbak.tar.gz >> /tmp/log/message

echo "start unzip rollback package!"
tar zxf /mnt/data/app_backup/powernewbak.tar.gz -C /root/  >> /tmp/log/message
if [ $? -ne 0 ] ; then
    df -h >> /tmp/log/message
    top -b -d 1 -n 1 >> /tmp/log/message
else
    #top -b -d 1 -n 1 >> /tmp/log/message
    sync
    echo "finish unzip rollback package!"
    echo "roll back ok!"
fi
