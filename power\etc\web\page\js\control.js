//   当前选择的业务对象
var selectBoId = Cookies.get("pagelist");
//BusinessObject.GetControlObject();
function patch_signal_control(paraval){
	var req_set = {data:{objectid:"signal",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
	request.addRequest([req_set]);

	function dev_set_succ(d,r){
		if (d.result == "ok") {
			mainvalue.controlsuccess = "success";
		} else {
			mainvalue.controlsuccess = "failure";
		}
	}
}

// 整流器解锁弹窗
var smr_unlock_popup = $.confirm({
    lazyOpen: true,
	title: "",
	content: "",
	boxWidth: '30%',
	useBootstrap: false,
    onOpenBefore: function () {   // 弹窗内容加载
		this.setTitle(mainvalue.i18nkeyword.system.smr_unlock_sn);
        this.setContent(
			'<div>' +
			'<input type="password" id="unlock_sn" style="width: 85%">' +
			'</div>'
			);
        this.buttons.confirm.setText(mainvalue.i18nkeyword.seco_cert.confirm);
		this.buttons.close.setText(mainvalue.i18nkeyword.seco_cert.close);
    },
	buttons: {
		confirm: {
			text: "",
			btnClass: 'btn-info',
			action: function () {
				var password = this.$content.find('#unlock_sn').val();
				var para = [{"sn":control_vmodel.smr_unlock_sn.toString(),"password":password}];
				var req = {data:{objectid:"smr_unlock",type:"val_set",paraval:JSON.stringify(para)},success:set_unlock_sn_success};
				request.addRequest([req]);
				smr_unlock_popup.close();
			}
		},
		close: {
			text: "",
			action: function () {
				smr_unlock_popup.close();
			}
		}

	},
});

function set_unlock_sn_success(d,r) {
	if (d.result === "ok") {
		setTimeout(function () {
			control_vmodel.smr_unlock_infos.clear();
			control_value_get();
		}, 3000);
		alert(d.data[0]['result_info']);
	}
}

var  control_vmodel = avalon.define({
	$id:'control_info',
	init_finished:0,
	eeprom_control_flag:1, // 默认显示按钮，0:无EEPROM控制量，不显示按钮
	control_infos:[],
	eeprom_infos:[[]],
	smr_unlock_infos:[],
	smr_unlock_sn:0,
	//设置控制量
    setDevValue:function(d){
		let sid = String(d)
    	var setControl = [{"sid":sid,"value":"1"}];
		if(sid == "281544770650113"){//能源网管SSH口令重置增加二次认证
			let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{req:"enable"}])},success:get_sig_control_cert_enable_success};
			request.addRequest([req]);
		} else if (judge_smr_unlock_sid(sid)) {
			var sigtype = calSidToDevType(BigInt(sid));
			control_vmodel.smr_unlock_sn = sigtype.devSn;  // 保存解锁整流器的序号
			smr_unlock_popup.open();  // 弹窗支持输入解锁码
		} else {
			patch_signal_control(setControl);
		}
	},
	//设置EEPROM
	eeprom_control:function(action){
		if (action === "0") {
			var Rq1	= {data:{objectid:"eeprom_info",type:"val_set",paraval:JSON.stringify([{"action":"0","usage":"1"}])},success:eeprom_to_csu_result};
			request.addRequest([Rq1]);
			showDataImport();
			normalheart.stop();
			importheart.start();
		} else if (action === "1") {
			var Rq2	= {data:{objectid:"eeprom_info",type:"val_set",paraval:JSON.stringify([{"action":"1","usage":"1"}])},success:csu_to_eeprom_result};
			request.addRequest([Rq2]);
			showDataInit();
		}
	},
});

function eeprom_to_csu_result(d,r) {
    if (d.result === "ok") {
		if (d.data[0]['error_info'] !== "") {
			alert(d.data[0]['error_info']);
		}
	} else {
        hideDataImport();
		normalheart.start();
		alert(mainvalue.i18nkeyword.operate_failure);
    }
}

function csu_to_eeprom_result(d,r) {
	hideDataInit();
	if (d.result === "ok") {
		alert(mainvalue.i18nkeyword.operate_successfully);
	} else {
		alert(mainvalue.i18nkeyword.operate_failure);
	}
}

function get_sig_control_cert_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	if(d.data[0].seco_cert_enable == 1){//二级认证使能开关开启
		modal_cmp_psw_fn = modal_cmp_psw_succ;
		modal_confirm_succ_fn = ssh_cmd_reset_control;
		$('#appModal').modal('show');
	}else{
		ssh_cmd_reset_control();
	}
}

function ssh_cmd_reset_control(){
	patch_signal_control([{"sid":"281544770650113","value":"1"}]);//能源网管SSH口令重置
}


function classify_signaldata_dev(signaldata) {
	var devin = {};
	var sigtype;
	var dev_str = "";
	var rslt_data = [];
	var index = 0;
	var smr_online_sn = {};
	var smr_online_index = 0;
	for (var i in signaldata) {
		sigtype = calSidToDevType(BigInt(signaldata[i].sid));
		dev_str = sigtype.devType + "_" + sigtype.devSn;
		if (sigtype.devType === 7) {
			if (!Object.values(smr_online_sn).includes(sigtype.devSn.toString())) {
				let index_str = smr_online_index.toString();
				smr_online_sn[index_str] = sigtype.devSn.toString();  // 依次保存在线整流器的序号，如{"0":"1", "1":"2"}
				smr_online_index++;
			}
		}
		if (dev_str in devin) {
			rslt_data[devin[dev_str]].push(signaldata[i]);
		} else {
			devin[dev_str] = index;
			rslt_data[devin[dev_str]] = new Array();
			rslt_data[devin[dev_str]].push(signaldata[i]);
			index++;
		}
	}
	// 获取判断整流器是否满足解锁条件
	if (smr_online_index !== 0) {
		smr_online_sn["smr_online_num"] = smr_online_index.toString();  // 请求参数如{"0":"1", "1":"2"，"smr_online_num":"2"}
		var req_get = {data:{objectid:"smr_unlock",type:"val_get",paranum:"1",paraval:JSON.stringify([smr_online_sn])},success:smr_unlock_get_succ};
		request.addRequest([req_get]);
	}

	function smr_unlock_get_succ(d,r) {
		if (d.result === 'ok') {
			addvalue(control_vmodel.smr_unlock_infos,d.data,"sid");
			// 获取满足解锁条件的整流器解锁sid信息，定制加入整流器解锁选项
			for (var i in control_vmodel.smr_unlock_infos) {
				sigtype = calSidToDevType(BigInt(control_vmodel.smr_unlock_infos[i].sid));
				dev_str = sigtype.devType + "_" + sigtype.devSn;
				rslt_data[devin[dev_str]].push(control_vmodel.smr_unlock_infos[i]);
			}
		}
	}

	return rslt_data;
}


function control_value_get() {
	// var paraval = [{"bo_id":selectBoId.toString()}];
	let boid = selectBoId.toString();
	if(selectBoId.toString().includes("-")){
		boid = boid.split("-")[1];
	}
	var paraval = [{"bo_id":boid}];
	var req_get = {data:{objectid:"control",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},success:control_value_get_succ};
	request.clearRequest(req_get);
	request.addRequest([req_get]);
	
	function control_value_get_succ(d,r) {
		if (d.result == 'ok') {
			//control_vmodel.control_infos = d.data;
			control_vmodel.control_infos = classify_signaldata_dev(d.data);
		}
	}
}

control_value_get();

function eeprom_control_get() {
	var req_get = {data:{objectid:"eeprom_control",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:eeprom_control_get_succ};
	request.addRequest([req_get]);

	function eeprom_control_get_succ(d,r) {
		if (d.result === 'ok') {
			let obj1 = {"device name":"EEPROM", "full_name":"", "action":"0"};
            let obj2 = {"device name":"EEPROM", "full_name":"", "action":"1"};

			setTimeout(function () {
				if (d.data[0]["eeprom_to_csu_valid"] === "1") {
					obj1.full_name = mainvalue.i18nkeyword.eeprom.eeprom_to_csu_control;
					control_vmodel.eeprom_infos[0].push(obj1);
				}

				if (d.data[0]["csu_to_eeprom_valid"] === "1") {
					obj2.full_name = mainvalue.i18nkeyword.eeprom.csu_to_eeprom_control;
					control_vmodel.eeprom_infos[0].push(obj2);
				}

				if (control_vmodel.eeprom_infos[0].length === 0) {
					control_vmodel.eeprom_control_flag = 0;
				}
			}, 2000);
		}
	}
}

eeprom_control_get();