﻿<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" >
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="ap_connection">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.ap_connection_config}}
                        </li>
                        <li>

                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span9">
                        <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_successfully}}
                        </div>
                        <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_failure}}
                        </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li class="active">
                                        <a data-toggle="tab" href="#ap_tab">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            {{i18nkeyword.menu.ap_connection_config}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane active" id="ap_tab">
                                        <table class="table table-striped table-bordered table-hover" >
                                            <tbody >
                                                <tr ms-for="(el,al) in APAttrData" ms-if="al.visible !== 'NO'">
                                                <td>{{al.full_name}}</td>
                                                <td ms-if="getconventionlength(al.convention)>0">
                                                    <select  ms-duplex="apcon_value[al.name]">
                                                        <option ms-for="(index,name) in al.convention" ms-attr="{value:index,selected:index==apcon_value[al.name]?true:false}">{{name}}</option>
                                                    </select>
                                                </td>
                                                <td ms-if="getconventionlength(al.convention)== 0 && al.name != 'AP Password'">
                                                    <input class="form-control input-medium pull-left" ms-duplex="apcon_value[al.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                </td>
                                                <td ms-if="getconventionlength(al.convention)== 0 && al.name == 'AP Password'">
                                                    <input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="apcon_value[al.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <button class="button button-small button-flat-primary" onclick="AP_value_set(this)">
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div>
                                </div>
                            </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span9-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/page/js/config_ap.js"></script>
    </body>
</html>
