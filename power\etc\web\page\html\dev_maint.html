<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="system">
            <!--#include virtual="/page/html/prompt.html" -->
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.system_maintain}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                            <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="time_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="zip_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.ziptype_failure}}
                            </div>
                            <div id="tar_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.tartype_failure}}
                            </div>
                            <div id="bin_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.bintype_failure}}
                            </div>
                            <div id="upsys_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.system.uploadsys_file_tip}}
                            </div>
                            <div id="upsysall_get_enable_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.seco_cert.get_seco_cert_enable_err}}
                            </div>
                            <div id="upsysall_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.system.uploadsysall_file_tip}}
                            </div>
                            <div id="upssl_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.system.uploadssl_file_tip}}
                            </div>
                            <div id="time_successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="file_empty_tip" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.system.fileempty}}
                            </div>
                            <div id="south_update_warn_tip" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.system.south_updata_tip}}
                            </div>
                            <div class="progress" id="progressHide" style="display:none">
                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="60"
                                     aria-valuemin="0" aria-valuemax="100" style="width: 0%;" id="progressBar">
                                    <span class="sr-only"></span>
                                </div>
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'sysmaintain' == tab_list?'active':''" ms-click="tabChange('sysmaintain')">
                                        <a data-toggle="tab"   href="#sysmaintain" ms-if="userlevel > '1'">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.system.sys_maintain}}
                                        </a>
                                    </li>
                                    <li ms-class="'slavemaintain' == tab_list?'active':''" ms-click="tabChange('slavemaintain')">
                                        <a data-toggle="tab" href="#slavemaintain" ms-if="userlevel > '1' && (smr_cfg_tag =='1' || pu_cfg_tag == '1' || spcu_cfg_tag == '1' || spu_cfg_tag == '1')">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.system.slave_update}}
                                        </a>
                                    </li>
                                    <li ms-class="'southmaintain' == tab_list?'active':''" ms-click="tabChange('southmaintain')">
                                        <a data-toggle="tab" href="#southmaintain" ms-if="userlevel > '1'&& (fbbms_cfg_tag =='1' || nfbbms_cfg_tag == '1' || ssw_direct_cfg_tag == '1' || acem_cfg_tag =='1' || sddu_cfg_tag =='1' || fctl_cfg_tag =='1') ">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.system.south_update}}
                                        </a>
                                    </li>
                                    <li ms-class="'sboardupdate' == tab_list?'active':''" ms-click="tabChange('sboardupdate')">
                                        <a data-toggle="tab" href="#sboardupdate" ms-if="userlevel > '1'">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.system.daughterboard_update}}
                                        </a>
                                    </li>
                                    <li ms-class="'paraMaintain' == tab_list?'active':''" ms-click="tabChange('paraMaintain')">
                                        <a data-toggle="tab" href="#paraMaintain" ms-if="userlevel > '1'">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.system.para_maintain}}
                                        </a>
                                    </li>
                                    <li ms-class="'spcuparaMaintain' == tab_list?'active':''" ms-click="tabChange('spcuparaMaintain')">
                                        <a data-toggle="tab" href="#spcuparaMaintain" ms-if="userlevel > '1'">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            SPCU {{i18nkeyword.system.para_maintain}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div id="sysmaintain" class="tab-pane" ms-class="'sysmaintain' == tab_list?'active':''">
                                        <form id="sys_up" style="display:none" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadsys();">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.sys_update}}
                                                </legend>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.system.uploadsys_file_tip}">
                                                            <input type="file" id="id-sys-file" name="uploadfile" class="file" data-max-file-size="12288"/> <!--升级文件的最大值定位12M-->
                                                            <input type="hidden" id ="up_type" name="type" value="val_set" />
                                                            <input type="hidden" id ="up_objid" name="objectid" value="update_check" />
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_sys_bt" type="submit" class="button button-small button-flat-primary">
                                                                {{i18nkeyword.system.upload_pack}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </fieldset>
                                        </form>
                                        <form id="sys_all_up" style="display:none" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadsysall();">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.sys_all_update}}
                                                </legend>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.system.uploadsysall_file_tip}">
                                                            <input type="file" id="id-sys-all-file" name="uploadfile" class="file" data-max-file-size="102400"/> <!--升级文件的最大值定位100M-->
                                                            <input type="hidden" id ="up_type" name="type" value="inst_add" />
                                                            <input type="hidden" id ="up_objid" name="objectid" value="update_check" />
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_sys_all_bt" type="submit" class="button button-small button-flat-primary">
                                                                {{i18nkeyword.system.upload_pack}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </fieldset>
                                        </form>
                                        <form id="ssl_up" style="display:none" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadssl();">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.ssl_upgrade}}
                                                </legend>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.system.uploadssl_file_tip}">
                                                            <input type="file" id="id-ssl-file" name="uploadfile" class="file" data-max-file-size="10"/> <!--升级文件的最大值定位10K-->
                                                            <input type="hidden" id ="up_type" name="type" value="val_set" />
                                                            <input type="hidden" id ="up_objid" name="objectid" value="ssl_update" />
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_ssl_bt" type="submit" class="button button-small button-flat-primary">
                                                                {{i18nkeyword.system.upload_pack}}
                                                            </button>
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <input type="button" id="ssl_downfile" onClick="do_ssl_downfile()" ms-attr="{value:i18nkeyword.system.ssl_download}" class="button button-small button-flat-primary">
                                                            </input>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </fieldset>
                                        </form>
                                        <form id="sys_reset" ms-if="userlevel>1" style="display:none" onSubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.sys_reset}}
                                                </legend>
                                                <button id="rebootbt" class="button button-small  button-flat-primary"
                                                style="margin-right:15px; margin-left: 13px;" onClick="devReboot()">
                                                    {{i18nkeyword.system.device_reset}}
                                                </button>
                                                <button id="facResetbt" class="button button-small  button-flat-primary"
                                                style="margin-right:15px" onClick="doFactoryReset()">
                                                    {{i18nkeyword.system.factory_reset}}
                                                </button>
                                                <button id="facResetbt" ms-if="userlevel > '2'" class="button button-small  button-flat-primary"
                                                style="margin-right:15px" onClick="dodefaultReset()">
                                                    {{i18nkeyword.system.default_reset}}
                                                </button>
                                                <button id="facResetbt" class="button button-small  button-flat-primary"
                                                style="margin-right:15px" onClick="uploadsysmul()">
                                                    触发MID_CAND_CON_UPDATE
                                                </button>
                                            </fieldset>
                                        </form>
                                        <form id="export_all" ms-if="userlevel > '2'" onsubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.export_all}}
                                                </legend>
                                                <div id="dl_tip_all" style="display:none" class="alert alert-block alert-success">
                                                    <small>
                                                        {{i18nkeyword.system.handling_export_later}}
                                                    </small>
                                                </div>
                                                <div id="dl_alldata_url" style="display:none" class="alert alert-block alert-success">
                                                </div>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="export_all_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="export_all_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="all_export_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_all_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <div style="display:block;">
                                                    <button id="dl_data" onClick="do_export_all()" style="margin-left: 12px;" class="button button-small  button-flat-primary button-large">
                                                        {{i18nkeyword.system.export_all}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                        <form id="export_eeprom" ms-if="userlevel > '2'" onsubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.export_eeprom}}
                                                </legend>
                                                <div id="dl_tip_eeprom" style="display:none" class="alert alert-block alert-success">
                                                    <small>
                                                        {{i18nkeyword.system.handling_export_later}}
                                                    </small>
                                                </div>
                                                <div id="dl_url_eeprom" style="display:none" class="alert alert-block alert-success">
                                                </div>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="export_eeprom_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="export_eeprom_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="eeprom_export_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_eeprom_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <div style="display:block;">
                                                    <button id="dl_eeprom" onClick="do_export_eeprom()" style="margin-left: 12px;" class="button button-small  button-flat-primary button-large">
                                                        {{i18nkeyword.system.export_eeprom}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                        <form id="del_hisinfo" ms-if="userlevel > '1'" onsubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.del_history_sql}}
                                                </legend>
                                                <select class="input-medium" style="width:auto" ms-duplex="del_historysql" id="historytype">
                                                    <option value="allsql">{{i18nkeyword.system.del_allsql}}</option>
                                                    <option value="history_alarm">{{i18nkeyword.system.del_history_alarm}}</option>
                                                    <option value="history_event">{{i18nkeyword.system.del_history_event}}</option>
                                                    <option value="history_data">{{i18nkeyword.system.del_history_data}}</option>
                                                    <!--ms-for:(i,type) in statistic_record_types-->
                                                        <option ms-attr="{'value':type.id}" ms-if="type.id!='1'">{{type.name}}</option>
                                                    <!--ms-for-end:-->
                                                </select>
                                                &nbsp;&nbsp;
                                                <button id="delhistory" class="button button-small button-flat-primary" onclick="delHisRecord()">
                                                    {{i18nkeyword.delete}}
                                                </button>
                                            </fieldset>
                                        </form>
                                    </div><!--系统维护-->
                                    <div id="slavemaintain" class="tab-pane" ms-class="'slavemaintain' == tab_list?'active':''">
                                        <div id="accordion" class="accordion-style1 panel-group">
                                            <div class="panel panel-default">
                                                <div id = "select_type" ms-css="{display:'inline'}" ms-if="smr_cfg_tag =='1' || pu_cfg_tag == '1' || spcu_cfg_tag == '1' || spu_cfg_tag == '1'">
                                                    <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_type}}</label>
                                                    <select class="form-control" id="slave-device-type-select" style="display:inline"  ms-duplex="slave_device_type" ms-click="change_list_cand_update_state_datas('slave-device-type-select')">
                                                        <option value="1" ms-visible="smr_cfg_tag =='1'">{{i18nkeyword.system.smr}}</option>
                                                        <option value="8" ms-visible="pu_cfg_tag =='1'">PU</option>
                                                        <option value="9" ms-visible="spcu_cfg_tag =='1'">SPCU</option>
                                                        <option value="10" ms-visible="spu_cfg_tag =='1'">SPU</option>
                                                    </select>
                                                </div>
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
                                                            <span class="green smaller lighter" style="font-size:17px">{{i18nkeyword.system.smr_update_control}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseOne">
                                                    <div class="panel-body">
                                                        <form id="slave_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                                        enctype="multipart/form-data" onsubmit="return uploadsmr(this);" >
                                                            <fieldset>
                                                                <table>
                                                                    <tr>
                                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.tartype_failure}">
                                                                            <input type="file" id="id-slave-file" name="uploadfile" class="file" data-max-file-size="10240"/> <!--升级文件的最大值定位10M-->
                                                                            <input type="hidden" id ="up_type" name="type" value="val_set" />
                                                                            <input type="hidden" id ="up_objid" name="objectid" ms-duplex="cand_upload_file_objid" />
                                                                        </td>
                                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                                            <button id="up_slave_bt" type="submit" class="button button-small button-flat-primary">
                                                                                {{i18nkeyword.upload_file}}
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <table style="margin: 0px 0px 5px 5px;">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td style="width:auto;padding-top:0px;vertical-align:top;" title="123">{{i18nkeyword.system.uploaded_files}}:</td>
                                                                            <td ms-if="slave_device_type=='1'" style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.smr_packet_name"></div>
                                                                            <td ms-if="slave_device_type=='8'" style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.pu_packet_name"></div>
                                                                            <td ms-if="slave_device_type=='9'" style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.spcu_packet_name"></div>
                                                                            <td ms-if="slave_device_type=='10'" style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.spu_packet_name"></div>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                                <div id="undate_mode" style = "margin-bottom:10px">
                                                                    <div id = "select_smr_update_count" ms-css="{display:slave_device_type=='1'?'inline':'none'}">
                                                                        <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_mode}}</label>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline"  ms-duplex="smr_update_mode">
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                            <option value="2">{{i18nkeyword.system.multi_smr}}</option>
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                        </select>
                                                                        <div id="smr_no" ms-css="{display:@getsmrnodivdisplay()}">
                                                                            <select id ="sig_no_min" class="form-control"  style="display:inline;width:160px" ms-duplex="smr_update_no_min">
                                                                                <option value="0">{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in smr_update_state_datas" ms-attr="{value:info.smr_no, selected:info.smr_no == smr_update_no_min?true:false}">{{info.smr_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="smr_no_min_max" ms-css="{display:@getsmrnominmaxdivdisplay()}">
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="smr_update_no_min">
                                                                                <option value="0" ms-attr="{selected:'0' == smr_update_no_min?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in smr_update_state_datas" ms-attr="{value:info.smr_no,selected:info.smr_no == smr_update_no_min?true:false}" >{{info.smr_no}}
                                                                                </option>
                                                                            </select>
                                                                            <span>--</span>
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="smr_update_no_max">
                                                                                <option value="0" ms-attr="{selected:'0' == smr_update_no_max?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in smr_update_state_datas" ms-attr="{value:info.smr_no,selected:info.smr_no == smr_update_no_max?true:false}" >{{info.smr_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <div id = "select_update_item" style="display:inline">
                                                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_update_type}}</label>
                                                                            <select class="form-control" id="form-field-select-1" style="display:inline;width:110px" ms-duplex="smr_update_type">
                                                                                <option value="1">{{i18nkeyword.system.smr_front}}</option>
                                                                                <option value="2">{{i18nkeyword.system.smr_back}}</option>
                                                                                <option value="0">{{i18nkeyword.system.smr_front_back}}</option>
                                                                            </select>
                                                                        </div>
                                                                        <div ms-if = "slave_device_type == '1'" style="padding-top:10px">
                                                                            <button id="up_slave_bt_smr_start" type="button" onclick="set_smr_update_cfg(0)" class="button button-small button-flat-primary" style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_start}}
                                                                            </button>
                                                                            <button id="up_slave_bt_smr_stop"  type="button" onclick="set_smr_update_cfg(1)" class="button button-small button-flat-primary"style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_end}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    <div id = "select_pu_update_count" ms-css="{display:slave_device_type=='8'?'inline':'none'}">
                                                                        <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_mode}}</label>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline"  ms-duplex="@pu_update_mode">
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                            <option value="2">{{i18nkeyword.system.multi_smr}}</option>
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                        </select>
                                                                        <div id="pu_no" ms-css="{display:@getpunodivdisplay()}">
                                                                            <select id ="sig_no_min" class="form-control"  style="display:inline;width:160px" ms-duplex="pu_update_no_min">
                                                                                <option value="0">{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in pu_update_state_datas" ms-attr="{value:info.pu_no, selected:info.pu_no == pu_update_no_min?true:false}">{{info.pu_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="pu_no_min_max" ms-css="{display:@getpunominmaxdivdisplay()}">
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="pu_update_no_min">
                                                                                <option value="0" ms-attr="{selected:'0' == pu_update_no_min?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in pu_update_state_datas" ms-attr="{value:info.pu_no,selected:info.pu_no == pu_update_no_min?true:false}" >{{info.pu_no}}
                                                                                </option>
                                                                            </select>
                                                                           <span>--</span>
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="pu_update_no_max">
                                                                                <option value="0" ms-attr="{selected:'0' == pu_update_no_max?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in pu_update_state_datas" ms-attr="{value:info.pu_no,selected:info.pu_no == pu_update_no_max?true:false}" >{{info.pu_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <div id = "select_pu_update_item" style="display:inline">
                                                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_update_type}}</label>
                                                                            <select class="form-control" id="form-field-select-1" style="display:inline;width:110px" ms-duplex="pu_update_type">
                                                                                <option value="2">{{i18nkeyword.system.smr_back}}</option>
                                                                            </select>
                                                                        </div>
                                                                        <div ms-if = "slave_device_type == '8'" style="padding-top:10px">
                                                                            <button id="up_slave_bt_pu_start" type="button" onclick="set_pu_update_cfg(0)" class="button button-small button-flat-primary" style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_start}}
                                                                            </button>
                                                                            <button id="up_slave_bt_pu_stop"  type="button" onclick="set_pu_update_cfg(1)" class="button button-small button-flat-primary"style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_end}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    <div id = "select_spcu_update_count" ms-css="{display:slave_device_type=='9'?'inline':'none'}">
                                                                        <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_mode}}</label>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline"  ms-duplex="@spcu_update_mode">
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                            <option value="2">{{i18nkeyword.system.multi_smr}}</option>
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                        </select>
                                                                        <div id="spcu_no" ms-css="{display:@getspcunodivdisplay()}">
                                                                            <select id ="sig_no_min" class="form-control"  style="display:inline;width:160px" ms-duplex="spcu_update_no_min">
                                                                                <option value="0">{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in spcu_update_state_datas" ms-attr="{value:info.spcu_no, selected:info.spcu_no == spcu_update_no_min?true:false}">{{info.spcu_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="spcu_no_min_max" ms-css="{display:@getspcunominmaxdivdisplay()}">
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="spcu_update_no_min">
                                                                                <option value="0" ms-attr="{selected:'0' == spcu_update_no_min?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in spcu_update_state_datas" ms-attr="{value:info.spcu_no,selected:info.spcu_no == spcu_update_no_min?true:false}" >{{info.spcu_no}}
                                                                                </option>
                                                                            </select>
                                                                            <span>--</span>
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="spcu_update_no_max">
                                                                                <option value="0" ms-attr="{selected:'0' == spcu_update_no_max?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in spcu_update_state_datas" ms-attr="{value:info.spcu_no,selected:info.spcu_no == spcu_update_no_max?true:false}" >{{info.spcu_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <div ms-if = "slave_device_type == '9'" style="padding-top:10px">
                                                                            <button id="up_slave_bt_spcu_start" type="button" onclick="set_spcu_update_cfg(0)" class="button button-small button-flat-primary" style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_start}}
                                                                            </button>
                                                                            <button id="up_slave_bt_spcu_stop"  type="button" onclick="set_spcu_update_cfg(1)" class="button button-small button-flat-primary"style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_end}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    <div id = "select_spu_update_count" ms-css="{display:slave_device_type=='10'?'inline':'none'}">
                                                                        <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_mode}}</label>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline"  ms-duplex="@spu_update_mode">
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                        </select>
                                                                        <div id="spu_no" style="display:inline">
                                                                            <select id ="sig_no_spcu" class="form-control"  style="display:inline;width:160px" ms-duplex="spcu_no_for_spu_update">
                                                                                <option value="0">{{i18nkeyword.select_spcu_device}}</option>
                                                                                <option ms-visible="spu_update_mode=='0'" ms-attr="{value:spcu_update_state_datas.length+1}">{{i18nkeyword.all}}</option>
                                                                                <option  ms-for="(i,info) in spcu_update_state_datas" ms-attr="{value:info.spcu_no,selected:info.spcu_no == spcu_no_for_spu_update?true:false}">{{info.spcu_no}}
                                                                                </option>
                                                                            </select>
                                                                            <select id ="sig_no_min" class="form-control"  style="display:inline;width:160px" ms-duplex="spu_update_no_min" ms-css="{display:@getspunodivdisplay()}">
                                                                                <option value="0">{{i18nkeyword.select_spu_device}}</option>
                                                                                <option ms-if="spcu_no_for_spu_update*1>=1" ms-for="i in spu_nos" ms-attr="{value:spu_nos[i-1], selected:spu_nos[i-1] == spu_update_no_min?true:false}">{{spu_nos[i-1]*1}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <div ms-if = "slave_device_type == '10'" style="padding-top:10px">
                                                                            <button id="up_slave_bt_spu_start" type="button" onclick="set_spu_update_cfg(0)" class="button button-small button-flat-primary" style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_start}}
                                                                            </button>
                                                                            <button id="up_slave_bt_spu_stop"  type="button" onclick="set_spu_update_cfg(1)" class="button button-small button-flat-primary"style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_end}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </fieldset>
                                                        </form>
                                                    </div><!--panel body-->
                                                </div>
                                            </div><!--子页面-->
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">
                                                            <span class="green smaller lighter" style="font-size:17px"> {{i18nkeyword.system.smr_update_state}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseTwo">
                                                    <div class="panel-body" style="padding:10px">
                                                        <div id="smr_update_status" ms-if = "slave_device_type == '1'">
                                                            <div id="smr_list" style="width:600px;float:left;display:inline;padding-right:40px">
                                                                <p>
                                                                    <!--ms-for:(i,info) in smr_update_state_datas-->
                                                                        <span class="badge" ms-class="@getsmrupdateclass(info.update_rst)">{{smr_no_to_string(info.smr_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                            </div>
                                                            <div id="smr_state_label" style="float:left;display:inline">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                            <div class="progress progress-striped active" id="smr_update_progress" style = "width:600px;display:none">
                                                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@smr_update_now_no_process+'%'}" >
                                                                    <span ms-text="smr_update_now_no_process+'%'"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="pu_update_status" ms-if = "slave_device_type == '8'">
                                                            <div id="pu_list" style="width:600px;float:left;display:inline;padding-right:40px">
                                                                <p>
                                                                    <!--ms-for:(i,info) in pu_update_state_datas-->
                                                                        <span class="badge" ms-class="@getpuupdateclass(info.update_rst)">{{@smr_no_to_string(info.pu_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                            </div>
                                                            <div id="pu_state_label" style="float:left;display:inline">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                            <div class="progress progress-striped active" id="pu_update_progress" style = "width:600px;display:none">
                                                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@pu_update_now_no_process+'%'}" >
                                                                    <span ms-text="pu_update_now_no_process+'%'"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="spcu_update_status" ms-if = "slave_device_type == '9'">
                                                            <div id="spcu_list" style="width:600px;float:left;display:inline;padding-right:40px">
                                                                <p>
                                                                    <!--ms-for:(i,info) in spcu_update_state_datas-->
                                                                        <span class="badge" ms-class="@getspcuupdateclass(info.update_rst)">{{@smr_no_to_string(info.spcu_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                            </div>
                                                            <div id="spcu_state_label" style="float:left;display:block">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                            <div class="progress progress-striped active" id="spcu_update_progress" style = "width:600px;display:none">
                                                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@spcu_update_now_no_process+'%'}" >
                                                                    <span ms-text="spcu_update_now_no_process+'%'"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="spu_update_status" ms-if = "slave_device_type == '10'">
                                                            <div id="spu_list" style="width:600px;float:left;display:block">
                                                                <!--ms-for:(index,spu_info_of_one_spcu) in spu_update_state_datas-->
                                                                <div>
                                                                    <span>SPCU {{@smr_no_to_string(index+1)}}</span>
                                                                    <!--ms-for:(i,info) in spu_info_of_one_spcu-->
                                                                    <span class="badge" ms-class="@getspuupdateclass(info)">{{@smr_no_to_string(i+1)}}</span>
                                                                    <!--ms-for-end:-->
                                                                    <div ms-if="spu_info_of_one_spcu[0].status=='1'">
                                                                        <div id="spu_update_progress_phase" style="display:block">
                                                                            {{spu_info_of_one_spcu[0].phase}}
                                                                        </div>
                                                                        <div class="progress progress-striped active" id="spu_update_progress" style = "display: block; width:360px">
                                                                            <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@spu_info_of_one_spcu[0].progress+'%'}" style="display: inline">
                                                                                <span ms-text="spu_info_of_one_spcu[0].progress+'%'"></span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!--ms-for-end:-->
                                                            </div>
                                                            <div id="spu_state_label" style="display:block">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div><!--子页面-->
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseThree">
                                                            <span class="green smaller lighter" style="font-size:17px">{{i18nkeyword.system.state_update_log}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseThree">
                                                    <div class="panel-body">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>{{i18nkeyword.history.generate_time}}</th>
                                                                    <th>{{i18nkeyword.name}}</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id ="cand_update_event_list">
                                                                <tr ms-for="hisvalue in cand_update_event_list">
                                                                    <td>{{hisvalue.save_time}}</td>
                                                                    <td>{{hisvalue.value}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div><!--子页面-->
                                        </div> <!--panale group-->
                                    </div><!--子设备升级页面-->
                                    <div id="southmaintain" class="tab-pane" ms-class="'southmaintain' == tab_list?'active':''">
                                        <div id="accordion" class="accordion-style1 panel-group">
                                            <div class="panel panel-default">
                                                <div id = "select_type" ms-css="{display:'inline'}" ms-if="fbbms_cfg_tag =='1' || nfbbms_cfg_tag == '1' || ssw_direct_cfg_tag == '1' || acem_cfg_tag =='1' || sddu_cfg_tag =='1' || fctl_cfg_tag =='1' || acmu_cfg_tag == '1'">
                                                    <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_type}}</label>
                                                    <select class="form-control" id="south-device-type-select" style="display:inline;width:190px"  ms-duplex="south_device_type" ms-change="change_list_south_update_state_datas('south-device-type-select')">
                                                        <option value="23" ms-visible="fbbms_cfg_tag =='1'">FBBMS</option>
                                                        <option value="33" ms-visible="nfbbms_cfg_tag =='1'">NFBBMS</option>
                                                        <option value="44" ms-visible="ssw_direct_cfg_tag =='1'">{{i18nkeyword.dev_maint.ssw_direct}}</option>
                                                        <option value="-30" ms-visible="acem_cfg_tag =='1'">ACEM</option>
                                                        <option value="34" ms-visible="sddu_cfg_tag =='1'">{{i18nkeyword.dev_maint.sddu}}</option>
                                                        <option value="41" ms-visible="sddu_cfg_tag =='1'">{{i18nkeyword.dev_maint.ssw_sddu}}</option>
                                                        <option value="61" ms-visible="fctl_cfg_tag =='1'">{{i18nkeyword.dev_maint.fctl}}</option>
                                                        <option value="66" ms-visible="acmu_cfg_tag =='1'">ACMU</option>
                                                    </select>
                                                </div>
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFour">
                                                            <span class="green smaller lighter" style="font-size:17px">{{i18nkeyword.system.smr_update_control}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseFour">
                                                    <div class="panel-body">
                                                        <form id="south_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                                        enctype="multipart/form-data" onsubmit="return uploadsouth(this);" >
                                                            <fieldset>
                                                                <table>
                                                                    <tr>
                                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.tartype_failure}">
                                                                            <input type="file" id="id-south-file" name="uploadfile" class="file" data-max-file-size="10240"/> <!--升级文件的最大值定位10M-->
                                                                            <input type="hidden" id ="up_type" name="type" value="val_set" />
                                                                            <input type="hidden" id ="up_paraval" name="paraval" ms-attr="{'value': south_upload_para}"/>
                                                                            <input type="hidden" id ="up_objid" name="objectid" ms-duplex="south_upload_file_objid"/>
                                                                        </td>
                                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                                            <button id="up_south_bt" type="submit" class="button button-small button-flat-primary">
                                                                                {{i18nkeyword.upload_file}}
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <table style="margin: 0px 0px 5px 5px;">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td style="width:auto;padding-top:0px;vertical-align:top;" title="123">{{i18nkeyword.system.uploaded_files}}:</td>
                                                                            <td ms-if="south_device_type=='23'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.fbbms_packet_name"></div>
                                                                            <td ms-if="south_device_type=='33'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.nfbbms_packet_name"></div>
                                                                            <td ms-if="south_device_type=='44'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.ssw_packet_name"></div>
                                                                            <td ms-if="south_device_type=='-30'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.aemb_packet_name"></div>
                                                                            <td ms-if="south_device_type=='34' && sddu_update_model=='1'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.sddu_packet_name"></div>
                                                                            <td ms-if="south_device_type=='34' && sddu_update_model=='2'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.smb_packet_name"></div>
                                                                            <td ms-if="south_device_type=='41'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.ssw_packet_name"></div>
                                                                            <td ms-if="south_device_type=='61'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.fctl_packet_name"></div>
                                                                            <td ms-if="south_device_type=='66'"style="padding-left:20px;white-space: pre-line;" ms-text="subdev_packets_uploaded.acmu_packet_name"></div>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                                <div id="update_mode" style = "margin-bottom:10px">
                                                                    <div id = "select_south_update_count" ms-css="{display:'inline'}">
                                                                        <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_mode}}</label>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline" ms-if="south_device_type != '-30' && south_device_type != '66'" ms-duplex="south_update_mode">
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                            <option value="2">{{i18nkeyword.system.multi_smr}}</option>
                                                                        </select>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline" ms-if="south_device_type == '66'" ms-duplex="south_update_mode">
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                            <option value="1">{{i18nkeyword.system.single_smr}}</option>
                                                                        </select>
                                                                        <select class="form-control" id="form-field-select-1" style="display:inline" ms-if="south_device_type == '-30'">
                                                                            <option value="0">{{i18nkeyword.system.all_smr}}</option>
                                                                        </select>
                                                                        <div id="south_no" ms-css="{display:@getsouthnodivdisplay()}" ms-if="south_device_type!=41">
                                                                            <select id ="sig_no_min" class="form-control"  style="display:inline;width:160px" ms-duplex="south_update_no_min">
                                                                                <option value="0">{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in south_update_state_datas" ms-attr="{value:info.south_no, selected:info.south_no == south_update_no_min?true:false}" ms-if="south_device_type != 44 || info.south_no <= 40 || info.south_no >= 50">
                                                                                    {{info.south_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="south_no_min_max" ms-css="{display:@getsouthnominmaxdivdisplay()}" ms-if="south_device_type!=41">
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="south_update_no_min">
                                                                                <option value="0" ms-attr="{selected:'0' == south_update_no_min?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in south_update_state_datas" ms-attr="{value:info.south_no,selected:info.south_no == south_update_no_min?true:false}" ms-if="south_device_type != 44 || info.south_no <= 40 || info.south_no >= 50">
                                                                                    {{info.south_no}}
                                                                                </option>
                                                                            </select>
                                                                            <span>--</span>
                                                                            <select class="form-control"  style="display:inline;width:120px" ms-duplex="south_update_no_max">
                                                                                <option value="0" ms-attr="{selected:'0' == south_update_no_max?true:false}" >{{i18nkeyword.select_device}}</option>
                                                                                <option  ms-for="(i,info) in south_update_state_datas" ms-attr="{value:info.south_no,selected:info.south_no == south_update_no_max?true:false}" ms-if="south_device_type != 44 || info.south_no <= 40 || info.south_no >= 50">
                                                                                    {{info.south_no}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="sddu_no" style="display:inline" ms-if="south_device_type==41">
                                                                            <select id ="sig_no_sddu" class="form-control"  style="display:inline;width:160px" ms-duplex="sddu_no_for_ssw">
                                                                                <option id="sel-sddu-no-for-ssw" value="0">{{i18nkeyword.select_sddu_device}}</option>
                                                                                <option ms-visible="south_update_mode=='0'" ms-attr="{value:sddu_nos[sddu_nos.length-1]+1}">{{i18nkeyword.all}}</option>
                                                                                <option  ms-for="i in sddu_nos" ms-if="sddu_nos[i-1] !== NaN" ms-attr="{value:sddu_nos[i-1], selected:sddu_nos[i-1] == sddu_no_for_ssw?true:false}">{{sddu_nos[i-1]*1}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        <div id="ssw_no" style="display:inline" ms-if="south_device_type==41&&south_update_mode!=0">
                                                                            <select id ="sig_no_min_ssw" class="form-control"  style="display:inline;width:160px" ms-duplex="ssw_update_no_min">
                                                                                <option value="0">{{i18nkeyword.select_ssw_device}}</option>
                                                                                <option  ms-for="i in ssw_nos" ms-attr="{value:ssw_nos[i-1], selected:ssw_nos[i-1] == ssw_update_no_min?true:false}">{{ssw_nos[i-1]*1}}
                                                                                </option>
                                                                            </select>
                                                                            <span ms-if="south_update_mode==2">--</span>
                                                                            <select id ="sig_no_max_ssw" class="form-control"  style="display:inline;width:160px" ms-duplex="ssw_update_no_max" ms-if="south_update_mode==2">
                                                                                <option value="0">{{i18nkeyword.select_ssw_device}}</option>
                                                                                <option  ms-for="i in ssw_nos" ms-attr="{value:ssw_nos[i-1], selected:ssw_nos[i-1] == ssw_update_no_max?true:false}">{{ssw_nos[i-1]*1}}
                                                                                </option>
                                                                            </select>
                                                                        </div>
                                                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                                        <div id = "select_update_item" style="display:inline" ms-if="south_device_type == '23' || south_device_type == '33'">
                                                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_update_type}}</label>
                                                                            <select class="form-control" id="form-field-select-1" style="display:inline;width:135px" ms-if="south_device_type == '23'" ms-duplex="south_update_type">
                                                                                <option value="1">BMS</option>
                                                                                <option value="2">BDCU</option>
                                                                            </select>
                                                                            <select class="form-control" id="form-field-select-1" style="display:inline;width:135px" ms-if="south_device_type == '33'">
                                                                                <option value="1">BMS</option>
                                                                            </select>
                                                                        </div>
                                                                        <div id = "select_update_item" style="display:inline" ms-if="south_device_type == '34'">
                                                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_update_model}}</label>
                                                                            <select class="form-control" id="sddu-model-select" style="display:inline;width:135px" ms-duplex="sddu_update_model" ms-change="change_sddu_update_model('sddu-model-select')">
                                                                                <option value="1">DCMU</option>
                                                                                <option value="2">SMB</option>
                                                                            </select>
                                                                        </div>
                                                                        <div id = "select_update_item" style="display:inline" ms-if="south_device_type == '66'">
                                                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_update_type}}</label>
                                                                            <select class="form-control" id="form-field-select-1" style="display:inline;width:110px" ms-duplex="south_update_type">
                                                                                <option value="1">{{i18nkeyword.system.program}}</option>
                                                                                <option value="3">{{i18nkeyword.system.font_library}}</option>
                                                                            </select>
                                                                        </div>
                                                                        <div style="padding-top:10px">
                                                                            <button id="up_south_bt_start" type="button" onclick="set_south_update_cfg(0)" class="button button-small button-flat-primary" style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_start}}
                                                                            </button>
                                                                            <button id="up_south_bt_stop"  type="button" onclick="set_south_update_cfg(1)" class="button button-small button-flat-primary"style="margin-right:20px">
                                                                                {{i18nkeyword.system.smr_update_end}}
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </fieldset>
                                                        </form>
                                                    </div><!--panel body-->
                                                </div>
                                            </div><!--子页面-->
                                            <div class="panel panel-default" ms-if="south_device_type != 41">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFive">
                                                            <span class="green smaller lighter" style="font-size:17px"> {{i18nkeyword.system.smr_update_state}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseFive">
                                                    <div class="panel-body" style="padding:10px">
                                                        <div id="south_update_status">
                                                            <div id="south_list1" style="width:600px;float:left;display:inline;padding-right:40px" ms-if="south_device_type != 44">
                                                                <p>
                                                                    <!--ms-for:(i,info) in south_update_state_datas-->
                                                                        <span class="badge" ms-class="@getsouthupdateclass(info.update_rst)">{{south_no_to_string(info.south_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                            </div>
                                                            <div id="south_list2" style="width:600px;float:left;display:inline;padding-right:40px" ms-if="south_device_type == 44">
                                                                <p>
                                                                    <!--ms-for:(i,info) in south_update_state_datas-->
                                                                        <span class="badge" ms-class="@getsouthupdateclass(info.update_rst)" ms-if="info.south_no <= 40">{{south_no_to_string(info.south_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                                <p>
                                                                    <!--ms-for:(i,info) in south_update_state_datas-->
                                                                        <span class="badge" ms-class="@getsouthupdateclass(info.update_rst)" ms-if="info.south_no >= 50">{{south_no_to_string(info.south_no)}}</span>
                                                                    <!--ms-for-end:-->
                                                                </p>
                                                            </div>
                                                            <div id="south_state_label" style="float:left;display:inline">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                            <div class="progress progress-striped active" id="south_update_progress" style = "width:600px;display:none">
                                                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@south_update_now_no_process+'%'}" >
                                                                    <span ms-text="south_update_now_no_process+'%'"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div><!--子页面-->
                                            <div class="panel panel-default" ms-if="south_device_type == 41">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseFive">
                                                            <span class="green smaller lighter" style="font-size:17px"> {{i18nkeyword.system.smr_update_state}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseFive">
                                                    <div class="panel-body" style="padding:10px">
                                                        <div id="south_sub_update_status">
                                                            <div id="south_sub_list" style="width:600px;float:left;display:inline;padding-right:40px">
                                                                <!--ms-for:(index,info) in south_sub_update_state_datas-->
                                                                    <div>
                                                                        <span>{{i18nkeyword.dev_maint.sddu}} {{@smr_no_to_string(index*1 + 1)}}</span>
                                                                        <!--ms-for:(i, ssw_states_one_sddu) in info-->
                                                                            <span class="badge" ms-class="@getsouthupdateclass(ssw_states_one_sddu.update_rst)">{{south_no_to_string(i+1)}}</span>
                                                                        <!--ms-for-end:-->
                                                                    </div>
                                                                    <div class="progress progress-striped active" id="south_update_progress" style = "width:600px;display:block" ms-if="sddu_status_ssw_updating[index].status=='1'">
                                                                        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:sddu_status_ssw_updating[index].progress+'%'}" >
                                                                            <span ms-text="sddu_status_ssw_updating[index].progress+'%'"></span>
                                                                        </div>
                                                                    </div>
                                                                <!--ms-for-end:-->
                                                            </div>
                                                            <div id="south_state_label" style="float:left;display:inline">
                                                                <p>
                                                                    <span class="label">-</span>{{i18nkeyword.system.state_not_start}}<br>
                                                                    <span class="label label-warning">-</span>{{i18nkeyword.system.state_updating}}<br>
                                                                    <span class="label label-success">-</span>{{i18nkeyword.system.state_update_success}}<br>
                                                                    <span class="label label-pink">-</span>{{i18nkeyword.system.state_update_failed}}<br>
                                                                </p>
                                                            </div>
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            </div><!--子页面-->
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <h4 class="panel-title">
                                                        <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion" href="#collapseSix">
                                                            <span class="green smaller lighter" style="font-size:17px">{{i18nkeyword.system.state_update_log}}</span>
                                                        </a>
                                                    </h4>
                                                </div>
                                                <div class="panel-collapse collapse in" id="collapseSix">
                                                    <div class="panel-body">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>{{i18nkeyword.history.generate_time}}</th>
                                                                    <th>{{i18nkeyword.name}}</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id ="south_update_event_list">
                                                                <tr ms-for="hisvalue in south_update_event_list">
                                                                    <td>{{hisvalue.save_time}}</td>
                                                                    <td>{{hisvalue.value}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div><!--子页面-->
                                        </div> <!--panale group-->
                                    </div><!--南向设备升级页面-->

                                    <div id="sboardupdate" class="tab-pane" ms-class="'sboardupdate' == tab_list?'active':''">
                                        <div id = "select_type" ms-css="{display:'inline'}">
                                            <label for="form-field-select-1" style="display:inline">{{i18nkeyword.system.select_type}}</label>
                                            <select class="form-control" id="form-field-select-1" style="display:inline"  ms-duplex="slave_sboard_type">
                                                <option value="1">UIB</option>
                                                <option value="2">IDDB</option>
                                            </select>
                                        </div>
                                        <form id="sboard_up" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadsboard();">           <!--return uploadsys()-->
                                            <fieldset>
                                                <table>
                                                    <tr>
                                                         <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.bintype_failure}">
                                                            <input type="file" id="id-sboard-file" name="uploadfile" class="file" data-max-file-size="10240"/> <!--升级文件的最大值定位10M-->
                                                            <input type="hidden" id ="up_type" name="type" value="attr_get" />
                                                            <input type="hidden" id ="up_objid" name="objectid" ms-duplex="sboard_upload_file_objid" />
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_sboard_bt" type="submit" class="button button-small button-flat-primary">
                                                                {{i18nkeyword.system.upload_pack}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div class="progress progress-striped active" id="uib_update_progress" style = "width:600px;display:none">
                                                    <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@uib_update_now_no_process+'%'}" >
                                                        <span ms-text="uib_update_now_no_process+'%'"></span>
                                                    </div>
                                                </div>
                                                <div class="progress progress-striped active" id="iddb_update_progress" style = "width:600px;display:none">
                                                    <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" ms-css="{width:@iddb_update_now_no_process+'%'}" >
                                                        <span ms-text="iddb_update_now_no_process+'%'"></span>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </form>
                                    </div><!--子板升级-->

                                    <div id="paraMaintain" class="tab-pane" ms-class="'paraMaintain' == tab_list?'active':''">
                                        <form id="para_down" style="display:none" onSubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.para_export}}
                                                </legend>
                                                <div id="dl_tip_para" style="display:none" class="alert alert-block alert-success">
                                                    <small>
                                                        {{i18nkeyword.system.handling_export_later}}
                                                    </small>
                                                </div>
                                                <div id="dl_url" style="display:none" class="alert alert-block alert-success">
                                                </div>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="export_para_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="export_para_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="para_export_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;" onclick="change_password_show('export_para_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <div style="display:block;">
                                                    <button id="dl_para" onClick="doDL_para()" style="margin-left: 12px;" class="button button-small  button-flat-primary button-large">
                                                        {{i18nkeyword.system.download_para}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                        <form id="para_up" style="display:none" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadpara();">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.para_import}}
                                                </legend>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="up_para_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_import}}
                                                        <input id="up_para_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char}"ms-duplex="para_import_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;" onclick="change_password_show('up_para_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.ziptype_failure}">
                                                            <input type="file" id="id-para-file" name="uploadfile" class="file" data-max-file-size="10240" accept=".zip"/>
                                                            <input type="hidden" id ="uppara_type" name="type" value="attr_set" />
                                                            <input type="hidden" id ="uppara_objid" name="objectid" value="paramaintain_v3" />
                                                            <input type="hidden" id ="uppara_paraval" name="paraval" ms-attr="{'value': para_paraval }"/>
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_para_bt" type="submit" class="button button-small button-flat-primary" onclick="change_butonflag()">
                                                                {{i18nkeyword.system.upload_para}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div ms-if="para_err_type == 1">
                                                    <td>
                                                        {{i18nkeyword.system.verify_para}}
                                                    </td>
                                                    <table>
                                                        <tr>
                                                            <table style="padding-top:10px; width:600px" id="sample-table-1" class="table table-striped table-bordered table-hover" >
                                                                <thead>
                                                                    <th>
                                                                        {{i18nkeyword.system.abnormal_data}}
                                                                    </th>
                                                                </thead>
                                                                <tbody>
                                                                    <tr ms-for="(n,al) in para_verify_value" ms-if="n < 5">
                                                                        <td>{{al["para verify"]}}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </tr>
                                                    </table>
                                                    <div ms-if="show_more_info_tip == 1">
                                                        <td>
                                                            {{i18nkeyword.system.please_download_file}}
                                                        </td>
                                                    </div>
                                                    <div>
                                                        <button id="para_verify_bt"  class="button button-small button-flat-primary" onclick="download_abnormal_data()">
                                                            {{i18nkeyword.system.download_abnormal_data}}
                                                        </button>
                                                    </div>
                                                </div>
                                                <div ms-if="para_err_type == 2">
                                                    <td>
                                                        {{i18nkeyword.system.verify_config}}
                                                    </td>
                                                    <table>
                                                        <tr>
                                                            <table id="sample-table-2" class="table table-striped table-bordered table-hover" >
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width: 5%; table-layout: fixed;">{{i18nkeyword.system.num}}</th>
                                                                        <th style="width: 15%; table-layout: fixed;">{{i18nkeyword.system.cfgobj_id}}</th>
                                                                        <th style="width: 15%; table-layout: fixed;">{{i18nkeyword.system.cfginst_id}}</th>
                                                                        <th style="width: 15%; table-layout: fixed;">{{i18nkeyword.system.para_name}}</th>
                                                                        <th style="table-layout: fixed;max-width: 200px;">{{i18nkeyword.system.set_value}}</th>
                                                                        <th style="width: 30%; table-layout: fixed;">{{i18nkeyword.system.Info}}</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr ms-for="(n,al) in para_verify_value" ms-if="n < 5">
                                                                        <td>{{al["Serial Number"]}}</td>
                                                                        <td>{{al["Configuration object ID"]}}</td>
                                                                        <td>{{al["Configuration instance ID"]}}</td>
                                                                        <td>{{al["Para name"]}}</td>
                                                                        <td>{{al["Set value"]}}</td>
                                                                        <td>{{al["Info."]}}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </tr>
                                                    </table>
                                                    <div ms-if="show_more_info_tip == 1">
                                                        <td>
                                                            {{i18nkeyword.system.please_download_file}}
                                                        </td>
                                                    </div>
                                                    <div>
                                                        <button id="para_verify_bt"  class="button button-small button-flat-primary" onclick="download_abnormal_data()">
                                                            {{i18nkeyword.system.download_abnormal_data}}
                                                        </button>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </form>
										<form id="para_bak" ms-if="userlevel > '1'" style="" onsubmit="return false;">
											<fieldset>
												<legend>
													{{i18nkeyword.system.para_bak_recover}}
												</legend>
												&nbsp;&nbsp;
												<button  class="button button-small button-flat-primary" style="margin-right:15px; margin-left: 13px;" onclick="para_bak()">
													{{i18nkeyword.system.para_bak}}
												</button>
												<button  class="button button-small button-flat-primary" style="margin-right:15px" onclick="para_recover(0)">
													{{i18nkeyword.system.para_recover}}
												</button>
												<button  class="button button-small button-flat-primary" style="margin-right:15px" onclick="para_recover(1)">
													{{i18nkeyword.system.recover_default_para}}
												</button>
												<br/>
												<br/>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="back_para_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="back_para_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="para_back_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;" onclick="change_password_show('back_para_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <small>
												    <a id="href_bak_file_export"  onclick="para_bak_file_export(this)" style="cursor:pointer; margin-left: 13px;" >
                                                        {{i18nkeyword.system.now_bak_file+": "}}<span id="bak_para_name_val">{{bak_para_name}}</span>
                                                    </a>
                                                </small>
											</fieldset>
										</form> 
                                    </div><!--参数维护-->
                                    <div id="spcuparaMaintain" class="tab-pane" ms-class="'spcuparaMaintain' == tab_list?'active':''">
                                        <form id="spcupara_down" onSubmit="return false;">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.para_export}}
                                                </legend>
                                                <div id="dl_tip_spcupara" style="display:none" class="alert alert-block alert-success">
                                                    <small>
                                                        {{i18nkeyword.system.handling_export_later}}
                                                    </small>
                                                </div>
                                                <div id="dl_url_spcupara" style="display:none" class="alert alert-block alert-success">
                                                </div>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="export_spcupara_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="export_spcupara_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="spcupara_export_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;" onclick="change_password_show('export_spcupara_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <div style="display:block;">
                                                    <button id="dl_spcupara" onClick="doDL_spcupara()" style="margin-left: 12px;" class="button button-small  button-flat-primary button-large">
                                                        {{i18nkeyword.system.download_para}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                        <form id="spcupara_up" action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadspcupara();">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.system.para_import}}
                                                </legend>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="up_spcupara_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_import}}
                                                        <input id="up_spcupara_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char}"ms-duplex="spcupara_import_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;" onclick="change_password_show('up_spcupara_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.ziptype_failure}">
                                                            <input type="file" id="id-spcupara-file" name="uploadfile" class="file" data-max-file-size="10240" accept=".zip"/>
                                                            <input type="hidden" id ="upspcupara_type" name="type" value="val_set" />
                                                            <input type="hidden" id ="upspcupara_objid" name="objectid" value="paramaintain_spcu" />
                                                            <input type="hidden" id ="upspcupara_paraval" name="paraval" ms-attr="{'value': spcupara_paraval }"/>
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_spcupara_bt" type="submit" class="button button-small button-flat-primary" onclick="change_butonflag_spcu()">
                                                                {{i18nkeyword.system.upload_para}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div ms-if="spcupara_verify_value.length != 0">
                                                    <td>
                                                        {{i18nkeyword.system.verify_para}}
                                                    </td>
                                                    <table>
                                                        <tr>
                                                            <table style="padding-top:10px; width:600px" id="sample-table-1" class="table table-striped table-bordered table-hover" >
                                                                <thead>
                                                                    <th>
                                                                        {{i18nkeyword.system.abnormal_data}}
                                                                    </th>
                                                                </thead>
                                                                <tbody>
                                                                    <tr ms-for="(n,al) in spcupara_verify_value">
                                                                        <td>{{al["para verify"]}}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </tr>
                                                    </table>
                                                    <button id="para_verify_bt"  class="button button-small button-flat-primary" onclick="download_abnormal_data_spcu()">
                                                        {{i18nkeyword.system.download_abnormal_data}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                    </div><!--SPCU参数维护-->
                                </div><!--/tabbable-->
                                <!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/js/private/opensrc_i18n.js"></script>
        <script src="/js/opensrc/jquery.form.min.js"></script>
        <script src="/js/opensrc/fileinput.js"></script>
        <script src="/page/js/dev_maint.js"></script>
         <!--[if !IE]> -->
            <script type="text/javascript">
                var empty =  get_opensrc_i18n_word("choose_file_tip");
                var choose = get_opensrc_i18n_word("choose_file");
                var change = get_opensrc_i18n_word("choose_again");
                $(function() {
                    $('#id-sys-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-sys-all-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-ssl-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-sboard-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-slave-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false,
                        previewWidth: 200,
                    });
                    $('#id-south-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false,
                        previewWidth: 200,
                    });
                    $('#id-para-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-spcupara-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                    $('#id-config-file').ace_file_input({
                        no_file: empty + "...",
                        btn_choose: choose,
                        btn_change: change,
                        droppable: false,
                        onchange: null,
                        thumbnail: false
                    });
                });
            </script>
            <!-- <![endif]-->
    </body>

</html>