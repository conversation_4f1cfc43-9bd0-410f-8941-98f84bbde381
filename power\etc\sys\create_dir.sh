#!/bin/sh

#add by hjd 
#create the main directory of "/mnt/data", this directory are forbided to change
mkdir -p /mnt/data/database /mnt/data/work /mnt/data/para_and_config /mnt/data/app_backup /mnt/data/tmp
mkdir -p /mnt/data/para_and_config/para /mnt/data/para_and_config/config /mnt/data/para_and_config/back
#create the main directory of "/mnt/memdisk" and "/mnt/backup"
mkdir -p /mnt/memdisk/config /mnt/memdisk/updating /mnt/memdisk/EEPROM/import /mnt/memdisk/EEPROM/export
mkdir -p /mnt/backup/para /mnt/backup/config /mnt/backup/data /mnt/backup/auth
#create the main directory of "/mnt/data/tmp"
mkdir /mnt/data/tmp /mnt/data/tmp/PIC  /mnt/data/ftp


####  you can add yourself directory on "/mnt/data/work"  ####

#web
mkdir -p /mnt/data/work/web /mnt/data/work/web/backup /mnt/data/work/web/download /mnt/data/work/web/download/web_export

#auth_management
mkdir -p /mnt/data/work/auth_management
#move auth file from /root/data to /mnt/data/work/auth_management
cp /root/power/data/defaultuser.bin  /mnt/data/work/auth_management/
cp /root/power/data/secauthuser.bin  /mnt/data/work/auth_management/

#sysmonitor
mkdir -p /mnt/data/work/sysmonitor 

#smrmgr
mkdir -p /mnt/data/work/smrmgr 

#productapp
mkdir -p /mnt/data/work/productapp 

#radius
mkdir -p /mnt/data/work/radius  /mnt/data/work/radius/ssl

#sub dev diagnosis data
mkdir -p /mnt/data/subdev_diagnosis_data
chmod 757 /mnt/data/subdev_diagnosis_data

#add user mode for non-root area
chmod -R g+r+w /mnt/data  /mnt/backup

chmod -R 640 /tmp
chmod 640 /mnt/data/database/*.sqlite

#Modify RSA cipher permissions
chmod 600 /root/power/etc/lighttpd/ssl/*.pem

# create sftp upload directory
if [ ! -d "/mnt/data/sftp" ]; then
    mkdir /mnt/data/sftp
    mkdir /mnt/data/sftp/upload
    mkdir /mnt/data/sftp/download
fi

chmod 755 /mnt /mnt/data /mnt/data/sftp
chmod 757 /mnt/data/sftp/upload
chmod 666 /mnt/data/sftp/download
chmod 600 /etc/shadow