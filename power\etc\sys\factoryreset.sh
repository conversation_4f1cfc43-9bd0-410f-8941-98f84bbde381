#!/bin/sh
#
# This script will be executed *after* all the other init scripts.
# You can put your own initialization stuff in here if you don't
# want to do the full Sys V style init stuff.
# xsx: delete system work para and data

echo "factory reset"

cd /mnt/data/work/
mv ./productapp/smrcodeauth*.bin /mnt/backup/
rm -rf *
mkdir productapp
mv /mnt/backup/smrcodeauth*.bin ./productapp/

cd /mnt/data/database
rm -rf *

cd  /mnt/data/para_and_config/back
rm -rf *

# 20190429, add by da<PERSON><PERSON><PERSON><PERSON>, don't delete dict_para.sqlite and config.xml
cd  /mnt/data/para_and_config/para
ls | grep -v dict_para.sqlite | xargs rm -rf

rm -rf  /mnt/backup/para_and_config


echo "factory reset ok!"
