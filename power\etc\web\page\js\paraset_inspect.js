BusinessObject.GetParasetObject();
var oldMap = new Map();
var  paraset_vmodel = avalon.define({
	$id:'paraset_inspect',
	init_finished:0,
    inspect_paras:[],
	sidArr:[],//所以校验参数sid列表
	para_head_dic:{},           //存储属性的属性
	dev_changesiddata : [],     // 存储变化的数据
	dev_set_data:[],     //  存储设置的参数
	dev_setfailure_data:[],     //  存储设置失败的数据
	get_paraset_value:function(sidData){
		if (sidData.Value == "val_invalid") {
			return mainvalue.i18nkeyword.value_invalid;
		} else {
			return sidData.Value+sidData.unit;
		}
	},
	changeDevData: function(sidvalue,changekey){
		var changeData = [];
		let changeobject = oldMap.get(sidvalue);
		changeobject[changekey] = event.target.value;
		oldMap.set(sidvalue,changeobject)
		changeData.push(changeobject);
		addvalue(this.dev_changesiddata,changeData,"sid");
	},
	jugge_setdata_failure:function(sid, attr_name){
		for (var i in paraset_vmodel.dev_setfailure_data) {
			if (sid == paraset_vmodel.dev_setfailure_data[i].sid) {
				if (typeof(paraset_vmodel.dev_setfailure_data[i][attr_name]) == "undefined") {
					return false;
				}
				if (paraset_vmodel.dev_setfailure_data[i][attr_name] == "failure") {
					return true;
				}
			}
		}
		return false;
	},
	setDevData:function(){
		var setData = [];
		var num = [];
		paraset_vmodel.dev_setfailure_data.clear();
		for(var i in paraset_vmodel.dev_changesiddata){
			var sidData = calSidToDevType(BigInt(paraset_vmodel.dev_changesiddata[i].sid));
			setData.push(paraset_vmodel.dev_changesiddata[i]);
			num.push(i);
		}
		if(num.length > 0){
			for(var j in num){
				if((j===0)||(num[j]===0)){
					paraset_vmodel.dev_changesiddata.splice(num[j],1);
				}
				else{
					paraset_vmodel.dev_changesiddata.splice(num[j]-1,1);
				}
			}
		} else{
			return;
		}
		paraset_vmodel.dev_set_data = setData;
		patch_para_change(setData);
	}
});


function patch_para_change(paraval){
	var set_tag = true;
	var req_set = {data:{objectid:"para_inspect",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
	request.addRequest([req_set]);

	paraset_vmodel.dev_changesiddata.clear();

	function dev_set_succ(d,r){
		setTimeout(function(){
			//paraset_vmodel.parameters.clear();
			paraset_get_signale_info();
		}, 3000);     //  3秒以后再进行系统刷新
		if (d.result == "ok") {
			set_tag = true;
			mainvalue.controlsuccess = "success";
			paraset_vmodel.dev_set_data = [];
		} else {
			mainvalue.controlsuccess = "failure";
			set_tag = false;
			var update_req =  {data:{objectid:"para_inspect",type:"val_get",paraval:JSON.stringify([])},success:update_signal_para};
			request.addRequest([update_req]);
		}
	}
	function update_signal_para(d,r) {
		addvalue(paraset_vmodel.inspect_paras,d.data,"sid");
		if (set_tag == false) {  //  没有设置成功
			get_failure_data(d.data);
			paraset_vmodel.dev_set_data = [];
			classsify_paraset(d.data);
		}
	}
	function get_failure_data(back_data) {
		var datas = [];
		for (var i in paraset_vmodel.dev_set_data) {
			if (paraset_vmodel.dev_set_data[i].sid == back_data[i].sid) {
				var para = {};
				para['sid'] = paraset_vmodel.dev_set_data[i].SID;
				for (var key in paraset_vmodel.dev_set_data[i]) {
					if (checkNumber(paraset_vmodel.dev_set_data[i][key])) {
						if (Math.abs(paraset_vmodel.dev_set_data[i][key] - back_data[i][key]) > 1e-6) {
							para[key] = "failure";
						}
					} else if (paraset_vmodel.dev_set_data[i][key] != back_data[i][key]) {  // 如果不相同则说明设置失败
						para[key] = "failure";
					}
				}
				datas.push(para);
			}
		}
		paraset_vmodel.dev_setfailure_data = datas;
	}
}

function paraset_get_signale_info() {
	var req_head = {data:{objectid:"para_inspect",type:"val_get",paraval:JSON.stringify()},success:para_head_succ};
	request.addRequest([req_head]);

	function para_head_succ(d,r){
		paraset_vmodel.inspect_paras.clear();
		for (var i in d.data) {
			if (d.data[i].sid != ""){
				paraset_vmodel.inspect_paras.push(d.data[i]);
			}
		}
	}
}

parameters_init_get();
function parameters_init_get(obj) {
	var req_get =  {data:{objectid:"para_inspect",type:"val_get",paraval:JSON.stringify([])},success:parameters_init_get_succ};
	request.clearRequest(req_get);
	request.addRequest([req_get]);

	function parameters_init_get_succ(d,r) {
		if (d.result != 'ok') {
			return;
		}
		paraset_vmodel.sidArr = [];
		oldMap.clear();

		var inspect_paras_array = [];
		for (var i in d.data) {
			if (d.data[i].sid != "") {
				if (inspect_paras_array.length == 0) {
					inspect_paras_array.push(d.data[i]["Device Name"]);
				} else {
					var flag = 0;
					for (var loopvar = 0; loopvar < inspect_paras_array.length; loopvar++) {
						if (inspect_paras_array[loopvar] == d.data[i]["Device Name"]) {
							flag = 1;
							break;
						}
					}
					if (flag == 0) {
						inspect_paras_array.push(d.data[i]["Device Name"]);
					}
				}
			}
		}

		for (var loopvar = 0; loopvar < inspect_paras_array.length; loopvar++) {
			for(var i in d.data) {
				if (d.data[i].sid != "") {
					if (inspect_paras_array[loopvar] == d.data[i]["Device Name"]) {
						paraset_vmodel.sidArr.push({sid:""+ d.data[i].sid});
					    paraset_vmodel.inspect_paras.push(d.data[i]);
					    oldMap.set(d.data[i].sid, d.data[i]);
					}
				}
			}
		}
	}
}
