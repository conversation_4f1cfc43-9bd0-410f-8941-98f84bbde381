<?xml version="1.0" encoding="UTF-8"?><!-- dev_type区分信号的设备类型，主要用于识别信号的个数，分为以下几类
	1.系统，1个，系统中只存在1个的信号
	2.整流器，最多48个
	3.电池，最多6个
	
	设备数量：
	开关电源：1 
	机房：1 
	铁锂电池：2 
	FSU：1

	设备类型：
	开关电源：6 
	机房：18 
	铁锂电池：32 
	FSU：38
	非智能门禁：99

	信号类型：2-遥信；3-遥测；4-遥控；5-遥调

	存在客户的一个id对应两个或多个zte_sid的情况，如果对应id小于4个，直接在对应信号后面按顺序增加zte_sid2，zte_sid3,zte_sid4;
		如果对应四个以上或者需要进行业务逻辑处理，可以在后面增加:
		custom_sig_name_get 获取监控点数据
		custom_sig_name_set 设置监控点数据
 --><signal_list>
	<alarm>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001001" sig_type="2" alarm_level="2" zte_sid="0xB001030030001"/>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001002" sig_type="2" alarm_level="2" zte_sid="0xB002030030001"/>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001003" sig_type="2" alarm_level="2" zte_sid="0xB003030030001"/>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001004" sig_type="2" alarm_level="2" zte_sid="0xB004030030001"/>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001005" sig_type="2" alarm_level="2" zte_sid="0xB005030030001"/>
		<signal cm_name="电池XX熔丝故障告警" dev_type="6" cm_sid="0406001006" sig_type="2" alarm_level="2" zte_sid="0xB006030030001"/>

		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003001" sig_type="2" alarm_level="3" zte_sid="0xB001030010001"/>
		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003002" sig_type="2" alarm_level="3" zte_sid="0xB002030010001"/>
		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003003" sig_type="2" alarm_level="3" zte_sid="0xB003030010001"/>
		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003004" sig_type="2" alarm_level="3" zte_sid="0xB004030010001"/>
		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003005" sig_type="2" alarm_level="3" zte_sid="0xB005030010001"/>
		<signal cm_name="电池XX温度过高告警" dev_type="6" cm_sid="0406003006" sig_type="2" alarm_level="3" zte_sid="0xB006030010001"/>

		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005001" sig_type="2" alarm_level="2" zte_sid="0xB001030070001"/>
		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005002" sig_type="2" alarm_level="2" zte_sid="0xB002030070001"/>
		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005003" sig_type="2" alarm_level="2" zte_sid="0xB003030070001"/>
		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005004" sig_type="2" alarm_level="2" zte_sid="0xB004030070001"/>
		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005005" sig_type="2" alarm_level="2" zte_sid="0xB005030070001"/>
		<signal cm_name="电池供电告警" dev_type="6" cm_sid="0406005006" sig_type="2" alarm_level="2" zte_sid="0xB006030070001"/>

		<signal cm_name="直流输出电压过低告警" dev_type="6" cm_sid="0406008001" sig_type="2" alarm_level="2" zte_sid="0xa001030050001" zte_sid2="0xa0010300f0001"/>
		<signal cm_name="直流输出电压过高告警" dev_type="6" cm_sid="0406009001" sig_type="2" alarm_level="2" zte_sid="0xa001030040001" zte_sid2="0xa0010300e0001"/>
		<signal cm_name="监控模块故障告警" dev_type="6" cm_sid="0406012001" sig_type="2" alarm_level="3" zte_sid="0x1001030090001"/>

		<signal cm_name="交流输入XX电压过高告警" dev_type="6" cm_sid="0406014001" sig_type="2" alarm_level="4" zte_sid="0x3001030040001" zte_sid2="0x3001030080001"/>
		<signal cm_name="交流输入XX电压过高告警" dev_type="6" cm_sid="0406014002" sig_type="2" alarm_level="4" zte_sid="0x3001030040002" zte_sid2="0x3001030080002"/>
		<signal cm_name="交流输入XX电压过高告警" dev_type="6" cm_sid="0406014003" sig_type="2" alarm_level="4" zte_sid="0x3001030040003" zte_sid2="0x3001030080003"/>

		<signal cm_name="交流输入XX电压过低告警" dev_type="6" cm_sid="0406015001" sig_type="2" alarm_level="4" zte_sid="0x3001030030001" zte_sid2="0x3001030070001"/>
		<signal cm_name="交流输入XX电压过低告警" dev_type="6" cm_sid="0406015002" sig_type="2" alarm_level="4" zte_sid="0x3001030030002" zte_sid2="0x3001030070002"/>
		<signal cm_name="交流输入XX电压过低告警" dev_type="6" cm_sid="0406015003" sig_type="2" alarm_level="4" zte_sid="0x3001030030003" zte_sid2="0x3001030070003"/>

		<signal cm_name="交流输入XX停电告警" dev_type="6" cm_sid="0406016001" sig_type="2" alarm_level="2" zte_sid="0x3001030010001"/>

		<signal cm_name="交流输入XX缺相告警" dev_type="6" cm_sid="0406017001" sig_type="2" alarm_level="3" zte_sid="0x3001030020001"/>
		<signal cm_name="交流输入XX缺相告警" dev_type="6" cm_sid="0406017002" sig_type="2" alarm_level="3" zte_sid="0x3001030020002"/>
		<signal cm_name="交流输入XX缺相告警" dev_type="6" cm_sid="0406017003" sig_type="2" alarm_level="3" zte_sid="0x3001030020003"/>

		<signal cm_name="交流输入XX频率过高告警" dev_type="6" cm_sid="0406018001" sig_type="2" alarm_level="4" zte_sid="0x30010300c0001"/>
		<signal cm_name="交流输入XX频率过低告警" dev_type="6" cm_sid="0406019001" sig_type="2" alarm_level="4" zte_sid="0x30010300b0001"/>
		<signal cm_name="防雷器故障告警" dev_type="6" cm_sid="0406022001" sig_type="2" alarm_level="4" zte_sid="0x4001030020001"/>

		<signal cm_name="整流模块XX故障告警" dev_type="6" cm_sid="0406024001" sig_type="2" alarm_level="3" zte_sid="0x7001030010001" zte_sid2="0x7001030270001" zte_sid3="0x7001030460001"/>
		<signal cm_name="整流模块XX故障告警" dev_type="6" cm_sid="0406024002" sig_type="2" alarm_level="3" zte_sid="0x7002030010001" zte_sid2="0x7002030270001" zte_sid3="0x7002030460001"/>

		<signal cm_name="整流模块XX风扇告警" dev_type="6" cm_sid="0406025001" sig_type="2" alarm_level="3" zte_sid="0x7001030020001"/>
		<signal cm_name="整流模块XX风扇告警" dev_type="6" cm_sid="0406025002" sig_type="2" alarm_level="3" zte_sid="0x7002030020001"/>

		<signal cm_name="整流模块XX过压关机告警" dev_type="6" cm_sid="0406026001" sig_type="2" alarm_level="3" zte_sid="0x7001030040001" zte_sid2="0x7001030060001"/>
		<signal cm_name="整流模块XX过压关机告警" dev_type="6" cm_sid="0406026002" sig_type="2" alarm_level="3" zte_sid="0x7002030040001" zte_sid2="0x7002030060001"/>

		<signal cm_name="整流模块XX温度过高告警" dev_type="6" cm_sid="0406027001" sig_type="2" alarm_level="4" zte_sid="0x7001030090001" zte_sid2="0x70010301b0001"/>
		<signal cm_name="整流模块XX温度过高告警" dev_type="6" cm_sid="0406027002" sig_type="2" alarm_level="4" zte_sid="0x7002030090001" zte_sid2="0x70020301b0001"/>

		<signal cm_name="整流模块XX通信状态告警" dev_type="6" cm_sid="0406028001" sig_type="2" alarm_level="3" zte_sid="0x70010301d0001"/>
		<signal cm_name="整流模块XX通信状态告警" dev_type="6" cm_sid="0406028002" sig_type="2" alarm_level="3" zte_sid="0x70020301d0001"/>

		<signal cm_name="二级低压脱离告警" dev_type="6" cm_sid="0406030004" sig_type="2" alarm_level="1" zte_sid="0xc001030020001" zte_sid2="0xc001030030001"/>

		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037001" sig_type="2" alarm_level="2" zte_sid="0xb001030050001"/>
		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037002" sig_type="2" alarm_level="2" zte_sid="0xb002030050001"/>
		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037003" sig_type="2" alarm_level="2" zte_sid="0xb003030050001"/>
		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037004" sig_type="2" alarm_level="2" zte_sid="0xb004030050001"/>
		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037005" sig_type="2" alarm_level="2" zte_sid="0xb005030050001"/>
		<signal cm_name="负载低压告警" dev_type="6" cm_sid="0406037006" sig_type="2" alarm_level="2" zte_sid="0xb006030050001"/>
		<signal cm_name="水浸告警" dev_type="18" cm_sid="0418001001" sig_type="2" alarm_level="2" zte_sid="0x9001030020001"/>
		<signal cm_name="烟雾告警" dev_type="18" cm_sid="0418002001" sig_type="2" alarm_level="1" zte_sid="0x9001030010001"/>
		<signal cm_name="温度过高告警" dev_type="18" cm_sid="0418004001" sig_type="2" alarm_level="3" zte_sid="0x9001030060001"/>
		<signal cm_name="温度超高告警" dev_type="18" cm_sid="0418005001" sig_type="2" alarm_level="2" zte_sid="0x90010300b0001"/>
		<signal cm_name="温度过低告警" dev_type="18" cm_sid="0418006001" sig_type="2" alarm_level="4" zte_sid="0x9001030070001" zte_sid2="0x90010300c0001"/>
		<signal cm_name="湿度过高告警" dev_type="18" cm_sid="0418007001" sig_type="2" alarm_level="4" zte_sid="0x9001030090001"/>
		<signal cm_name="湿度过低告警" dev_type="18" cm_sid="0418008001" sig_type="2" alarm_level="4" zte_sid="0x90010300a0001"/>

		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009001" sig_type="2" alarm_level="1" zte_sid="0x21001030110001"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009002" sig_type="2" alarm_level="1" zte_sid="0x21001030110002"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009003" sig_type="2" alarm_level="1" zte_sid="0x21001030110003"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009004" sig_type="2" alarm_level="1" zte_sid="0x21001030110004"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009005" sig_type="2" alarm_level="1" zte_sid="0x21002030110001"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009006" sig_type="2" alarm_level="1" zte_sid="0x21002030110002"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009007" sig_type="2" alarm_level="1" zte_sid="0x21002030110003"/>
		<signal cm_name="电芯充电高温告警" dev_type="47" cm_sid="0447009008" sig_type="2" alarm_level="1" zte_sid="0x21002030110004"/>

		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010001" sig_type="2" alarm_level="3" zte_sid="0x21001030150001"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010002" sig_type="2" alarm_level="3" zte_sid="0x21001030150002"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010003" sig_type="2" alarm_level="3" zte_sid="0x21001030150003"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010004" sig_type="2" alarm_level="3" zte_sid="0x21001030150004"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010005" sig_type="2" alarm_level="3" zte_sid="0x21002030150001"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010006" sig_type="2" alarm_level="3" zte_sid="0x21002030150002"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010007" sig_type="2" alarm_level="3" zte_sid="0x21002030150003"/>
		<signal cm_name="电芯充电低温告警" dev_type="47" cm_sid="0447010008" sig_type="2" alarm_level="3" zte_sid="0x21002030150004"/>

		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011001" sig_type="2" alarm_level="1" zte_sid="0x21001030130001"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011002" sig_type="2" alarm_level="1" zte_sid="0x21001030130002"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011003" sig_type="2" alarm_level="1" zte_sid="0x21001030130003"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011004" sig_type="2" alarm_level="1" zte_sid="0x21001030130004"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011005" sig_type="2" alarm_level="1" zte_sid="0x21002030130001"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011006" sig_type="2" alarm_level="1" zte_sid="0x21002030130002"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011007" sig_type="2" alarm_level="1" zte_sid="0x21002030130003"/>
		<signal cm_name="电芯放电高温告警" dev_type="47" cm_sid="0447011008" sig_type="2" alarm_level="1" zte_sid="0x21002030130004"/>

		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012001" sig_type="2" alarm_level="3" zte_sid="0x21001030170001"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012002" sig_type="2" alarm_level="3" zte_sid="0x21001030170002"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012003" sig_type="2" alarm_level="3" zte_sid="0x21001030170003"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012004" sig_type="2" alarm_level="3" zte_sid="0x21001030170004"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012005" sig_type="2" alarm_level="3" zte_sid="0x21002030170001"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012006" sig_type="2" alarm_level="3" zte_sid="0x21002030170002"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012007" sig_type="2" alarm_level="3" zte_sid="0x21002030170003"/>
		<signal cm_name="电芯放电低温告警" dev_type="47" cm_sid="0447012008" sig_type="2" alarm_level="3" zte_sid="0x21002030170004"/>

		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013001" sig_type="2" alarm_level="1" zte_sid="0x21001030120001"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013002" sig_type="2" alarm_level="1" zte_sid="0x21001030120002"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013003" sig_type="2" alarm_level="1" zte_sid="0x21001030120003"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013004" sig_type="2" alarm_level="1" zte_sid="0x21001030120004"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013005" sig_type="2" alarm_level="1" zte_sid="0x21002030120001"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013006" sig_type="2" alarm_level="1" zte_sid="0x21002030120002"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013007" sig_type="2" alarm_level="1" zte_sid="0x21002030120003"/>
		<signal cm_name="电芯充电高温保护" dev_type="47" cm_sid="0447013008" sig_type="2" alarm_level="1" zte_sid="0x21002030120004"/>

		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014001" sig_type="2" alarm_level="3" zte_sid="0x21001030160001"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014002" sig_type="2" alarm_level="3" zte_sid="0x21001030160002"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014003" sig_type="2" alarm_level="3" zte_sid="0x21001030160003"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014004" sig_type="2" alarm_level="3" zte_sid="0x21001030160004"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014005" sig_type="2" alarm_level="3" zte_sid="0x21002030160001"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014006" sig_type="2" alarm_level="3" zte_sid="0x21002030160002"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014007" sig_type="2" alarm_level="3" zte_sid="0x21002030160003"/>
		<signal cm_name="电芯充电低温保护" dev_type="47" cm_sid="0447014008" sig_type="2" alarm_level="3" zte_sid="0x21002030160004"/>

		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017001" sig_type="2" alarm_level="1" zte_sid="0x21001030140001"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017002" sig_type="2" alarm_level="1" zte_sid="0x21001030140002"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017003" sig_type="2" alarm_level="1" zte_sid="0x21001030140003"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017004" sig_type="2" alarm_level="1" zte_sid="0x21001030140004"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017005" sig_type="2" alarm_level="1" zte_sid="0x21002030140001"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017006" sig_type="2" alarm_level="1" zte_sid="0x21002030140002"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017007" sig_type="2" alarm_level="1" zte_sid="0x21002030140003"/>
		<signal cm_name="电芯放电高温保护" dev_type="47" cm_sid="0447017008" sig_type="2" alarm_level="1" zte_sid="0x21002030140004"/>

		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018001" sig_type="2" alarm_level="3" zte_sid="0x21001030180001"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018002" sig_type="2" alarm_level="3" zte_sid="0x21001030180002"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018003" sig_type="2" alarm_level="3" zte_sid="0x21001030180003"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018004" sig_type="2" alarm_level="3" zte_sid="0x21001030180004"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018005" sig_type="2" alarm_level="3" zte_sid="0x21002030180001"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018006" sig_type="2" alarm_level="3" zte_sid="0x21002030180002"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018007" sig_type="2" alarm_level="3" zte_sid="0x21002030180003"/>
		<signal cm_name="电芯放电低温保护" dev_type="47" cm_sid="0447018008" sig_type="2" alarm_level="3" zte_sid="0x21002030180004"/>

		<signal cm_name="环境低温告警" dev_type="47" cm_sid="0447019001" sig_type="2" alarm_level="3" zte_sid="0x210010300c0001"/>
		<signal cm_name="环境低温告警" dev_type="47" cm_sid="0447019002" sig_type="2" alarm_level="3" zte_sid="0x210020300c0001"/>

		<signal cm_name="环境高温告警" dev_type="47" cm_sid="0447020001" sig_type="2" alarm_level="3" zte_sid="0x210010300b0001"/>
		<signal cm_name="环境高温告警" dev_type="47" cm_sid="0447020002" sig_type="2" alarm_level="3" zte_sid="0x210020300b0001"/>

		<signal cm_name="功率高温告警" dev_type="47" cm_sid="0447023001" sig_type="2" alarm_level="3" zte_sid="0x21001030050001"/>
		<signal cm_name="功率高温告警" dev_type="47" cm_sid="0447023002" sig_type="2" alarm_level="3" zte_sid="0x21002030050001"/>

		<signal cm_name="功率高温保护" dev_type="47" cm_sid="0447024001" sig_type="2" alarm_level="2" zte_sid="0x21001030060001"/>
		<signal cm_name="功率高温保护" dev_type="47" cm_sid="0447024002" sig_type="2" alarm_level="2" zte_sid="0x21002030060001"/>

		<signal cm_name="充电过流告警" dev_type="47" cm_sid="0447025001" sig_type="2" alarm_level="3" zte_sid="0x21001030070001"/>
		<signal cm_name="充电过流告警" dev_type="47" cm_sid="0447025002" sig_type="2" alarm_level="3" zte_sid="0x21002030070001"/>

		<signal cm_name="充电过流保护" dev_type="47" cm_sid="0447026001" sig_type="2" alarm_level="2" zte_sid="0x21001030080001"/>
		<signal cm_name="充电过流保护" dev_type="47" cm_sid="0447026002" sig_type="2" alarm_level="2" zte_sid="0x21002030080001"/>

		<signal cm_name="放电过流告警" dev_type="47" cm_sid="0447027001" sig_type="2" alarm_level="3" zte_sid="0x21001030090001"/>
		<signal cm_name="放电过流告警" dev_type="47" cm_sid="0447027002" sig_type="2" alarm_level="3" zte_sid="0x21002030090001"/>

		<signal cm_name="放电过流保护" dev_type="47" cm_sid="0447028001" sig_type="2" alarm_level="2" zte_sid="0x210010300a0001"/>
		<signal cm_name="放电过流保护" dev_type="47" cm_sid="0447028002" sig_type="2" alarm_level="2" zte_sid="0x210020300a0001"/>

		<signal cm_name="输出短路保护" dev_type="47" cm_sid="0447030001" sig_type="2" alarm_level="1" zte_sid="0x21001030230001"/>
		<signal cm_name="输出短路保护" dev_type="47" cm_sid="0447030002" sig_type="2" alarm_level="1" zte_sid="0x21002030230001"/>

		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001001" sig_type="2" alarm_level="3" zte_sid="0x210010300d0001"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001002" sig_type="2" alarm_level="3" zte_sid="0x210010300d0002"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001003" sig_type="2" alarm_level="3" zte_sid="0x210010300d0003"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001004" sig_type="2" alarm_level="3" zte_sid="0x210010300d0004"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001005" sig_type="2" alarm_level="3" zte_sid="0x210010300d0005"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001006" sig_type="2" alarm_level="3" zte_sid="0x210010300d0006"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001007" sig_type="2" alarm_level="3" zte_sid="0x210010300d0007"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001008" sig_type="2" alarm_level="3" zte_sid="0x210010300d0008"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001009" sig_type="2" alarm_level="3" zte_sid="0x210010300d0009"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001010" sig_type="2" alarm_level="3" zte_sid="0x210010300d000a"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001011" sig_type="2" alarm_level="3" zte_sid="0x210010300d000b"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001012" sig_type="2" alarm_level="3" zte_sid="0x210010300d000c"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001013" sig_type="2" alarm_level="3" zte_sid="0x210010300d000d"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001014" sig_type="2" alarm_level="3" zte_sid="0x210010300d000e"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001015" sig_type="2" alarm_level="3" zte_sid="0x210010300d000f"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001016" sig_type="2" alarm_level="3" zte_sid="0x210020300d0001"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001017" sig_type="2" alarm_level="3" zte_sid="0x210020300d0002"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001018" sig_type="2" alarm_level="3" zte_sid="0x210020300d0003"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001019" sig_type="2" alarm_level="3" zte_sid="0x210020300d0004"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001020" sig_type="2" alarm_level="3" zte_sid="0x210020300d0005"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001021" sig_type="2" alarm_level="3" zte_sid="0x210020300d0006"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001022" sig_type="2" alarm_level="3" zte_sid="0x210020300d0007"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001023" sig_type="2" alarm_level="3" zte_sid="0x210020300d0008"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001024" sig_type="2" alarm_level="3" zte_sid="0x210020300d0009"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001025" sig_type="2" alarm_level="3" zte_sid="0x210020300d000a"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001026" sig_type="2" alarm_level="3" zte_sid="0x210020300d000b"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001027" sig_type="2" alarm_level="3" zte_sid="0x210020300d000c"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001028" sig_type="2" alarm_level="3" zte_sid="0x210020300d000d"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001029" sig_type="2" alarm_level="3" zte_sid="0x210020300d000e"/>
		<signal cm_name="单体过压告警" dev_type="47" cm_sid="0447001030" sig_type="2" alarm_level="3" zte_sid="0x210020300d000f"/>

		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003001" sig_type="2" alarm_level="3" zte_sid="0x210010300f0001"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003002" sig_type="2" alarm_level="3" zte_sid="0x210010300f0002"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003003" sig_type="2" alarm_level="3" zte_sid="0x210010300f0003"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003004" sig_type="2" alarm_level="3" zte_sid="0x210010300f0004"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003005" sig_type="2" alarm_level="3" zte_sid="0x210010300f0005"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003006" sig_type="2" alarm_level="3" zte_sid="0x210010300f0006"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003007" sig_type="2" alarm_level="3" zte_sid="0x210010300f0007"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003008" sig_type="2" alarm_level="3" zte_sid="0x210010300f0008"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003009" sig_type="2" alarm_level="3" zte_sid="0x210010300f0009"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003010" sig_type="2" alarm_level="3" zte_sid="0x210010300f000a"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003011" sig_type="2" alarm_level="3" zte_sid="0x210010300f000b"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003012" sig_type="2" alarm_level="3" zte_sid="0x210010300f000c"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003013" sig_type="2" alarm_level="3" zte_sid="0x210010300f000d"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003014" sig_type="2" alarm_level="3" zte_sid="0x210010300f000e"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003015" sig_type="2" alarm_level="3" zte_sid="0x210010300f000f"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003016" sig_type="2" alarm_level="3" zte_sid="0x210020300f0001"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003017" sig_type="2" alarm_level="3" zte_sid="0x210020300f0002"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003018" sig_type="2" alarm_level="3" zte_sid="0x210020300f0003"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003019" sig_type="2" alarm_level="3" zte_sid="0x210020300f0004"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003020" sig_type="2" alarm_level="3" zte_sid="0x210020300f0005"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003021" sig_type="2" alarm_level="3" zte_sid="0x210020300f0006"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003022" sig_type="2" alarm_level="3" zte_sid="0x210020300f0007"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003023" sig_type="2" alarm_level="3" zte_sid="0x210020300f0008"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003024" sig_type="2" alarm_level="3" zte_sid="0x210020300f0009"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003025" sig_type="2" alarm_level="3" zte_sid="0x210020300f000a"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003026" sig_type="2" alarm_level="3" zte_sid="0x210020300f000b"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003027" sig_type="2" alarm_level="3" zte_sid="0x210020300f000c"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003028" sig_type="2" alarm_level="3" zte_sid="0x210020300f000d"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003029" sig_type="2" alarm_level="3" zte_sid="0x210020300f000e"/>
		<signal cm_name="单体欠压告警" dev_type="47" cm_sid="0447003030" sig_type="2" alarm_level="3" zte_sid="0x210020300f000f"/>

		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002001" sig_type="2" alarm_level="3" zte_sid="0x210010300e0001"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002002" sig_type="2" alarm_level="3" zte_sid="0x210010300e0002"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002003" sig_type="2" alarm_level="3" zte_sid="0x210010300e0003"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002004" sig_type="2" alarm_level="3" zte_sid="0x210010300e0004"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002005" sig_type="2" alarm_level="3" zte_sid="0x210010300e0005"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002006" sig_type="2" alarm_level="3" zte_sid="0x210010300e0006"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002007" sig_type="2" alarm_level="3" zte_sid="0x210010300e0007"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002008" sig_type="2" alarm_level="3" zte_sid="0x210010300e0008"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002009" sig_type="2" alarm_level="3" zte_sid="0x210010300e0009"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002010" sig_type="2" alarm_level="3" zte_sid="0x210010300e000a"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002011" sig_type="2" alarm_level="3" zte_sid="0x210010300e000b"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002012" sig_type="2" alarm_level="3" zte_sid="0x210010300e000c"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002013" sig_type="2" alarm_level="3" zte_sid="0x210010300e000d"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002014" sig_type="2" alarm_level="3" zte_sid="0x210010300e000e"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002015" sig_type="2" alarm_level="3" zte_sid="0x210010300e000f"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002016" sig_type="2" alarm_level="3" zte_sid="0x210020300e0001"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002017" sig_type="2" alarm_level="3" zte_sid="0x210020300e0002"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002018" sig_type="2" alarm_level="3" zte_sid="0x210020300e0003"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002019" sig_type="2" alarm_level="3" zte_sid="0x210020300e0004"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002020" sig_type="2" alarm_level="3" zte_sid="0x210020300e0005"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002021" sig_type="2" alarm_level="3" zte_sid="0x210020300e0006"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002022" sig_type="2" alarm_level="3" zte_sid="0x210020300e0007"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002023" sig_type="2" alarm_level="3" zte_sid="0x210020300e0008"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002024" sig_type="2" alarm_level="3" zte_sid="0x210020300e0009"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002025" sig_type="2" alarm_level="3" zte_sid="0x210020300e000a"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002026" sig_type="2" alarm_level="3" zte_sid="0x210020300e000b"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002027" sig_type="2" alarm_level="3" zte_sid="0x210020300e000c"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002028" sig_type="2" alarm_level="3" zte_sid="0x210020300e000d"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002029" sig_type="2" alarm_level="3" zte_sid="0x210020300e000e"/>
		<signal cm_name="单体过压保护" dev_type="47" cm_sid="0447002030" sig_type="2" alarm_level="3" zte_sid="0x210020300e000f"/>

		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004001" sig_type="2" alarm_level="3" zte_sid="0x21001030100001"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004002" sig_type="2" alarm_level="3" zte_sid="0x21001030100002"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004003" sig_type="2" alarm_level="3" zte_sid="0x21001030100003"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004004" sig_type="2" alarm_level="3" zte_sid="0x21001030100004"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004005" sig_type="2" alarm_level="3" zte_sid="0x21001030100005"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004006" sig_type="2" alarm_level="3" zte_sid="0x21001030100006"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004007" sig_type="2" alarm_level="3" zte_sid="0x21001030100007"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004008" sig_type="2" alarm_level="3" zte_sid="0x21001030100008"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004009" sig_type="2" alarm_level="3" zte_sid="0x21001030100009"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004010" sig_type="2" alarm_level="3" zte_sid="0x2100103010000a"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004011" sig_type="2" alarm_level="3" zte_sid="0x2100103010000b"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004012" sig_type="2" alarm_level="3" zte_sid="0x2100103010000c"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004013" sig_type="2" alarm_level="3" zte_sid="0x2100103010000d"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004014" sig_type="2" alarm_level="3" zte_sid="0x2100103010000e"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004015" sig_type="2" alarm_level="3" zte_sid="0x2100103010000f"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004016" sig_type="2" alarm_level="3" zte_sid="0x21002030100001"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004017" sig_type="2" alarm_level="3" zte_sid="0x21002030100002"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004018" sig_type="2" alarm_level="3" zte_sid="0x21002030100003"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004019" sig_type="2" alarm_level="3" zte_sid="0x21002030100004"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004020" sig_type="2" alarm_level="3" zte_sid="0x21002030100005"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004021" sig_type="2" alarm_level="3" zte_sid="0x21002030100006"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004022" sig_type="2" alarm_level="3" zte_sid="0x21002030100007"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004023" sig_type="2" alarm_level="3" zte_sid="0x21002030100008"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004024" sig_type="2" alarm_level="3" zte_sid="0x21002030100009"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004025" sig_type="2" alarm_level="3" zte_sid="0x2100203010000a"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004026" sig_type="2" alarm_level="3" zte_sid="0x2100203010000b"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004027" sig_type="2" alarm_level="3" zte_sid="0x2100203010000c"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004028" sig_type="2" alarm_level="3" zte_sid="0x2100203010000d"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004029" sig_type="2" alarm_level="3" zte_sid="0x2100203010000e"/>
		<signal cm_name="单体欠压保护" dev_type="47" cm_sid="0447004030" sig_type="2" alarm_level="3" zte_sid="0x2100203010000f"/>
		
		<signal cm_name="总压过压告警" dev_type="47" cm_sid="0447005001" sig_type="2" alarm_level="1" zte_sid="0x21001030010001"/>
		<signal cm_name="总压过压告警" dev_type="47" cm_sid="0447005002" sig_type="2" alarm_level="1" zte_sid="0x21002030010001"/>

		<signal cm_name="总压欠压告警" dev_type="47" cm_sid="0447007001" sig_type="2" alarm_level="2" zte_sid="0x21001030030001"/>
		<signal cm_name="总压欠压告警" dev_type="47" cm_sid="0447007002" sig_type="2" alarm_level="2" zte_sid="0x21002030030001"/>

		<signal cm_name="总压过压保护" dev_type="47" cm_sid="0447006001" sig_type="2" alarm_level="1" zte_sid="0x21001030020001"/>
		<signal cm_name="总压过压保护" dev_type="47" cm_sid="0447006002" sig_type="2" alarm_level="1" zte_sid="0x21002030020001"/>

		<signal cm_name="总压欠压保护" dev_type="47" cm_sid="0447008001" sig_type="2" alarm_level="2" zte_sid="0x21001030040001"/>
		<signal cm_name="总压欠压保护" dev_type="47" cm_sid="0447008002" sig_type="2" alarm_level="2" zte_sid="0x21002030040001"/>

		<signal cm_name="备电时长过短" dev_type="47" cm_sid="0447015001" sig_type="2" alarm_level="2" zte_sid="0x21001030300001"/>
		<signal cm_name="备电时长过短" dev_type="47" cm_sid="0447015002" sig_type="2" alarm_level="2" zte_sid="0x21002030300001"/>

		<signal cm_name="门磁开关状态" dev_type="99" cm_sid="0499005001" sig_type="2" alarm_level="3" zte_sid="0x9001030030001"/>
		<!--需要自定义处理告警信号的放在下面-->
		<signal cm_name="一级低压脱离告警" dev_type="6" cm_sid="0406030001" sig_type="2" alarm_level="2" custom_sig_name_get="get_first_level_low_volt_disc_alarm"/>
		<signal cm_name="电池保护告警" dev_type="47" cm_sid="0447016001" sig_type="2" alarm_level="3" custom_sig_name_get="get_batt_protect_alarm"/>
		<signal cm_name="电池保护告警" dev_type="47" cm_sid="0447016002" sig_type="2" alarm_level="3" custom_sig_name_get="get_batt_protect_alarm"/>
		<!--其它告警必须放在最后处理-->
		<signal cm_name="其他告警" dev_type="6" cm_sid="0406029001" sig_type="2" alarm_level="3" custom_sig_name_get="is_other_alarm_exist"/>
	</alarm>

	<du_alarm>
		<signal cm_name="负载熔丝XX故障告警" dev_type="6" cm_sid="0406007001" sig_type="2" alarm_level="1" ssw_sid="0x2c001030020001" du_sid="0xa001030110001" sddu_comm_sid="0x22001030030001" sddu_sw_sid="0x22001030070001"/>
		<signal cm_name="第XX路直流输出分路断开告警" dev_type="6" cm_sid="0406032001" sig_type="2" alarm_level="2" ssw_sid="0x2c001030020001" du_sid="0xa001030110001" sddu_comm_sid="0x22001030030001" sddu_sw_sid="0x22001030070001"/>
	</du_alarm>
	
	<signals>
		<!--AI  遥测-->
		<signal cm_name="交流输入XX相电压Ua" cm_sid="0406101001" dev_type="6" sig_type="3" zte_sid="0x3001010030001"/>
		<signal cm_name="交流输入XX相电压Ub" cm_sid="0406102001" dev_type="6" sig_type="3" zte_sid="0x3001010040001"/>
		<signal cm_name="交流输入XX相电压Uc" cm_sid="0406103001" dev_type="6" sig_type="3" zte_sid="0x3001010050001"/>
		<signal cm_name="交流输入XX相电流Ia" cm_sid="0406107001" dev_type="6" sig_type="3" zte_sid="0x3001010090001"/>
		<signal cm_name="交流输入XX相电流Ib" cm_sid="0406108001" dev_type="6" sig_type="3" zte_sid="0x30010100a0001"/>
		<signal cm_name="交流输入XX相电流Ic" cm_sid="0406109001" dev_type="6" sig_type="3" zte_sid="0x30010100b0001"/>
		<signal cm_name="交流输入XX频率" cm_sid="0406110001" dev_type="6" sig_type="3" zte_sid="0x30010100f0001"/>

		<signal cm_name="直流电压" cm_sid="0406111001" dev_type="6" sig_type="3" zte_sid="0xa001010010001"/>
		<signal cm_name="直流负载总电流" cm_sid="0406112001" dev_type="6" sig_type="3" zte_sid="0xa001010020001"/>

		<signal cm_name="整流模块XX电流" cm_sid="0406113001" dev_type="6" sig_type="3" zte_sid="0x7001010020001"/>
		<signal cm_name="整流模块XX电流" cm_sid="0406113002" dev_type="6" sig_type="3" zte_sid="0x7002010020001"/>

		<signal cm_name="整流模块XX温度" cm_sid="0406114001" dev_type="6" sig_type="3" zte_sid="0x7001010030001"/>
		<signal cm_name="整流模块XX温度" cm_sid="0406114002" dev_type="6" sig_type="3" zte_sid="0x7002010030001"/>

		<signal cm_name="电池组XX电流" cm_sid="0406115001" dev_type="6" sig_type="3" zte_sid="0xb001010030001"/>
		<signal cm_name="电池组XX电流" cm_sid="0406115002" dev_type="6" sig_type="3" zte_sid="0xb002010030001"/>
		<signal cm_name="电池组XX电流" cm_sid="0406115003" dev_type="6" sig_type="3" zte_sid="0xb003010030001"/>
		<signal cm_name="电池组XX电流" cm_sid="0406115004" dev_type="6" sig_type="3" zte_sid="0xb004010030001"/>
		<signal cm_name="电池组XX电流" cm_sid="0406115005" dev_type="6" sig_type="3" zte_sid="0xb005010030001"/>
		<signal cm_name="电池组XX电流" cm_sid="0406115006" dev_type="6" sig_type="3" zte_sid="0xb006010030001"/>
		
		<signal cm_name="配置模块数量" cm_sid="0406123001" dev_type="6" sig_type="3" zte_sid="0x8001010050001"/>
		
		<signal cm_name="整流模块XX额定容量" cm_sid="0406124001" dev_type="6" sig_type="3" zte_sid="0x70010102c0001"/>
		<signal cm_name="整流模块XX额定容量" cm_sid="0406124002" dev_type="6" sig_type="3" zte_sid="0x70020102c0001"/>

		<signal cm_name="电池组XX温度" cm_sid="0406125001" dev_type="6" sig_type="3" zte_sid="0xb001010040001"/>
		<signal cm_name="电池组XX温度" cm_sid="0406125002" dev_type="6" sig_type="3" zte_sid="0xb002010040001"/>
		<signal cm_name="电池组XX温度" cm_sid="0406125003" dev_type="6" sig_type="3" zte_sid="0xb003010040001"/>
		<signal cm_name="电池组XX温度" cm_sid="0406125004" dev_type="6" sig_type="3" zte_sid="0xb004010040001"/>
		<signal cm_name="电池组XX温度" cm_sid="0406125005" dev_type="6" sig_type="3" zte_sid="0xb005010040001"/>
		<signal cm_name="电池组XX温度" cm_sid="0406125006" dev_type="6" sig_type="3" zte_sid="0xb006010040001"/>

		<signal cm_name="电池组XX额定容量" cm_sid="0406127001" dev_type="6" sig_type="3" zte_sid="0xc001050010001"/>
		<signal cm_name="电池组XX额定容量" cm_sid="0406127002" dev_type="6" sig_type="3" zte_sid="0xc001050010002"/>
		<signal cm_name="电池组XX额定容量" cm_sid="0406127003" dev_type="6" sig_type="3" zte_sid="0xc001050010003"/>
		<signal cm_name="电池组XX额定容量" cm_sid="0406127004" dev_type="6" sig_type="3" zte_sid="0xc001050010004"/>
		<signal cm_name="电池组XX额定容量" cm_sid="0406127005" dev_type="6" sig_type="3" zte_sid="0xc001050010005"/>
		<signal cm_name="电池组XX额定容量" cm_sid="0406127006" dev_type="6" sig_type="3" zte_sid="0xc001050010006"/>

		<signal cm_name="电池组额定总容量" cm_sid="0406128001" dev_type="6" sig_type="3" zte_sid="0xc0010100a0001"/>
		<signal cm_name="电池组实际总容量" cm_sid="0406129001" dev_type="6" sig_type="3" zte_sid="0xc0010100b0001"/>
		<signal cm_name="电池组剩余容量百分比" cm_sid="0406130001" dev_type="6" sig_type="3" zte_sid="0xc0010100c0001"/>
		<signal cm_name="移动租户电流" cm_sid="0406135001" dev_type="6" sig_type="3" zte_sid="0xa001010170001"/>
		<signal cm_name="移动租户电量" cm_sid="0406136001" dev_type="6" sig_type="3" zte_sid="0xa0010700a0001"/>
		<signal cm_name="联通租户电流" cm_sid="0406137001" dev_type="6" sig_type="3" zte_sid="0xa001010180001"/>
		<signal cm_name="联通租户电量" cm_sid="0406138001" dev_type="6" sig_type="3" zte_sid="0xa0010700b0001"/>
		<signal cm_name="电信租户电流" cm_sid="0406139001" dev_type="6" sig_type="3" zte_sid="0xa001010190001"/>
		<signal cm_name="电信租户电量" cm_sid="0406140001" dev_type="6" sig_type="3" zte_sid="0xa0010700c0001"/>

		<signal cm_name="均充电压设定值" cm_sid="0406143001" dev_type="6" sig_type="3" zte_sid="0xc001050030001"/>
		<signal cm_name="浮充电压设定值" cm_sid="0406144001" dev_type="6" sig_type="3" zte_sid="0xc001050020001"/>
		<signal cm_name="温度补偿系数" cm_sid="0406145001" dev_type="6" sig_type="3" zte_sid="0xc001050380001"/>
		<signal cm_name="一级低压脱离XX设定值" cm_sid="0406146001" dev_type="6" sig_type="3" zte_sid="0x2c001050020001"/>
		<signal cm_name="休眠高门限值" cm_sid="0406149001" dev_type="6" sig_type="3" zte_sid="0x80010500b0001"/>
		<signal cm_name="休眠低门限值" cm_sid="0406150001" dev_type="6" sig_type="3" zte_sid="0x80010500c0001"/>
		<signal cm_name="负载低压门限值" cm_sid="0406151001" dev_type="6" sig_type="3" zte_sid="0xa001050020001"/>
		<signal cm_name="测试终止电压值" cm_sid="0406153001" dev_type="6" sig_type="3" zte_sid="0xc001050040001"/>
		<signal cm_name="浮充转均充门限电压" cm_sid="0406156001" dev_type="6" sig_type="3" zte_sid="0xc001050230001"/>
		<signal cm_name="自动均充功能状态" cm_sid="0406158001" dev_type="6" sig_type="3" zte_sid="0xc001050e90001"/>

		<signal cm_name="广电租户电流" cm_sid="0406169001" dev_type="6" sig_type="3" zte_sid="0xa0010101a0001"/>
		<signal cm_name="广电租户电量" cm_sid="0406170001" dev_type="6" sig_type="3" zte_sid="0xa0010700d0001"/>
		<signal cm_name="行业外租户电流" cm_sid="0406171001" dev_type="6" sig_type="3" zte_sid="0xa0010101b0001"/>
		<signal cm_name="行业外租户电量" cm_sid="0406172001" dev_type="6" sig_type="3" zte_sid="0xa0010700e0001"/>
		<signal cm_name="环境温度" cm_sid="0418101001" dev_type="18" sig_type="3" zte_sid="0x9001010010001"/>
		<signal cm_name="环境湿度" cm_sid="0418102001" dev_type="18" sig_type="3" zte_sid="0x9001010020001"/>

		<signal cm_name="预计备电时长" cm_sid="0447101001" dev_type="47" sig_type="3" zte_sid="0x21001010140001"/>
		<signal cm_name="预计备电时长" cm_sid="0447101002" dev_type="47" sig_type="3" zte_sid="0x21002010140001"/>

		<signal cm_name="电池温度xx" cm_sid="0447102001" dev_type="47" sig_type="3" zte_sid="0x21001010050001"/>
		<signal cm_name="电池温度xx" cm_sid="0447102002" dev_type="47" sig_type="3" zte_sid="0x21001010050002"/>
		<signal cm_name="电池温度xx" cm_sid="0447102003" dev_type="47" sig_type="3" zte_sid="0x21001010050003"/>
		<signal cm_name="电池温度xx" cm_sid="0447102004" dev_type="47" sig_type="3" zte_sid="0x21001010050004"/>
		<signal cm_name="电池温度xx" cm_sid="0447102005" dev_type="47" sig_type="3" zte_sid="0x21002010050001"/>
		<signal cm_name="电池温度xx" cm_sid="0447102006" dev_type="47" sig_type="3" zte_sid="0x21002010050002"/>
		<signal cm_name="电池温度xx" cm_sid="0447102007" dev_type="47" sig_type="3" zte_sid="0x21002010050003"/>
		<signal cm_name="电池温度xx" cm_sid="0447102008" dev_type="47" sig_type="3" zte_sid="0x21002010050004"/>

		<signal cm_name="电池组总电压" cm_sid="0447103001" dev_type="47" sig_type="3" zte_sid="0x21001010010001"/>
		<signal cm_name="电池组总电压" cm_sid="0447103002" dev_type="47" sig_type="3" zte_sid="0x21002010010001"/>

		<signal cm_name="电池单体电压XX" cm_sid="0447104001" dev_type="47" sig_type="3" zte_sid="0x21001010040001"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104002" dev_type="47" sig_type="3" zte_sid="0x21001010040002"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104003" dev_type="47" sig_type="3" zte_sid="0x21001010040003"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104004" dev_type="47" sig_type="3" zte_sid="0x21001010040004"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104005" dev_type="47" sig_type="3" zte_sid="0x21001010040005"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104006" dev_type="47" sig_type="3" zte_sid="0x21001010040006"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104007" dev_type="47" sig_type="3" zte_sid="0x21001010040007"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104008" dev_type="47" sig_type="3" zte_sid="0x21001010040008"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104009" dev_type="47" sig_type="3" zte_sid="0x21001010040009"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104010" dev_type="47" sig_type="3" zte_sid="0x2100101004000a"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104011" dev_type="47" sig_type="3" zte_sid="0x2100101004000b"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104012" dev_type="47" sig_type="3" zte_sid="0x2100101004000c"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104013" dev_type="47" sig_type="3" zte_sid="0x2100101004000d"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104014" dev_type="47" sig_type="3" zte_sid="0x2100101004000e"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104015" dev_type="47" sig_type="3" zte_sid="0x2100101004000f"/>

		<signal cm_name="电池单体电压XX" cm_sid="0447104016" dev_type="47" sig_type="3" zte_sid="0x21002010040001"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104017" dev_type="47" sig_type="3" zte_sid="0x21002010040002"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104018" dev_type="47" sig_type="3" zte_sid="0x21002010040003"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104019" dev_type="47" sig_type="3" zte_sid="0x21002010040004"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104020" dev_type="47" sig_type="3" zte_sid="0x21002010040005"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104021" dev_type="47" sig_type="3" zte_sid="0x21002010040006"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104022" dev_type="47" sig_type="3" zte_sid="0x21002010040007"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104023" dev_type="47" sig_type="3" zte_sid="0x21002010040008"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104024" dev_type="47" sig_type="3" zte_sid="0x21002010040009"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104025" dev_type="47" sig_type="3" zte_sid="0x2100201004000a"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104026" dev_type="47" sig_type="3" zte_sid="0x2100201004000b"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104027" dev_type="47" sig_type="3" zte_sid="0x2100201004000c"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104028" dev_type="47" sig_type="3" zte_sid="0x2100201004000d"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104029" dev_type="47" sig_type="3" zte_sid="0x2100201004000e"/>
		<signal cm_name="电池单体电压XX" cm_sid="0447104030" dev_type="47" sig_type="3" zte_sid="0x2100201004000f"/>
		
		<signal cm_name="剩余容量" cm_sid="0447105001" dev_type="47" sig_type="3" zte_sid="0x21001010060001"/>
		<signal cm_name="剩余容量" cm_sid="0447105002" dev_type="47" sig_type="3" zte_sid="0x21002010060001"/>

		<signal cm_name="电流" cm_sid="0447106001" dev_type="47" sig_type="3" zte_sid="0x21001010020001"/>
		<signal cm_name="电池放电次数" cm_sid="0447113001" dev_type="47" sig_type="3" zte_sid="0x21001010080001"/>
		<signal cm_name="电池ID" cm_sid="0447114001" dev_type="47" sig_type="3" zte_sid="0x21001080040001"/>
		<signal cm_name="梯级电池型号" cm_sid="0447117001" dev_type="47" sig_type="3" zte_sid="0x21001080010001"/>
		<signal cm_name="BMS型号" cm_sid="0447120001" dev_type="47" sig_type="3" zte_sid="0x21001080010001"/>
		<signal cm_name="BMS通信软件版本号" cm_sid="0447121001" dev_type="47" sig_type="3" zte_sid="0x21001080020001"/>

		<signal cm_name="CPU利用率" cm_sid="0438101001" dev_type="38" sig_type="3" zte_sid="0x1001010010001"/>
		<signal cm_name="内存利用率" cm_sid="0438102001" dev_type="38" sig_type="3" zte_sid="0x1001010020001"/>
		<!-- 需要自定义处理的遥测量添加到下面 -->
		<signal cm_name="设备系统时间" cm_sid="0406121001" dev_type="6" sig_type="3" custom_sig_name_get="get_device_system_time"/>
		<signal cm_name="系统额定容量" cm_sid="0406122001" dev_type="6" sig_type="3" custom_sig_name_get="get_system_rated_capacity"/>
		<signal cm_name="蓄电池组数" cm_sid="0406126001" dev_type="6" sig_type="3" custom_sig_name_get="get_battery_group_count"/>
		<signal cm_name="二级低压脱离XX设定值" cm_sid="0406147001" dev_type="6" sig_type="3" custom_sig_name_get="get_llvd2_voltage_lower_threshold"/>
		<signal cm_name="均充触发安时门限" cm_sid="0406157001" dev_type="6" sig_type="3" custom_sig_name_get="get_equalizing_charge_capacity_threshold"/>
		<signal cm_name="温度补偿功能状态" cm_sid="0406159001" dev_type="6" sig_type="3" custom_sig_name_get="get_temperature_compensation_status"/>
		<signal cm_name="设备系统时间" cm_sid="0438103001" dev_type="38" sig_type="3" custom_sig_name_get="get_device_system_time"/>
		<signal cm_name="电池充放电状态" cm_sid="0447107001" dev_type="47" sig_type="3" custom_sig_name_get="get_batt_status"/>
		<signal cm_name="电池充放电状态" cm_sid="0447107002" dev_type="47" sig_type="3" custom_sig_name_get="get_batt_status"/>
		<!--遥控-->
		<signal cm_name="整流模块XX远程开控制" cm_sid="0406204001" dev_type="6" sig_type="4" zte_sid="0x7001040030001"/>
		<signal cm_name="整流模块XX远程开控制" cm_sid="0406204002" dev_type="6" sig_type="4" zte_sid="0x7002040030001"/>

		<signal cm_name="整流模块XX远程关控制" cm_sid="0406205001" dev_type="6" sig_type="4" zte_sid="0x7001040020001"/>
		<signal cm_name="整流模块XX远程关控制" cm_sid="0406205002" dev_type="6" sig_type="4" zte_sid="0x7002040020001"/>

		<signal cm_name="是否允许自动均充" cm_sid="0406206001" dev_type="6" sig_type="4" zte_sid="0xc001050e90001"/>
		<signal cm_name="电池测试开启" cm_sid="0406212001" dev_type="6" sig_type="4" zte_sid="0xc001040030001"/>
		<signal cm_name="电池测试关闭" cm_sid="0406213001" dev_type="6" sig_type="4" zte_sid="0xc0010400a0001"/>
		
		<!-- 需要自定义处理的遥控量添加到下面 -->
		<signal cm_name="均充控制" cm_sid="0406202001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_auto_equal_enabled"/>
		<signal cm_name="时钟同步" cm_sid="0406207001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_clock_synchronization"/>
		<signal cm_name="温度补偿功能开启" cm_sid="0406208001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_temp_compensation_enabled"/>
		<signal cm_name="温度补偿功能关闭" cm_sid="0406209001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_temp_compensation_disabled"/>
		<signal cm_name="自动均充功能开启" cm_sid="0406216001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_auto_equal_enabled"/>
		<signal cm_name="自动均充功能关闭" cm_sid="0406217001" dev_type="6" sig_type="4" custom_sig_name_set="ctrl_auto_equal_disabled"/>
		<signal cm_name="时钟同步" cm_sid="0438201001" dev_type="38" sig_type="4" custom_sig_name_set="ctrl_clock_synchronization"/>
		<!--遥调-->
		<signal cm_name="浮充电压设定值" cm_sid="0406303001" dev_type="6" sig_type="5" zte_sid="0xc001050020001"/>
		<signal cm_name="均充工作允许设定" cm_sid="0406306001" dev_type="6" sig_type="5" zte_sid="0xc001050150001"/>
		<signal cm_name="均充电压设定值" cm_sid="0406308001" dev_type="6" sig_type="5" zte_sid="0xc001050030001"/>
		<signal cm_name="直流输出电压过低设定值" cm_sid="0406310001" dev_type="6" sig_type="5" zte_sid="0xa001050020001"/>
		<signal cm_name="直流输出电压过高设定值" cm_sid="0406311001" dev_type="6" sig_type="5" zte_sid="0xa001050010001"/>
		<signal cm_name="均充电流设定值" cm_sid="0406313001" dev_type="6" sig_type="5" zte_sid="0xc001050240001"/>
		<signal cm_name="负载低压门限" cm_sid="0406317001" dev_type="6" sig_type="5" zte_sid="0xa001050020001"/>
		<signal cm_name="测试终止电压" cm_sid="0406319001" dev_type="6" sig_type="5" zte_sid="0xc001050040001"/>
		<signal cm_name="浮充转均充门限电压" cm_sid="0406322001" dev_type="6" sig_type="5" zte_sid="0xc001050230001"/>
		<signal cm_name="休眠高门限" cm_sid="0406325001" dev_type="6" sig_type="5" zte_sid="0x80010500b0001"/>

		<!-- 需要自定义处理的遥调量添加到下面 -->
		<signal cm_name="电池充电限流设定值" cm_sid="0406301001" dev_type="6" sig_type="5" custom_sig_name_get="get_batt_charge_curr_limit_setting_value" custom_sig_name_set="set_batt_charge_curr_limit_setting_value"/>
		<signal cm_name="温度补偿系数" cm_sid="0406302001" dev_type="6" sig_type="5" custom_sig_name_get="get_temp_compensation_coeff" custom_sig_name_set="set_temp_compensation_coeff"/>
		<signal cm_name="均充持续时间设定值" cm_sid="0406307001" dev_type="6" sig_type="5" custom_sig_name_get="get_equal_charge_duration_setting_value" custom_sig_name_set="set_equal_charge_duration_setting_value"/>
		<signal cm_name="均充间隔周期" cm_sid="0406312001" dev_type="6" sig_type="5" custom_sig_name_get="get_equal_interval_period" custom_sig_name_set="set_equal_interval_period"/>
		<signal cm_name="均充触发安时门限" cm_sid="0406323001" dev_type="6" sig_type="5" custom_sig_name_get="get_equal_trigger_ah_threshold" custom_sig_name_set="set_equal_trigger_ah_threshold"/>
	</signals>
	
	
	<du_signal>
		<signal cm_name="配电单元XX电流" cm_sid="0406166001" dev_type="6" sig_type="3" ssw_sid="0x2c001010020001" du_sid="0xa0010100f0001" sddu_comm_sid="0x22001010020001" sddu_sw_sid="0x22001010080001"/>
		<signal cm_name="配电单元XX电量" cm_sid="0406167001" dev_type="6" sig_type="3" ssw_sid="0x2c001010050001" du_sid="0xa001070060001" sddu_comm_sid="0x22001010030001" sddu_sw_sid="0x220010100b0001"/>
		<signal cm_name="第XX路直流负载电流" cm_sid="0406116001" dev_type="6" sig_type="3" ssw_sid="0x2c001010020001" du_sid="0xa0010100f0001" sddu_comm_sid="0x22001010020001" sddu_sw_sid="0x22001010080001"/>
		<signal cm_name="配电单元XX上电授权状态" cm_sid="0406168001" dev_type="6" sig_type="3" ssw_sid="0x2c001020030001" du_sid="0xa001020240001" sddu_comm_sid="0x22001020230001" sddu_sw_sid="0x220010200a0001"/>
		<signal cm_name="配电单元XX名称" cm_sid="0406164001" dev_type="6" sig_type="3" ssw_sid="0x2c001050140001" du_sid="0xa0010502c0001" sddu_comm_sid="0x22001050400001" sddu_sw_sid="不支持"/>
		<signal cm_name="控制分户XX直流输出开通" cm_sid="0406218001" dev_type="6" sig_type="4" ssw_sid="0x2c001040020001" du_sid="0xa001040020001" sddu_comm_sid="0x22001040020001" sddu_sw_sid="0x22001040050001"/>
		<signal cm_name="控制分户XX直流输出关闭" cm_sid="0406219001" dev_type="6" sig_type="4" ssw_sid="0x2c001040010001" du_sid="0xa001040010001" sddu_comm_sid="0x22001040010001" sddu_sw_sid="0x22001040040001"/>
		<signal cm_name="设置配电单元XX名称" cm_sid="0406326001" dev_type="6" sig_type="5" ssw_sid="0x2c001050140001" du_sid="0xa0010502c0001" sddu_comm_sid="0x22001050400001" sddu_sw_sid="不支持"/>
		<signal cm_name="设置配电单元XX上电授权状态" cm_sid="0406328001" dev_type="6" sig_type="5" ssw_sid="0x2c001050070001" du_sid="0xa001050290001" sddu_comm_sid="0x220010505e0001" sddu_sw_sid="0x22001050260001"/>
	</du_signal>

</signal_list>