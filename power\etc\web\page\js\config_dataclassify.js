var vmodel = avalon.define({
	$id:'classify',
	classify_devType:[],
	classify_mediumdata:[],
	classify_searchvalue:{"sid":"0"},
	classify_selectdevsid:"-1",
	classify_selectdevtype:"0",
	classify_selectmediumdata:"",
    classify_devtype_tag:[],         //  主要用于做设备列表显示
	classify_value:[],
	classify_values_dic:{},
	classify_selecttochange:[],
	changeClassiflyValue:function(d,num){
		if (d.target.id == "web_tag") {
			var changevalue = {"sid":"" , "web": "" };
			changevalue.sid = vmodel.classify_value[num].sid;
			changevalue.web =  (d.target.checked === false) ? "1" : "0";
			addvalue(vmodel.classify_selecttochange,[changevalue],"sid");
		}
		if (d.target.id == "gui_tag") {
			var changevalue = {"sid":"" , "gui": "" };
			changevalue.sid = vmodel.classify_value[num].sid;
			changevalue.gui =  (d.target.checked === false) ? "1" : "0";
			addvalue(vmodel.classify_selecttochange,[changevalue],"sid");
		}
	},
	get_classify_selectval:function(sid, medium) {
		var status = "";
		for (var i in vmodel.classify_selecttochange) {
			if (vmodel.classify_selecttochange[i][medium] != "") {
				status = vmodel.classify_selecttochange[i][medium];
				return status;
			}
		}
		status = vmodel.classify_values_dic[sid][medium];
		return status;
	},
	checkSidIndex:function(sid){
		/*
		var index = calSidToDevType(sid).devSigIndex;
		if (index > 1) {
			return 0;
		}*/
		return 1;
	},
	checkDevIndex:function(sid){
		sid = BigInt(sid);
        var dev_type = calSidToDevType(sid).devType;
		if (!vmodel.classify_devtype_tag[dev_type]) {
            vmodel.classify_devtype_tag[dev_type] = 1;
			return 1;
		}
		return 0;
	},
	getSidParentName:function(name,sid){
		if (judge_specail_sid_show(sid)) {
			return name;   //   输入干接点告警不进行处理
		}
		return name.replace(/\[([^)]+)\]/,'');
	},
	getDevParentName:function(name){
		var reg = /[^_]*/
		return reg.exec(name);
	}
});

//  获取设备列表
get_dev_list_all();

//获取设备信号类型
var devType = {data:{objectid:"signal_type",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])},success:getDevType};
request.addRequest([devType]);

function getDevType(d,r){
	var array = [];
	if((d.result ==="ok")&&(d.datanum >0)){
		for (var i in d.data) {
			if (d.data[i].value == '3') {    
				continue;
			}
			array.push(d.data[i]);
		}
	}
	vmodel.classify_devType = array;
}

vmodel.$watch("classify_selectdevsid", function(a) {
	if((vmodel.classify_selectdevtype!=="0")&&(vmodel.classify_selectdevsid!=="0")){
		var sigtype = BigInt(vmodel.classify_selectdevtype)*BigInt(268435456);
		var sidvalue = BigInt(vmodel.classify_selectdevsid) + sigtype;
		vmodel.classify_searchvalue.sid = sidvalue.toString();
	}
	else{
		if(vmodel.classify_selectdevsid==="0")
			vmodel.classify_selectdevtype = 0;
		vmodel.classify_searchvalue.sid = vmodel.classify_selectdevsid;
	}
});

vmodel.$watch("classify_selectdevtype", function(a) {
	if(vmodel.classify_selectdevsid!=="0"){
		var sigtype = BigInt(vmodel.classify_selectdevtype) * BigInt(268435456);
		var sidvalue = BigInt(vmodel.classify_selectdevsid) + sigtype;
		vmodel.classify_searchvalue.sid = sidvalue.toString();
	}
});

vmodel.$watch("classify_selectmediumdata", function(a) {
	vmodel.classify_searchvalue.medium = vmodel.classify_selectmediumdata;
});

function generate_para_sid(in_sid) {
	in_sid = BigInt(in_sid);
    var siginfo =  calSidToDevType(in_sid);
    var dev_type = siginfo.devType;
    var sig_type = siginfo.sigType;
    return calSid(dev_type,1,sig_type,0,0);
}

function doClassifySearch(){
	if (vmodel.classify_selectdevsid * 1.0 < 0) {
		return;
	}
    vmodel.classify_searchvalue.sid=generate_para_sid(vmodel.classify_searchvalue.sid);
	var devType = {data:{objectid:"classify_data",type:"val_get",paranum:"1",paraval:JSON.stringify([vmodel.classify_searchvalue])},success:showClassifyData};
	request.addRequest([devType]);
}

function showClassifyData(d,r){
	vmodel.classify_value.removeAll();   // 无论成功与否只要系统响应则先清除页面
	if(d.result ==="ok"){
		vmodel.classify_selecttochange.removeAll();
		addvalue(vmodel.classify_value,d.data,"sid");
		var value_dic = transformDataToDic(d.data,"sid");
		vmodel.classify_values_dic = value_dic;
	}
}

function setClassifly_succ(d,r) {
	vmodel.classify_selecttochange.clear();
	if (d.result === "ok") {
		alert(mainvalue.i18nkeyword.operate_successfully);
	} else {
		alert(mainvalue.i18nkeyword.operate_failure);
	}
}

function setClassiflyData(){
	if (vmodel.classify_selecttochange.length == 0) {
		return;
	}
	var setdata = {data:{objectid:"classify_data",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.classify_selecttochange)},success:setClassifly_succ};
	request.addRequest([setdata]);
}
