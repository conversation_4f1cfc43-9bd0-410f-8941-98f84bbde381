var radius_status = "0";

var radius_status_req = {data:{objectid:"plat.radius",type:"list",paraval:""},success:radius_status_req_succ};
function radius_status_req_succ(d, r) {
    if (d.result == "ok") {
		radius_status = d.data[0]['status'];
		set_cookie_with_path("radius", d.data[0]['status']);
    }
}
request.addRequest([radius_status_req]);

var loginReq = {data:{objectid:"login",type:"val_set",paraval:""},success:checkRtn, error:loginFailed};

/* 初始化获取logo标志位 */
getLogoFlag();

var get_flag_Rq	= {data:{objectid:"eeprom_info",type:"attr_get",paraval:JSON.stringify([{}])},success:get_eeprom_flag_succ};
var set_flag_Rq	= {data:{objectid:"eeprom_info",type:"attr_set",paraval:JSON.stringify([{}])}};

function get_eeprom_flag_succ(d, r) {
    if (d.result === "ok") {
		// 如果EEPROM配置确认之前选择是取消同步，返回登录页面时重新修改为未确认，登录后正常弹窗
		if (d.data[0]['eeprom_comfirm_flag'] === "2") {
			set_flag_Rq.data.paraval = JSON.stringify([{"eeprom_comfirm_flag": "0"}]);
			request.addRequest([set_flag_Rq]);
		}
    }
}
/* 获取EEPROM配置确认标记位 */
request.addRequest([get_flag_Rq]);

//request.logout();

// if (request.getUserLevel() > 0) {
// 	gotopage("indexhtml");
// }
// watchTask.pause();
$("#loginname").focus();

var user= "";
function doLogin() {
	user = $("#loginname").val();
	var pass = $("#loginpsw").val();
    if (pass == "" || user == "") {
        return;
    }
    if (radius_status != "1") {
        pass = $.sha256(pass);
    }
    var q = loginReq;
    q.data ={objectid:"login",type:"val_set",paraval:JSON.stringify([{"username":user,"pswd":pass}])};
    request.addRequest([q]);
    $("#loginBT").attr("disabled", "disabled");
}


function get_session_num() {
	user = $("#loginname").val();
	var pass = $("#loginpsw").val();
    if (pass == "" || user == "") {
        return;
	}
	var radius_status_req = {data:{objectid:"login",type:"attr_get",paraval:""},success:session_num_req_succ};
	request.addRequest([radius_status_req]);
}

function session_num_req_succ(d, r) {
    if (d.result == "ok") {
		session_num = d.data[0]['session_num'];
		if(session_num <= 0){
			if(confirm(mainvalue.i18nkeyword.login.maxuser)){
				doLogin();
			}
		}else {
			doLogin();
		}
    }
}

function checkRtn(d, r) {
	if (d.result == "error") {
		$("#loginname").val("");
		$("#loginpsw").val("");
		popupTipsDiv($('#messagealert'), 2000);
		$("#loginBT").attr("disabled", false);
		$("#loginname").focus();
		return;
	} 
	if (d.result == "ok") {
		if (typeof(d.data[0].userlevel) == "undefined") { // �޵ȼ��ֶ�
            //alert(d.data[0].remain);
            alert(mainvalue.i18nkeyword.login.login_error+"!");
            return;
        }
        set_cookie_with_path("user", user);
		var userlevel = d.data[0].userlevel;
		set_cookie_with_path("level", userlevel);
		watchTask.reset();
		gotopage("index.html");
		return;
	}
	if (d.result == "login_error") {
		$("#loginname").val("");
		$("#loginpsw").val("");
		popupTipsDiv($('#message_overtimes'), 5000);
		return;
	}		
}

function loginFailed(r) {
	$("#loginname").val("");
	$("#loginpsw").val("");
	popupTipsDiv($('#messagealert'), 2000);
	$("#loginBT").attr("disabled", false);
	$("#loginname").focus();
}

function click_enter(){
	if(event.keyCode == 13){
		get_session_num();
		return false;
	}
}