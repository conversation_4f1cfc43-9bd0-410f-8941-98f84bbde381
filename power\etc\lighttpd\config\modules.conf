#######################################################################
##
##  Modules to load
## -----------------
##
## at least mod_access and mod_accesslog should be loaded
## all other module should only be loaded if really neccesary
##
## - saves some time
## - saves memory
##
## the default module set contains:
##
## "mod_indexfile", "mod_dirlisting", "mod_staticfile"
##
## you dont have to include those modules in your list
##
## Modules, which are pulled in via conf.d/*.conf
##
## NOTE: the order of modules is important.
##
## - mod_accesslog     -> conf.d/access_log.conf
## - mod_compress      -> conf.d/compress.conf
## - mod_status        -> conf.d/status.conf
## - mod_webdav        -> conf.d/webdav.conf
## - mod_cml           -> conf.d/cml.conf
## - mod_evhost        -> conf.d/evhost.conf
## - mod_simple_vhost  -> conf.d/simple_vhost.conf
## - mod_mysql_vhost   -> conf.d/mysql_vhost.conf
## - mod_trigger_b4_dl -> conf.d/trigger_b4_dl.conf
## - mod_userdir       -> conf.d/userdir.conf
## - mod_rrdtool       -> conf.d/rrdtool.conf
## - mod_ssi           -> conf.d/ssi.conf
## - mod_cgi           -> conf.d/cgi.conf
## - mod_scgi          -> conf.d/scgi.conf
## - mod_fastcgi       -> conf.d/fastcgi.conf
## - mod_proxy         -> conf.d/proxy.conf
## - mod_secdownload   -> conf.d/secdownload.conf
## - mod_expire        -> conf.d/expire.conf
##

server.modules = (
  "mod_access",
  "mod_alias",
#  "mod_auth",
#  "mod_evasive",
  "mod_redirect",
#  "mod_rewrite",
   "mod_evasive",
  "mod_setenv",
#  "mod_usertrack",
   "mod_openssl",
)

##
#######################################################################

#######################################################################
##
##  Config for various Modules
##

##
## mod_ssi
##
include "conf.d/ssi.conf"
alias.url += ( "/power/page" => "/root/power/etc/web/page/html/" )
##
## mod_status
##
#include "conf.d/status.conf"

##
## mod_webdav
##
#include "conf.d/webdav.conf"

##
## mod_compress
##
#include "conf.d/compress.conf"

##
## mod_userdir
##
#include "conf.d/userdir.conf"

##
## mod_magnet
##
#include "conf.d/magnet.conf"

##
## mod_cml
##
#include "conf.d/cml.conf"

##
## mod_rrdtool
##
#include "conf.d/rrdtool.conf"

##
## mod_proxy
##
#include "conf.d/proxy.conf"

##
## mod_expire
##
#include "conf.d/expire.conf"

##
## mod_secdownload
##
#include "conf.d/secdownload.conf"

##
#######################################################################

#######################################################################
##
## CGI modules
##

##
## SCGI (mod_scgi)
##
#include "conf.d/scgi.conf"

##
## FastCGI (mod_fastcgi)
##
include "conf.d/fastcgi.conf"

##
## plain old CGI (mod_cgi)
##
#include "conf.d/cgi.conf"

##
#######################################################################

#######################################################################
##
## VHost Modules
##
##  Only load ONE of them!
## ========================
##

##
## You can use conditionals for vhosts aswell.
## 
## see http://www.lighttpd.net/documentation/configuration.html
##

##
## mod_evhost
##
#include "conf.d/evhost.conf"

##
## mod_simple_vhost
##
#include "conf.d/simple_vhost.conf"

##
## mod_mysql_vhost
##
#include "conf.d/mysql_vhost.conf"

##
#######################################################################
