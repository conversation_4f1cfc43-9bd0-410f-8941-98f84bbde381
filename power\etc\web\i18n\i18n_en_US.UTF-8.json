{"copyright": "2024 ZTE Corporation. All rights reserved", "language": "Language", "logout": "Logout", "value_invalid": "Invalid Value", "para_confirm_title": "Change parameter confirmation", "para_confirm_content": "The following are the parameters to be changed. Please confirm again whether to modify them (the modification will be automatically cancelled after 60 seconds):", "config_error": "The following parameter constraint relationship conflicts, please reset:", "config_check_error": "The following configuration is incorrect, please reset:", "Disallow_input": "Disallow input", "Disabled": "Disabled", "Enabled": "Enabled", "char": "CHAR", "about": "About", "contact": "Contact Us", "second_10": "10 sec", "second_1": "1 sec", "second": "s", "set": "Set", "add": "Add", "ok": "OK", "yes": "Yes", "no": "No", "submit": "Submit", "active": "Set", "recover": "Reset", "firsttext": "First", "prevtext": "Prev", "nexttext": "Next", "lasttext": "Last", "jumptext": "Jump", "device": "<PERSON><PERSON>", "all": "All", "null": "<PERSON><PERSON>", "none": "None", "total": "A Total of", "signal": "Signal", "search": "Search", "NO": "NO.", "name": "Name", "alias": "<PERSON>as Name", "state": "State", "empty": "Empty", "delete": "Delete", "export": "Export", "delete_confirm": "Delete Confirm?", "data_loading": "Data Loading...", "data_importing": "Importing, please wait...", "update_loading": "Update...", "update_failure": "Update Failed", "update_success": "Update Success", "operate_overtime": "Operate Overtime.", "operate_successfully": "Operate Successfully", "default_pswd_tip": "The current password used belongs to the default password in the user manual, and there is a risk of leakage. It is recommended to modify it in a timely manner. The security risks caused by using the default password will be borne by the user themselves!", "no_record": "No Record", "no_dry_contact": "No Dry Contact", "no_channel_config": "No Channel Config", "operate_failure": "Operate Failure", "ziptype_failure": "Please select the file with the .zip suffix!", "tartype_failure": "Please select the file with the .tar.gz suffix!", "bintype_failure": "Please select the file with the .bin suffix!", "choose_file": "<PERSON><PERSON>", "choose_again": "Change again", "choose_file_tip": "Please choose File", "reboot_confirm": "Are You Sure to Reboot?", "reboot_waite": "Reboot....", "jumptologinpage": "Rebooting..., it will jump to login page", "later_try": "Please try again later.", "input_empty": "Input is empty.", "upload_file": "Upload File", "attr_name": "Parameter Name", "attr_value": "Parameter Value", "all_select": "Select All", "none_select": "Select Invert", "select_device": "Select Device", "select_spcu_device": "Select SPCU Device", "select_spu_device": "Select SPU Device", "select_sddu_device": "Select SDDU Device", "select_ssw_device": "Select SSW Device", "select": "Please Select", "no_connection_tip": "No connection. Data is not real-time!", "operate_reboot_confirm": "This operation may cause the system to restart. Please confirm whether to continue.", "encryption_password_export": "Export file encryption password:", "encryption_password_import": "Import file decryption password:", "pswd_empty": "Password is not allowed to be empty", "reboot_confirm_tips": "The setting is successful, it will take effect after reboot. Please confirm whether to reboot!", "check_input_tip": "'&/;' or space not allowed", "input_max_length": "Maximum Length", "server": "Server", "client": "Client", "no_displayable_signal": "No Displayable Signals", "no_displayable_data": "No Displayable Data", "control_cmd_success": "Control command send success", "abnormal_data_upload": "Abnormal data, please upload again!!!", "data_modified": "The server data has changed and will be queried again", "menu": {"site_overview": "Site Overview", "active_alarm": "Real Alarm", "devices_data": "Devices Data", "batter_data": "Batter Data", "para_config": "Parameter Config", "site_config": "Site Config", "south_net_para": "Southbound Network Parameters", "north_net_para": "Northbound Network Parameters", "north_protocol": "Northbound Protocol", "serial_port_config": "Serial Port Config", "ap_connection_config": "AP Config", "picture_para": "Picture Parameter", "ai_di_config": "AI/DI/DO Config", "dry_contact_para": "Dry Contact", "RADIUS_config": "RADIUS Config", "dry_contact_output_config": "Dry Contact Output Configuration", "plc_config": "PLC Config", "system_maintain": "System Maintain", "system_info": "System Information", "historical_record": "Historical Record", "site_video": "Site Video", "users_management": "Users Management", "data_classily": "Signal Show Config", "superuser": "Manufacturer User", "secauthuser": "Secondary Certification", "north_netmanage_connect_status": "NMS Connection Status", "north_protocol_name": "Protocol Name", "north_link_status": "Connection Status", "north_link_status0": "Connected", "north_link_status1": "Disconnected", "north_ip_name": "IP Address", "north_com_name": "COM. Name", "north_link_num": "Current Network Management Connection Numbers", "config_guide": "Config Guide"}, "info_scan": {"csu_version": "System Name", "cpu_rate": "CPU", "mem_rate": "Mem.", "Mask": "Mask", "Critical": "Critical", "Major": "Major", "Minor": "Minor", "Warning": "Warning"}, "env_scan_info": {"temp": "Temp", "humidity": "<PERSON><PERSON><PERSON><PERSON>"}, "battery_group_info": {"batt_manage_sta": "Battery Management Status"}, "config_guide": {"system_time": "System Time", "ac_para": "AC Parameter", "batt_para": "Battery Parameter", "net_para": "Northbound Network"}, "devlist": {"devices_data": "Device Information", "realdata": "Real Data", "paraset": "Parameter Set", "devcontrol": "Device Control", "analog_data": "Analog Data", "digital_data": "Digital Data", "alarm_data": "Alarm Parameter", "control_data": "Control Data", "parameter_data": "Parameter Data", "asset_data": "Asset Information", "signame": "Signal Name", "status": "Status", "value": "Value", "trace_info": "Trace Info.", "unit": "Unit", "value_convention": "Value Convention", "Data_refresh_interval": "Data Refresh Interval", "alarmname": "Name", "alarmlevel": "Level", "alarmdelay": "Delay", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "backlash": "Backlash", "alarmrelay": "<PERSON><PERSON>", "parametername": "Parameter Name", "name": "Name", "info": "Information", "period": "Period", "percent_threshold": "<PERSON><PERSON>", "absolute_threshold": "Absolute Threshold", "controlname": "Control Name", "devicename": "Device Name", "signalname": "Signal Name", "hisdata_para": "Storage Parameter", "range": "Range", "no_invalid_signal": " No Valid Signal", "device_list": "Device List", "inspect_param_set": "Adjust", "inspect_name": "Signal Name", "inspect_currvalue": "Current Value", "inspect_slop": "<PERSON>lop", "inspect_offset": "Offset", "deliver_spcu_para": "Deliver SPCU Parameters"}, "realalarm": {"active_alarm": "Real Alarm", "real_alarm": "Real Alarm", "name": "Alarm Name", "generated_time": "Generated Time", "level": "Alarm Level", "dry_contact_output": "Dry Contact Output"}, "history": {"history_inf": "Historical Record", "history_alarm": "Historical Alarm", "history_data": "Historical Data", "history_event": "Operate Record", "smr_update_event": "SMR Update Event", "export_historydata": "Export All History Data", "export_historyevent": "Export All Operate Record", "export_historyalarm": "Export All History Alarm", "time_range": "Time Range", "to": "To", "lines_per_page": "Lines Per. Page", "search": "Search", "num": "No.", "start_time": "Generated Time", "end_time": "Recover Time", "generate_time": "Generated Time", "alarm_name": "Alarm Name", "alarm_level": "Alarm Level", "device": "<PERSON><PERSON>", "signal": "Signal", "all": "All", "signal_name": "Signal Name", "signal_value": "Signal Value", "signal_unit": "Signal Unit", "statistic_records": "Event Record", "peak_record": "Peak Records", "record_starttime": "Start Tmie", "record_endtime": "End Time", "peak_type": "Peak Type", "operator": "Operator", "content": "Content", "event_type": "Event Type", "running_record": "Running Events", "oprate_record": "Operate Events", "can_device_update_record": "CAN Device Update Events", "com_device_update_record": "COM Device Update Events", "smr_device_update_record": "SMR Update Events", "uib_device_update_record": "UIB/IDDB Update Events", "safety_record": "Safety Events", "record_type": "Record Type", "select_type": "Select Type", "record_detail": "Record Details"}, "system": {"system_info": "System Information", "system_state": "System State", "dry_contact_state": "Dry Contact State", "dry_contact_control": "Dry Contact Control", "dry_contact_name": "Dry Contact Name", "control_operate": "Control Action", "control_state": "Control State", "buzzer_control": "Buzzer State", "outrelay_control": "Outrelay Control", "set_time": "Set Time", "timezone_select": "Timezone Select", "time_out_range": "Time is out of range.", "date_not_correct": "Invalid date or the date out of range", "synchro_local_time": "Synchro Local Time", "del_history_sql": "Delete History Record", "del_allsql": "All", "del_history_alarm": "History Alarm", "del_history_event": "Operate Record", "del_history_data": "History Data", "del_peak_record": "Peak Record", "sys_maintain": "System Maintain", "sys_update": "CSU Update", "sys_all_update": "CSU All Update", "uib_update": "UIB Update", "iddb_update": "IDDB Update", "fileempty": "File is empty", "upload_pack": "Upload Pack", "sys_reset": "System Reset", "device_reset": "<PERSON><PERSON>", "factory_reset": "Factory Reset", "default_reset": "<PERSON><PERSON>bly", "romove_his_record": "Delete Historical Records", "all_record": "All Records", "para_maintain": "Parameter Maintain", "para_export": "Parameter Export", "para_import": "Parameter Import", "download_para": "Export Parameter File", "export_config_file": "Export Config File", "upload_para": "Upload Parameter File", "upload_success": "Upload Success", "upload_failed": "Upload Failed", "import_success": "Import Success", "import_failed": "Import Failed", "export_success": "Export Complete", "download_page_tip": "Please Visit Transfer Webpage.", "export_failed": "Export Failed", "handling_export_later": "Processing, please do not refresh or close this page, please wait...", "factory_reset_now": "Factory reseting, please wait...", "default_reset_now": "Restore default forcibly, please wait...", "confirm_factory_reset": "Are you sure to factory reset?", "click_download": "Download", "slave_update": "CAN Device Update", "south_update": "South Device Update", "daughterboard_update": "Smart Board Update", "smr_update_control": "Update Config", "uploaded_files": "Uploaded files", "select_mode": "Select Mode", "select_type": "Select Type", "smr": "SMR", "smr_unlock_sn": "Unlock SN", "select_update_type": "Select Type", "select_update_model": "Select Model", "single_smr": "Single", "multi_smr": "Multi", "all_smr": "All", "smr_front": "SMR-Front", "smr_back": "SMR-Back", "smr_front_back": "Front-Back", "font_library": "Font-Library", "program": "Program", "smr_update_start": "Start Update", "smr_update_end": "Stop Update", "smr_update_state": "Update State", "state_not_start": "Not Start", "state_updating": "Doing", "state_update_success": "Success", "state_update_failed": "Failed", "state_update_log": "Update Logs", "para_bak_recover": "Parameter Bakup & Recover", "para_bak": "Parameter Bakup", "para_recover_influence": "It will restore parameter and config.", "para_recover_confirm": "Are You Sure to recover backup parameter?", "para_recover": "Recover Backup Parameter", "factory_reset_influence": "It will restore parameter.", "factory_reset_confirm": "Are You Sure to Factory Reset?", "default_reset_influence": "It will restore parameter and config.", "default_reset_confirm": "Are You Sure to <PERSON><PERSON> Forcibly?", "para_file": "Parameter File", "now_bak_file": "Current Bakup Parameter File", "recover_default_para_influence": "It will restore parameter.", "recover_default_para_confirm": "Are You Sure to recover default parameter?", "recover_default_para": "Recover De<PERSON><PERSON>", "export_all": "Export All Data", "export_eeprom": "Export EEPROM Data", "start_end_error_tip": "Wrong selection range, unable to upgrade", "start_end_error_tip_v2": "Wrong selection range, unable to stop upgrade", "device_maintain": "Devices Maintain", "ssl_upgrade": "SSL Update", "ssl_download": "SSL download", "sys_time": "System Time", "verify_para": "<PERSON><PERSON><PERSON>", "verify_config": "Verify configuration", "abnormal_data": "Abnormal Data", "download_abnormal_data": "Download Abnormal Data", "num": "No.", "cfgobj_id": "Configuration object", "cfginst_id": "Configuration instance", "para_name": "Parameter Name", "set_value": "set_value", "Info": "Information", "please_download_file": "Please download the file to view more information", "upgrade_file_trans_prog": "Upgrade file transfer progress", "spu_upgrade_prog": "SPU upgrade progress", "uploadsysall_file_tip": "Please select all_update.tar.gz or all_update_default.tar.gz!", "uploadsys_file_tip": "Please select powernew.tar.gz or powernewdefault.tar.gz!", "uploadssl_file_tip": "Please select sslnew.zip or sslnew.tar.gz!", "uploadsys_list_check_tips": "Packet type error. Please use right packet! Continue to upgrade?", "uploadsys_filesys_check_tips": "File sys change. Advise to use system software! Continue to upgrade?", "refresh_page_tip": "Please refresh the page!", "south_updata_tip": "When upgrading to a southbound device, the data displayed on the real-time data page may not be real-time"}, "seco_cert": {"get_seco_cert_enable_err": "Failed to obtain secondary certification enable", "modal_title": "Secondary Certification", "username": "Username", "password": "Password", "close": "Cancel", "confirm": "Confirm", "psw_err": "Incorrect Password"}, "eeprom": {"syn_csu_and_eeprom": "CSU unmatch EEPROM, please sync", "mac_address_change": "MAC ADDR. changed, inform net manager", "csu_and_eeprom_not_match": "CSU unmatch EEPROM", "eeprom_to_csu": "Import EEPROM to CSU", "csu_to_eeprom": "Save to EEPROM", "cancel_syn": "Cancel sync", "confirm": "Confirm", "eeprom_to_csu_control": "EEPROM Config Import CSU", "csu_to_eeprom_control": "CSU Config Synchronize To EEPROM", "others_confirmed_tip": "Others confirmed"}, "north_protocol": {"Wireless_SC": "Wireless SC Prot.", "Energy_SC": "Energy SC Prot.", "A_INTER": "A Interface Prot.", "B_INTER": "B Interface Prot.", "north_protocol": "North Protocol", "north_protocol_cfg": "Northbound Protocol", "snmp_manager_cfg": "SNMP Manager", "snmp_user_cfg": "SNMP UserInfo", "snmp_para_self": "SNMP Paras", "snmp_v3user": "V3 UserInfo", "snmp_para_trap": "SNMP Trap Paras", "snmp_trap": "Trap Paras", "mqtt_cfg": "MQTT Paras", "mqtt_pswd_len_tip": "The maximum length of MQTT Password is 32 characters!", "sps_config": "Sps. Config", "protocol_name": "Protocol Name", "link_type": "Link Type", "link_name": "Link Name", "link_encryption": "Link Encryption", "link_encryption_username": "Link Encryption username", "link_encryption_Passwd": "Link Encryption password", "ssh_enable": "SSH Enable", "ssh_username": "SSH Username", "ssh_password": "SSH Password", "ip_addr": "IP Addr.", "port": "Port", "protocol_set_tips": "Set north protocol before modify protocol paras if you want to change protocol.", "csu_role": "CSU Role", "data_port": "Data Port", "inform_port": "Inform Port", "listen_port": "Listen Port", "select_csu_role_tip": "CSU Role of IP Link", "modofy_link_para": "Modify Link Parameter", "link_COM": "COM. Link", "link_IP": "IP Link", "current_nms_status": "Current NMS Status", "snmp_v3_user_over_num": "The number of users has reached the upper limit and cannot be added.", "pswd_tip": "Insufficient password strength; please enter 8 or more digits, uppercase and lowercase letters, special characters", "pswd_toolong_tip": "Password is too long, password length should be less than 32 characters!!!", "pswd_tooshort_tip": "Password is too short, password must contain at least 8 characters!!!", "pswd_toolong_tip_v2": "Password is too long, password length should be less than 32 bytes!!!", "snmp_v3_sameusername": "The username is duplicate, please reset", "v3username_samewith_notifname": "The SNMP User Name is same with SNMP V3 Notification Name, please reset", "notifname_samewith_v3username": "The SNMP V3 Notification Name is same with SNMP User Name, please reset", "v3username_samewith_key": "The SNMP User Name is same with Key", "v3userkey_sameas_before": "The SNMP Key is same as before", "ssh_passwd_tip": "The maximum length of the SSH Password is 20 characters!", "config_para_tip": "The input is illegal, with a value range of ", "device_id_tip": "The input is illegal and should be a numeric string of no more than 14 characters", "ip_illegal_tip": "The IP address input is illegal, with a value range of 0.0.0.0 ~~ ***************", "b_inter_samedeviceid": "The monitoring point ID is the same, please reset it!!!"}, "northnet": {"north_net_para": "Northbound Network Parameters", "wired_connection": "Wired Connection", "wireless_connection": "Wireless Connection", "vpn_configuration": "VPN Configuration", "wireless_network_sig_info": "Wireless Network Information", "wireless_network_operator": "Network Operator", "wireless_module_status": "Modem Network Status", "wireless_sig_strlength": "Signal Strlength", "connection_status": "Connection Status", "pswd_len_tip": "The maximum length of Password and Backup Password is 32 characters!", "nerwork_dest": "Network Dest", "network_mask": "Network Mask", "net_para_config": "Network Parameters Configuration", "static_route_config": "Static Routes Configuration", "static_route_tip": "Static routing configuration cannot be set to 0.0.0.0", "ip_illegal_tip": "The IP address input is illegal, with a value range of 0.0.0.0 ~~ ***************", "netmask_illegal_tip": "The network mask input is illegal, with a value range of 0.0.0.0 ~~ ***************", "username_illegal_tip": "User name should be an English string between 0 and 31 in length", "pswd_illegal_tip": "The password should be an English string between 0 and 32 in length", "ipv6_illegal_tip": "IPv6 address input is illegal"}, "classify": {"data_classify": "Data Classify", "medium": "Medium", "hide_show": "Hide/Show"}, "com": {"serial_port_settings": "Serial Port Config"}, "dry_contact": {"channel_name": "Channel Name", "sid": "SID", "alarm_abnormal_status": "Alarm/Abnormal Status", "default_output_status": "Default Output Status", "alarm_level": "Alarm Level", "dry_contact_output": "Dry Contact Output"}, "radius": {"para": "Parameter", "file": "File", "ca_cert": "CA Cert", "client_cert": "Client Cert", "privite_key": "Private Key", "pswd_len_tip": "The maximum length of RADIUS Secret and Private Key Password is 32 characters!", "uploadcacert_file_tip": "Please select radius_rootca.pem!", "uploadclicert_file_tip": "Please select radius_user.pem!", "uploadprikey_file_tip": "Please select radius_userkey.pem!"}, "plc": {"wrong_alias": "Wrong Alias", "alias_too_long": "<PERSON><PERSON> is too long", "wrong_group_description": "wrong Group Description", "group_description_too_long": "Group description is too long", "no_correct_output": "No alarm or DO output", "too_much_output": "Too many alarms or DO output", "no_sen_para": "No PLC sentence", "data_empty": "Data Not Be Empty", "input_plc_desc": "Please Input PLC Description", "plc_config_import": "PLC Config Import", "plc_config_export": "PLC Config Export", "ok": "OK", "set": "Set", "reset": "Reset", "dry_contact": "Dry Contact", "no_alarm": "No Alarm", "alarm": "Alarm", "backlash": "Backlash", "type": "Type", "constant": "Constant", "register": "Register", "signal": "Signal", "plc_edittable": "PLC EditTable", "alter": "Alter", "add": "Add", "submit": "Submit", "cancel": "Cancel", "disabled": "Disabled", "enabled": "Enabled", "plc_desc": "PLC Desc.", "add_plc": "Add PLC", "delete": "Delete", "edit": "Edit", "output": "Output", "input_two": "Input 2", "operator": "Operator", "input_one": "Input 1", "NO": "NO.", "not_active": "NotActive", "active": "Active", "enable_state": "PLC Enable State", "PAR_note": "The PAR operator indicates that alarms of the same type of devices are connected in parallel, Input 1 can select any device as required", "output_register_check": "The output register is used repeatedly", "input_register_check": "The input register without output values is used", "PAR_check": "The PAR operator can only be used to calculate alarms", "EQ_check": "The = operators cannot be calculated with analog", "NE_check": "The ≠ operators cannot be calculated with analog", "para_maintain": "PLC Config Maintain"}, "ap": {"ap_name_empty": "AP Name can not be empty!", "ap_pswd_max_length": "The maximum length of AP Password is 32 characters!", "ap_pswd_min_length": "The minimum length of AP Password is 8 characters!"}, "user": {"usermanage": "Users Management", "adduser": "Add user", "alterpswd": "Alter Password", "oldpswd": "Old Password", "newpswd": "New Password", "newpswd2": "Confirm New Password", "addusername": "User Name", "adduserpsw1": "Password", "adduserpsw2": "Confirm Password ", "userlevel": "User Lever", "administrator": "Administrator", "general_user": "General User", "username": "User", "last_time": "Last Login Time", "is_admin": "User Level", "deleteuser": "Delete User", "yestag": "Yes", "notag": "No", "pswnotmatchalert": "Passwords do not match.", "insertrighttest": "Please enter a valid value.", "usernamehavespaces": "Please do not use spaces.", "usernumbermax": "The maximum number of users is reached.", "addfailedalert": "Add user failure.", "addsuccessalert": "Add user success.", "alterfailedalert": "Password change failure.", "altersuccessalert": "Password change success.", "delfailedalert": "Delete user failure.", "delsuccessalert": "Delete user success.", "confirmdeleteuser": "Delete user ", "confirmend": "?", "pswd_tip": "Password must contain uppercase and lowercase letters, numbers, and special characters", "max_login_wrong_times": "<PERSON> login wrong times", "gui_pswd_tip": "Enter four digit password", "sameuserandpwd": "The password contains either username or the inversion of username, please reset!", "username_toolong_tip": "Username is too long, Username length should be less than 64 characters!!!", "username_empty_tip": "Username can not be empty!"}, "common": {"submit": "Submit", "lost_auth": "Permission lost,", "login_again": "please login again."}, "login": {"plat_name": "Energy Management", "login_error": "Incorrect username or password", "empty": "Log In", "login": "<PERSON><PERSON>", "copyright": "© 2024 ZTE Corporation. All rights reserved", "pswd_reset": "Password forget?", "fill_username": "Please enter username", "fill_password": "Please enter password", "over_times_tip": "Your login errors have reached their maximum number. Please try again after 30 minutes", "error_tip_part1": "remaining attempts:", "maxuser": "The number of logged-in users reaches the maximum. Do you want to continue?", "error_tip_part2": "the account will then be locked!"}, "pswdReset": {"return": "Return", "find_password": "Retrieve Password", "dev_serial": "Serial Num.", "identify_code": "Identify Code", "time_valid": "Valid Time", "reset": "Reset", "check": "Check", "sn_input": "Input SN Code", "check_failed": "Check failed！", "user_name": "User Name", "user_name_input": "Please enter user name", "pswd_new": "New Password", "new_pswd_input": "Please enter new password", "pswd_confirm": "PassWord Confirm", "new_pswd_input_again": "Please enter password again", "alter": "Alter", "weak_pswd": "The Weak PassWord！", "different_pswd": "The PassWords Entered Are Different！", "alter_success": "PassWord Altered Successfully！", "user_invalid": "Invalid User", "pswd_reset_fail": "password reset failure, the account has been locked!", "tip": "Tip: please contact the manufacturer for the SN code."}, "plot": {"vol_input": "Vol. Input", "ele_input": "Curr. Input", "vol_output": "Vol. Output", "ele_output": "Curr. Output", "solar_pu_total_out_curr": "PU Tol. Out Curr.", "solar_pu_total_out_power": "PU Tol. Out Power"}, "trance": {"tranceinfo": "Trace Information", "inneralarm": "Real Inner Alarm", "his_inneralarm": "History Inner Alarm"}, "dev_maint": {"ssw_direct": "SSW (Direct Connection)", "ssw_sddu": "SDDU_SSW", "sddu": "SDDU", "fctl": "FCTL"}, "temp_humid_conf": {"tab_name": "Temperature & Humidity", "channel_name": "Channel Name", "temp_sensor": "Temperature Sensor", "humid_sensor": "<PERSON><PERSON><PERSON><PERSON>", "sid": "SID"}, "x1": "user1", "x2": "user2", "test1": "choose1", "test2": "choose2", "test3": "choose3", "test4": "choose4", "test5": "choose5", "test6": "choose6", "test7": "choose7"}