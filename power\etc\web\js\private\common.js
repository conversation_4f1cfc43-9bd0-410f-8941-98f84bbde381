if( top.location != self.location ) top.location.href = self.location;
if( window.history.replaceState ) window.history.replaceState( null, null, window.location.href );
var INPUT_EMPTY =  601;	// 输入为空
var TIME_OVER_MAX    =  602;	//时间超过最大值
var TIME_NOT_REG     =  603;    //时间格式不正确

var userName = request.getUsername();
var userLevel = request.getUserLevel();
var language = Cookies.get("lan");
var radius_status_com = Cookies.get("radius");
var defaultLang = '';
let modal_cmp_psw_fn = null;//模态框执行成功
let modal_confirm_succ_fn = null;//模态框确认按钮函数
var logo_flag = sessionStorage.getItem('logo_flag');//logo标志位

let WEB_SPECIFY_STR = "efe0247af5a30c63938edea768d2fdcfa8c4451ed211f9715996025335d37c36";//明文密码上送替换字符串

avalon.config({debug:false});
/**************正则表达式**************/
var REGEX_IPADDRESS			=	/^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/;
var REGEX_FORMATDATE		=	/^([1][7-9][0-9][0-9]|[2][0][0-9][0-9])(\-)([0][1-9]|[1][0-2]|[1-9])(\-)([0][1-9]|[1-2][0-9]|[3][0-1]|[1-9])(\s)([0-1][0-9]|[2][0-3]|[0-9])(:)([0-5][0-9]|[0-9])(:)([0-5][0-9]|[0-9])$/;

var mainvalue = avalon.define({
	$id:'container',
	menuactive:"",
	menulistactive:"",
	languagedata:[],
	systemtime:{"realtime":"","settime":""},

	business_obj_data:[],
	realdata_obj_data:[],
	paraset_obj_data:[],
	control_obj_data:[],
	version_obj_data:[],

	devsid_listvalue:[],

	username:userName,
	seco_username:userName,
	userlevel:userLevel,
	radius_status_com:radius_status_com,
	logo_flag: logo_flag,
	pagervalue:[],
	alarmlevelclass:["","label-important","label-warning","label-info",""],

	doconvention:{},

	paperattr : {id:"",listcom:{},getcom:{},counts:"",totalnum:"",currentpage:"",htmlvalue:""},
	tmpTotalNum:{},
	pagerattrall : [],
	papervalue:[],
	controlsuccess:"",
	devsidname:[],
	devsidtype:[],

	i18nchange:{},
	i18nkeyword:{},

	eeprom_comfirm:"0",   // 0:EEPROM配置不确认 1:EEPROM配置确认
	eeprom_select:"0",   // 0:3个选项 1:2个选项,去除EEPROM配置导入CSU
	eeprom_comfirm_flag:"0", // 0:未进行EEPROM配置确认 1:已进行EEPROM配置确认 2:取消同步,下次登录再确认
	apptest_model:"0",   // 0:非apptest模式 1:apptest模式
	eeprom_state:"1",    // 0:正常 1:异常

	changeLan:function(lan){
		request.setLanguage(lan);
		page = Cookies.get("page");
		if (typeof(page)=='undefined' || page=='') {
			page="login.html";
		} else {
			let pathname = location.pathname;
			let path_split = pathname.split("/");
			page = path_split[path_split.length - 1];
		}
		
		request.redirect(page, $("body").attr("id"));
	},

	gotopage: function(page, list) {
		
		set_cookie_with_path("tab_list", "");
		set_cookie_with_path("tab_list2", "");
		set_cookie_with_path("page", page);
			if(list ===undefined)
				set_cookie_with_path("pagelist", "");
			else
				set_cookie_with_path("pagelist", list);
		request.redirect(page);
	},
	getconventionlength : function(con) {
        var length = 0;
        if (con == '') {
        	return 0;
        }
        for(var i in con){
        	length++;
        }
        return length;
    }
});

/*以下部分为系统语言管理部分*/
var listLan = {data:{objectid:"plat.language_category",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:listLanguageNum};
request.addRequest([listLan]);

function handle_language(obj) {
    if(typeof(language) == "undefined"){
        defaultLang = "en_US.UTF-8";
        $("[i18n]").i18n({
            defaultLang: defaultLang,
            filePath: "/i18n/"
        });
    }
    else{
        defaultLang = Cookies.get("lan");
        $("[i18n]").i18n({
            defaultLang: defaultLang,
            filePath: "/i18n/"
        });
    }
}

function listLanguageNum(d,r){
	if (d.data.length == 0) {
		handle_language();
		return;
	}
	for(var i in d.data){
		var instidata = [];
		instidata.push(d.data[i]);
		var rq = {data:{objectid:"plat.language_category",type:"val_get",paranum:"1",paraval:JSON.stringify(instidata)},success:getLanguageData};
			request.addRequest([rq]);
	}

}

function getLanguageData(d,r){
	addvalue(mainvalue.languagedata,d.data,"instid");
	if(typeof(language) == "undefined"){
		if(d.data[0]["Default Language"] === "1"){
            set_cookie_with_path("lan", d.data[0]["Language Encode Info"]);
            language = d.data[0]["Language Encode Info"];
        }
	}
    handle_language();
}

//以下为获取业务对象操作函数
var BusinessObject = {
	GetRealdataObject:function(){
		var para = {"service":"1"};
		var Rq = {data:{objectid:"business_object",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_realdata_obj_succ};
		request.addRequest([Rq]);
		function get_realdata_obj_succ(d,r) {
			if (d.result == "ok") {
				mainvalue.realdata_obj_data = d.data;
			}
		}
	},
	GetParasetObject:function(){
		var para = {"service":"2"};
		var Rq = {data:{objectid:"business_object",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_paraset_obj_succ};
		request.addRequest([Rq]);
		function get_paraset_obj_succ(d,r) {
			if (d.result == "ok") {
				mainvalue.paraset_obj_data = d.data;
			}
		}
	},
	GetControlObject:function(){
		var para = {"service":"3"};
		var Rq = {data:{objectid:"business_object",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_control_obj_succ};
		request.addRequest([Rq]);
		function get_control_obj_succ(d,r) {
			if (d.result == "ok") {
				mainvalue.control_obj_data = d.data;
			}
		}
	},
	GetVersionObject:function(){
		var para = {"service":"4"};
		var Rq = {data:{objectid:"business_object",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_version_obj_succ};
		request.addRequest([Rq]);
		function get_version_obj_succ(d,r) {
			if (d.result == "ok") {
				mainvalue.version_obj_data = d.data;
			}
		}
	},
	GetAllObject:function(){
		var para = {"service":"0"};
		var Rq = {data:{objectid:"business_object",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_all_obj_succ};
		request.addRequest([Rq]);
		function get_all_obj_succ(d,r) {
			if (d.result == "ok") {
				mainvalue.realdata_obj_data.clear();
				mainvalue.paraset_obj_data.clear();
				mainvalue.control_obj_data.clear();
				mainvalue.version_obj_data.clear();
				for (var i in d.data) {
					if (d.data[i]['service'] == '1') {
						mainvalue.realdata_obj_data.push(d.data[i]);
					}else if(d.data[i]['service'] == '2'){
						mainvalue.paraset_obj_data.push(d.data[i]);
					}else if(d.data[i]['service'] == '3'){
						mainvalue.control_obj_data.push(d.data[i]);
					}else{
						mainvalue.version_obj_data.push(d.data[i]);
					}
				}
			}
		}
	},
}

BusinessObject.GetAllObject();

function changelist(obj, type){
	//  分别表示实时数据、参数设置、设备控制、设备信息
	switch (type * 1){
		case 1: {
			BusinessObject.GetRealdataObject();
			break;
		};
		case 2: {
			BusinessObject.GetParasetObject();
			break;
		};
		case 3: {
			BusinessObject.GetControlObject();
			break;
		};
		case 4: {
			BusinessObject.GetVersionObject();
			break;
		};
		default:
			break;
	}
    setTimeout(function(){
        if(!obj.parent().hasClass("active")&&obj.parent().find(".submenu").css("display")=="none"){
            $("#sidebar>.nav-list>.active>.submenu").slideUp(500);
            $("#sidebar>.nav-list>.active").addClass("open");
        }
    }, 0);
}

/*历史记录相关操作的公共函数*/
function seachdata(id){
	for(var i in mainvalue.pagerattrall){
		if(id === mainvalue.pagerattrall[i].id){
			mainvalue.pagerattrall[i].htmlvalue = "";
			if(mainvalue.pagerattrall[i].totalnum === "0"){
				var num = 0;
				var length = mainvalue.papervalue.length;
				for(var j = 0;j < length ; j++){
					if(mainvalue.papervalue[j-num].id === id){
						if(j === 0 ){
							mainvalue.papervalue.splice(j,1);
							num ++;
						}
						else{
							mainvalue.papervalue.splice(j-num,1);
							num ++;
						}
					}

				}
				hideDataInit();
				return;
			}
			mainvalue.pagerattrall[i].htmlvalue = "<xmp ms-widget=\"{is:'ms-pager',counts:@pagerattrall["+i+"].counts,totalnum:@pagerattrall["+i+"].totalnum,getlistvar:@pagerattrall["+i+"].getcom,pageid:@pagerattrall["+i+"].id}\"></xmp>";
			request.addRequest([{data:{objectid:mainvalue.pagerattrall[i].id,type:"val_get",paranum:"0",dataindex:i,paraval:JSON.stringify([mainvalue.pagerattrall[i].getcom])},success:getHisValue}]);
		}
	}

}

function getHisValue(d,r){
	var datalog = [];
	var indexnum = r.data.dataindex;
	var offset = JSON.parse(r.data.paraval)[0].offset;
	var counts = JSON.parse(r.data.paraval)[0].counts;
	if (typeof(d.datanum) =='undefined') {
		hideDataInit();
		return;
	}
	var num = JSON.parse(d.datanum);
	if(mainvalue.paperattr && parseInt(mainvalue.paperattr.totalnum) > counts && num < counts){
		alert(mainvalue.i18nkeyword.data_modified);
		doEventsearch();
	}
	if (num == 0) {
		hideDataInit();
		return;
	}
	for(var i in d.data)
	{
		var valuedata = d.data[i];
		if(num < counts && parseInt(offset) < counts){
			valuedata.index = parseInt(i) + 1;
		}
		else{
			// valuedata.index = parseInt(offset) + parseInt(i);
			var tabPage = Cookies.get("tab_list");
			if(tabPage && mainvalue.tmpTotalNum[tabPage]){
				mainvalue.paperattr.totalnum = mainvalue.tmpTotalNum[tabPage];
			}
			valuedata.index = mainvalue.paperattr.totalnum - parseInt(offset) + 1 + parseInt(i);
		}
		valuedata.id = r.data.objectid;
		datalog.push(valuedata);
	}
	num = 0;
	var length = mainvalue.papervalue.length;
	for(var j = 0;j < length ; j++){
		if(mainvalue.papervalue[j-num].id === r.data.objectid){
			if(j === 0 ){
				mainvalue.papervalue.splice(j,1);
				num ++;
			}
			else{
				mainvalue.papervalue.splice(j-num,1);
				num ++;
			}
		}

	}
	for(var m = datalog.length-1; m >= 0; m--){
		mainvalue.papervalue.push(datalog[m]);
	}
	hideDataInit();
}


/*获取系统DO的信息*/
function listDoInstid(){
	var Rq = {data:{objectid:"plat.do",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getDoAttr};
	request.addRequest([Rq]);
}


function getDoAttr(d,r){
	if(request.getDataResult(d) === "ok"){
		var Rq = {data:{objectid:"plat.do",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getDoConvention};
		request.addRequest([Rq]);
	}
}

function getDoConvention(d,r){
	if(request.getDataResult(d) === "ok"){
		var doctrlmode = [];
		for(var i in d.data){
			if(d.data[i]["Ctrl Mode"] === "1" || d.data[i]["Ctrl Mode"] === "3"){
				doctrlmode.push(d.data[i]);
			}
		}
		mainvalue.doconvention=transformArrayToObject(doctrlmode,"Channel NO.","Channel Name");
	}

}

/* 初始化获取logo标志位 */
function getLogoFlag() {
	var Rq = {data:{objectid:"logo_flag",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:saveLogoFlag};
	request.addRequest([Rq]);
}

/* 保存到sessionStorage中持久化存储 */
function saveLogoFlag(d,r) {
	if (d.result == "ok") {
		mainvalue.logo_flag = d.data[0]['logo_flag'];
		sessionStorage.setItem('logo_flag',d.data[0]['logo_flag']);
		setHtmlHead();
	}
}

/* 检查logo_flag是否为空 */
function checkLogoFlag() {
	if(mainvalue.logo_flag === null) {  // 如果sessionStorage没有存储logo_flag，重新初始化获取
		getLogoFlag();
	}
}

/* 当检测到与预期结果不符时，更新HTML页面的标题和图标 */
function setHtmlHead() {
	if(mainvalue.logo_flag == "0" && document.title != "Energy Management") {
		document.title = 'Energy Management';
		$("#logo_icon").attr("href","/page/assets/img/white.ico");
	}
	else if(mainvalue.logo_flag == "1" && document.title != "ZTE Energy Management") {
		document.title = 'ZTE Energy Management';
		$("#logo_icon").attr("href","/page/assets/img/star.ico");
	}
}

checkLogoFlag();
setHtmlHead();

mainvalue.$watch("controlsuccess", function(a) {
	if(mainvalue.controlsuccess ==="success"){
		$("#"+"setSuccessalart").fadeIn();
		setTimeout(function(){$("#"+"setSuccessalart").fadeOut();mainvalue.controlsuccess ="";}, 4000);
	}
	if(mainvalue.controlsuccess ==="failure"){
		$("#"+"setFailurealart").fadeIn();
		setTimeout(function(){$("#"+"setFailurealart").fadeOut();mainvalue.controlsuccess ="";}, 4000);
	}

});

SystemTimeTask = function() {
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:_gettimeHandler};
	var settimeRq = {data:{objectid:"system_time",type:"val_set",paranum:"1",paraval:JSON.stringify([{}])},success:_settimeHandler};
	var timesecond = {time:"", second:0};
	var bindid = "systime";
	var bchanged = false;
	var flag = 0;     //addOneSecond()函数只被调用一次
	var now_sys_time_str = "";

	function getSecondfromstr(_str) {
		var arr = _str.split(":");
		timesecond.time = arr[0] + ":" + arr[1] + ":";
		timesecond.second = parseInt(arr[2]);
	}

	function formattimetostr(_time) {
		var _year = _time.time.split(" ")[0].split("-")[0];
		var _month = _time.time.split(" ")[0].split("-")[1];
		var _day = _time.time.split(" ")[0].split("-")[2];
		var _hour = _time.time.split(" ")[1].split(":")[0];
		var _minute = _time.time.split(" ")[1].split(":")[1];

		return _year + "-" + (_month.length <= 1? "0" + _month: _month) + "-" + (_day.length <= 1? "0" + _day: _day) + " " + (_hour.length <= 1? "0" + _hour: _hour) + ":" + (_minute.length <= 1? "0" + _minute: _minute) + ":" + (_time.second < 10? "0" + _time.second: _time.second);
	}

	function addOneSecond() {
		setTimeout(_changetime, 1000);
	}

	function setTimeHTML() {
	    var time_str = formattimetostr(timesecond);
		$("#"+bindid).html(time_str);
		now_sys_time_str = time_str;
	}

	function _changetime() {
		timesecond.second ++;
		if(timesecond.second > 59) {
			flag = 0;
			request.addRequest([gettimeRq]);
		} else {
			setTimeHTML();
			addOneSecond();
		}
	}

	function _gettimeHandler(d, r) {
		if(request.getDataResult(d) == "ok") {
			if($("#"+bindid).length > 0) {
				getSecondfromstr(d.data[0].time);
				setTimeHTML();
				if(flag == 0){
					addOneSecond();
					flag = 1;
				}
			}
		}
	}

	function _settimeHandler(d, r) {
		if(request.getDataResult(d) == "ok") {
			getSecondfromstr(JSON.parse(r.data.paraval)[0].time);
			popupTipsDiv($("#time_successalert"), 1000);
			setTimeHTML();
		} else {
			popupTipsDiv($("#time_failurealert"), 1000);
		}
	}

	function _settime(timestr) {
		if (bchanged) {
			bchanged = false;
			$("#timeinput").val("");
			var time = timestr;
			var setsystime = new Date(time.replace(/-/g,"\/"));
			var maxsystime = new Date("2036-12-31 23:59:59".replace(/-/g,"\/"));
			var minsystime = new Date("1990-01-01 00:00:00".replace(/-/g,"\/"));
			if(setsystime>=maxsystime || setsystime<=minsystime)
			{
				$("#timeinput").val("");
				return TIME_OVER_MAX;
			}
			if(REGEX_FORMATDATE.test(timestr)){
				settimeRq.data.paraval = JSON.stringify([{"time":timestr}]);
				request.addRequest([settimeRq]);
			}
			else{
				$("#timeinput").val("");
				return TIME_NOT_REG;
			}
		}
	}

	return {
		start: function() {
			request.addRequest([gettimeRq]);
		},
		settime: function(timestr) {
			if (timestr=='') {
				return INPUT_EMPTY;
			}
			bchanged = true;
			return _settime(timestr);
		},
		synchro: function(){
				bchanged = true;
				_settime(pcTime());
		},
		getsystime:function() {
			return now_sys_time_str;
		}
	};
};

var systimeTask = new SystemTimeTask();
systimeTask.start();

function pcTime(){
	var date = new Date();
	var year = date.getFullYear();
	var month = date.getMonth()+1;
	var day = date.getDate();
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	var str = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day) + " " + (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
	return str;
}


avalon.component('ms-pager', {
    template: heredoc(function () {
					/*
					<ul class="pagination" ms-visible="1!==totalPages">
					    <li class="first"
					        ms-class='{disabled: @isDisabled("first", 1)}'>
					        <a ms-click='@cbProxy($event,"first")'>
					        {{@firsttext}}
					        </a>
					    </li>
					    <li class="prev"
					        ms-class='{disabled: @isDisabled("prev",1)}'>
					        <a ms-click='@cbProxy($event,"prev")'>
					        {{@prevtext}}
					        </a>
					    </li>
					    <li ms-for='page in @pages'
					        ms-class='{active: page === @currentPage}' >
					        <a ms-click='@cbProxy($event,page)'>
					        {{page}}
					        </a>
					    </li>
					    <li class="next"
					        ms-class='{disabled: @isDisabled("next",@totalPages)}'>
					        <a ms-click='@cbProxy($event,"next")'>
					        {{nexttext}}
					        </a>
					    </li>
					    <li class="last"
					        ms-class='{disabled: @isDisabled("last",@totalPages)}'>
					        <a ms-click='@cbProxy($event,"last")'>
					        {{lasttext}}
					        </a>
					    </li>
					    <li class="jump"
					        ms-class='{disabled: @isDisabled("first",1)}'>
					        <input type='text' class='form-control input-small' ms-duplex='jumppage'/>
					        <div style='display:inline'><a href='#' ms-click='@cbProxy($event,jumppage)'>{{jump}}</a></div>
					    </li>
					</ul>
					*/
    }),
    defaults: {
        getHref: function (href) {
            return href;
        },
        getTitle: function (title) {
            return title;
        },
        isDisabled : function (name, page) {
            return (this.$buttons[name] = (this.currentPage === page));
        },
        $buttons   : {},
        pageid:"",
        showPages: 5,
        pages: [],
        jumppage: 1,
        firsttext:'',
        prevtext:'',
        nexttext:'',
        lasttext:'',
        jump:'',
        totalPages: 15,
        totalnum:170,
        counts:10,
        currentPage: 1,
        getlistvar:{},
        onPageClick: avalon.noop,//让用户重写
        // cbProxy: avalon.noop, //待实现
        getHisList:function (p){
        	this.currentPage = p;
			var alarmSearch = this.getlistvar;
			// var pageoffset = this.totalnum -p*this.counts+1;
			// var pageoffset = (p==1)? this.totalnum :this.totalnum - (p-1)*this.counts+1;
			var pageoffset = p*this.counts;
			if(pageoffset<=0){
				alarmSearch.offset = "1";
				alarmSearch.counts = (this.totalnum -(p-1)*this.counts).toString();
			}
			else{
				// alarmSearch.offset = pageoffset.toString();
				if(pageoffset > this.totalnum){
					pageoffset = this.totalnum;
					// alarmSearch.counts = this.totalnum - (p-1)*this.counts;
					alarmSearch.counts = (this.totalnum - (p-1)*this.counts).toString();
				}else{
					alarmSearch.counts = this.counts;
				}
				alarmSearch.offset = pageoffset.toString();
			}
			var indexnum = 0;
			for(var i in mainvalue.pagerattrall){
				if(this.pageid === mainvalue.pagerattrall[i].id){
					indexnum = i;
				}
			}
			showDataInit();
			request.addRequest([{data:{objectid:this.pageid,type:"val_get",dataindex:indexnum,paranum:"0",paraval:JSON.stringify([alarmSearch])},success:getHisValue}]);
        },
        toPage     : function (p) {
            var cur = this.currentPage;
            var max = this.totalPages;
            switch (p) {
                case 'first':
                    return 1;
                case 'prev':
                    return Math.max(cur - 1, 1);/*从第一页开始*/
                case 'next':
                    return Math.min(cur + 1, max);
                case 'last':
                    return max;
                default:
                    return parseInt(p);
            }
        },
        cbProxy: function (e, p) {
		    if (this.$buttons[p] || p === this.currentPage) {
		        e.preventDefault();
		        return ;//disabled, active不会触发
		    }
		    var cur = this.toPage(p);
		    if((cur > this.totalPages) || (cur < 1) || isNaN(cur))
		    {
		    	alert(mainvalue.i18nkeyword.value_invalid);
		    	return;
		    }
		    this.getHisList(cur);
		    var obj = getPages.call(this, cur);
		    this.pages = obj.pages;
		    this.currentPage = obj.currentPage;
		    return this.onPageClick(e, p);
		},
        onInit: function (e) {
            var a = getPages.call(this, this.currentPage);
            this.pages = a.pages;
            this.currentPage = a.currentPage;
            this.totalPages = a.totalPages ;
        },
        onReady:function(e){
        	this.firsttext = mainvalue.i18nkeyword.firsttext;
        	this.prevtext = mainvalue.i18nkeyword.prevtext;
       		this.nexttext = mainvalue.i18nkeyword.nexttext;
        	this.lasttext = mainvalue.i18nkeyword.lasttext + "( "+this.totalPages+" )";
        	this.jump = mainvalue.i18nkeyword.jumptext;
        }
    }
});

function getPages(currentPage) {
    var pages = [];
    var pagelist = Math.ceil(this.totalnum/this.counts);
    var s = '';
    if(pagelist>5){
		s = 5;
    }
    else{
    	s = pagelist;
    }
   // var s = this.showPages
    //var total = this.totalPages
    var total = pagelist;
    var half = Math.floor(s / 2);
    var start = currentPage - half + 1 - s % 2;
    var end = currentPage + half;

    // handle boundary case
    if (start <= 0) {
        start = 1;
        end = s;
    }
    if (end > total) {
        start = total - s + 1;
        end = total;
    }

    var itPage = start;
    while (itPage <= end) {
        pages.push(itPage);
        itPage++;
    }

    return {currentPage: currentPage, pages: pages,totalPages:total};
}


//将时间信息转换成标准时间字符串信息
function timeToStr(date){
	var year = date.getFullYear();
	var month = date.getMonth()+1;
	var day = date.getDate();
	var hours = date.getHours();
	var minutes = date.getMinutes();
	var seconds = date.getSeconds();
	var str = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day)+" " + (hours < 10 ? "0" + hours : hours)+":" + (minutes < 10 ? "0" + minutes : minutes)+":" + (seconds < 10 ? "0" + seconds : seconds);
	return str;
}

//将html转换为字符串信息
function heredoc(fn) {
    return fn.toString().replace(/^[^\/]+\/\*!?\s?/, '').
            replace(/\*\/[^\/]+$/, '').trim().replace(/>\s*</g, '><');
}


//页面跳转
function gotopage(page, list) {
	set_cookie_with_path("tab_list", "");
	set_cookie_with_path("tab_list2", "");
	set_cookie_with_path("page", page);
	if(list ===undefined)
		set_cookie_with_path("pagelist", "");
	else
		set_cookie_with_path("pagelist", list);
	request.redirect(page);
}

//给VM对象添加或更新现象
function addvalue(object,addpara,key){
	var changesiddata = [];
	for(var paranum = 0; paranum<addpara.length;paranum++){
		var matching =0;
		if(key!==undefined){
			if(addpara[paranum].convention !==undefined){
				addpara[paranum].convention = transformToJson(addpara[paranum].convention);
			}
			for(var i = 0; i<object.length;i++)
			{
				if(object[i][key] == addpara[paranum][key]){
					matching = 1;
					var objectValueData = {};
					for(var num in object[i]){
						objectValueData[num] = object[i][num];
					}

					for(var j in addpara[paranum] ){
						objectValueData[j] = addpara[paranum][j];
					}
					object.splice(i,1);
					object.splice(i,0,objectValueData);
					changesiddata.push(addpara[paranum][key]);
					break;
				}
			}
		}
		if((matching ===0)&&(addpara[paranum][key]!==undefined)){
			object.push(addpara[paranum]);
			changesiddata.push(addpara[paranum][key]);
		}
	}
	return changesiddata;
}

function transformToJson(conventionvalue){
	if (typeof(conventionvalue) == 'undefined') {
		return;
	}
	if(conventionvalue !==""){
		var con = []
		//处理选项只有一个的情况
		if(!conventionvalue.includes(",")){
			con = [conventionvalue]
		}else{
			con = conventionvalue.split(",");
		}
		if(con.length < 1){
			return {};
		}
		var jsonstr="";
		for(var i in con)
		{
			var data=con[i].split(":");
			if((data[0] ===undefined)||(data[0] ===""))
				continue;
			jsonstr+= "\""+data[0]+"\""+":"+"\""+data[1]+"\"";
			if(i != con.length-1)
				jsonstr+=",";
		}
		if(jsonstr[jsonstr.length-1] ===",")
			jsonstr=jsonstr.substring(0,jsonstr.length-1);
		var outvalue = "{"+jsonstr+"}";
		con = JSON.parse(outvalue);
	    return con;
	    }
	else return {};
}

function transformArrayToObject(arr,key1,key2){
	var resultdata = {};
	for(var i in arr){
		resultdata[arr[i][key1].toString()] = arr[i][key2]!==undefined ? arr[i][key2].toString():"val_invalid";
	}
	return resultdata;
}

//将对象数组转化为字典,如[{"sid":"111","value":222}]->["111":{"sid":"111","value":222}]
function transformDataToDic(data, key) {
	var data_num = data.length;
	var dic = {};
	for (var i = 0; i < data_num; i++) {
		var newkey = data[i][key];
		var obj = clone(data[i]);
		dic[newkey]= obj;
	}
	//dic["length"] = data_num;
	return dic;
}


function getDevSidAll(d,r){
	var Rq = {data:{objectid:"device",type:"attr_get",paranum:"4",paraval:JSON.stringify(d.data)},success:getDevSidNameAndSn};
	request.addRequest([Rq]);
}

function getDevSidNameAndSn(d,r){
	var num = 0;
	var sidvalue = [];
	for(var i in d.data){
		//sidvalue[num] = d.data[i];
		mainvalue.devsid_listvalue.push(d.data[i]);
		num++;
	}
}

var devAttrRq = {
	list  : {data:{objectid:"analogdata",type:"list",paranum:"1",paraval:""},success:getDevAttrSid},
	attrget:{data:{objectid:"analogdata",type:"attr_get",paranum:"2",paraval:""},success:getSidData}
};
var deytpyelist=["","analogdata","digitaldata","alarmdata","controldata","parameterdata","factoryinfo","vesioninfo"];

function getDevListSid(sid,type){
	if(sid === ""){
		vmodel.relevancesid.sid = "" ;
		vmodel.relevancesid.devtype = "";
		vmodel.relevancesid.sigsid = "";
		return;
	}
	for(var i in type){
		devAttrRq.list.data.objectid = deytpyelist[type[i]];
		mainvalue.devsidtype.push(deytpyelist[type[i]]);
		devAttrRq.list.data.paraval = JSON.stringify([{"sid":sid.toString()}]);
	    request.addRequest([devAttrRq.list]);
	}
}

function getDevAttrSid(d,r){
	if((d.result !=="ok")||(d.datanum === 0))
	{
		for(var i in mainvalue.devsidtype){
			if(mainvalue.devsidtype[i] === d.objectid){
				mainvalue.devsidtype.splice(i,1);
				break;
			}
		}
		return;
	}
    var receiveData = JSON.stringify(d.data);
    devAttrRq.attrget.data.paraval = receiveData;
    devAttrRq.attrget.data.objectid = d.objectid;
    request.addRequest([devAttrRq.attrget]);
}

function getSidData(d,r){
	addvalue(mainvalue.devsidname,d.data,"sid");
	for(var i in mainvalue.devsidtype){
		if(mainvalue.devsidtype[i] === d.objectid){
			mainvalue.devsidtype.splice(i,1);
			break;
		}
	}
	if(mainvalue.devsidtype.length <1){
		var sid = BigInt('0x'+vmodel.comvalue.SID);
		var siddata = calSidToDevSid(sid);
		if(siddata !== vmodel.relevancesid.devtype){
			vmodel.relevancesid.sid = "" ;
			vmodel.relevancesid.sigsid = "";
		}
		else{
			vmodel.relevancesid.sid = sid ;
			vmodel.relevancesid.devtype = siddata;
			vmodel.relevancesid.sigsid = sid;
		}

	}
}

if(Cookies.get("pagelist")!==""){
	mainvalue.menuactive = Cookies.get("pagelist");
	mainvalue.menulistactive = Cookies.get("page");
}
else{
	mainvalue.menuactive = Cookies.get("page");
	mainvalue.menulistactive = Cookies.get("pagelist");
}

function channelSlide(obj){
	var id = $(obj).closest(".widget-box").attr("id");
	if($("#" + id + ">.widget-body").css("display")=="none"){
		$(obj).find(".widget-toolbar>a>i").attr("class", "icon-chevron-up");
		//$(obj).find(".widget-toolbar>a>").html("<i class='icon-chevron-up'></i>");
		$("#" + id + ">.widget-header>.lighter").addClass("fontbold");
		$("#" + id + ">.widget-body").slideDown(200);
		$("#" + id + " Button").css("display", "");

	}
	else{
		$(obj).find(".widget-toolbar>a>i").attr("class", "icon-chevron-down");
		//$(obj).find(".widget-toolbar>a>").html("<i class='icon-chevron-down'></i>");
		$("#" + id + ">.widget-header>.lighter").removeClass("fontbold");
		$("#" + id + ">.widget-body").slideUp(200);
		$("#" + id + " Button").css("display", "none");
	}
}

request.addMainRequest([]);

function get_dev_list_all() {
	var deviceparaval = JSON.stringify([{"sid":"0"}]);
	var getdevinfoRq = {data:{objectid:"device",type:"list",paranum:"0",paraval:deviceparaval},success:getDevSidAll};
	request.addRequest([getdevinfoRq]);
}

/************按钮点击前后*****************/
function addDisabled(obj){
	obj.addClass("disabled");
	obj.attr("disabled", true);
}
function removeDisabled(obj){
	obj.removeClass("disabled");
	obj.attr("disabled", false);
}

//通过SID计算sid 包含的详细信息
function calSidToDevType(sid){
	sid = BigInt(sid);
	var sidData = {"devType":0,"devSn":0,"sigType":0,"devSigVar":0,"devSigIndex":0};
	sidData.devType = Number(sid/BigInt(281474976710656));
	if(sidData.devType>0){
		sidData.devSn = Number((sid%BigInt(281474976710656))/BigInt(68719476736));
		sidData.sigType = Number((sid%BigInt(68719476736))/BigInt(268435456));
		sidData.devSigVar = Number((sid%BigInt(268435456))/BigInt(65536));
		sidData.devSigIndex = Number(sid%BigInt(65536));
	}
	return sidData;
}

//计算sid
function calSid(devtype, dev_sn, sig_type, sig_var, sig_index) {
	var _devtype = devtype || 0;
	var _devsn   = dev_sn  || 0;
	var _sigtype = sig_type || 0;
	var _sigvar  = sig_var  || 0;
	var _sigindex = sig_index || 0;
	var rslt = new BigNumber(_devtype);
	rslt = rslt.multipliedBy(new BigNumber('281474976710656'));
	rslt = rslt.plus(new BigNumber(_devsn*68719476736));
	rslt = rslt.plus(new BigNumber(_sigtype*268435456))
	rslt = rslt.plus(new BigNumber(_sigvar*65536));
	rslt = rslt.plus(new BigNumber(_sigindex));
	return rslt.toString();
}

//通过SID计算出设备SID
function calSidToDevSid(sid){
	sid = BigInt(sid);
	var sidData = calSidToDevType(sid);
	var devsid = BigInt(sidData.devType) * 281474976710656n + BigInt(sidData.devSn) * 68719476736n;
	return devsid;
}

//  判断一个sid是否显示设备和信号维度
function judge_specail_sid_show(sid) {
	sid = BigInt(sid);
    var sigtype = calSidToDevType(sid);
    // 输入干接点状态、输入干接点告警、PLC自定义状态、PLC自定义告警、扩展温度无效、扩展温度高、扩展温度低、电池温度无效
    var specail_sid = [0x1001020020001, 0x1001030030001, 0x2001020060001, 0x2001030060001,
                       0x9001030110001, 0x9001030120001, 0x9001030130001,0xb001030040001];
    for (let i in specail_sid) {
        var specail_sigtype = calSidToDevType(BigInt(specail_sid[i]));
        if (sigtype.devType == specail_sigtype.devType && sigtype.sigType == specail_sigtype.sigType && sigtype.devSigVar == specail_sigtype.devSigVar) {
            return true;
        }
    }
    return false;
}

//  判断一个sid是否为输入干接点告警
function judge_specail_sid(sid) {
	sid = BigInt(sid);
	var sigtype = calSidToDevType(sid);
	if (sigtype.devType == "1" && sigtype.devSn == "1"
	&& ((sigtype.sigType == "3" && sigtype.devSigVar == "3") || (sigtype.sigType == "2" && sigtype.devSigVar == "2"))  // 输入干接点告警或状态
	){
		return true;
	}
	return false;
}

//  判断一个sid是否为输入干接点状态
function judge_inner_status_sid(sid) {
	sid = BigInt(sid);
	var sigtype = calSidToDevType(sid);
	if (sigtype.devType == "1" && sigtype.devSn == "1"
	&& ((sigtype.sigType == "2" && sigtype.devSigVar == "2"))  // 输入干接点状态
	){
		return true;
	}
	return false;
}

// 判断一个sid是否为整流器解锁
function judge_smr_unlock_sid(sid) {
	sid = BigInt(sid);
	var sigtype = calSidToDevType(sid);
	if (sigtype.devType === 7 && sigtype.sigType === 4 && sigtype.devSigVar === 12) {  // 整流器解锁
		return true;
	}
	return false;
}

function onlyNumber(obj){
	var pos = obj.selectionEnd;
	var value = obj.value ;
	//得到第一个字符是否为负号
	var t = obj.value.charAt(0);
	//先把非数字的都替换掉，除了数字和.
	obj.value = obj.value.replace(/[^\d\.]/g,'');
	//必须保证第一个为数字而不是.
	obj.value = obj.value.replace(/^\./g,'');
	//保证只有出现一个.而没有多个.
	obj.value = obj.value.replace(/\.{2,}/g,'.');
	//保证.只出现一次，而不能出现两次以上
	obj.value = obj.value.replace('.','$#$').replace(/\./g,'').replace('$#$','.');
	//如果第一位是负号，则允许添加
	if(t == '-'){
		obj.value = '-'+obj.value;
	}
	if(value === obj.value)
		obj.setSelectionRange(pos,pos);
	else
		obj.setSelectionRange(pos,pos-1);
}

/****************提示信息显示*********************/
AlertTask = function() {
	var alertID;

	function _hide() {
		$("#"+alertID).fadeOut();
	}

	return {
		show: function(_id) {
			alertID = _id;
			$("#"+alertID).fadeIn();
			setTimeout(_hide, 4000);
		},
		stay: function(_id) {
			alertID = _id;
			$("#"+alertID).fadeIn();
		},
		dismiss: function(_id) {
			alertID = _id;
			_hide();
		}
	};
};

/*obj:弹出的提示框对象*/
/*time:显示时间*/
/*author:肖胜贤*/
function popupTipsDiv(obj, time) {
	obj.show();
	obj.delay(time).hide(0);
}

/****************页面活动监听*********************/
WatchTask = function() {
	var mouseHasMoved = false;
	if (window.hasOwnProperty('SharedWorker')) {
		var worker = new SharedWorker("/js/private/worker.js");
		worker.port.start();

		worker.port.onmessage = function(evt){
			if (evt.data == "timeout") {
				deleteSessionById();
			}
			if ((evt.data == "ifmousemove")) {
				if (mouseHasMoved === true) {
					worker.port.postMessage("mousemoved")
					mouseHasMoved = false;
				} else {
					worker.port.postMessage("iftimeout");
				}
			}
		}
	};
	return {
		start: function(){
			$(document).ready(function(){
				$("body").mousemove(function() {
					if (mouseHasMoved === false) {
						mouseHasMoved = true;
					}
			  });
		  });
		},
		reset: function() {
			if (window.hasOwnProperty('SharedWorker')) {
				worker.port.postMessage("reset");
			}
		},
		pause: function() {
			if (window.hasOwnProperty('SharedWorker')) {
				worker.port.postMessage("pause");
			}
		}
	};
};

function setCookieTime(key,value,time){
	var expiresDate= new Date();
	expiresDate.setTime(expiresDate.getTime() + (time * 60 * 1000));
	Cookies.set(key, value, {
  			path : '/power',    // 系统默认路径
  			expires : expiresDate
 	});
}

var watchTask = new WatchTask();
watchTask.start();

function isContains(str, substr) {
    return new RegExp(substr).test(str);
}

function tr_toggle(obj) {
	var span = 0;
	var img_src= ['/page/assets/img/plus.gif','/page/assets/img/minus.gif'];
	var parent = $(obj).parent().parent()[0];
	if(isContains(obj.src,img_src[0])) {
		span = 1;
	} else{
		span = 0;
	}
	$(parent).siblings('.child_'+parent.id).toggle();
	obj.src = img_src[span];
}

function doLogout() {
	var logoutPara = JSON.stringify([{}]); // 无需额外参数
	var logoutRq = {data:{objectid:"login",type:"inst_delete",paranum:"0",paraval:logoutPara},success:logout_success};
	request.addRequest([logoutRq]);
}

function deleteSessionById() {
	var type = "byid";
	var para = [{"type":type}];
	var deleteSessionByIdPara = JSON.stringify(para);
	var deleteSessionByIdRq = {data:{objectid:"login",type:"attr_set",paraval:deleteSessionByIdPara,success:logout_success}};
	request.addRequest([deleteSessionByIdRq]);
}

function logout_success(d,r){
	request.logout();
	gotopage('login.html');
}

/*****************下载任务处理*******************/
DownloadTask = function() {
	var _expRq =
	[
		{data:{objectid:"paramaintain",type:"attr_get",paraval:JSON.stringify([{}])},refresh:3,success:_downloadTaskHandler},
		{data:{objectid:"export_hisdata",type:"attr_get",paraval:JSON.stringify([{}])},refresh:1,success:_downloadTaskHandler},
		{data:{objectid:"export_hisevent",type:"attr_get",paraval:JSON.stringify([{}])},refresh:1,success:_downloadTaskHandler},
		{data:{objectid:"export_hisalarm",type:"attr_get",paraval:JSON.stringify([{}])},refresh:1,success:_downloadTaskHandler},
		{data:{objectid:"configmaintain",type:"attr_get",paraval:JSON.stringify([{}])},refresh:3,success:_downloadTaskHandler},
		{data:{objectid:"record_export",type:"attr_get",paraval:JSON.stringify([{}])},refresh:3,success:_downloadTaskHandler},
		{data:{objectid:"export_all",type:"attr_get",paraval:JSON.stringify([{}])},refresh:3,success:_downloadTaskHandler}
	];
	var _downloadCompleteHandler;

	function _hasDownloadTask(n) {
		if(Cookies.get("dl") == n) {
			_setDownloadTask(n);
			return true;
		}
		return false;
	}

	function _setDownloadTask(n,para) {
		if(Cookies.get("dl") == undefined) {
			Cookies.get("dl", n);
		}
		if (_expRq[n].data.objectid == "record_export") {
			_expRq[n].data.paraval = JSON.stringify([para]);
		}
		setTimeout(function(){request.addRequest([_expRq[n]]);}, 3000);
	}

	function _downloadTaskHandler(d, r) {
		if (d.result == "ok" && d.datanum > 0) {
			request.clearRequest(r);
			if(_downloadCompleteHandler != undefined) {
					_downloadCompleteHandler(true, d.data[0].filename, d.data[0].fileurl);
			} else {
					alert(mainvalue.i18nkeyword.system.export_success+","+mainvalue.i18nkeyword.system.download_page_tip);
			}
		} else {
			_clearDownloadTask();
			alert(mainvalue.i18nkeyword.system.export_failed+","+mainvalue.i18nkeyword.later_try);
			if(_downloadCompleteHandler != undefined) {
				_downloadCompleteHandler(false, "", "");
			}
		}
	}

	function _clearDownloadTask() {
		Cookies.remove("dl");
	}

	return {
		checkAllDownloadTask: function() {
			_hasDownloadTask(0);
			_hasDownloadTask(1);
		},

		hasDownloadTask: function(type, callback) {
			_downloadCompleteHandler = callback;
			return _hasDownloadTask(type);
		},
		//type:0-记录;1-参数
		setDownloadTask: function(type, callback, para) {
			_downloadCompleteHandler = callback;
			_setDownloadTask(type, para);
		},

		clearDownloadTask: function() {
			_clearDownloadTask();
		}
	};
};

var downloadTask = new DownloadTask();


function refresh_value(src_data, new_data) {
    for (var j in new_data) {
        for (var i in src_data) {
            if (src_data[i].sid == new_data[j].sid) {
                src_data[i].value = new_data[j].value;
            }
        }
    }
}

function storeTotalNum(){
	var tabPage = Cookies.get("tab_list");
	if(tabPage){
		mainvalue.tmpTotalNum[tabPage] = mainvalue.paperattr.totalnum;
	}
}

/*页面覆盖的层*/
function showDataInit(){
	watchTask.pause();
	$("#pageLock").show();
	$("#dataInit").show();
	$("#dataInit").css("position", "fixed");
	$("#dataInit").css("top", "50vh");
	$("#dataInit").css("right", "50%");
}

function hideDataInit(){
	watchTask.reset();
	$("#pageLock").hide();
	$("#dataInit").hide();
}

function showDataImport(){
	watchTask.pause();
	$("#pageLock").show();
	$("#dataImport").show();
	$("#dataImport").css("position", "fixed");
	$("#dataImport").css("top", "50vh");
	$("#dataImport").css("right", "50%");
}

function hideDataImport(){
	watchTask.reset();
	$("#pageLock").hide();
	$("#dataImport").hide();
}

// UT8编码计算占字符长度
function getStrLeng_UTF8(str) {
	var totalLength = 0;
	var charCode = -1;
	for (var i = 0; i < str.length; i++) {
		charCode = str.charCodeAt(i);
		if (charCode <= parseInt("0x7F")) {
			totalLength += 1;
		} else if (charCode <= parseInt("0x7FF")) {
			totalLength += 2;
		} else if (charCode <= parseInt("0xFFFF")) {
			totalLength += 3;
		} else if (charCode <= parseInt("0x1FFFFF")) {
			totalLength += 4;
		} else if (charCode <= parseInt("0x3FFFFFF")) {
			totalLength += 5;
		} else {
			totalLength += 6;
		}
	}
	return totalLength;
}

// 密码框铭文密文切换
function change_password_show(_id) {
	($("#"+_id).attr('type') == "password")? $("#"+_id).attr("type","text"):$("#"+_id).attr("type","password");
}

function pswd_strong_check_commom(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

// 认证模态框确认
function confirmSecauth(){
	let psw = $("#modal-input").val();
	let req = {data:{objectid:"seco_cert",type:"val_get",paranum:"0",paraval:JSON.stringify([{username:Cookies.get("user"), req:"password", pswd:$.sha256(psw)}])},success:modal_cmp_psw_fn};
	$("#modal-input").val("");
	$('#appModal').modal('hide');

	request.addRequest([req]);
}

function clearPswdSecauth(){
	$("#modal-input").val("");
	$('#appModal').modal('hide');
}

// 认证模态框：密码对比
function modal_cmp_psw_succ(d,r){
	if((d.result !="ok")){
		popupTipsDiv($("#upsysall_get_enable_err"), 1000);
		return;
	}
	if(d.data[0].cmp_result == "ok"){
		myCode =  "func(para);";
		var initFunc = function (interpreter, scope) {
			interpreter.setProperty(scope, 'para', String(location));
			var wrapper = function(){
				return modal_confirm_succ_fn();
			}
			interpreter.setProperty(scope, "func", interpreter.createNativeFunction(wrapper));
		};
		var myInterpreter = new Interpreter(myCode, initFunc);
		myInterpreter.run();

	}else{
		alert(mainvalue.i18nkeyword.seco_cert.psw_err);
	}
}

function pswd_strong_check_commom(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

// 校验ip地址（主机ip/网络掩码）是否合法
function checkIPAddress(index,ipstr) {
    var min_ip_len = 7;
	var max_ip_len = 15;
	var max_ip_section1 = index ? 255 : 223; // ip第一段的最大值

    if((ipstr.length > max_ip_len) || (ipstr.length < min_ip_len)) {
        return false;
    }

    var ip_list = ipstr.split('.');

    if(ip_list.length != 4) {
        return false;
	}
	
	// 主机ip范围：0.0.0.0 ~ ***************
	// 网络掩码范围：0.0.0.0 ~ ***************
    for(var i in ip_list) {
        var num = ip_list[i];
        if(i == 0) {
			// 考虑如果ip带有非数字，如num为“1a”非纯数字字符串的情况
            if(!(num >= 0) || num > max_ip_section1) {
                return false;
            }
        } else {
            if(!(num >= 0) || num > 255) {
                return false;
            }
        }
    }

    return true;
}

function set_cookie_with_path(key, value){
	Cookies.set(key, value, {
		"path":"/power"
	})
}

function check_paswd_include_username_rev(username, pswd){
	// 密码不能和用户名或者用户名的逆序相同
	var username_reverse = username.split('').reverse().join('');
	if(pswd.includes(username) || pswd.includes(username_reverse)) {
		return false;
	}
	return true;
}

// 获取EEPROM读写状态，用于判断环境是否有EEPROM
var Rq = {data:{objectid:"eeprom_control",type:"attr_get",paraval:JSON.stringify([{}])},success:get_eeprom_state_succ};
request.addRequest([Rq]);

function get_eeprom_state_succ(d,r) {
	if (d.result === "ok") {
		mainvalue.eeprom_state = d.data[0]['eeprom_state'];
	}
}