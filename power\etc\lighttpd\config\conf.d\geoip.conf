#######################################################################
##
##  GeoIP Module 
## --------------- 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModGeoip
##
## mod_geoip is a module for fast ip/location lookups. It uses MaxMind
## GeoIP / GeoCity databases. If the ip was found in the database the
## module sets the appropriate environments variables to the request,
## thus making other modules/fcgi be informed.
##
server.modules += ( "mod_geoip" )

##
## mod_geoip will determine the database type automatically so if you
## enter GeoCity databse path it will load GeoCity Env.
##
#geoip.db-filename = "/path/to/GeoCityLite.dat"

##
## If enabled, mod_geoip will load the database binary file to memory
## for very fast lookups. The only penalty is memory usage.
##
#geoip.memory-cache = "disable"

##
#######################################################################
