#!/bin/sh

# udhcpc script edited by <PERSON> <<EMAIL>>

[ -z "$1" ] && echo "Error: should be called from udhcpc" && exit 1

RESOLV_CONF="/etc/resolv.conf"
[ -n "$broadcast" ] && BROADCAST="broadcast $broadcast"
[ -n "$subnet" ] && NETMASK="netmask $subnet"

case "$1" in
	deconfig)
		#/sbin/ifconfig $interface 0.0.0.0
		;;

	renew|bound)
		/sbin/ifconfig $interface $ip $BROADCAST $NETMASK

 
		#if [ -n "$router" ] ; then
		#	while route del default gw 0.0.0.0 dev $interface 2>/dev/null ; do
		#		:
		#	done
                # 
		#	metric=0
		#	for i in $router ; do
		#		route add default gw $i dev $interface metric $((metric++))
		#                echo add route default i:$i if:$interface >> /var/run/test		
		#	done
		#fi

		#echo -n > $RESOLV_CONF
		#[ -n "$domain" ] && echo search $domain >> $RESOLV_CONF
		#for i in $dns ; do
		#	echo adding dns $i
		#	echo nameserver $i >> $RESOLV_CONF
		#done
		;;
esac

exit 0
