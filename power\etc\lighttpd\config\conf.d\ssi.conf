#######################################################################
##
##  Server Side Includes
## -----------------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModSSI
##
server.modules += ( "mod_ssi" )

##
## which extensions should be ran through mod_ssi.
##
ssi.extension              = ( ".shtml" ,".html")

##
## The  ssi.conditional-requests  directive only affects requests
## handled by the SSI module and allows to declare which SSI pages
## are cacheable and which are not. This directive can be enabled
## or disabled globally and/or in any context.
##
## As the name of this directive suggests, conditional requests will
## be handled appropriately for any SSI page for which the directive
## is enabled. In particular, the "ETag" and "Last-Modified" headers
## will both be sent. Conversely, these headers will NOT be sent for
## pages for which the directive is disabled.
##
## The directive should be set to "enable" ONLY for requests that are
## known to generate cacheable documents. An SSI page which only
## includes contents from other static files and/or which uses SSI
## commands that produce predictable output (e.g. the echo command
## for the LAST_MODIFIED variable) is likely to be cacheable.
##
## The directive should be set to "disable" for ALL other documents,
## that is, for SSI pages which depend on non-predictable input such
## as (but not limited to) output from ssi exec commands, data from
## the client's request headers (other than the request URI), or any
## other non constant input such as the current date or time, the
## client's user-agent, etc...
##
## Disabled by default.
##
## For further explanation of conditional requests, please see
## Hypertext Transfer Protocol (HTTP/1.1): Conditional Requests
## https://tools.ietf.org/html/rfc7232
##
ssi.conditional-requests = "disable"

##
#######################################################################
