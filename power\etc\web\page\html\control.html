 <!DOCTYPE html>
<html>
    <head>
		<!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        
        <div class="main-container container-fluid" ms-controller="control_info">
			<!--#include virtual="/page/html/prompt.html" -->
			<!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.devlist.devcontrol}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                        <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
							<i class="icon-warning-sign  icon-animated-bell">
							</i>
							{{i18nkeyword.operate_successfully}}
						</div>
						<div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
							<i class="icon-warning-sign  icon-animated-bell">
							</i>
							{{i18nkeyword.operate_failure}}
						</div>
						<!--PAGE CONTENT BEGINS-->
						<div class="tab-content">
							<div id="devcontrol" class="tab-pane active"   style="height:600px">
								<div ms-if="control_infos.length != 0">
									<div  ms-for="(j,devinfo) in control_infos" class="panel panel-default">
										<div class="panel-heading">
											<h4 class="panel-title">
												<a class="accordion-toggle">
													{{control_infos[j][0]['device name']}}
												</a>
											</h4>
										</div>

										<div class="panel-collapse collapse in" >
											<div class="panel-body">
											   <table class="table table-striped table-bordered">
													<tbody id="control_body">
														<tr ms-for="(i,control) in control_infos[j]" ms-if="i%2===0">
															<td class="center" style="width:45%">
																<button class="btn btn-xs btn-info center" ms-click="setDevValue(@control_infos[j][i].sid)">
																	<span class="bigger-110">{{control_infos[j][i].full_name}}</span>
																</button></td>
															<td class="center" style="width:45%">
																<button class="btn btn-xs btn-info center" ms-click="setDevValue(@control_infos[j][i+1].sid)" ms-if="@control_infos[j][i+1].full_name!='undefined'" >
																	<span class="bigger-110">{{control_infos[j][i+1].full_name}}</span>
																</button></td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
								<div ms-if="control_infos.length == 0">
									{{i18nkeyword.no_displayable_data}}
								</div>
							</div>
						</div>
						<!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/control.js"></script>
    </body>

</html>