 <!DOCTYPE html>
<html>
	<head>
		<!--#include virtual="/page/html/include.html" -->
	</head>

	<body class="navbar-fixed ms-controller" ms-controller="container">
		<!--#include virtual="/page/html/prompt.html" -->
		<!--#include virtual="/page/html/header.html" -->

		<div class="main-container container-fluid" ms-controller="history">
			<!--#include virtual="/page/html/menu.html" -->
			<div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.history.history_inf}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
				<div class="page-content">
					<div class="row-fluid">
						<div class="span10">
							<!--PAGE CONTENT BEGINS-->
							<div class="tabbable">
								<ul class="nav nav-tabs" id="myTab">
									<li ms-class="'hisalarm' == tab_list?'active':''" ms-click="tabChange('hisalarm')">
										<a data-toggle="tab" href="#hisalarm">
											<i class="blue icon-warning-sign bigger-110">
											</i>
											{{i18nkeyword.history.history_alarm}}
										</a>
									</li>
									<li ms-class="'hisdata' == tab_list?'active':''" ms-click="tabChange('hisdata')">
										<a data-toggle="tab" href="#hisdata">
											<i class="blue icon-th-list bigger-110">
											</i>
											{{i18nkeyword.history.history_data}}
										</a>
									</li>
									<li ms-class="'hisevent' == tab_list?'active':''" ms-click="tabChange('hisevent')">
										<a data-toggle="tab" href="#hisevent" ms-if="userlevel > '1'">
											<i class="blue icon-pencil bigger-110">
											</i>
											{{i18nkeyword.history.history_event}}
										</a>
									</li>
									<li ms-class="'statistic_record' == tab_list?'active':''" ms-click="tabChange('statistic_record')">
										<a data-toggle="tab" href="#statistic_record" ms-if="userlevel > '1'">
											<i class="blue icon-signal bigger-110">
											</i>
											{{i18nkeyword.history.statistic_records}}
										</a>
									</li>
								</ul>
								<div class="tab-content">
									<div id="hisalarm" class="tab-pane" ms-class="'hisalarm' == tab_list?'active':''">
										<table class="table">
											<tbody>
												<tr>
													<td>
														{{i18nkeyword.history.time_range}}
														<input id="alarm_starttext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyalarm_time.start_time" onfocus="laydate.render({elem:'#alarm_starttext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:'1970-01-01 00:00:00',max:$('#alarm_endtext').val(),ready:function(){$('#alarm_starttext')[0].eventHandler = false;},done:function(){$('#alarm_starttext').focus();$('#alarm_starttext').blur();}})" onblur="checkDateTime(this)"/>
														{{i18nkeyword.history.to}}
														<input id="alarm_endtext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyalarm_time.end_time" onfocus="laydate.render({elem:'#alarm_endtext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:$('#alarm_starttext').val(),ready:function(){$('#alarm_endtext')[0].eventHandler = false;},done:function(){$('#alarm_endtext').focus();$('#alarm_endtext').blur();}})" onblur="checkDateTime(this)"/>
														{{i18nkeyword.history.lines_per_page}}
														<select ms-duplex="@historyalarm_counts" class="input-mini" id="alarmPerPage">
															<option value="10">10</option>
															<option value="20">20</option>
															<option value="100">100</option>
														</select>&nbsp;&nbsp;&nbsp;&nbsp;
														<button id="alarm_searchBT" class="button button-small button-flat-primary" onClick="doAlarmsearch()">
														{{i18nkeyword.history.search}}
														</button>
													</td>
												</tr>
												<tr>
													<td>
														<div style="float:right">
															<div style="float:left;display:none" id="dlhisalarm_url"  class="alert alert-block alert-success">
															</div>
															<label style="display:inline;">
																<input type="checkbox" name="export_hisalarm_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
																{{i18nkeyword.encryption_password_export}}
																<input id="export_hisalarm_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="export_hisalarm_passwd">
															</label>
															<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_hisalarm_input')"><i class="icon-eye-open" ></i></span>
															<button id="export_hisalarm" class="button button-small button-flat-primary" onClick="doDL_hisalarm()">
															{{i18nkeyword.history.export_historyalarm}}
															</button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
										<table class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.history.num}}</th>
													<th>{{i18nkeyword.history.start_time}}</th>
													<th>{{i18nkeyword.history.end_time}}</th>
													<th>{{i18nkeyword.history.alarm_name}}</th>
													<th>{{i18nkeyword.history.alarm_level}}</th>
												</tr>
											</thead>
											<tbody id ="alarm_list">
												<tr ms-for="hisvalue in papervalue" ms-if="hisvalue.id == 'history_alarm'">
													<td>{{hisvalue.index}}</td>
													<td>{{hisvalue.start_time}}</td>
													<td>{{hisvalue.end_time}}</td>
													<td>{{hisvalue.full_name}}</td>
													<td>
													<center><span class="label arrowed arrowed-in arrowed-in-right" ms-class="@alarmlevelclass[hisvalue.level]">
														{{@hisvalue.level_name}}</span>
													</center>
													</td>
												</tr>
											</tbody>
										</table>
										<div class="hr hr8 hr-double"></div>
										<div class="pagination" style="cursor:pointer;" ms-for="(i,paper) in pagerattrall" ms-if="pagerattrall[i].id=='history_alarm'" ms-html="pagerattrall[i].htmlvalue"></div>
									</div>
									<div id="hisdata" class="tab-pane" ms-class="'hisdata' == tab_list?'active':''">
										<table class="table table-bordered">
                                            <tbody>
												<tr>
													<td>
														{{i18nkeyword.history.device}}
														<select class="editable input-medium" ms-duplex="historydata_selectdevsid"  style="width:165px" id="selecthisdata">
															<option value = "0">{{i18nkeyword.history.all}}</option>
															<option ms-for="sidnum in devsid_listvalue" ms-attr="{value:sidnum.sid}">{{sidnum["device name"]}}</option>
														</select>
													</td>
													<td>
														{{i18nkeyword.history.time_range}}
														<input id="data_starttext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historydata_time.start_time" onfocus="laydate.render({elem:'#data_starttext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:'1970-01-01 00:00:00',max:$('#data_endtext').val(),ready:function(){$('#data_starttext')[0].eventHandler = false;},done:function(){$('#data_starttext').focus();$('#data_starttext').blur();}})" onblur="checkDateTime(this)"/>
														{{i18nkeyword.history.to}}
														<input id="data_endtext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historydata_time.end_time" onfocus="laydate.render({elem:'#data_endtext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:$('#data_starttext').val(),ready:function(){$('#data_endtext')[0].eventHandler = false;},done:function(){$('#data_endtext').focus();$('#data_endtext').blur();}})" onblur="checkDateTime(this)"/>
													</td>
												</tr>
												<tr>
													<td>
														{{i18nkeyword.history.signal}}
														<select class="editable input-medium" ms-duplex="historydata_selectsigsid" style="width:165px" id="selecthissigdata">
															<option value = "0" >{{i18nkeyword.history.all}}</option>
															<option ms-for="sidnum in historydata_sigsidvalue" ms-attr="{value:sidnum.sid}">{{sidnum["full_name"]}}</option>
														</select>
													</td>
													<td>
														{{i18nkeyword.history.lines_per_page}}
														<select ms-duplex="@historydata_counts" class="input-mini" id="dataPerPage">
															<option value="10">10</option>
															<option value="20">20</option>
															<option value="100">100</option>
														</select>&nbsp;&nbsp;&nbsp;&nbsp;
														<button id="alarm_searchBT" class="button button-small button-flat-primary" onClick="doHisDatasearch()">
														{{i18nkeyword.history.search}}
														</button>
														<div style="float:right; margin-top: 4px;">
															<div style="float:left;display:none" id="dlhisdata_url"  class="alert alert-block alert-success">
															</div>
															<label style="display:inline;">
																<input type="checkbox" name="export_hisdata_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
																{{i18nkeyword.encryption_password_export}}
																<input id="export_hisdata_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="export_hisdata_passwd">
															</label>
															<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_hisdata_input')"><i class="icon-eye-open" ></i></span>
															<button id="export_hisdata" class="button button-small button-flat-primary" onClick="doDL_hisdata()">
															{{i18nkeyword.history.export_historydata}}
															</button>
														</div>
													</td>
												</tr>
                                            </tbody>
                                        </table>
										<table class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.history.num}}</th>
													<th>{{i18nkeyword.history.start_time}}</th>
													<th>{{i18nkeyword.history.signal_name}}</th>
													<th>{{i18nkeyword.history.signal_value}}</th>
													<th>{{i18nkeyword.history.signal_unit}}</th>
												</tr>
											</thead>
											<tbody id ="alarm_list">
												<tr ms-for="hisvalue in papervalue" ms-if="hisvalue.id == 'history_data'">
													<td>{{hisvalue.index}}</td>
													<td>{{hisvalue.save_time}}</td>
													<td>{{hisvalue.full_name}}</td>
													<td>{{hisvalue.value}}</td>
													<td>{{hisvalue.unit}}</td>
												</tr>
											</tbody>
										</table>
										<div class="hr hr8 hr-double"></div>
										<div class="pagination" style="cursor:pointer;" ms-for="(i,paper) in pagerattrall" ms-if="pagerattrall[i].id=='history_data'" ms-html="pagerattrall[i].htmlvalue"></div>
									</div>
									<div id="hisevent" class="tab-pane" ms-class="'hisevent' == tab_list?'active':''">
										<table class="table">
											<tbody>
												<tr>
													<td>
														{{i18nkeyword.history.time_range}}
														<input id="event_starttext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyevent_time.start_time" onfocus="laydate.render({elem:'#event_starttext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:'1970-01-01 00:00:00',max:$('#event_endtext').val(),ready:function(){$('#event_starttext')[0].eventHandler = false;},done:function(){$('#event_starttext').focus();$('#event_starttext').blur();}})" onblur="checkDateTime(this)"/>
														{{i18nkeyword.history.to}}
														<input id="event_endtext" autocomplete="off" class="form-control input-medium" type="text" ms-duplex="@historyevent_time.end_time" onfocus="laydate.render({elem:'#event_endtext',lang:'en',type:'datetime',format:'yyyy-MM-dd HH:mm:ss',theme:'#62a8d1',min:$('#event_starttext').val(),ready:function(){$('#event_endtext')[0].eventHandler = false;},done:function(){$('#event_endtext').focus();$('#event_endtext').blur();}})" onblur="checkDateTime(this)"/>
														{{i18nkeyword.history.event_type}}
														<select ms-duplex="@historyevent_type" class="input-medium" id="hiseventtype" style="width: 160px">
															<option value="0">{{i18nkeyword.history.all}}</option>
															<option value="100">{{i18nkeyword.history.running_record}}</option>
															<option value="200">{{i18nkeyword.history.oprate_record}}</option>
															<option value="201">{{i18nkeyword.history.can_device_update_record}}</option>
															<option value="204">{{i18nkeyword.history.com_device_update_record}}</option>
															<option value="203">{{i18nkeyword.history.uib_device_update_record}}</option>
															<option value="300">{{i18nkeyword.history.safety_record}}</option>
														</select>&nbsp;&nbsp;&nbsp;&nbsp;
														{{i18nkeyword.history.lines_per_page}}
														<select ms-duplex="@historyevent_counts" class="input-mini" id="eventPerPage">
															<option value="10">10</option>
															<option value="20">20</option>
															<option value="100">100</option>
														</select>&nbsp;&nbsp;&nbsp;&nbsp;
														<button id="event_searchBT" class="button button-small button-flat-primary" onClick="doEventsearch()">
														{{i18nkeyword.history.search}}
														</button>
													</td>
												</tr>
												<tr>
													<td colspan="2">
														<div style="float:right">
															<div style="float:left;display:none" id="dlhisevent_url"  class="alert alert-block alert-success">
															</div>
															<label style="display:inline;">
																<input type="checkbox" name="export_hisevent_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
																{{i18nkeyword.encryption_password_export}}
																<input id="export_hisevent_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="export_hisevent_passwd">
															</label>
															<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_hisevent_input')"><i class="icon-eye-open" ></i></span>
															<button id="export_hisevent" class="button button-small button-flat-primary" onClick="doDL_hisevent()">
															{{i18nkeyword.history.export_historyevent}}
															</button>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
										<table class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.history.num}}</th>
													<th>{{i18nkeyword.history.generate_time}}</th>
													<th>{{i18nkeyword.history.operator}}</th>
													<th>{{i18nkeyword.history.content}}</th>
												</tr>
											</thead>
											<tbody id ="alarm_list">
												<tr ms-for="hisvalue in papervalue" ms-if="hisvalue.id == 'history_event'">
													<td>{{hisvalue.index}}</td>
													<td>{{hisvalue.save_time}}</td>
													<td>{{hisvalue.operator}}</td>
													<td style="white-space:pre;">{{hisvalue.value}}</td>
												</tr>
											</tbody>
										</table>
										<div class="hr hr8 hr-double"></div>
										<div class="pagination" style="cursor:pointer;" ms-for="(i,paper) in pagerattrall" ms-if="pagerattrall[i].id=='history_event'" ms-html="pagerattrall[i].htmlvalue"></div>
									</div><!--历史事件-->
									<div id="statistic_record" class="tab-pane" ms-class="'statistic_record' == tab_list?'active':''">
										<div class="widget-box span6">
											<div class="widget-header">
												<tr>
													<div>
														{{i18nkeyword.history.record_type}}
														<select id="recordlistselect" class = "input-medium" ms-duplex="@statistic_records_type">
															<option value="-1" >{{i18nkeyword.history.select_type}}</option>
															<!--ms-for:(i,type) in statistic_record_types-->
																<option ms-attr="{'value':type.id}" ms-if="type.id!='1'">{{type.name}}</option>
															<!--ms-for-end:-->
														</select>
														<button id="records_searchBT" class="button button-small button-flat-primary" onClick="doRecordsearch()">
															{{i18nkeyword.history.search}}
														</button>
													</div>
												</tr>
												<tr>
													<div style="float:right;">
														<label style="display:inline;">
															<input type="checkbox" name="record_export_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
															{{i18nkeyword.encryption_password_export}}
															<input id="record_export_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="record_export_passwd">
														</label>
														<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('record_export_input')"><i class="icon-eye-open" ></i></span>
														<button id="record_export" class="button button-small button-flat-primary" style="margin-left:20px" onClick="doDL_report()">
															{{i18nkeyword.export}}
														</button>
													</div>
												</tr>
												<div style="clear:both;"></div>
											</div>

											<div class="widget-body" style="overflow-y:auto; height:530px;">
												<div class="widget-main" id="query_record">
													<div id="recordlist" class="content"  ms-if = "is_statistic_empty == 0">
														<!--ms-for:(i,record) in statistic_indexs-->
															<a ms-attr="{'id':'records'+(statistic_indexs.length - i)}" href="#" class="dd2-content" ms-click="record_show(statistic_indexs.length - i -1)">
																<p class="pull-left">{{statistic_indexs.length - i}}# [{{statistic_indexs[statistic_records.length - i -1]}}]</p>
															</a>
														<!--ms-for-end:-->
													</div>
													<div id="recordlist" class="content" style="text-align:center" ms-if = "is_statistic_empty == 1">
														{{i18nkeyword.no_record}}
													</div>
												</div>
												<div id="export_record">
													<div style="float:left;display:none" id="dlreport_url"  class="alert alert-block alert-success">
													</div>
												</div>
											</div>
										</div>
										<div class="span6 widget-container-span" id = "record_detail" style="display:none">
											<div class="widget-box">
												<div class="widget-header dark">
													<h6 id="eventcontentname">{{i18nkeyword.history.record_detail}}:{{record_details_scan}}</h6>
												</div>
												<div class="widget-body"style="overflow-y:auto; height:530px;">
													<div class="widget-main padding-4">
														<div id="eventcontent" class="content">
																<div class="dd2-content dark" id="startTime">
																<p class="pull-left">{{i18nkeyword.history.record_starttime}}</p>
																<p class="pull-right">{{record_details.start_time}}</p>
																</div>
																<div class="dd2-content dark" id="endTime">
																<p class="pull-left">{{i18nkeyword.history.record_endtime}}</p>
																<p class="pull-right">{{record_details.end_time}}</p>
																</div>
																<!--ms-for:(i,data) in record_details.records-->
																<div class="dd2-content dark">
																<p class="pull-left">{{data.name}}</p>
																<p class="pull-right">{{data.value}}</p>
																<div style="clear:both;"></div>
																</div>
																<!--ms-for-end:-->
														</div>
													</div>
												</div>
											</div>
										</div>
									</div><!--统计记录-->
								</div>
							</div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
						</div><!--/span10-->
					</div><!--/row-fluid-->
				</div><!--/page-content-->
			</div><!--/main-contain-->
		</div><!--/main-container-->
		<!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/history.js"></script>
		<script src="/js/opensrc/laydate/laydate.js"></script>
	</body>

</html>