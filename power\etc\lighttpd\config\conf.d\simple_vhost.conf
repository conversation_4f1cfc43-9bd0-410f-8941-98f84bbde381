#######################################################################
##
##  Simple Virtual hosting 
## ------------------------ 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModSimpleVhost
##
server.modules += ( "mod_simple_vhost" )

##  If you want name-based virtual hosting add the next three settings and load
##  mod_simple_vhost
##
## document-root =
##   virtual-server-root + virtual-server-default-host + virtual-server-docroot
## or
##   virtual-server-root + http-host + virtual-server-docroot
##
simple-vhost.server-root   = vhosts_dir + "/"
simple-vhost.default-host  = "default.example.com"
simple-vhost.document-root = "/htdocs/"

##
## Print some errors for finding the document-root
##
#simple-vhost.debug = "enable"

##
#######################################################################
