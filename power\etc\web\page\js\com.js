var vmodel = avalon.define({
	$id:'comdemo',
	comvalue:{},//当前选择的串口的Value信息
	selectedid:"",//当前选择的实例ID信息
	selectedindex:1,
	comvalues:[],     //得到的串口数据
	comstructure:[],  //串口对象结构信息	
});

var com_info = {
	GET:function(paraval,paranum) {
		var Rq = {data:{objectid:"plat.com",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},paranum:paranum,success:getcomvalue_succ};
		request.addRequest([Rq]);
		
		//回调函数
		function getcomvalue_succ(d,r){
			//addvalue(vmodel.comvalues,d.data,"instid");
			var data_use = [];
			for (var i in d.data) {
				if(/^plat\.COM\d+$/.test(d.data[i].instid) && d.data[i].instid != "plat.COM0")
					{
						data_use.push(d.data[i]);
					}
				}
			vmodel.comvalues = data_use;
			if (vmodel.selectedid == "") {
				vmodel.comvalue = vmodel.comvalues[0];
				vmodel.selectedid =data_use[0].instid;
			} else {
				for (var i in vmodel.comvalues) {
					if (vmodel.comvalues[i].instid == vmodel.selectedid) {
						vmodel.comvalue = vmodel.comvalues[i];
						vmodel.selectedid =vmodel.comvalues[i].instid;
						break;
					}
				}
			}
		}
	},
	HEAD:function(paraval,paranum) {
		var Rq = {data:{objectid:"plat.com",type:"attr_get",paranum:"1",paraval:JSON.stringify(paraval)},paranum:paranum,success:getcomhead_succ};
		request.addRequest([Rq]);

		//回调函数
		function getcomhead_succ(d,r) {
			addvalue(vmodel.comstructure,d.data,"name");
		}
	},
	PUT:function(paraval,paranum) {
		var self = this;
		var Rq = {data:{objectid:"plat.com",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)},paranum:paranum,success:putcomval_succ};
		request.addRequest([Rq]);
		
		//回调函数
		function putcomval_succ(d,r) {
			if(d.result ==="ok"){
				mainvalue.controlsuccess ="success";
			}
			else{
				mainvalue.controlsuccess ="failure";
			}
			//var para = {"instid":vmodel.selectedid};
			self.GET([],0);
		}
	}
};

com_info.HEAD([],0);
com_info.GET([],0);

function com_value_set(obj) {
	com_info.PUT([vmodel.comvalue], 1);
}

vmodel.$watch("selectedid", function(a) {
	for(var i in vmodel.comvalues){
		if(vmodel.comvalues[i].instid === vmodel.selectedid){
			vmodel.comvalue = vmodel.comvalues[i];
			break;
		}
	}
});

