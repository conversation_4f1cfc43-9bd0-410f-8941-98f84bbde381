﻿<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" >
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="userdemo">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.user.usermanage}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span9">
                            <div id="pswnotmatchalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.pswnotmatchalert}}
                            </div>
                            <div id="insertrighttest" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.insertrighttest}}
                            </div>
                            <div id="alterfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.alterfailedalert}}
                            </div>
                            <div id="altersuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.user.altersuccessalert}}
                            </div>
                            <div id="opfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                 {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="opsuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                 {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="sameuserandpwd" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.sameuserandpwd}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li class="active">
                                        <a data-toggle="tab" href="#web_tab">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            Web
                                        </a>
                                    </li>
                                </ul>

                                <div class="tab-content">
                                    <div class="tab-pane active">
                                        <table  ms-if="userlevel === '2'" class="table table-striped table-bordered table-hover" id="alterusertable">
                                            <thead>
                                                <tr class="info">
                                                    <th>{{i18nkeyword.user.alterpswd}}</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                    <tr class="">
                                                        <td id="usernameinfo">{{userdata[0].username}}</td>
                                                        <td></td>
                                                    </tr>
                                                    <tr class="">
                                                        <td ><!--[if IE]>新密码：<![endif]-->
                                                            <input id="alterpsw" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.newpswd,'title':i18nkeyword.user.pswd_tip}" class="form-control input-medium" style="width: 160px;" onchange="check_alter_pswd_strength(this)" oninput="check_alter_pswd_strength(this)">
                                                            </input>
                                                            <div id="pw_level2"  class="pw-strength" style="display:none">
                                                                <div class="pw-bar"></div>
                                                                <div class="pw-bar-on"></div>
                                                                <div class="pw-txt">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td ><!--[if IE]>确认新密码：<![endif]-->
                                                            <input id="alterpsw2" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.newpswd2}"  class="form-control input-medium" style="width: 160px;">
                                                            </input>
                                                        </td>
                                                    </tr>
                                                    <tr class="">
                                                        <td ><button class="button button-small button-flat-primary" onClick="alterwebuser()" id="alteruserbt">{{i18nkeyword.submit}}</button></td>
                                                        <td></td>
                                                    </tr>
                                            </tbody>
                                        </table> <!--修改web密码table-->
                                    </div>
                                </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span9-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <link rel="stylesheet" href="/page//html/plug_pswd/css/plug_pswd.css" />
        <script src="/page/js/superuser.js"></script>
        <script src="/page//html/plug_pswd/js/plug_pswd.js"></script>
    </body>
</html>
