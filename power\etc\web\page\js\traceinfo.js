//   当前选择的业务对象
var  traceinfo_vmodel = avalon.define({
	$id:'traceinfo',
	traceinfos:[],
	tab_list:'0',
	tab_change:function(tab){
		traceinfo_vmodel.tab_list = tab;
		set_cookie_with_path("tab_list", traceinfo_vmodel.tab_list);
	}
});

function get_traceinfos(time) {
	var	 req = {data:{objectid:"traceinfo",type:"val_get",paraval:JSON.stringify([{}])},refresh:time,success:get_traceinfos_succ};
	request.clearRequest(req);
	request.addRequest([req]);

	function get_traceinfos_succ(d,r){
		if(d.result == "ok") {
			traceinfo_vmodel.traceinfos = d.data;
        } else {
            traceinfo_vmodel.traceinfos = [];
        }
	}
}

//改变数据刷新时间
function change_trace_fleshtime(time) {
	get_traceinfos(time);
}

get_traceinfos(10);

function init_tab_select(tab_list) {
	var tab = Cookies.get("tab_list");
	if (tab == "" || typeof(tab) == 'undefined') {
		set_cookie_with_path("tab_list", tab_list);
		return tab_list;
	} else {
		return tab;
	}
}

traceinfo_vmodel.tab_list = init_tab_select(traceinfo_vmodel.tab_list);
