var is_strong_pswd = -1;   // -1无密码， 0：密码长度不足, 1:弱 2：中，3：强
var is_level_show = false;
var alter_strong_pswd = -1;
var alter_level_show = false;
function check_pswd_strength(obj) {
	var strongRegex = new RegExp(/^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^`{|}~]).*$/, "g");
	var mediumRegex = new RegExp("^(?=.{7,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))).*$", "g");
	var enoughRegex = new RegExp("(?=.{6,}).*", "g");
	if($(obj).val() == ""){
		$("#pw_level").css("display", "none");
	} else {
		$("#pw_level").css("display", "block");
	}
	if (false == enoughRegex.test($(obj).val())) {
		$('#pw_level').removeClass('pw-weak');
		$('#pw_level').removeClass('pw-medium');
		$('#pw_level').removeClass('pw-strong');
		$('#pw_level').addClass('pw-defule');
		is_strong_pswd = 0;
	}
	else if (strongRegex.test($(obj).val())) {
		$('#pw_level').removeClass('pw-weak');
		$('#pw_level').removeClass('pw-medium');
		$('#pw_level').removeClass('pw-strong');
		$('#pw_level').addClass('pw-strong');
		is_strong_pswd = 3;
	}
	else if (mediumRegex.test($(obj).val())) {
		$('#pw_level').removeClass('pw-weak');
		$('#pw_level').removeClass('pw-medium');
		$('#pw_level').removeClass('pw-strong');
		$('#pw_level').addClass(' pw-medium');
		is_strong_pswd = 2;
	}
	else {
		$('#pw_level').removeClass('pw-weak');
		$('#pw_level').removeClass('pw-medium');
		$('#pw_level').removeClass('pw-strong');
		$('#pw_level').addClass('pw-weak');
		is_strong_pswd = 1;
	}
	return true;
}
function check_alter_pswd_strength(obj) {
	var strongRegex = new RegExp(/^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^`{|}~]).*$/, "g");
	var mediumRegex = new RegExp("^(?=.{7,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))).*$", "g");
	var enoughRegex = new RegExp("(?=.{6,}).*", "g");
	if($(obj).val() == ""){
		$("#pw_level2").css("display", "none");
	} else {
		$("#pw_level2").css("display", "block");
	}
	if (false == enoughRegex.test($(obj).val())) {
		$('#pw_level2').removeClass('pw-weak');
		$('#pw_level2').removeClass('pw-medium');
		$('#pw_level2').removeClass('pw-strong');
		$('#pw_level2').addClass('pw-defule');
		alter_strong_pswd = 0;
	}
	else if (strongRegex.test($(obj).val())) {
		$('#pw_level2').removeClass('pw-weak');
		$('#pw_level2').removeClass('pw-medium');
		$('#pw_level2').removeClass('pw-strong');
		$('#pw_level2').addClass('pw-strong');
		alter_strong_pswd = 3;
	}
	else if (mediumRegex.test($(obj).val())) {
		$('#pw_level2').removeClass('pw-weak');
		$('#pw_level2').removeClass('pw-medium');
		$('#pw_level2').removeClass('pw-strong');
		$('#pw_level2').addClass(' pw-medium');
		alter_strong_pswd = 2;
	}
	else {
		$('#pw_level2').removeClass('pw-weak');
		$('#pw_level2').removeClass('pw-medium');
		$('#pw_level2').removeClass('pw-strong');
		$('#pw_level2').addClass('pw-weak');
		alter_strong_pswd = 1;
	}
	return true;
}