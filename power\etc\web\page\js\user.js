var vmodel = avalon.define({
	$id:'userdemo',
	userlevels:["None", "General User", "Administrator"],
	userlevels_dic:{},
	userdata:[], //用户管理数据结构
	delete_user_event:deluser,
	loginpara_val:[],     //登录参数值
	loginpara_attr:[],    //登录参数结构
	radius_status:0,
	delete_flag:'0',      //删除按钮显示
});

//   当前选择的登录模式
	vmodel.radius_status = Cookies.get("radius");

//   获取用户等级
function get_user_level(obj) {
	var req = {data:{objectid:"userinfo",type:"attr_get",paraval:JSON.stringify([{}])},success:get_level_succ};
	request.addRequest([req]);

	function get_level_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.userlevels = d.data;
			vmodel.userlevels_dic = transformDataToDic(d.data, "level");
		}
	}
}
get_user_level();

// 定义用户信息类
function userinfo(username, pswd, level, logintime) {
	this.username  = username;
	this.pswd = pswd;
	this.userlevel = level;
	this.logintime = logintime;
}
//获取用户信息
var get_userinfo = {data:{objectid:"userinfo",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getUserInfo};
request.addRequest([get_userinfo]);
function getUserInfo(d,r){
	if((d.result ==="ok")&&(d.datanum >0)){
		vmodel.userdata = d.data;
		vmodel.delete_flag = "0";
		for (var i in d.data) {
			if (d.data[i].del_no != '1' && d.data[i].userlevel < '3' && d.data[i].username != mainvalue.username) {
				vmodel.delete_flag = "1";
				break;
			}
		}
	} else {
		vmodel.delete_flag = "0";
	}
}

var add_userinfo = {data:{objectid:"userinfo",type:"inst_add",paranum:"0",paraval:JSON.stringify([{}])},success:add_user_success};

function addNewUser(d,r) {
	var username = $("#newuser").val();
	var userlevel = $("#superlevel option:selected").val();
	var pswd = $("#newpsw").val();
	var confirmpswd = $("#newpsw2").val();
	$(" #newuser").val("");
	$(" #newpsw").val("");
	$("#newpsw2").val("");
	// 密码强度显示消失
	$("#pw_level").css("display", "none");

	$("#superlevel").get(0).selectedIndex=0;
	if (vmodel.userdata.length > 9) {
		popupTipsDiv($("#usernumbermax"), 1000);
		return;
	}
	// 约束检查
	if (username == "" || username == undefined || username == null) {
		popupTipsDiv($("#insertrighttest_empty"), 1000);
		return ;
	}

	//约束用户名空格检查
	if(username.indexOf(' ')!=-1){	
		popupTipsDiv($("#usernamehavespaces"), 1000);    
		return;
	}

	if (username.length > 63) {
		popupTipsDiv($("#insertrighttest_toolong"), 1000);
		return;
	}

	// 连续两次输入密码必须一致
	if (confirmpswd != pswd) {
		popupTipsDiv($("#pswnotmatchalert"), 1000);
		return;
	}

	//约束密码长度在8位字符以上
	if (pswd.length < 8) {
		alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
		return;
	}
	//约束密码在32位字符以内
	if (pswd.length > 32) {
		alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
		return;
	}
	
	var strong_pswd_enable_req = {data:{objectid:"strong_pswd_enable",type:"val_get",paranum:"0",paraval:JSON.stringify([{username:username, pswd:pswd, userlevel:userlevel }])},success:get_strong_pswd_enable_success};
	request.addRequest([strong_pswd_enable_req]);

}

function get_strong_pswd_enable_success(d,r){
	if((d.result !="ok")){
		popupTipsDiv($("#addfailedalert"), 1000);
		return;
	} 
	let paraval = JSON.parse(r.data.paraval)[0];
	if(d.data[0].strong_pswd_enable == 1 && !is_strong_new_pswd(paraval.username, paraval.pswd)){//强密码校验开关开启 且 强密码校验失败
		return false;
	}
	newuser = new userinfo(paraval.username, paraval.pswd, paraval.userlevel, "");
	add_userinfo.data.paraval = JSON.stringify([newuser]);
	request.addRequest([add_userinfo]);
}

function is_strong_new_pswd(username, pswd){
	// 检查密码强度是否符合要求
	if (is_strong_pswd < 3) {   // 必须为强密码
        alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
		//popupTipsDiv($("#insertrighttest"), 1000);
		return false;
	}
	
	// 密码不能和用户名或者用户名的逆序相同
	var username_reverse = username.split('').reverse().join('');
	if(pswd.includes(username) || pswd.includes(username_reverse)) {
		popupTipsDiv($("#sameuserandpwd"), 2000);
		return false;
	}

	return true;
}


function add_user_success(d,r) {
	if((d.result !="ok")){
		popupTipsDiv($("#addfailedalert"), 1000);
		return;
	} else {
		request.addRequest([get_userinfo]);
		popupTipsDiv($("#addsuccessalert"), 1000);
	}
}

var delete_userinfo = {data:{objectid:"userinfo",type:"inst_delete",paranum:"0",paraval:JSON.stringify([{}])},success:delsuccess};
function deluser(_name) {
	if(confirm(mainvalue.i18nkeyword.user.confirmdeleteuser+_name+mainvalue.i18nkeyword.user.confirmend)) {
		if (_name === userName)  {
			popupTipsDiv($("#delfailedalert"), 1000);
			return;
		} //不能删除用户自身
		var para = {"username":_name};
		delete_userinfo.data.paraval = JSON.stringify([para]);
		request.addRequest([delete_userinfo]);
	}
}
function delsuccess(d, r) {
	if(d.result == "ok") {
		request.addRequest([get_userinfo]);
		popupTipsDiv($("#delsuccessalert"), 1000);
	} else {
		popupTipsDiv($("#delfailedalert"), 1000);
	}
}

var alter_userinfo = {data:{objectid:"userinfo",type:"val_set",paranum:"0",paraval:JSON.stringify([{}])},success:altersuccess};

function alteruser() {
	var currpsw = $("#currpsw").val();
	var newpsw = $("#alterpsw").val();
	var newpsw2 = $("#alterpsw2").val();
	if(currpsw !=="" && newpsw !=="" && newpsw2 !=="") {
		
		//约束密码在8位以上
		if (newpsw.length < 8) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
			$("#currpsw").val("");
			$("#alterpsw").val('');
			$("#alterpsw2").val('');
			$("#pw_level2").css("display", "none");
			return;
		}
		//约束密码在32位以内
		if (newpsw.length > 32) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			$("#currpsw").val("");
			$("#alterpsw").val('');
			$("#alterpsw2").val('');
			$("#pw_level2").css("display", "none");
			return;
		}
		if(newpsw != newpsw2) {
			$("#currpsw").val("");
			$("#alterpsw").val("");
			$("#alterpsw2").val("");
			$("#pw_level2").css("display", "none");
			popupTipsDiv($("#pswnotmatchalert"), 1000);
			return;
			
		}
		var para = {"username":userName, "pswd":$.sha256(currpsw), "newpswd":newpsw};
		var strong_pswd_enable_req = {data:{objectid:"strong_pswd_enable",type:"val_get",paranum:"0",paraval:JSON.stringify([para])},success:get_alert_strong_pswd_enable_success};
		request.addRequest([strong_pswd_enable_req]);

		
	} else {
		$("#currpsw").val("");
		$("#alterpsw").val("");
		$("#alterpsw2").val("");
		$("#pw_level2").css("display", "none");
		popupTipsDiv($("#insertrighttest"), 1000);
	}
}

function get_alert_strong_pswd_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#addfailedalert"), 1000);
		return;
	} 
	let paraval = JSON.parse(r.data.paraval)[0];
	if(d.data[0].strong_pswd_enable == 1 && !is_alter_strong_pswd(paraval.username, paraval.newpswd)){//强密码校验开关开启 且 强密码校验失败
		return false;
	}

	var para = {"username":paraval.username, "pswd":paraval.pswd, "newpswd":paraval.newpswd};
	alter_userinfo.data.paraval= JSON.stringify([para]);
	request.addRequest([alter_userinfo]);
}

function is_alter_strong_pswd(username, newpsw){
	// 检查密码强度是否符合要求
	if (alter_strong_pswd < 3) {   // 必须为强密码
		alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
		$("#alterpsw").val('');
		$("#alterpsw2").val('');
		$("#pw_level2").css("display", "none");
		return false;
	}

	var username_reverse = username.split('').reverse().join('');
	if(newpsw.includes(username) || newpsw.includes(username_reverse)) {
		popupTipsDiv($("#sameuserandpwd"), 2000);
		return false;
	}
	return true;
}

function altersuccess(d, r) {
	$("#currpsw").val("");
	$("#alterpsw").val("");
	$("#alterpsw2").val("");
	$("#pw_level2").css("display", "none");
	if(d.result != "ok") {
		popupTipsDiv($("#alterfailedalert"), 1000);
	} else {
		popupTipsDiv($("#altersuccessalert"), 3000);
		doLogout();
	}
}


var  loginpara ={
	GET:function(obj){
		var para = {instid:''};
		var Rq = {data:{objectid:"plat.loginpara",type:"val_get",paraval:JSON.stringify([para])},success:get_loginpara_value_succ};
		request.addRequest([Rq]);

		function get_loginpara_value_succ(d,r) {
			if (d.result == 'ok') {
				vmodel.loginpara_val = d.data;
			}
		}
	},
	PUT:function(obj) {
		var self = this;
		var Rq = {data:{objectid:"plat.loginpara",type:"val_set",paraval:JSON.stringify(vmodel.loginpara_val)},success:set_loginpara_value_succ};
		request.addRequest([Rq]);

		function set_loginpara_value_succ(d,r) {
			if (d.result == 'ok') {
				popupTipsDiv($("#opsuccessalert"), 1000);
			} else {
				popupTipsDiv($("#opfailedalert"), 1000);
				self.GET();
			}
		}
	}
}

function set_loginpara(obj){
	loginpara.PUT();
}
//  获取登录参数
loginpara.GET();