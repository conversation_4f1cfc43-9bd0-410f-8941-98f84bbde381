#!/bin/sh
#
# This script will be executed after configuring imports, restoring backup files,
# and other operations to restart all software processes.
# Executing this script without executing the reboot - f command
# can reduce the kernel startup time by approximately half a minute

cd /root
APP=`ls /root/power/bin`
killall -9 $APP

BAKUP_PATH="/mnt/backup"
SYS_PARA_BAKUP=$BAKUP_PATH/para_and_config #backup running data fir
SYS_PARA="/mnt/data" #running data dir 's parent dir

# clear tmp dir
rm -rf /mnt/memdisk

#create the main directory of "/mnt/memdisk" and "/mnt/backup"
mkdir -p /mnt/memdisk/config /mnt/memdisk/updating

if [ ! -d /mnt/data/para_and_config ] ;then
    cp -rf $SYS_PARA_BAKUP $SYS_PARA
    echo "boot.sh restore bakup para_and_config"
fi

#create the main directory of non-root area
/root/power/etc/sys/create_dir.sh

# recover Certificate, after create_dir.sh
RADIUS_BAKUP_CRET="/mnt/backup/radius" #backup dir
RADIUS_SSL_CRET="/mnt/data/work/radius/ssl"
WEB_BAKUP_CRET="/mnt/backup/web" #backup dir
WEB_SSL_CRET="/mnt/data/work/web/ssl"
if [ -d $RADIUS_BAKUP_CRET ] ; then
    rm -rf $RADIUS_SSL_CRET
    mv  $RADIUS_BAKUP_CRET  $RADIUS_SSL_CRET
fi
if [ -d $WEB_BAKUP_CRET ] ; then
    rm -rf $WEB_SSL_CRET
    mv  $WEB_BAKUP_CRET  $WEB_SSL_CRET
fi

# recover comdmgr candmgr statsave statsavebak auth_management, after create_dir.sh
WORK_DIR="/mnt/data/work/"
STATSAVE_CRET="$WORK_DIR/productapp"
COMDMGR_DIR="$BAKUP_PATH/comdmgr"
CANDMGR_DIR="$BAKUP_PATH/candmgr"
AUTH_MANAGEMENT_DIR="$BAKUP_PATH/auth_management"
if [ -f $BAKUP_PATH/statsave.bin ] ; then
    mv $BAKUP_PATH/statsave*.bin $STATSAVE_CRET
fi

if [ -d $COMDMGR_DIR ] ; then
    mv $COMDMGR_DIR $WORK_DIR
fi

if [ -d $CANDMGR_DIR ] ; then
    mv $CANDMGR_DIR $WORK_DIR
fi

if [ -d $AUTH_MANAGEMENT_DIR ] ; then
    cp -rf $AUTH_MANAGEMENT_DIR $WORK_DIR
    rm -rf $AUTH_MANAGEMENT_DIR
fi

# update ssl, after create_dir.sh
SSL_CERT="/mnt/data/work/web/ssl"
SSL_CERT_PATH="/mnt/data/work/web"
SSL_CERT_SOURCE="/root/power/etc/lighttpd/ssl"
if [ ! -d $SSL_CERT ] ; then
    echo "syn ssl certificate ..."
    cp -rf  $SSL_CERT_SOURCE  $SSL_CERT_PATH
fi
if [ ! -f /mnt/data/work/web/ssl/dh2048.pem ] ; then
    echo "recover ssl certificate ..."
    rm -rf $SSL_CERT
    cp -rf  $SSL_CERT_SOURCE  $SSL_CERT_PATH
fi


# clear share memory and semaphore
rm -rf /var/sem.* /var/shm.*

# start SysMonitor_run to restart all process
/root/power/bin/SysMonitor_run &

