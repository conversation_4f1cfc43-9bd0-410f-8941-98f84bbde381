<!DOCTYPE html>
<html>    
    <head>		
        <meta charset="utf-8" />
        <!-- <title>ZTE Energy Management</title> -->
        <title></title>
        <link id="logo_icon" href="/page/assets/img/white.ico" type="image/x-icon" rel="shortcut icon"/>
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<link rel="stylesheet" href="/page/assets/css/bootstrap.min.css" />
		<link rel="stylesheet" href="/page/assets/css/bootstrap-responsive.min.css" />
		<link rel="stylesheet" href="/page/assets/css/font-awesome.min.css" />
		<link rel="stylesheet" href="/page/assets/css/ace.min.css" />
		<link rel="stylesheet" href="/page/assets/css/ace-responsive.min.css" />
		<link rel="stylesheet" href="/page/assets/css/ace-skins.min.css" />
		<link rel="stylesheet" href="/page/assets/css/button.css" />

		<!--[if IE 7]>
		  <link rel="stylesheet" href="/page/assets/css/font-awesome-ie7.min.css" />
		<![endif]-->

		<!--[if lte IE 8]>
		  <link rel="stylesheet" href="/page/assets/css/ace-ie.min.css" />
		<![endif]-->

		<!--[if !IE]> -->
		<link rel="stylesheet" href="/page/assets/css/zte_min_other.css" />
		<script type="text/javascript">
			window.jQuery || document.write("<script src='/js/opensrc/jquery-3.6.3.min.js'>"+"<"+"/script>");
		</script>
		<!-- <![endif]-->

		<script src="/js/opensrc/bootstrap.min.js"></script>
		<script src="/js/opensrc/js.cookie.js"></script>
		<script src="/js/opensrc/jquery.sha256.js"></script>
		<script src="/js/opensrc/ace-elements.min.js"></script>
		<script src="/js/opensrc/ace.min.js"></script>
		<script src="/js/opensrc/avalon.js"></script>
		<script src="/js/opensrc/jqueryv1.i18n.js"></script>
		<script src="/js/opensrc/json2.js"></script>
		<script src="/js/opensrc/jquery-migrate-3.4.1.min.js"></script>
		<script src="/js/opensrc/acorn_interpreter.js"></script>
		<script>
			document.write('<script src="/js/private/request.js?v='+ new Date().getTime() +'"><\/script>');
		</script>
		<script>
			document.write('<script src="/js/private/common.js?v='+ new Date().getTime() +'"><\/script>');
		</script>
		<!-- end of basic styles -->
		<link rel="stylesheet" href="/page/assets/css/login.css" />
		<link rel="stylesheet" href="/page/assets/css/style.css" />
    </head>

    <body ms-controller="container" style="background-color: #f5f5f5 !important;color: #438eb9;">
		<div id="return_to_login" style="margin:30px 200px 30px 50px">
			<span class="ui-icon icon-double-angle-left bigger-140"></span>
			<a style="font-size: 16px;color: #438eb9;" href="#" onclick="gotopage('login.html')">{{i18nkeyword.pswdReset.return}}</a>
		</div>
		
		<div id="content"  ms-controller="password_reset" style="width: 300px; margin:auto;padding: 30px;">
			<h4>{{i18nkeyword.pswdReset.find_password}}</h4>
			<div style="border: solid thin;border-radius: 10px; padding: 33px;">
				<div id="pswd_reset" style="display: block;">
                    <div id="serial_num" style ="margin: 0px 0px 12px 0px;">
                    	{{i18nkeyword.pswdReset.dev_serial}}:&nbsp;<span>{{identify_code[0].serial_num}}</span> 
                    </div>
                    <div id="identify_code" style ="margin: 0px 0px 12px 0px;">
                    	{{i18nkeyword.pswdReset.identify_code}}:&nbsp;<span>{{identify_code[0].rand}}</span> 
                    	<a href="#" style="color: #438eb9;" onclick="codeReget()">
                    		<i class="icon-refresh bigger-100" style="float: right;">{{i18nkeyword.pswdReset.reset}}</i>
                    	</a>
                    </div>
                    <div id="time_remaining" style ="margin: 0px 0px 12px 0px;">
                    	{{i18nkeyword.pswdReset.time_valid}}:&nbsp;<span>{{time_valid[0].time_valid}}</span> 
                    </div>
                    <input id="SN_code" autocomplete="off" style="height: 25px; width: 160px;margin-left: 0px;border: 1px solid #d1d5da; border-radius: 5px;" ms-attr="{'placeholder':i18nkeyword.pswdReset.sn_input}" ms-duplex="SN_code" onkeydown="sn_click_enter()"/>
                    <button style="color: #438eb9;float: right; height: 25px;display: none;" onclick="SN_check()">{{i18nkeyword.pswdReset.check}}</button>
                </div>
                
                <div id="pswd_alter" style="display: block;">
                    <div>{{i18nkeyword.pswdReset.user_name}}:</div> 
                        <input id="user_name" autocomplete="off" style="height: 25px; width: 185px;margin-left: 0px;border: 1px solid #d1d5da; border-radius: 5px;" ms-attr="{'placeholder':i18nkeyword.pswdReset.user_name_input}" ms-duplex="user_name"/>
                    <div>{{i18nkeyword.pswdReset.pswd_new}}:</div>
                        <input id="pswd_new" type="password" autocomplete="new-password" style="height: 25px; width: 180px;margin-left: 0px;border: 1px solid #d1d5da; border-radius: 5px!important;" ms-attr="{'placeholder':i18nkeyword.pswdReset.new_pswd_input}" ms-duplex="pswd_new" onkeyup="check_alter_pswd_strength(this)"/>
						<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('pswd_new')"><i class="icon-eye-open" ></i></span>
                    <div id="pw_level2"  class="pw-strength" style="display:none; margin-bottom: 4px;">  
                    	<div class="pw-bar"></div>
                    	<div class="pw-bar-on"></div>
                    	<div class="pw-txt">
                    	</div>
                    </div>
                    <div>{{i18nkeyword.pswdReset.pswd_confirm}}:</div>
                        <input id="pswd_confirm" type="password" autocomplete="new-password" style="height: 25px; width: 180px;margin-left: 0px;border: 1px solid #d1d5da; border-radius: 5px!important;" ms-attr="{'placeholder':i18nkeyword.pswdReset.new_pswd_input_again}" ms-duplex="pswd_new_2" onkeydown="alert_enter()"/>
						<span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('pswd_confirm')"><i class="icon-eye-open" ></i></span>
                    <button style="color: #438eb9;float: right; height: 25px; width: 50px;" onclick="pswd_alter()">{{i18nkeyword.pswdReset.alter}}</button>
                </div>
			</div>
            <div>{{i18nkeyword.pswdReset.tip}}</div>
		</div>
		
		<div class="copyright" style="margin-top:240px;text-align: center;">
			<span ms-if="logo_flag=='1'">{{i18nkeyword.login.copyright}}</span>
		</div>
	</body>
    <link rel="stylesheet" href="/page//html/plug_pswd/css/plug_pswd.css" />
    <script src="/page//html/plug_pswd/js/plug_pswd.js"></script>
	<script src="/page/js/password_reset.js"></script>
</html>