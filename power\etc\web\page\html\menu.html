
<a class="menu-toggler" id="menu-toggler" href="#">
    <span class="menu-text"></span>
</a>
<div class="sidebar fixed" id="sidebar">
    <ul class="nav nav-list">
        <!-- 站点概览 -->
        <li ms-attr="{class:menuactive=='index.html'?'active':''}" ms-visible="userlevel > '0'">
            <a href="#" onClick="gotopage('index.html')">
                <i class="icon-bar-chart"></i>
                <span class="menu-text" >
                    {{i18nkeyword.menu.site_overview}}
                </span>
            </a>
        </li>
        <!-- 实时告警 -->
        <li ms-attr="{class:menuactive=='alarm.html'?'active':''}" ms-visible="userlevel > '0'">
            <a href="#" onClick="gotopage('alarm.html')">
                <i class="icon-bell"></i>
                <span class="menu-text" >
                    {{i18nkeyword.menu.active_alarm}}
                </span>
            </a>
        </li>
        <!-- 实时数据 -->
        <li ms-attr="{class:menulistactive=='object_realdata.html'?'active':''}" ms-visible="userlevel > '0'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))" >
                <i class="icon-list-alt"></i>
                <span class="menu-text">
                    {{i18nkeyword.devlist.realdata}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="devList">
                <li ms-for="bobj in realdata_obj_data" ms-attr="{class:menuactive==('realdata_obj_data-'+bobj.bo_id)?'active':''}">
                    <a href="#" ms-click="gotopage('object_realdata.html', 'realdata_obj_data-'+bobj.bo_id)" >
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text" >
                            {{bobj["name"]}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
        <li ms-attr="{class:menulistactive=='paraset.html'||menulistactive=='param_inspect.html'?'active':''}" ms-if="userlevel > '1'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-cog"></i>
                <span class="menu-text">
                    {{i18nkeyword.devlist.paraset}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="devList">
                <li ms-for="bobj in paraset_obj_data" ms-attr="{class:menuactive==('paraset_obj_data-'+bobj.bo_id)?'active':''}">
                    <a href="#" ms-click="gotopage('paraset.html','paraset_obj_data-'+bobj.bo_id)">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text" >
                            {{bobj["name"]}}
                        </span>
                    </a>
                </li>
                <li ms-attr="{class:menulistactive=='param_inspect.html'?'active':''}" ms-if="userlevel > '2'">
                    <a href="#" onclick="gotopage('param_inspect.html','paraset.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.devlist.inspect_param_set}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
		<li ms-attr="{class:menulistactive==='control.html' || menulistactive==='eeprom_control.html'?'active':''}" ms-if="userlevel > '1'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-wrench"></i>
                <span class="menu-text">
                    {{i18nkeyword.devlist.devcontrol}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="devList">
                <li ms-for="bobj in control_obj_data" ms-attr="{class:menuactive==('control_obj_data-'+bobj.bo_id)?'active':''}">
                    <a href="#" ms-click="gotopage('control.html','control_obj_data-'+bobj.bo_id)">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text" >
                            {{bobj["name"]}}
                        </span>
                    </a>
                </li>
                <!-- 定制目录 -->
                <li id="block1" ms-if="userlevel > '2' && eeprom_state === '0'" ms-attr="{class:menulistactive==='eeprom_control.html'?'active':''}" >
                    <a href="#" onclick="gotopage('eeprom_control.html','control.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                            EEPROM
                        </span>
                    </a>
                </li>
            </ul>
        </li>
        <li id="mainmenu_config" ms-attr="{class:menuactive=='config.html'?'active':''}" ms-if="userlevel > '1'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-pencil"></i>
                <span class="menu-text">
                    {{i18nkeyword.menu.para_config}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="configList" ms-if="userlevel > '1'">
                 <li id="block1" ms-attr="{class:menulistactive=='config_basic.html'?'active':''}" >   <!--ÔÝÊ±ÆÁ±Î-->
                    <a href="#" onclick="gotopage('config_basic.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.config_guide}}
                        </span>
                    </a>
                </li>
                <li id="block2" ms-visible="0==1">   <!--ÔÝÊ±ÆÁ±Î-->
                    <a href="#">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.south_net_para}}
                        </span>
                    </a>
                </li>
                <li id="block3" ms-attr="{class:menulistactive=='config_northnet.html'?'active':''}" ms-visible="1==1">
                    <a href="#" onclick="gotopage('config_northnet.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.north_net_para}}
                        </span>
                    </a>
                </li>
                <li id="block4" ms-attr="{class:menulistactive=='config_northprotocol.html'?'active':''}" ms-visible="1==1">
                    <a href="#" onclick="gotopage('config_northprotocol.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.menu.north_protocol}}
                        </span>
                    </a>
                </li>
                <li id="block5" ms-attr="{class:menulistactive=='config_com.html'?'active':''}">
                    <a href="#" onclick="gotopage('config_com.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.menu.serial_port_config}}
                        </span>
                    </a>
                </li>
                <li id="block6" ms-if="userlevel > '1'" ms-attr="{class:menulistactive=='config_dataclassify.html'?'active':''}">
                    <a href="#" onclick="gotopage('config_dataclassify.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.menu.data_classily}}
                        </span>
                    </a>
                </li>
                <li id="block7" ms-visible="0==1">   <!--ÔÝÊ±ÆÁ±Î-->
                    <a href="#">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.picture_para}}
                        </span>
                    </a>
                </li>
                <li id="block8" ms-attr="{class:menulistactive=='config_didoai.html'?'active':''}" ms-if="userlevel > '2'">
                    <a href="#" onclick="gotopage('config_didoai.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.ai_di_config}}
                        </span>
                    </a>
                </li>
                <li id="block9" ms-attr="{class:menulistactive=='config_dry_contact.html'?'active':''}">
                    <a href="#" onclick="gotopage('config_dry_contact.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.dry_contact_para}}
                        </span>
                    </a>
                </li>
                <!-- <li id="block10" ms-attr="{class:menulistactive=='config_radius.html'?'active':''}">
                    <a href="#" onclick="gotopage('config_radius.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.RADIUS_config}}
                        </span>
                    </a>
                </li> -->
                <li id="block11" ms-attr="{class:menulistactive=='config_plc.html'?'active':''}" ms-if="userlevel > '2'">
                    <a href="#" onclick="gotopage('config_plc.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                        {{i18nkeyword.menu.plc_config}}
                        </span>
                    </a>
                </li>
                <li id="block12" ms-attr="{class:menulistactive=='config_ap.html'?'active':''}">
                    <a href="#" onclick="gotopage('config_ap.html','config.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                            {{i18nkeyword.menu.ap_connection_config}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
        <li ms-attr="{class:menuactive=='sys.html'?'active':''}" ms-visible="userlevel > '0'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-legal"></i>
                <span class="menu-text">
                    {{i18nkeyword.menu.system_maintain}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="system_submenu">
                 <li id="block1" ms-attr="{class:menulistactive=='sysinfo.html'?'active':''}" >
                    <a href="#" onclick="gotopage('sysinfo.html','sys.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.menu.system_info}}
                        </span>
                    </a>
                </li>
				<li id="block2" ms-attr="{class:menulistactive=='dev_maint.html'?'active':''}" ms-visible="userlevel>'1'">
                    <a href="#" onclick="gotopage('dev_maint.html','sys.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.system.device_maintain}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
        <li ms-attr="{class:menuactive=='history.html'?'active':''}" ms-visible="userlevel > '0'">
            <a href="#" onClick="gotopage('history.html')">
                <i class="icon-print"></i>
                <span class="menu-text">
                    {{i18nkeyword.menu.historical_record}}
                </span>
            </a>
        </li>
        <li ms-attr="{class:menuactive=='usermanage.html'?'active':''}" ms-visible="userlevel == '2' || (userlevel=='1' && radius_status_com == '0')">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-user"></i>
                <span class="menu-text">
                    {{i18nkeyword.menu.users_management}}
                </span>
				 <i class="icon-chevron-down"></i>
            </a>
			<ul class="submenu" id="user_manage_menu">
                 <li id="block1" ms-attr="{class:menulistactive=='user.html'?'active':''}" >
                    <a href="#" onclick="gotopage('user.html','usermanage.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         Web
                        </span>
                    </a>
                </li>
				<li id="block2" ms-attr="{class:menulistactive=='guiuser.html'?'active':''}" ms-visible="userlevel=='2'" ms-if="0==1">
					<a href="#" onclick="gotopage('guiuser.html','usermanage.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         GUI
                        </span>
                    </a>
                </li>
				<li id="block3" ms-attr="{class:menulistactive=='superuser.html'?'active':''}" ms-visible="userlevel == '2'">
					<a href="#" onclick="gotopage('superuser.html','usermanage.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.menu.superuser}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
		<li ms-attr="{class:menulistactive=='version.html'?'active':''}" ms-visible="userlevel > '1'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-cogs"></i>
                <span class="menu-text">
                    {{i18nkeyword.devlist.devices_data}}
                </span>
                <i class="icon-chevron-down"></i>
            </a>
            <ul class="submenu" id="devList">
                <li ms-for="bobj in version_obj_data" ms-attr="{class:menuactive==('version_obj_data-'+bobj.bo_id)?'active':''}">
                    <a href="#" ms-click="gotopage('version.html','version_obj_data-'+bobj.bo_id)">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text" >
                            {{bobj["name"]}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
		<li ms-attr="{class:menuactive=='trance.html'?'active':''}" ms-visible="userlevel == '3'">
            <a href="#" class="dropdown-toggle" onclick="changelist($(this))">
                <i class="icon-laptop"></i>
                <span class="menu-text">
					{{i18nkeyword.trance.tranceinfo}}
                </span>
				 <i class="icon-chevron-down"></i>
            </a>
			<ul class="submenu" id="user_manage_menu">
                 <li id="block1" ms-attr="{class:menulistactive=='inner_alarm.html'?'active':''}" >
                    <a href="#" onclick="gotopage('inner_alarm.html','trance.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.trance.inneralarm}}
                        </span>
                    </a>
                </li>
				<li id="block1" ms-attr="{class:menulistactive=='history_inner_alarm.html'?'active':''}" >
                    <a href="#" onclick="gotopage('history_inner_alarm.html','trance.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.trance.his_inneralarm}}
                        </span>
                    </a>
                </li>
				<li id="block1" ms-attr="{class:menulistactive=='traceinfo.html'?'active':''}" >
                    <a href="#" onclick="gotopage('traceinfo.html','trance.html')">
                        <i class="icon-arrow-right"></i>
                        <span class="menu-text">
                         {{i18nkeyword.trance.tranceinfo}}
                        </span>
                    </a>
                </li>
            </ul>
        </li>
    </ul>
    <div class="sidebar-collapse" id="sidebar-collapse">
        <i class="icon-double-angle-left"></i>
    </div>
</div><!--/.sidebar fixed-->
<!-- inline scripts related to this page -->
