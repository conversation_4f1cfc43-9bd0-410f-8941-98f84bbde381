<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" >
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="dry_contact_info">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            <span i18n="com.serial_">
                            {{i18nkeyword.menu.dry_contact_para}}
                            </span>
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>

                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                        <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_successfully}}
                        </div>
                        <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_failure}}
                        </div>
                        <!--PAGE CONTENT BEGINS-->
                        <div class="tabbable" ms-if="di_title_show == '1' || do_title_show == '1'">
                            <ul class="nav nav-tabs" id="myTab">
                                <li ms-class="'diconfig' == tab_list?'active':''" ms-click="tabChange('diconfig')" ms-if="di_title_show == '1'">
                                    <a data-toggle="tab" href="#diconfig" >
                                        <i class="blue icon-user bigger-110">
                                        </i>
                                        DI
                                    </a>
                                </li>
                                <li ms-class="'doconfig' == tab_list?'active':''" ms-click="tabChange('doconfig')" ms-if="do_title_show == '1'">
                                    <a data-toggle="tab" href="#doconfig" >
                                        <i class="blue icon-user bigger-110">
                                        </i>
                                        DO
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div id="diconfig" class="tab-pane" ms-class="'diconfig' == tab_list?'active':''">
                                    <table id="sample-table-1" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr >
                                                <th>{{i18nkeyword.dry_contact.channel_name}}</th>
                                                <th>[{{i18nkeyword.devlist.devicename}}] {{i18nkeyword.dry_contact.sid}}</th>
                                                <th>{{i18nkeyword.dry_contact.alarm_abnormal_status}}</th>
                                                <th>{{i18nkeyword.alias}}</th>
                                                <th>{{i18nkeyword.dry_contact.alarm_level}}</th>
                                                <th>{{i18nkeyword.dry_contact.dry_contact_output}}</th>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            <tr ms-for="(n,al) in di_ai_instidvalue">
                                                <td>{{al["Channel Name"]}}</td>
                                                <td style="min-width:200px">
                                                    <span ms-if="di_ai_instidvalue[n].sid != '-1'&& (al['Is Valid'] == 'Valid')">{{"["+al["Device Name"]+"] "+al["full_name"]}}</span>
                                                    <span ms-if="di_ai_instidvalue[n].sid != '-1'&& al['Is Valid'] == 'InValid'">{{i18nkeyword.null}}</span>
                                                    <span ms-if="di_ai_instidvalue[n].sid == '-1'">{{i18nkeyword.null}}</span>
                                                </td>
                                                <td>
                                                    <select  ms-duplex="di_ai_instidvalue[n]['Preset Status']" data-duplex-changed="changeChannelData(n)">
                                                        <option ms-for="(index,name) in convention" ms-attr="{value:index, selected:index==di_ai_instidvalue[n]['Preset Status']?true:false}">{{name}}</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input ms-if="@judge_inrelay(@di_ai_instidvalue[n].sid)" class="form-control input-medium pull-left" style="width:285px" ms-attr="{'title':i18nkeyword.check_input_tip+','+i18nkeyword.input_max_length+':31'+i18nkeyword.char}" ms-duplex="di_ai_instidvalue[n]['Channel Alias']" data-duplex-changed="changeChannelData(n)"></td>
                                                <td>
                                                    <select  ms-if="@judge_alm_level(di_ai_instidvalue[n])  && al['Is Valid'] == 'Valid'" class="editable input-medium" ms-duplex="di_ai_instidvalue[n].alm_level" data-duplex-changed="changeChannelData(n)">
                                                        <option ms-for="(num,name) in alm_levels" ms-attr="{value:num, selected:num==di_ai_instidvalue[n].alm_level?true:false}">
                                                            {{name}}
                                                        </option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select ms-if="judge_alm_relay(di_ai_instidvalue[n]) && al['Is Valid'] == 'Valid'"  class="editable input-medium" ms-duplex="di_ai_instidvalue[n].alm_relay" data-duplex-changed="changeChannelData(n)" style="width:165px">
                                                        <option value = "0" >{{i18nkeyword.null}}</option>
                                                        <!--ms-for:(num,name) in doconvention-->
                                                        <option ms-attr="{value:num,selected:num==di_ai_instidvalue[n].alm_relay?true:false}">{{name}}</option>
                                                        <!--ms-for-end:-->
                                                    </select>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <button style="position: fixed ! important; right: 300px; top: 85px;" class="button button-small button-flat-primary" onClick="setContactData()">
                                    <i class="icon-pencil bigger-100"></i>
                                    {{i18nkeyword.set}}
                                    </button>
                                </div>

                                <div id="doconfig" class="tab-pane" ms-class="'doconfig' == tab_list?'active':''">
                                    <table id="sample-table-2" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{i18nkeyword.dry_contact.channel_name}}</th>
                                                <th>[{{i18nkeyword.devlist.devicename}}] {{i18nkeyword.dry_contact.sid}}</th>
                                                <th>{{i18nkeyword.dry_contact.default_output_status}}</th>
                                            </tr>
                                        </thead>

                                        <tbody>
                                            <!--ms-for:(i,doinfo) in do_instidvalue-->
                                            <tr>
                                                <td>{{doinfo["Channel Name"]}}</td>
                                                <td >
                                                    <span ms-if="doinfo['sid'] == '-1'">{{i18nkeyword.null}}</span>
                                                    <span ms-if="doinfo['sid'] != '-1' && doinfo['Is Valid'] == 'InValid'">{{i18nkeyword.null}}</span>
                                                    <span ms-if="doinfo['sid'] != '-1' && doinfo['Is Valid'] == 'Valid'">{{"["+doinfo["device name"]+"] "+doinfo["full_name"]}}</span>
                                                </td>
                                                <td>
                                                    <select  ms-duplex="doinfo['Present Status']" data-duplex-changed="changeDoChannelData(i)">
                                                        <option ms-for="(index,name) in do_convention" ms-attr="{value:index, selected:index==doinfo['Present Status']?true:false}">{{name}}</option>
                                                    </select>
                                                </td>
                                            </tr>
                                            <!--ms-for-end:-->
                                        </tbody>
                                    </table>
                                    <button style="position: fixed ! important; right: 300px; top: 85px;" class="button button-small button-flat-primary" onClick="setPresetStatus()">
                                    <i class="icon-pencil bigger-100"></i>
                                    {{i18nkeyword.set}}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="tabbable" ms-if="di_title_show == '0' && do_title_show == '0'" style="text-align:center">
                            {{i18nkeyword.no_dry_contact}}
                        </div>
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
              <!-- inline scripts related to this page -->
        <script src="/page/js/config_dry_contact.js"></script>
    </body>
</html>