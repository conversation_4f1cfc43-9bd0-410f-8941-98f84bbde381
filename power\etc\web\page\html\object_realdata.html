 <!DOCTYPE html>
<html>
	<head>
		<!--#include virtual="/page/html/include.html" -->
	</head>

	<body class="navbar-fixed ms-controller" ms-controller="container">
		<!--#include virtual="/page/html/header.html" -->

		<div class="main-container container-fluid" ms-controller="realdata_info">
			<!--#include virtual="/page/html/menu.html" -->
			<div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.devlist.realdata}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
				<div class="page-content">
					<div class="row-fluid">
						<div class="span11">
							<!--PAGE CONTENT BEGINS-->
							<div class="tabbable">
								<ul class="nav nav-tabs" id="myTab">
									<li class="active" >
										<a data-toggle="tab" href="#realdata">
											<i class="blue icon-tint bigger-110">
											</i>
											{{i18nkeyword.devlist.realdata}}
										</a>
									</li>
									<li class="pull-right">{{i18nkeyword.devlist.Data_refresh_interval}}
										<select id="fleshselect" class = "input-small" onchange="changeFleshtime(this.value)">
											<option value="10" selected>{{i18nkeyword.second_10}}</option>
											<option value="1" >{{i18nkeyword.second_1}}</option>
										</select>
									</li>
								</ul>
								<div class="tab-content">
									<div id="realdata" class="tab-pane active"  style="height:600px">
										<div class="widget-box span4">
											<div class="widget-header">
												<h6>{{i18nkeyword.devlist.device_list}}</h6>
											</div>
											<div class="widget-body" style="overflow-y:auto; height:530px;">
												<div class="widget-main">
													<div id="devlist" class="content">
														<!--ms-for:(i,dev) in realdata_device_table-->
															<a ms-attr="{'id':'dev_'+(dev.sid)}" href="#" class="dd2-content" ms-click="devinfo_show(dev.sid, dev['device name'])">
																<p class="pull-left">{{dev['device name']}}</p>
															</a>
														<!--ms-for-end:-->
													</div>
												</div>
											</div>
										</div>
										<div class="span8 widget-container-span" id = "devinfo">
											<div class="widget-box">
												<div class="widget-header dark">
													<h6 id="eventcontentname">{{current_devname}}</h6>
												</div>
												<div class="widget-body"style="overflow-y:auto; height:530px;">
													<div class="widget-main padding-4">
														<div id="realdata_content" class="content" ms-if = "is_devinfo_empty == 0">
																<!--ms-for:(i,data) in current_devinfo-->
																<div class="dd2-content dark">
																<p class="pull-left">{{data.full_name}}</p>
																<p class="pull-right">{{@get_sid_show_value(data.value, data.unit)}}</p>
																<div style="clear:both;"></div>
																</div>
																<!--ms-for-end:-->
														</div>
														<div id="realdata_content" class="content" ms-if = "is_devinfo_empty == 1">
															{{i18nkeyword.no_displayable_signal}}
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
						</div><!--/span11-->
					</div><!--/row-fluid-->
				</div><!--/page-content-->
			</div><!--/main-contain-->
		</div><!--/main-container-->
		<!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/object_realdata.js"></script>
	</body>

</html>