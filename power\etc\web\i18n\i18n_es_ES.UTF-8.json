{"test1": "elegir1", "all": "Todos", "test2": "elegir2", "test5": "elegir5", "test4": "elegir4", "reboot_waite": "Reiniciar....", "test6": "elegir6", "Disallow_input": "Entrada No permitida", "later_try": "Por favor <PERSON>elo de nuevo mas tarde", "choose_again": "Cambiar", "choose_file_tip": "Por favor elija Archivo", "dry_contact": {"default_output_status": "Estado de salida predeterminado", "dry_contact_output": "Salida de contacto seco", "alarm_level": "<PERSON><PERSON> de <PERSON>", "alarm_abnormal_status": "Estado de alarma / anormal", "channel_name": "Nombre del Canal", "sid": "SID"}, "test3": "elegir3", "jumptext": "Saltar", "firsttext": "Primero", "operate_reboot_confirm": "Esta operacion puede hacer que el sistema se reinicie. Confirme si desea continuar.", "encryption_password_export": "Export file encryption password:", "encryption_password_import": "Import file decryption password:", "pswd_empty": "Password is not allowed to be empty", "yes": "Si", "total": "Un total de", "jumptologinpage": "Reiniciando ..., saltara a la pagina de inicio de sesion", "select": "Por favor seleccione", "plot": {"vol_input": "Vol. Entrada", "ele_input": "Corriente de Entrada", "ele_output": "Corriente de Salida", "vol_output": "Vol. Salida", "solar_pu_total_out_curr": "PU corriente total de salida", "solar_pu_total_out_power": "PU potencia total de salida"}, "env_scan_info": {"temp": "Temperatura", "humidity": "Humedad"}, "test7": "elegir7", "copyright": "2024 ZTE Corporation.  Todos los derechos reservados", "NO": "NO.", "data_modified": "The server data has changed and will be queried again", "menu": {"active_alarm": "Alarma real", "superuser": "Usuario del fabricante", "secauthuser": "Secondary Certification", "RADIUS_config": "Configuracion RADIUS", "north_link_status": "Estado de conexion", "north_netmanage_connect_status": "Estado de conexion NMS", "dry_contact_output_config": "Configuracion de salida de contacto seco", "plc_config": "Configuracion del PLC", "serial_port_config": "Configuracion de puerto serial", "ap_connection_config": "AP Config", "batter_data": "Datos del bateria", "system_info": "Informacion del sistema", "south_net_para": "Parametros de red en direccion sur", "devices_data": "Datos de dispositivos", "north_protocol": "Protocolo en direccion norte", "site_video": "Video del sitio", "site_overview": "Descripcion general del sitio", "ai_di_config": "Configuracion AI / DI / DO", "system_maintain": "Mantenimiento del sistema", "historical_record": "Record historico", "site_config": "Configuracion del sitio", "north_com_name": "COM. Nombre", "data_classily": "Configuracion de propiedades display", "config_guide": "Guia de configuracion", "north_link_status1": "Desconectado", "north_link_status0": "Conectado", "north_protocol_name": "Nombre del protocolo", "users_management": "Gestion de usuarios", "north_net_para": "Parametros de red en direccion norte", "north_ip_name": "Direccion IP", "dry_contact_para": "Contacto seco", "picture_para": "Parametros imagen", "para_config": "Configuracion de parametros", "north_link_num": "Numeros de Conexion de Administracion Red Actual"}, "system": {"fileempty": "El archivo esta vacio", "outrelay_control": "Control de relevo", "verify_para": "Verificar Parametro", "verify_config": "Verificar Configuracion", "select_type": "Seleccione tipo", "download_page_tip": "Visite la pagina web de transferencia.", "device_reset": "Restablecimiento del dispositivo", "upload_success": "Subida exitosa", "smr_front": "SMR-Frente", "smr_unlock_sn": "Unlock SN", "smr_update_start": "Iniciar la actualizacion", "state_update_success": "Exito", "uib_update": "Actualizacion de UIB", "smr": "SMR", "recover_default_para_influence": "It will restore parameter.", "recover_default_para_confirm": "Are You Sure to recover default parameter?", "recover_default_para": "Recuperar parametros de recuperacion predeterminado", "state_update_log": "Actualizar registros", "export_all": "Exportar todos los datos", "export_eeprom": "Exportar datos EEPROM", "factory_reset": "Restablecimiento de fabrica", "default_reset": "<PERSON><PERSON>bly", "smr_update_state": "Actualizacion de estado", "export_config_file": "Exportar archivo de configuracion", "dry_contact_state": "Estado de contacto seco", "all_smr": "Todos", "dry_contact_name": "Nombre de contacto seco", "factory_reset_influence": "It will restore parameter.", "factory_reset_confirm": "¿Esta seguro de restablecer los valores de fabrica?", "default_reset_influence": "It will restore parameter and config.", "default_reset_confirm": "Are You Sure to <PERSON><PERSON> Forcibly?", "upload_para": "Cargar archivo de parametros", "del_history_alarm": "Alarmas del historial", "factory_reset_now": "Reinicio de fabrica, espere ...", "default_reset_now": "Restore default forcibly, please wait...", "slave_update": "CAN Actualizacion del dispositivo", "system_info": "Informacion del sistema", "para_recover_influence": "It will restore parameter and config.", "para_recover_confirm": "Are You Sure to recover backup parameter?", "para_recover": "Recuperar parametros de copia de seguridad", "romove_his_record": "Eliminar registros historicos", "para_bak_recover": "Parametro Bakup & Restaurar", "sys_update": "Actualizacion de CSU", "now_bak_file": "Archivo Parametros Backup Actual", "para_file": "Archivo de parametros", "buzzer_control": "Estado del zumbador", "sys_all_update": "Actualizacion completa de CSU", "sys_time": "Hora del sistema", "export_success": "Exportacion completa", "control_state": "Estado de control", "confirm_factory_reset": "¿Esta seguro de restablecer los valores de fabrica?", "download_abnormal_data": "Descar<PERSON> datos anormales", "start_end_error_tip": "<PERSON><PERSON> de seleccion incorrecto, no se puede actualizar", "start_end_error_tip_v2": "<PERSON>ngo de seleccion incorrecto, no se puede detener la actualización", "all_record": "Todos los registros", "iddb_update": "Actualizacion IDDB", "del_allsql": "Todos", "state_update_failed": "Ha fallado", "single_smr": "Unico", "synchro_local_time": "Sincronizacion de la hora local", "para_import": "Importacion de parametros", "del_peak_record": "Registros pico", "south_update": "Actualizacion del dispositivo sur", "para_export": "Exportacion de parametros", "del_history_data": "Datos historicos", "state_updating": "Haciendo", "smr_update_control": "Actualizar configuracion", "para_bak": "Parametro Bakup", "system_state": "Estado del sistema", "upload_pack": "Cargar paquete de actualizacion", "para_maintain": "Mantenimiento de parametros", "smr_back": "SMR-Atras", "del_history_event": "Registro de operaciones", "dry_contact_control": "Control de contacto seco", "upload_failed": "Subida fallida", "import_success": "Importación exitosa", "import_failed": "importación fallida", "multi_smr": "Multi", "handling_export_later": "<PERSON><PERSON><PERSON><PERSON>, no actualice ni cierre esta página, por favor espere...", "click_download": "<PERSON><PERSON><PERSON>", "abnormal_data": "<PERSON>tos anormales", "download_para": "Exportar archivo de parametros", "ssl_download": "Descarga SSL", "daughterboard_update": "Actualizacion de sub-tarjeta", "sys_reset": "Reinicio de sistema", "export_failed": "Exportacion fallida", "smr_update_end": "Detener actualizacion", "select_update_type": "Seleccione tipo", "select_update_model": "Seleccione del modelo", "smr_front_back": "Frente-atras", "font_library": "Biblioteca-de-fuentes", "program": "Procedimiento", "uploaded_files": "Uploaded files", "select_mode": "Se<PERSON>ccionar modo", "sys_maintain": "Mantenimiento del sistema", "del_history_sql": "Eliminar registro de historial", "device_maintain": "Mantenimiento de dispositivos", "ssl_upgrade": "Actualizacion SSL", "timezone_select": "Seleccionar zona horaria", "set_time": "<PERSON><PERSON> tiempo", "control_operate": "Accion de control", "time_out_range": "El tiempo esta fuera de rango.", "date_not_correct": "Fecha o fecha inválida fuera del alcance", "state_not_start": "No comienza", "num": "No.", "cfgobj_id": "Configuration object", "cfginst_id": "Configuration instance", "para_name": "Nombre del parametro", "set_value": "valor aju<PERSON>o", "Info": "Informacion", "please_download_file": "Por favor, descargue el archivo para mas informacion", "upgrade_file_trans_prog": "Upgrade file transfer progress", "spu_upgrade_prog": "SPU upgrade progress", "uploadsysall_file_tip": "Please select all_update.tar.gz or all_update_default.tar.gz!", "uploadsys_file_tip": "Please select powernew.tar.gz or powernewdefault.tar.gz!", "uploadssl_file_tip": "Please select sslnew.zip or sslnew.tar.gz!", "uploadsys_list_check_tips": "Packet type error. Please use right packet! Continue to upgrade?", "uploadsys_filesys_check_tips": "File sys change. Advise to use system software! Continue to upgrade?", "refresh_page_tip": "Please refresh the page!", "south_updata_tip": "When upgrading to a southbound device, the data displayed on the real-time data page may not be real-time"}, "seco_cert": {"get_seco_cert_enable_err": "Failed to obtain secondary certification enable", "modal_title": "Secondary Certification", "username": "Username", "password": "Password", "close": "Cancel", "confirm": "Confirm", "psw_err": "Incorrect Password"}, "eeprom": {"syn_csu_and_eeprom": "CSU unmatch EEPROM, please sync", "mac_address_change": "MAC ADDR. changed, inform net manager", "csu_and_eeprom_not_match": "CSU unmatch EEPROM", "eeprom_to_csu": "Import EEPROM to CSU", "csu_to_eeprom": "Save to EEPROM", "cancel_syn": "Cancel sync", "confirm": "Confirm", "eeprom_to_csu_control": "EEPROM Config Import CSU", "csu_to_eeprom_control": "CSU Config Synchronize To EEPROM", "others_confirmed_tip": "Others confirmed"}, "submit": "Enviar", "second_10": "10 seg", "add": "Agregar", "device": "Dispositivo", "choose_file": "Escoger", "prevtext": "Anterior", "upload_file": "Cargar archivo", "empty": "<PERSON><PERSON><PERSON>", "delete_confirm": "Eliminar ¿Confirmar?", "para_confirm_title": "Cambio de confirmación de parámetros", "para_confirm_content": "Los siguientes son los parámetros a cambiar. Confirme nuevamente si se modifica (la modificación se cancelará automáticamente después de 60 segundos):", "config_error": "Los siguientes param. conflictos de relacion de restriccion, restablezca:", "update_success": "Actualizacion exitosa", "attr_name": "Nombre del parametro", "about": "Acerca de", "none_select": "Seleccione Invertir", "second_1": "1 seg", "devlist": {"inspect_offset": "Compensar", "period": "Periodo", "percent_threshold": "Umbral de porcentaje", "threshold": "Umbral", "unit": "Unidad", "devicename": "Nombre del dispositivo", "digital_data": "Datos digitales", "asset_data": "Informacion de activos", "paraset": "Establecimiento de parametros", "devices_data": "Informacion del dispositivo", "alarm_data": "Parametro de alarma", "controlname": "Nombre de control", "analog_data": "Datos analogicos", "signalname": "Nombre de la senal", "status": "Estado", "absolute_threshold": "Umbral absoluto", "inspect_slop": "Pendiente", "device_list": "Lista de dispositivos", "devcontrol": "Control del dispositivo", "realdata": "Datos reales", "parameter_data": "Datos de parametros", "no_invalid_signal": "Senal No Valida", "parametername": "Nombre del parametro", "backlash": "Barra invertida", "name": "Nombre", "info": "Informacion", "Data_refresh_interval": "Intervalo de actualizacion de datos", "inspect_name": "Nombre de la senal", "inspect_currvalue": "Valor actual", "trace_info": "Informacion de seguimiento.", "signame": "Nombre de la senal", "control_data": "Datos de control", "inspect_param_set": "Ajustar", "value": "Valor", "value_convention": "Convencion de valor", "range": "Ra<PERSON>", "alarmname": "Nombre", "hisdata_para": "Parametro de almacenamiento", "alarmrelay": "<PERSON><PERSON>", "alarmdelay": "<PERSON><PERSON><PERSON>", "alarmlevel": "<PERSON><PERSON>", "deliver_spcu_para": "Deliver SPCU Parameters"}, "x2": "usuario2", "northnet": {"wireless_network_operator": "Operador de red", "connection_status": "Estado de conexion", "wireless_sig_strlength": "Fuerza de la senal", "north_net_para": "Parametros de red en direccion norte", "wireless_module_status": "Estado de red del modem", "wireless_connection": "Conexion inalambrica", "wireless_network_sig_info": "Informacion red inalambrica", "wired_connection": "Conexion por cable", "vpn_configuration": "Configuración VPN", "pswd_len_tip": "The maximum length of Password and Backup Password is 32 characters!", "nerwork_dest": "Network Dest", "network_mask": "Network Mask", "net_para_config": "Configuración de los parámetros de la red", "static_route_config": "Configuración de enrutamiento estático", "static_route_tip": "La configuración de enrutamiento estático no se puede configurar en 0.0.0.0", "ip_illegal_tip": "La dirección IP introducida es ilegal y el rango de valores es de 0.0.0.0 a ***************", "netmask_illegal_tip": "La entrada de máscara de red es ilegal, y su rango de valores es de 0.0.0.0 a ***************.", "username_illegal_tip": "La longitud del nombre de usuario debe ser una cadena en inglés entre 0 y 31", "pswd_illegal_tip": "La contraseña debe ser una cadena en inglés con una longitud entre 0 y 32", "ipv6_illegal_tip": "Entrada ilegal de Dirección IPv6"}, "x1": "usuario1", "config_guide": {"batt_para": "Parametro de la bateria", "system_time": "Hora del sistema", "net_para": "Red en direccion norte", "ac_para": "Parametro de AC"}, "operate_successfully": "Operacion exitosa", "no_record": "Sin Registro", "no_dry_contact": "Contacto sin seco", "no_channel_config": "Configuración sin canal", "name": "Nombre", "value_invalid": "Valor no valido", "operate_failure": "Operacion fallida", "signal": "<PERSON>al", "server": "<PERSON><PERSON><PERSON>", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pswdReset": {"reset": "Reiniciar", "check_failed": "Verificacion fallida!", "return": "Regreso", "sn_input": "Ingrese el codigo SN", "new_pswd_input_again": "Ingrese la contrasena nuevamente", "tip": "Consejo: pongase en contacto con el fabricante para obtener el codigo SN.", "pswd_new": "Nueva contrasena", "alter": "Alterar", "new_pswd_input": "Por favor ingrese una nueva contrasena", "user_invalid": "<PERSON><PERSON><PERSON> invalido", "pswd_reset_fail": "password reset failure, the account has been locked!", "find_password": "<PERSON><PERSON><PERSON><PERSON>", "user_name_input": "Por favor ingrese el nombre de usuario", "alter_success": "Contrasena modificada con exito!", "identify_code": "Codigo de identificacion", "time_valid": "Tiempo valido", "weak_pswd": "La contrasena debil!", "different_pswd": "Las contrasenas ingresadas son diferentes!", "user_name": "Nombre de usuario", "check": "Verificar", "dev_serial": "Num. serial", "pswd_confirm": "Con<PERSON><PERSON> confirm<PERSON>"}, "common": {"login_again": "Por favor iniciar sesion", "lost_auth": "<PERSON><PERSON><PERSON>,", "submit": "Enviar"}, "reboot_confirm": "¿Esta seguro de reiniciar?", "radius": {"ca_cert": "Certificado de CA", "privite_key": "Llave privada", "file": "Archivo", "para": "Parametro", "client_cert": "Certificado de cliente", "pswd_len_tip": "The maximum length of RADIUS Secret and Private Key Password is 32 characters!", "uploadcacert_file_tip": "Please select radius_rootca.pem!", "uploadclicert_file_tip": "Please select radius_user.pem!", "uploadprikey_file_tip": "Please select radius_userkey.pem!"}, "plc": {"wrong_alias": "Wrong Alias", "alias_too_long": "<PERSON><PERSON> is too long", "wrong_group_description": "wrong Group Description", "group_description_too_long": "Group description is too long", "no_correct_output": "No alarm or DO output", "too_much_output": "Too many alarms or DO output", "no_sen_para": "No PLC sentence", "data_empty": "Data Not Be Empty", "input_plc_desc": "Please Input PLC Description", "plc_config_import": "PLC Config Import", "plc_config_export": "PLC Config Export", "ok": "OK", "set": "<PERSON><PERSON><PERSON>", "reset": "Reiniciar", "dry_contact": "Contacto seco", "no_alarm": "No Alarm", "alarm": "Alarm", "backlash": "Barra invertida", "type": "Type", "constant": "Constant", "register": "Register", "signal": "<PERSON>al", "plc_edittable": "PLC EditTable", "alter": "Alterar", "add": "Agregar", "submit": "Enviar", "cancel": "Cancel", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": "Activado", "plc_desc": "PLC Desc.", "add_plc": "Add PLC", "delete": "Bo<PERSON>r", "edit": "Edit", "output": "Output", "input_two": "Input 2", "operator": "Operador", "input_one": "Input 1", "NO": "NO.", "not_active": "NotActive", "active": "Active", "enable_state": "PLC Enable State", "PAR_note": "The PAR operator indicates that alarms of the same type of devices are connected in parallel, Input 1 can select any device as required", "output_register_check": "The output register is used repeatedly", "input_register_check": "The input register without output values is used", "PAR_check": "The PAR operator can only be used to calculate alarms", "EQ_check": "The = operators cannot be calculated with analog", "NE_check": "The ≠ operators cannot be calculated with analog", "para_maintain": "PLC Config Maintain"}, "ap": {"ap_name_empty": "AP Name can not be empty!", "ap_pswd_max_length": "The maximum length of AP Password is 32 characters!", "ap_pswd_min_length": "The minimum length of AP Password is 8 characters!"}, "no_connection_tip": "Sin conexion. ¡Los datos no son en tiempo real!", "battery_group_info": {"batt_manage_sta": "Estado de gestion de la bateria"}, "nexttext": "Proximo", "delete": "Bo<PERSON>r", "ziptype_failure": "Seleccione el archivo con el sufijo .zip.", "tartype_failure": "Seleccione el archivo con el sufijo .tar.gz.", "bintype_failure": "Seleccione el archivo con el sufijo .bin.", "set": "<PERSON><PERSON><PERSON>", "char": "<PERSON><PERSON>", "second": "s", "export": "Exportar", "all_select": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "recover": "Reiniciar", "check_input_tip": "'& /;' o espacio no permitido", "north_protocol": {"snmp_para_self": "Parametros SNMP", "csu_role": "Rol de CSU", "protocol_set_tips": "Configure el protocolo norte antes de modificar los parametros del protocolo si desea cambiar el protocolo.", "Energy_SC": "Gestion de energia", "A_INTER": "A Interface Prot.", "B_INTER": "B Interface Prot.", "snmp_manager_cfg": "Administrador SNMP", "link_IP": "Enlace IP", "ip_addr": "Direc. IP", "port": "Puerto", "north_protocol": "Protocolo  Norte", "mqtt_cfg": "Parametros de MQTT", "mqtt_pswd_len_tip": "The maximum length of MQTT Password is 32 characters!", "protocol_name": "Nombre del protocolo", "listen_port": "Puerto de escucha", "modofy_link_para": "Modificar parametro de enlace", "Wireless_SC": "Protocolo adm. red inalambrica", "snmp_trap": "Param. de Capturas", "snmp_v3user": "UserInfo V3", "link_name": "Nombre del enlace", "sps_config": "Configuracion protocolos", "inform_port": "Puerto de Informacion", "ssh_enable": "Habilitar SSH", "current_nms_status": "Estado de NMS Actual", "link_type": "<PERSON><PERSON><PERSON> <PERSON>", "pswd_toolong_tip": "La contrasena es demasiado larga, la longitud de la contrasena debe ser inferior a 32 caracteres", "pswd_tooshort_tip": "Password is too short, password must contain at least 8 characters!!!", "pswd_toolong_tip_v2": "Password is too long, password length should be less than 32 bytes!!!", "ssh_username": "Nombre de usuario SSH", "link_encryption_Passwd": "Contrasena de cifrado de enlace", "pswd_tip": "Seguridad de contrasena insuficiente; ingrese 8 o mas digitos, letras mayusculas y minusculas, caracteres especiales", "link_encryption": "<PERSON><PERSON><PERSON>", "snmp_user_cfg": "Informacion de usuario SNMP", "data_port": "Puerto de datos", "snmp_v3_user_over_num": "El numero de usuarios ha alcanzado el limite superior y no se puede agregar.", "snmp_v3_sameusername": "El nombre de usuario esta duplicado, restablezca", "v3username_samewith_notifname": "The SNMP User Name is same with SNMP V3 Notification Name, please reset", "notifname_samewith_v3username": "The SNMP V3 Notification Name is same with SNMP User Name, please reset", "v3username_samewith_key": "The SNMP User Name is same with Key", "v3userkey_sameas_before": "The SNMP Key is same as before", "north_protocol_cfg": "Protocolo en direccion norte", "snmp_para_trap": "Parametros de captura SNMP", "ssh_password": "Contrasena SSH", "link_encryption_username": "Nombre de usuario de cifrado de enlace", "select_csu_role_tip": "Rol de CSU del enlace IP", "link_COM": "CO<PERSON><PERSON>", "ssh_passwd_tip": "The maximum length of the SSH Password is 20 characters!", "config_para_tip": "La entrada es ilegal y el rango de valores es", "device_id_tip": "La entrada es ilegal y debería ser una cadena numérica de no más de 14 caracteres", "ip_illegal_tip": "La dirección IP introducida es ilegal y el rango de valores es de 0.0.0.0 a ***************", "b_inter_samedeviceid": "¡El ID del punto de monitoreo es el mismo, ¡ por favor, vuelva a configurarlo!!!"}, "null": "<PERSON><PERSON>", "none": "None", "update_loading": "Actualizar...", "no": "No", "trance": {"his_inneralarm": "Alarmas internas del historial", "tranceinfo": "Informacion de seguimiento", "inneralarm": "Alarma interior real"}, "dev_maint": {"ssw_direct": "SSW (Direct Connection)", "ssw_sddu": "SDDU_SSW", "sddu": "SDDU", "fctl": "FCTL"}, "select_device": "Seleccione el dispositivo", "select_spcu_device": "Select SPCU Device", "select_spu_device": "Select SPU Device", "select_sddu_device": "Select SDDU Device", "select_ssw_device": "Select SSW Device", "state": "Estado", "client": "Cliente", "classify": {"data_classify": "Clasificacion de datos", "medium": "Medio", "hide_show": "Ocultar/mostrar"}, "input_empty": "Entrada esta vacia.", "info_scan": {"Major": "Mayor", "Warning": "Advertencia", "csu_version": "Nombre del sistema", "Mask": "Mascara", "cpu_rate": "CPU", "mem_rate": "Mem.", "Critical": "<PERSON><PERSON><PERSON>", "Minor": "<PERSON><PERSON>"}, "Enabled": "Activado", "reboot_confirm_tips": "La configuracion es correcta, entrara en vigor despues de reiniciar. Confirme si desea reiniciar.", "data_loading": "Cargando datos ...", "data_importing": "Importación en curso, por favor Espere...", "realalarm": {"active_alarm": "Alarma real", "dry_contact_output": "Salida de contacto seco", "name": "Nombre de alarma", "level": "<PERSON><PERSON> de <PERSON>", "generated_time": "Tiempo generado", "real_alarm": "Alarma real"}, "logout": "<PERSON><PERSON><PERSON> se<PERSON>", "user": {"delsuccessalert": "Eliminar usuario exitoso", "adduser": "Agregar usuario", "oldpswd": "Contrasena anterior", "addsuccessalert": "Agregar usuario exitoso", "max_login_wrong_times": "Numero de veces de inicio de sesion incorrectos", "altersuccessalert": "Cambio de contrasena exitoso", "insertrighttest": "Por favor ingrese un valor valido", "last_time": "Hora del ultimo inicio de sesion", "adduserpsw1": "<PERSON><PERSON><PERSON>", "sameuserandpwd": "La contrasena contiene el nombre de usuario o la inversion del nombre de usuario, restablezca!", "deleteuser": "<PERSON><PERSON><PERSON> usuario", "adduserpsw2": "Confirmar contrasena ", "alterfailedalert": "Error de cambio de contrasena", "delfailedalert": "Eliminar usuario fallido", "addfailedalert": "Agregar usuario fallido", "alterpswd": "Modificar contrasena", "username": "Usuario", "gui_pswd_tip": "Ingrese una contrasena de cuatro digitos", "administrator": "Administrador", "newpswd": "Nueva contrasena", "confirmend": "?", "pswd_tip": "La contrasena debe contener letras mayusculas y minusculas, numeros y caracteres especiales", "usernumbermax": "Se alcanza el numero maximo de usuarios", "usernamehavespaces": "Please do not use spaces.", "is_admin": "<PERSON>vel de usuario", "usermanage": "Gestion de usuarios", "userlevel": "<PERSON>vel de usuario", "confirmdeleteuser": "<PERSON><PERSON><PERSON> usuario ", "newpswd2": "Confirmar con<PERSON><PERSON> nueva", "general_user": "Usuario general", "notag": "No", "yestag": "Si", "pswnotmatchalert": "Las contrasenas no coinciden", "addusername": "Nombre de usuario", "username_toolong_tip": "Username is too long, Username length should be less than 64 characters!!!", "username_empty_tip": "Username can not be empty!"}, "active": "<PERSON><PERSON><PERSON>", "no_displayable_signal": "Sin senales visibles", "no_displayable_data": "No Displayable Data", "search": "Buscar", "ok": "OK", "lasttext": "Ultimo", "language": "Idioma", "operate_overtime": "Tiempo de expiracion de operacion", "default_pswd_tip": "The current password used belongs to the default password in the user manual, and there is a risk of leakage. It is recommended to modify it in a timely manner. The security risks caused by using the default password will be borne by the user themselves!", "alias": "Nombre Alias", "contact": "Contactenos", "control_cmd_success": "Comando de control envio exitoso", "abnormal_data_upload": "<PERSON><PERSON> anormales, carguelo de nuevo.", "login": {"pswd_reset": "¿Olvidaste tu contrasena?", "plat_name": "Gestion de energia", "copyright": "© 2024 ZTE Corporation.  Todos los derechos reservados", "fill_username": "Por favor ingrese el nombre de usuario", "login_error": "Nombre de usuario o contrasena incorrecta", "maxuser": "El numero de usuarios registrados alcanza el maximo. ¿Desea continuar?", "fill_password": "Por favor ingrese contrasena", "error_tip_part2": "¡la cuenta se bloqueara entonces!", "error_tip_part1": "intentos restantes:", "over_times_tip": "Sus errores de inicio de sesion han alcanzado su numero maximo. Vuelve a intentarlo despues de 30 minutos.", "login": "Iniciar sesion", "empty": "Iniciar sesion"}, "history": {"all": "Todos", "select_type": "Seleccione tipo", "event_type": "Tipo de evento", "history_event": "Registro de operaciones", "peak_type": "Tipo de pico", "time_range": "Intervalo de tiempo", "peak_record": "Registros pico", "com_device_update_record": "COM. Eventos de actualizacion de dispositivo", "num": "No.", "uib_device_update_record": "Eventos de actualizacion de UIB / IDDB", "operator": "Operador", "export_historydata": "Exportar todos los datos del historial", "signal_value": "Valor de la senal", "record_endtime": "Hora de finalizacion", "oprate_record": "Registro de operaciones", "content": "Contenido", "to": "A", "signal_unit": "Unidad de senal", "history_inf": "Record historico", "safety_record": "Eventos de seguridad", "smr_update_event": "Evento de actualizacion de SMR", "statistic_records": "Registro de eventos", "signal_name": "Nombre de la senal", "record_detail": "Detalles del registro", "start_time": "Tiempo generado", "lines_per_page": "Lineas por. Pagina", "export_historyalarm": "Exportar todas las alarmas del historial", "record_starttime": "Tiempo de Inicio", "record_type": "Tipo de registro", "smr_device_update_record": "Eventos de actualizacion de SMR", "device": "Dispositivo", "running_record": "Eventos de ejecucion", "history_data": "Informacion historica", "generate_time": "Tiempo generado", "search": "Buscar", "alarm_level": "<PERSON><PERSON> de <PERSON>", "signal": "<PERSON>al", "alarm_name": "Nombre de alarma", "can_device_update_record": "CAN Eventos de actualizacion de dispositivo", "history_alarm": "Alarma historica", "end_time": "Tiempo de recuperacion", "export_historyevent": "Exportar todos los registros operativos"}, "com": {"serial_port_settings": "Configuracion de puerto serial"}, "temp_humid_conf": {"tab_name": "Temperature & Humidity", "channel_name": "Channel Name", "temp_sensor": "Temperature Sensor", "humid_sensor": "<PERSON><PERSON><PERSON><PERSON>", "sid": "SID"}, "update_failure": "Actualizacion fallida", "input_max_length": "<PERSON><PERSON><PERSON> maxima", "attr_value": "Valor de parametro"}