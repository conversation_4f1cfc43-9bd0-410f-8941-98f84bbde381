<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="classify">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-if="userlevel > '1'">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.data_classily}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>

                <div class="page-content" >
                    <div class="row-fluid">
                        <div class="span10">
                            <div id="failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li class="active">
                                        <a data-toggle="tab" href="#dataclassify">
                                            <i class="blue icon-cog bigger-110"></i>
                                            {{i18nkeyword.menu.data_classily}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div id="dataclassify" class="tab-pane active">
                                        <table class="table table-bordered">
                                            <tbody>
                                              <tr>
                                                <td>
                                                    {{i18nkeyword.device}}
                                                    <select class="editable input-medium" ms-duplex="classify_selectdevsid"  style="width:auto; min-width: 150px;" id="selecthisdata">
                                                        <option value = "-1">{{i18nkeyword.select_device}}</option>
                                                        <option ms-for="sidnum in devsid_listvalue" ms-if="checkDevIndex(sidnum.sid)" ms-attr="{value:sidnum.sid}">{{@getDevParentName(sidnum["device name"])}}</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    {{i18nkeyword.signal}}
                                                    <select class="editable input-medium" ms-duplex="classify_selectdevtype" style="width:auto; min-width: 150px;" id="selecthissigdata">
                                                        <option value = "0">{{i18nkeyword.all}}</option>
                                                        <option ms-for="devtype in classify_devType" ms-attr="{value:devtype.value}">{{devtype["name"]}}</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <button id="classify_searchBT" class="button button-small button-flat-primary" onClick="doClassifySearch()">
                                                    {{i18nkeyword.search}}
                                                    </button>
                                                </td>
                                              </tr>
                                            </tbody>
                                        </table>
                                        <table class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th>{{i18nkeyword.NO}}</th>
                                                    <th>{{i18nkeyword.name}}</th>
                                                    <th>Web</th>
                                                    <th>GUI</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!--ms-for:(num,classifly) in classify_value-->
                                                <tr ms-if = "@checkSidIndex(classifly.sid) == '1'">
                                                    <td>{{num+1}}</td>
                                                    <td>{{@getSidParentName(classifly["full_name"], classifly.sid)}}</td>
                                                    <td>
                                                        <center ms-if="classifly.web == '1'">
                                                            <input type="checkbox" id="web_tag" ms-change="changeClassiflyValue($event,num)"  class="ace ace-switch ace-switch-3">
                                                            <span class="lbl"></span>
                                                        </center>
                                                        <center ms-if="classifly.web == '0'">
                                                            <input type="checkbox" id="web_tag" checked="" ms-change="changeClassiflyValue($event,num)"  class="ace ace-switch ace-switch-3">
                                                            <span class="lbl"></span>
                                                        </center>
                                                    </td>
                                                    <td>
                                                        <center ms-if="classifly.gui == '1'">
                                                            <input type="checkbox" id="gui_tag" ms-change="changeClassiflyValue($event,num)"  class="ace ace-switch ace-switch-3">
                                                            <span class="lbl"></span>
                                                        </center>
                                                        <center ms-if="classifly.gui == '0'">
                                                            <input type="checkbox" id="gui_tag" checked="" ms-change="changeClassiflyValue($event,num)"  class="ace ace-switch ace-switch-3">
                                                            <span class="lbl"></span>
                                                        </center>
                                                    </td>
                                                </tr>
                                                <!--ms-for-end:-->
                                            </tbody>
                                        </table>
                                        <button class="button button-small button-flat-primary" style="position: fixed ! important; right: 10%; top: 210px;" onClick="setClassiflyData()"><i class="icon-pencil bigger-100"></i>
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div>
                                </div>
                            </div><!--/tabbable-->
                        </div><!--/span10-->
                            <!--PAGE CONTENT ENDS-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-content-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/page/js/config_dataclassify.js"></script>
</body>

</html>