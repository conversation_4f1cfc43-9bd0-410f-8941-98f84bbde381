<!DOCTYPE html>
<html>    
    <head>
		<!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        
        <div class="main-container container-fluid" ms-controller="paraset_inspect">
			<!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.devlist.inspect_param_set}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
							<div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
								<i class="icon-warning-sign  icon-animated-bell">
								</i>
								{{i18nkeyword.operate_successfully}}
							</div>
							<div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
								<i class="icon-warning-sign  icon-animated-bell">
								</i>
								{{i18nkeyword.operate_failure}}
							</div>
							<!--PAGE CONTENT BEGINS-->
							<div id="paraset" class="tab-content" >								
															   
								<div class="tab-pane active" id="alarmpara" style="padding-top:30px;">
									<table ms-if="inspect_paras.length != 0" id="sample-table-1" class="table table-striped table-bordered table-hover">
										<thead>
											<tr>
											    <th>{{i18nkeyword.devlist.devicename}}</th>
												<th>{{i18nkeyword.devlist.inspect_name}}</th>
												<th>{{i18nkeyword.devlist.inspect_currvalue}}</th>
												<th>
													{{i18nkeyword.devlist.inspect_offset}}
												</th>
												<th>{{i18nkeyword.devlist.inspect_slop}}</th>
											</tr>
										</thead>

										<tbody>
											<!--ms-for:(i,param) in inspect_paras-->
											<tr>
												<td>{{param["Device Name"]}}</td>
												<td>{{param.full_name}}</td>
												<td>{{@get_paraset_value(param)}}</td>
												<td>
													<input ms-if="@jugge_setdata_failure(@param.sid,'Offset')" ms-css="{'color':'#ff0000'}"
													class="form-control input-medium pull-left"
													ms-duplex="param.Offset" ms-change="changeDevData(@param.sid,'Offset')" />
													<input ms-if="!@jugge_setdata_failure(@param.sid,'Offset')" 
													class="form-control input-medium pull-left" 
													ms-duplex="param.Offset" ms-change="changeDevData(@param.sid,'Offset')" />
												</td>
												<td>
													<input ms-if="@jugge_setdata_failure(@param.sid,'Slope')" ms-css="{'color':'#ff0000'}"
													class="form-control input-medium pull-left"
													ms-duplex="param.Slope" ms-change="changeDevData(@param.sid,'Slope')" />
													<input ms-if="!@jugge_setdata_failure(@param.sid,'Slope')" 
													class="form-control input-medium pull-left" 
													ms-duplex="param.Slope" ms-change="changeDevData(@param.sid,'Slope')" />
												</td>
											</tr>
											<!--ms-for-end:-->
										</tbody>
									</table>
									<button style="position: fixed ! important; right: 10%; top: 100px;" class="button button-small button-flat-primary" ms-click="setDevData"><i class="icon-pencil bigger-100"></i>
									{{i18nkeyword.set}}
									</button>  
								</div>
								
							</div>
							<!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->        
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/paraset_inspect.js"></script>
    </body>

</html>