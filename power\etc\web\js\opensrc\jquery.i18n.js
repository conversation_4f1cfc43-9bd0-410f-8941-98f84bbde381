(function($){$.i18n={};$.fn.extend({i18n:function(options){var defaults={lang:"",defaultLang:"",filePath:"",filePrefix:"i18n_",fileSuffix:"",forever:true,callback:function(){}};function getCookie(name){var arr=document.cookie.split('; ');for(var i=0;i<arr.length;i++){var arr1=arr[i].split('=');if(arr1[0]==name){return arr1[1]}}return''};function setCookie(name,value,myDay){var oDate=new Date();oDate.setDate(oDate.getDate()+myDay);document.cookie=name+'='+value+'; expires='+oDate};var options=$.extend(defaults,options);if(getCookie('i18n_lang')!=""&&getCookie('i18n_lang')!="undefined"&&getCookie('i18n_lang')!=null){defaults.defaultLang=getCookie('i18n_lang')}else if(options.lang==""&&defaults.defaultLang==""){};if(options.lang!=null&&options.lang!=""){if(options.forever){setCookie('i18n_lang',options.lang)}else{$.removeCookie("i18n_lang")}}else{options.lang=defaults.defaultLang};var i=this;$.ajax({type:"post",url:options.filePath+options.filePrefix+options.lang+options.fileSuffix+".json",dataType:"json",success:function(data){var i18nLang={};if(data!=null){i18nLang=data}mainvalue.i18nkeyword={};mainvalue.i18nkeyword=i18nLang;options.callback()}})}})})(jQuery);