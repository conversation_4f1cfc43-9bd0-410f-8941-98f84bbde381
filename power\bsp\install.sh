#!/bin/sh

module_adc="ti_adc"
device_adc="adc" 

module_cstn="cstn"
device_cstn="spilcd" 

module_io="ioctl"
device_io="ioctl"

major=-1

rmmod $module_io
if [ "$?" != "0" ]
then
echo ...error ignored
fi
lsmod
/sbin/insmod /root/power/bsp/$module_io.ko
if [ "$?" = "0" ]
then
rm -rf /dev/${device_io}
major=`cat /proc/devices | awk "\\$2==\"$device_io\" {print \\$1}"`
if [ "$?" = "0" ]
then
mknod /dev/${device_io} c $major 0
echo "Created nodes /dev/${device_io}"
fi
fi

major=-1
rmmod $module_adc
if [ "$?" != "0" ]
then
echo ...error ignored
fi
lsmod
/sbin/insmod /root/power/bsp/$module_adc.ko
if [ "$?" = "0" ]
then
rm -rf /dev/${device_adc}
major=`cat /proc/devices | awk "\\$2==\"$device_adc\" {print \\$1}"`
if [ "$?" = "0" ]
then
mknod /dev/${device_adc} c $major 0
echo "Created nodes /dev/${device_adc}"
fi
fi

major=-1
rmmod $module_cstn
if [ "$?" != "0" ]
then
echo ...error ignored
fi
lsmod
/sbin/insmod /root/power/bsp/$module_cstn.ko
if [ "$?" = "0" ]
then
rm -rf /dev/${device_cstn}
major=`cat /proc/devices | awk "\\$2==\"$device_cstn\" {print \\$1}"`
if [ "$?" = "0" ]
then
mknod /dev/${device_cstn} c $major 0
echo "Created nodes /dev/${device_cstn}"
fi
fi

/sbin/insmod /root/power/bsp/boardtype_driver.ko

rm -rf /dev/i2c-1 /dev/i2c-2
mknod /dev/i2c-1 c 89 1
mknod /dev/i2c-2 c 89 2



#rm -rf /dev/spilcd
#/sbin/insmod /root/power/bsp/cstn.ko
#mknod /dev/spilcd c 250 5
#lsmod
exit
