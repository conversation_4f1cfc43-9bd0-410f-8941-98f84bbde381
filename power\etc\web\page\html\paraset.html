 <!DOCTYPE html>
<html>
	<head>
		<!--#include virtual="/page/html/include.html" -->
		<style>
			.user-dis{
				display: none;
			}
		</style>
	</head>

	<body class="navbar-fixed ms-controller" ms-controller="container">
		<!--#include virtual="/page/html/prompt.html" -->
		<!--#include virtual="/page/html/header.html" -->

		<div class="main-container container-fluid" ms-controller="paraset_info">
			<!--#include virtual="/page/html/menu.html" -->
			<div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.devlist.paraset}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
				<div class="page-content">
					<div class="row-fluid">
						<div class="span10">
							<div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
								<i class="icon-warning-sign  icon-animated-bell">
								</i>
								{{i18nkeyword.operate_successfully}}
							</div>
							<div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
								<i class="icon-warning-sign  icon-animated-bell">
								</i>
								{{i18nkeyword.operate_failure}}
							</div>
							<!--PAGE CONTENT BEGINS-->
							<div id="paraset" class="tab-content" >
								<ul class="nav nav-tabs" id="myTab">
									<li ms-class="'parameter' == tab_list?'active':''" ms-click="tabChange('parameter')">
										<a data-toggle="tab"  href="#parameter">
											<i class="blue icon-certificate bigger-110">
											</i>
											{{i18nkeyword.devlist.parameter_data}}
										</a>
									</li>
									<li ms-class="'alarm_para' == tab_list?'active':''" ms-click="tabChange('alarm_para')">
										<a data-toggle="tab"  href="#alarmpara">
											<i class="blue icon-bell bigger-110">
											</i>
											{{i18nkeyword.devlist.alarm_data}}
										</a>
									</li>
								</ul>
								<div class="tab-pane" id="alarmpara" ms-class="'alarm_para' == tab_list?'active':''">
									<div class="dd-draghandle">
										<ol class="dd-list">
											<li ms-for="(index,el) in alarm_block_dic"class="dd-item dd2-item">
												<div class="dd-handle dd2-handle">
													<i class="normal-icon  icon-cog orange bigger-130"></i>
													<i class="drag-icon icon-move bigger-125"></i>
												</div>
												<div class="dd2-content">{{@getDevSidParentName(el[0]["device name"])}}
												</div>
												<table ms-if="alarm_paras.length != 0 || parameters.length != 0 || hisdata_paras.length != 0" id="sample-table-1" class="table table-striped table-bordered table-hover">
													<thead>
														<tr>
															<th style="width:30%">{{i18nkeyword.devlist.signalname}}</th>
															<th style="width:35%">
																<i class="icon-time bigger-110 hidden-480"></i>
																{{i18nkeyword.devlist.alarmlevel}}
															</th style="width:35%">
															<th>{{i18nkeyword.devlist.alarmrelay}}</th>
														</tr>
													</thead>

													<tbody>
														<!--ms-for:(i,alarm) in el-->
														<tr ms-if = "@checkSidIndex(alarm.sid) == '1'">
															<td>{{@getSidParentName(alarm.full_name, alarm.sid)}}</td>
															<td>
																<select  class="editable input-medium" ms-duplex="alarm.alm_level" data-duplex-changed="changeDevData(@alarm.sid,'alm_level')">
																	<option ms-for="(num,name) in para_head_dic[alarm.sid].alm_level_list" ms-attr="{value:num,selected:num==alarm.alm_level?true:false}">
																		{{name}}
																	</option>
																</select>
															</td>
															<td>
																<select  class="editable input-medium" ms-duplex="alarm.alm_relay" data-duplex-changed="changeDevData(@alarm.sid,'alm_relay')" style="width:165px" >
																	<option value = "0" >{{i18nkeyword.null}}</option>
																	<!--ms-for:(num,name) in doconvention-->
																	<option ms-attr="{value:num,selected:num==alarm.alm_relay?true:false}">{{name}}</option>
																	<!--ms-for-end:-->
																</select>
															</td>
														</tr>
														<!--ms-for-end:-->
													</tbody>
												</table>
											</li>
										</ol>
									</div>
									<button style="position: fixed ! important; right: 10%; top: 100px;" class="button button-small button-flat-primary" onClick="setDevData(1,'attr')"<i class="icon-pencil bigger-100"></i>
									{{i18nkeyword.set}}
									</button>
								</div><!--告警参数设置页面-->
								<div class="tab-pane" id="parameter" ms-class="'parameter' == tab_list?'active':''">
									<div class="dd-draghandle">
										<ol class="dd-list">
											<li ms-for="(index,el) in para_block_dic" class="widget-box" style="margin-bottom:5px;" ms-attr="{id:index}">
												<div class="widget-header widget-header-flat widget-header-small" ms-click="@showTableContent($event, index)">
													<h5 class="lighter">
														<i class="icon-cog"></i>
														{{el[0]["block_name"]}}
													</h5>
													<div class="widget-toolbar pull-right">
														<a>
															<i class="icon-chevron-down"></i>
														</a>
													</div>
												</div>
												<div class="widget-body user-dis" style="overflow-y:auto;margin-bottom:5px">
													<div class="widget-body-inner" style="display: block;">
														<div class="widget-main no-padding">
															<table class="table table-striped table-bordered table-hover">
																<thead>
																	<tr class="info">
																		<th style="width: 30%;">{{i18nkeyword.devlist.signalname}}</th>
																		<th style="width: 35%;"><i class="icon-time bigger-110 hidden-480"></i>{{i18nkeyword.devlist.value}}</th>
																		<th>{{i18nkeyword.devlist.range}}</th>
																	</tr>
																</thead>
																<tbody >
																	<!--ms-for:(i,parameter) in el-->
																	<tr ms-if="getconventionlength(parameter.convention)<=0">
																		<td >{{parameter.full_name}}</td>
																		<td ms-if="parameter.encrypt != '1'">
																			<input ms-if="@jugge_setdata_failure(@parameter.sid,'value')" ms-css="{'color':'#ff0000'}"
																			ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
																			ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
																			<input ms-if="!@jugge_setdata_failure(@parameter.sid,'value')"
																			ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
																			ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
																		</td>
																		<td ms-if="parameter.encrypt == '1'">
																			<input type="password" autocomplete="new-password"
																			ms-if="@jugge_setdata_failure(@parameter.sid,'value')" ms-css="{'color':'#ff0000'}"
																			ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
																			ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
																			<input type="password" autocomplete="new-password"
																			ms-if="!@jugge_setdata_failure(@parameter.sid,'value')"
																			ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
																			ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
																		</td>
																		<td style="font-family:none;">{{@get_paraset_rang_value(parameter)}}</td>
																	</tr>
																	<tr ms-if="getconventionlength(parameter.convention)>0">
																		<td >{{parameter.full_name}}</td>
																		<td >
																			<select  class="editable input-medium" ms-duplex="parameter.value" data-duplex-changed="changeDevData(@parameter.sid,'value')">
																				<option ms-for="(num,name) in parameter.convention" ms-attr="{value:num,selected:num==parameter.value?true:false}">{{name}}</option>
																			</select>
																		</td>
																		<td></td>
																	</tr>
																	<!--ms-for-end:-->
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</li><!--参数列表-->
										</ol>
										<button style="position: fixed ! important; right: 10%; top: 100px;" class="button button-small button-flat-primary" onClick="setDevData(2,'value')"
											<i class="icon-pencil bigger-100"></i>
											{{i18nkeyword.set}}
										</button>
									</div>
								</div><!--参数量设置页面-->
							</div>
							<!--PAGE CONTENT ENDS-->
						</div><!--/span10-->
					</div><!--/row-fluid-->
				</div><!--/page-content-->
			</div><!--/main-contain-->
		</div><!--/main-container-->
		<!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/paraset.js"></script>
	</body>

</html>