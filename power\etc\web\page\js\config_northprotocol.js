var default_snmp_manager = {
			"SNMP Manager IP":	"127.0.0.1",
			"SNMP Notification Port":	"162",
			"SNMP Manager Name":	"default_snmp_manager",
};

var default_northpro_value = [{
	"data port":	"0",
	"IP address":	"0.0.0.0",
}, {
	"data port":	"0",
	"IP address":	"0.0.0.0",
}, {
	"data port":	"0",
	"IP address":	"0.0.0.0",
}, {
	"listen port":	"0",
}, {
	"link inst id":	"plat.COM1",
}];

var default_NMS_value = [{
	"ip_nms_id":	"pdt.ip_1104_nms1",
	"ip_nms_name":	"ip_1104_data_nms1",
}, {
	"ip_nms_id":	"pdt.ip_1104_nms2",
	"ip_nms_name":	"ip_1104_data_nms2",
}, {
	"ip_nms_id":	"pdt.ip_1104_nms3",
	"ip_nms_name":	"ip_1104_data_nms3",
}];

var default_COM_value = [{
	"instid":	"plat.COM1",
	"Serial Name":	"COM1",
}, {
	"instid":	"plat.COM2",
	"Serial Name":	"COM2",
}];

var IP_MIN = "0.0.0.0";
var IP_MAX = "***************";

var b_inter_id_maxlen = 14;
var snmp_pswd_maxlen = 32;
var snmp_pswd_minlen = 8;

function del_snmp_manager_succ(d,r) {
		snmp_manager_list();
}
function del_snmp_v3user_succ(d,r) {
		snmp_v3user_list();
}
var vmodel = avalon.define({
	$id:'northprotacal',
	tab_list:'sps_config_1104_tab',
	tab_list2:'snmpuser_tab',
	structuredata:[], //N接口的通用结构信息
	ifnvalue:{},// N接口信息,
	ifnvalue_backup:{},// N接口信息,
	snmp_manager_structre:[],
	snmp_manager_infos:[],
	snmp_v3user_structre:[],
	snmp_v3user_infos:[],
	snmp_v3user_infos_backup:[],
	snmp_v3user_attr_changed:{},
	snmp_v3user_attr_ifchanged:false,
	add_snmp_manager_info:default_snmp_manager,
	add_snmp_v3user_info:{},
	mqtt_structre:[],
	mqtt_infos:[],
	snmp_v3user_changeNo:0,
	snmp_v3user_duplexNo:0,
	snmp_changeNo:0,
	snmp_duplexNo:0,
	snmp_manager_changeNo:0,
	snmp_trap_changeNo:0,
	snmp_trap_duplexNo:0,

	init_current_north_protocol:'SNMP',
	current_north_protocol:[],
	snmp_v3user_add:0,

	north_sps_b_inter_ListData:[],
	north_sps_b_inter_AttrData:[],
	north_sps_b_inter_ValueData:[],
	north_sps_b_inter_CheckData:[], //存储配置参数值和其对应的最小最大值，对配置文件进行适应性校验

	// cfg_1104_link_type:"com_link",
	north_sps_1104_values:default_northpro_value,
	north_sps_1104_value:default_northpro_value[3],
	north_sps_1104_value_com:{},
	north_sps_1104_values_recuce:[],
	north_sps_1104_ip_client_values:default_NMS_value,
	cfg_1104_link_id:"",
	cfg_1104_client_link_id:"pdt.ip_1104_nms1",
	north_sps_1104_csu_role:"1",    //  默认为服务端
	//north_sps_1104_inform_port:"0",

	// cfg_1363_link_type:"com_link",
	north_sps_1363_values:default_northpro_value,
	north_sps_1363_value:default_northpro_value[3],
	north_sps_1363_value_com:{},
	north_sps_1363_values_recuce:[],
	north_sps_1363_ip_client_values:default_NMS_value,
	cfg_1363_link_id:"",
	cfg_1363_client_link_id:"pdt.ip_1363_nms1",
	north_sps_1363_csu_role:"1",
	//north_sps_1363_inform_port:"0",

	// cfg_sm_link_type:"com_link",
	north_sps_sm_values:default_northpro_value,
	north_sps_sm_value:default_northpro_value[3],
	north_sps_sm_value_com:{},
	north_sps_sm_values_recuce:[],
	north_sps_sm_ip_client_values:default_NMS_value,
	cfg_sm_link_id:"",
	cfg_sm_client_link_id:"pdt.ip_sm_nms1",
	north_sps_sm_csu_role:"1",    //  默认为服务端
	//north_sps_sm_inform_port:"0",

	// cfg_power_sm_link_type:"com_link",
	north_sps_power_sm_values:default_northpro_value,
	north_sps_power_sm_value:default_northpro_value[3],
	north_sps_power_sm_value_com:{},
	north_sps_power_sm_values_recuce:[],
	north_sps_power_sm_ip_client_values:default_NMS_value,
	cfg_power_sm_link_id:"",
	cfg_power_sm_client_link_id:"pdt.ip_power_sm_nms1",
	north_sps_power_sm_csu_role:"1",    //  默认为服务端
	//north_sps_power_sm_inform_port:"0",

	// cfg_a_inter_link_type:"com_link",
	north_sps_a_inter_values:default_northpro_value,
	north_sps_a_inter_value:default_northpro_value[3],
	north_sps_a_inter_value_com:{},
	north_sps_a_inter_values_recuce:[],
	north_sps_a_inter_ip_client_values:default_NMS_value,
	cfg_a_inter_link_id:"",
	// cfg_a_inter_client_link_id:"pdt.ip_a_inter_nms1",
	cfg_a_inter_client_link_id:"pdt.ip_a_inter_nms1",
	north_sps_a_inter_csu_role:"1",
	//north_sps_a_inter_inform_port:"0",

	csu_role_set_para:{"csu role":	"0", "protocol":"1104"},

	comvalues:default_COM_value,
	ip_link_values:[],
	link_types:['plat.com', 'plat.ip_link'],

	judge_b_inter_para:function(paraname) {
		if (paraname == 'Inform IP Address') {
			return true;
		}
		return false;
	},
	delete_snmp_manager:function(instid,name) {
		if(confirm(mainvalue.i18nkeyword.user.confirmdeleteuser+name+mainvalue.i18nkeyword.user.confirmend)) {
			var para = {"instid":instid};
			var del_snmp_manager_req = {data:{objectid:"plat.snmp_manager",type:"inst_delete",paranum:"1",paraval:JSON.stringify([para])},success:del_snmp_manager_succ};
			request.addRequest([del_snmp_manager_req]);
		}
	},
	delete_snmp_v3user:function(instid,name) {
		if(confirm(mainvalue.i18nkeyword.user.confirmdeleteuser+name+mainvalue.i18nkeyword.user.confirmend)) {
			var para = {"instid":instid};
			var del_snmp_v3user_req = {data:{objectid:"plat.snmp_v3user",type:"inst_delete",paranum:"1",paraval:JSON.stringify([para])},success:del_snmp_v3user_succ};
			request.addRequest([del_snmp_v3user_req]);
		}
	},
	judge_snmp_trap_para:function(paraname) {
		if (paraname == 'SNMP Read Community' || paraname == 'SNMP Write Community') {
			return 'snmp_self'; //snmp 自身属性
		} else {
			return 'snmp_trap'; //snmp trap属性
		}
	},
	judge_snmp_v3user_para:function(trapmode,paraname) {
		if (trapmode == 3) {  //参考约束关系 3表示trap v3
			return false;
		} else {
			if (paraname == 'SNMP Notification Mode') {
				return false;
			}
			return true;
		}
		return false;
	},
	judge_active_protocol_set:function(tab_id) {
		var protocol = vmodel.current_north_protocol[0]['current protocol']?vmodel.current_north_protocol[0]['current protocol']:"*xsx*";
		if (checkStringStr(tab_id,protocol)) {
			return 'true'
		}
		return 'false';
	},
	show_disallow_input:function(){
		return mainvalue.i18nkeyword.Disallow_input + " & ";
	},
	changecanmake: function(){
		vmodel.ifnvalue['SNMP Enable'] = event.target.value;
	},
	changecanmake2: function(){
		vmodel.ifnvalue['SNMP Community Strong Passwd Enable'] = event.target.value;
	},
	changecanmake3: function(){
		vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] = event.target.value;
	},
	changeChannelData: function(type, changeid){
		switch(changeid){
			case "snmp":
				if(type == 1) {
					vmodel.snmp_duplexNo++;
				} else {
					vmodel.snmp_changeNo = 1;
				}
				break;
			case "snmp_v3user":
			    if(type == 1) {
					vmodel.snmp_v3user_duplexNo++;
				} else {
					vmodel.snmp_v3user_changeNo = 1;
				}
				break;
			case "snmp_manager":
			    vmodel.snmp_manager_changeNo = 1;
				break;
			case "snmp_trap": 
				if(type == 1) {
					vmodel.snmp_trap_duplexNo++;
				} else {
					vmodel.snmp_trap_changeNo = 1;
				}				
				break;
			default:
				break;
		}
	},
	changeSnmpNameOrKey: function(userindex, attr) {
		vmodel.snmp_v3user_attr_changed.userindex = userindex;
		vmodel.snmp_v3user_attr_changed.attr = attr;
		vmodel.snmp_v3user_attr_ifchanged = true;
	},
	checkSnmpNameAndKey:function() {
		if (false == vmodel.snmp_v3user_attr_ifchanged) {
			return ;
		}
		vmodel.snmp_v3user_attr_ifchanged = false;
		let userindex = vmodel.snmp_v3user_attr_changed.userindex;
		let attr = vmodel.snmp_v3user_attr_changed.attr;
		if (attr == "SNMP User Name") {
			if (vmodel.snmp_v3user_infos[userindex][attr] == vmodel.snmp_v3user_infos[userindex]["SNMP Authentication Key"] ||
				vmodel.snmp_v3user_infos[userindex][attr] == vmodel.snmp_v3user_infos[userindex]["SNMP Privacy Key"]) {
				alert(vmodel.snmp_v3user_infos[userindex]["SNMP User Name"] + " " + mainvalue.i18nkeyword.north_protocol.v3username_samewith_key);
				snmp_v3user_list();
				return;
			}
		} else if (attr == "SNMP Authentication Key" || attr == "SNMP Privacy Key") {
			if (vmodel.snmp_v3user_infos[userindex][attr] == vmodel.snmp_v3user_infos_backup[userindex][attr]) {
				alert(vmodel.snmp_v3user_infos[userindex]["SNMP User Name"] + " " + mainvalue.i18nkeyword.north_protocol.v3userkey_sameas_before);
				snmp_v3user_list();
				return;
			}
			if (vmodel.snmp_v3user_infos[userindex][attr] == vmodel.snmp_v3user_infos[userindex]["SNMP User Name"]) {
				alert(vmodel.snmp_v3user_infos[userindex]["SNMP User Name"] + " " + mainvalue.i18nkeyword.north_protocol.v3username_samewith_key);
				snmp_v3user_list();
				return;
			}
		} else {
			return ;
		}
	},
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
		send_request_by_tab(tab);
    },
    tabChange2 : function(tab) {
        vmodel.tab_list2 = tab;
        set_cookie_with_path("tab_list2", vmodel.tab_list2);
    },
});

function send_request_by_tab(tab){
	switch(tab){
		case "sps_config_1104_tab":
			send_1104_request();
			clear_request_by_flag(["1363", "sm", "power_sm", "a_inter", "b_inter", "snmp", "mqtt"]);
			break;
		case "sps_config_1363_tab":
			send_1363_request();
			clear_request_by_flag(["1104", "sm", "power_sm", "a_inter", "b_inter", "snmp", "mqtt"]);
			break;
		case "sps_config_sm_tab":
			send_sm_request();
			clear_request_by_flag(["1104", "1363", "power_sm", "a_inter", "b_inter", "snmp", "mqtt"]);
			break;
		case "sps_config_power_sm_tab":
			send_power_sm_request();
			clear_request_by_flag(["1104", "1363", "sm", "a_inter", "b_inter", "snmp", "mqtt"]);
			break;
		case "sps_config_a_inter_tab":
			send_a_inter_request();
			clear_request_by_flag(["1104", "1363", "sm", "power_sm", "b_inter", "snmp", "mqtt"]);
			break;
		case "sps_config_b_inter_tab":
			send_b_inter_request();
			clear_request_by_flag(["1104", "1363", "sm", "power_sm", "a_inter", "snmp", "mqtt"]);
			break;
		case "SNMP_config_tab":
			send_snmp_request();
			clear_request_by_flag(["1104", "1363", "sm", "power_sm", "a_inter", "b_inter", "mqtt"]);
			break;
		case "mqtt_config_tab":
			send_mqtt_request();
			clear_request_by_flag(["1104", "1363", "sm", "power_sm", "a_inter", "b_inter", "snmp"]);
			break;
		default:
			clear_request_by_flag(["1104", "1363", "sm", "power_sm", "a_inter", "b_inter", "snmp", "mqtt"]);
			break;
	}
}

function clear_request_by_flag(arr){
	var map = {
			"1104":clear_1104_request,
			"1363":clear_1363_request,
			"sm":clear_sm_request,
			"power_sm":clear_power_sm_request,
			"a_inter":clear_a_inter_request,
			"b_inter":clear_b_inter_request,
			"snmp":clear_snmp_request,
			"mqtt":clear_mqtt_request
			};
	for(var i=0;i<arr.length;i++){
		if(map[arr[i]]){
			map[arr[i]]();
		}
	}
}
/*************************************************************************************************************************************/
/****************************************sps协议栈(1104\1363\SM\POWER_SM\A接口)参数设置接口***************************************************/
function  classify_sps_values(obj) {
	vmodel.north_sps_1104_values.clear();
	vmodel.north_sps_1363_values.clear();
	vmodel.north_sps_sm_values.clear();
	vmodel.north_sps_power_sm_values.clear();
	vmodel.north_sps_a_inter_values.clear();
	for (var i in vmodel.north_sps_values) {
		if (checkStringStr(vmodel.north_sps_values[i].full_name, "1104")) {
			vmodel.north_sps_1104_values.push(vmodel.north_sps_values[i]);
		}
		if (checkStringStr(vmodel.north_sps_values[i].full_name, "1363")) {
			vmodel.north_sps_1363_values.push(vmodel.north_sps_values[i]);
		}
		if (checkStringStr(vmodel.north_sps_values[i].full_name, "SM")) {
			vmodel.north_sps_sm_values.push(vmodel.north_sps_values[i]);
		}
		if (checkStringStr(vmodel.north_sps_values[i].full_name, "POWER_SM")) {
			vmodel.north_sps_power_sm_values.push(vmodel.north_sps_values[i]);
		}
		if (checkStringStr(vmodel.north_sps_values[i].full_name, "a_inter")) {
			vmodel.north_sps_a_inter_values.push(vmodel.north_sps_values[i]);
		}
	}
}
var north_sps_info = {
	GET_1104:function(obj) {
		var Rq = {data:{objectid:"pdt_sps_north_protocol_1104",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_1104_north_sps_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_1104_north_sps_value_succ(d,r){
			vmodel.north_sps_1104_values = d.data;
			//vmodel.north_sps_1104_inform_port = vmodel.north_sps_1104_values[3]['inform port'];
			vmodel.north_sps_1104_csu_role = vmodel.north_sps_1104_values[0].csu_now_role;
			if (vmodel.north_sps_1104_csu_role == '0') {
				vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[0];
				vmodel.cfg_1104_link_id = vmodel.north_sps_1104_values[0].cfg_link_id;
				vmodel.north_sps_1104_csu_role = '0';
			} else if (vmodel.north_sps_1104_csu_role == '1'){
				vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[3];
				vmodel.cfg_1104_link_id = vmodel.north_sps_1104_values[3].cfg_link_id;
				vmodel.north_sps_1104_csu_role = '1';
			}
			addvalue(vmodel.north_sps_1104_values_recuce, d.data, "cfg_link_id");
			var array = [];
			for (var i in d.data) {
				if (d.data[i]['link type id'] == 'plat.ip_link') {
					array.push(d.data[i]);
				}
			}
			vmodel.north_sps_1104_ip_client_values = array;
			// vmodel.cfg_1104_client_link_id = array[0].ip_nms_id;
			var temp = vmodel.cfg_1104_client_link_id;
			vmodel.cfg_1104_client_link_id = "pdt.ip_1104_nms1";
			vmodel.cfg_1104_client_link_id = temp;
			vmodel.north_sps_1104_value_com = vmodel.north_sps_1104_values[4];
		}
	},
	PUT_1104:function(index) {
		var self = this;
		if (index == '1') {
			vmodel.north_sps_1104_value_com['csu_now_role']=vmodel.north_sps_1104_csu_role;
			var paraval = vmodel.north_sps_1104_value_com;
		} else {
			vmodel.north_sps_1104_value['csu_now_role']=vmodel.north_sps_1104_csu_role;
			if (vmodel.north_sps_1104_value['csu_now_role'] == "0" &&
				vmodel.north_sps_1104_value["SSH Status"] == "1" &&
				false == check_north_sps_sshpswd(vmodel.north_sps_1104_value["SSH Passwd"])) {
				alert(mainvalue.i18nkeyword.north_protocol.ssh_passwd_tip)
				self.GET_1104();
				return ;
			}
			var paraval = vmodel.north_sps_1104_value;
		}
		var Rq = {data:{objectid:"pdt_sps_north_protocol_1104",type:"val_set",paranum:"1",paraval:JSON.stringify([paraval])},success:put_1104_north_sps_value_succ};
		request.addRequest([Rq]);
		showDataInit();

		//回调函数
		function put_1104_north_sps_value_succ(d,r) {
			hideDataInit();
			if(d.result == "ok"){
				mainvalue.controlsuccess = "success";
				if (index == '2'){
					var inform_port = vmodel.north_sps_1104_value['inform port'];
					var ip_link_num = vmodel.north_sps_1104_values.length - 1;
					for (var i =0; i < ip_link_num; i++) {
						vmodel.north_sps_1104_values[i]['inform port'] = inform_port;
					}
					if(vmodel.north_sps_1104_csu_role == 1){
						self.GET_1363();
						self.GET_sm();
						self.GET_power_sm();
						self.GET_a_inter();
					}
				}
			}
			else{
				mainvalue.controlsuccess = "failure";
				self.GET_1104();
			}
		}
	},
	GET_1363:function(obj) {
		var Rq = {data:{objectid:"pdt_sps_north_protocol_1363",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_1363_north_sps_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_1363_north_sps_value_succ(d,r){
			vmodel.north_sps_1363_values = d.data;
			//vmodel.north_sps_1363_inform_port = vmodel.north_sps_1363_values[3]['inform port'];
			vmodel.north_sps_1363_csu_role = vmodel.north_sps_1363_values[0].csu_now_role;
			if (vmodel.north_sps_1363_csu_role == '0') {
				vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[0];
				vmodel.cfg_1363_link_id = vmodel.north_sps_1363_values[0].cfg_link_id;
				vmodel.north_sps_1363_csu_role = '0';
			} else if (vmodel.north_sps_1363_csu_role == '1'){
				vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[3];
				vmodel.cfg_1363_link_id = vmodel.north_sps_1363_values[3].cfg_link_id;
				vmodel.north_sps_1363_csu_role = '1';
			}
			addvalue(vmodel.north_sps_1363_values_recuce, d.data, "cfg_link_id");
			var array = [];
			for (var i in d.data) {
				if (d.data[i]['link type id'] == 'plat.ip_link') {
					array.push(d.data[i]);
				}
			}
			vmodel.north_sps_1363_ip_client_values = array;
			// vmodel.cfg_1363_client_link_id = array[0].ip_nms_id;
			var temp = vmodel.cfg_1363_client_link_id;
			vmodel.cfg_1363_client_link_id = "pdt.ip_1363_nms1";
			vmodel.cfg_1363_client_link_id = temp;
			vmodel.north_sps_1363_value_com = vmodel.north_sps_1363_values[4];
		}
	},
	PUT_1363:function(index) {
		var self = this;
		if (index == '1') {
			vmodel.north_sps_1363_value_com['csu_now_role']=vmodel.north_sps_1363_csu_role;
			var paraval = vmodel.north_sps_1363_value_com;
		} else {
			vmodel.north_sps_1363_value['csu_now_role']=vmodel.north_sps_1363_csu_role;
			if (vmodel.north_sps_1363_value['csu_now_role'] == "0" &&
				vmodel.north_sps_1363_value["SSH Status"] == "1" &&
				false == check_north_sps_sshpswd(vmodel.north_sps_1363_value["SSH Passwd"])) {
				alert(mainvalue.i18nkeyword.north_protocol.ssh_passwd_tip)
				self.GET_1363();
				return ;
			}
			var paraval = vmodel.north_sps_1363_value;
		}
		var Rq = {data:{objectid:"pdt_sps_north_protocol_1363",type:"val_set",paranum:"1",paraval:JSON.stringify([paraval])},success:put_1363_north_sps_value_succ};
		request.addRequest([Rq]);
		showDataInit();

		//回调函数
		function put_1363_north_sps_value_succ(d,r) {
			hideDataInit();
			if(d.result == "ok"){
				mainvalue.controlsuccess = "success";
				if (index == '2'){
					var inform_port = vmodel.north_sps_1363_value['inform port'];
					var ip_link_num = vmodel.north_sps_1363_values.length - 1;
					for (var i =0; i < ip_link_num; i++) {
						vmodel.north_sps_1363_values[i]['inform port'] = inform_port;
					}
					if(vmodel.north_sps_1363_csu_role == 1){
						self.GET_1104();
						self.GET_sm();
						self.GET_power_sm();
						self.GET_a_inter();
					}
				}
			}
			else{
				mainvalue.controlsuccess = "failure";
				self.GET_1363();
			}
		}
	},
	GET_sm:function(obj) {
		var Rq = {data:{objectid:"pdt_sps_north_protocol_sm",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_sm_north_sps_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_sm_north_sps_value_succ(d,r){
			vmodel.north_sps_sm_values = d.data;
			//vmodel.north_sps_sm_inform_port = vmodel.north_sps_sm_values[3]['inform port'];
			vmodel.north_sps_sm_csu_role = vmodel.north_sps_sm_values[0].csu_now_role;
			if (vmodel.north_sps_sm_csu_role == '0') {
				vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[0];
				vmodel.cfg_sm_link_id = vmodel.north_sps_sm_values[0].cfg_link_id;
				vmodel.north_sps_sm_csu_role = '0';
			} else if (vmodel.north_sps_sm_csu_role == '1'){
				vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[3];
				vmodel.cfg_sm_link_id = vmodel.north_sps_sm_values[3].cfg_link_id;
				vmodel.north_sps_sm_csu_role = '1';
			}
			addvalue(vmodel.north_sps_sm_values_recuce, d.data, "cfg_link_id");
			var array = [];
			for (var i in d.data) {
				if (d.data[i]['link type id'] == 'plat.ip_link') {
					array.push(d.data[i]);
				}
			}
			vmodel.north_sps_sm_ip_client_values = array;
			// vmodel.cfg_sm_client_link_id = array[0].ip_nms_id;
			var temp = vmodel.cfg_sm_client_link_id;
			vmodel.cfg_sm_client_link_id = "pdt.ip_sm_nms1";
			vmodel.cfg_sm_client_link_id = temp;
			vmodel.north_sps_sm_value_com = vmodel.north_sps_sm_values[4];
		}
	},
	PUT_sm:function(index) {
		var self = this;
		if (index == '1') {
			vmodel.north_sps_sm_value_com['csu_now_role']=vmodel.north_sps_sm_csu_role;
			var paraval = vmodel.north_sps_sm_value_com;
		} else {
			vmodel.north_sps_sm_value['csu_now_role']=vmodel.north_sps_sm_csu_role;
			if (vmodel.north_sps_sm_value['csu_now_role'] == "0" &&
				vmodel.north_sps_sm_value["SSH Status"] == "1" &&
				false == check_north_sps_sshpswd(vmodel.north_sps_sm_value["SSH Passwd"])) {
				alert(mainvalue.i18nkeyword.north_protocol.ssh_passwd_tip)
				self.GET_sm();
				return ;
			}
			var paraval = vmodel.north_sps_sm_value;
		}
		var Rq = {data:{objectid:"pdt_sps_north_protocol_sm",type:"val_set",paranum:"1",paraval:JSON.stringify([paraval])},success:put_sm_north_sps_value_succ};
		request.addRequest([Rq]);
		showDataInit();

		//回调函数
		function put_sm_north_sps_value_succ(d,r) {
			hideDataInit();
			if(d.result == "ok"){
				mainvalue.controlsuccess ="success";
				if (index == '2'){
					var inform_port = vmodel.north_sps_sm_value['inform port'];
					var ip_link_num = vmodel.north_sps_sm_values.length - 1;
					for (var i =0; i < ip_link_num; i++) {
						vmodel.north_sps_sm_values[i]['inform port'] = inform_port;
					}
					if(vmodel.north_sps_sm_csu_role == 1){
						self.GET_1104();
						self.GET_1363();
						self.GET_power_sm();
						self.GET_a_inter();
					}
				}
			}
			else{
				mainvalue.controlsuccess ="failure";
				self.GET_sm();
			}
		}
	},
	GET_power_sm:function(obj) {
		var Rq = {data:{objectid:"pdt_sps_north_protocol_power_sm",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_power_sm_north_sps_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_power_sm_north_sps_value_succ(d,r){
			vmodel.north_sps_power_sm_values = d.data;
			//vmodel.north_sps_power_sm_inform_port = vmodel.north_sps_power_sm_values[3]['inform port'];
			vmodel.north_sps_power_sm_csu_role = vmodel.north_sps_power_sm_values[0].csu_now_role;
			if (vmodel.north_sps_power_sm_csu_role == '0') {
				vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[0];
				vmodel.cfg_power_sm_link_id = vmodel.north_sps_power_sm_values[0].cfg_link_id;
				vmodel.north_sps_power_sm_csu_role = '0';
			} else if (vmodel.north_sps_power_sm_csu_role == '1'){
				vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[3];
				vmodel.cfg_power_sm_link_id = vmodel.north_sps_power_sm_values[3].cfg_link_id;
				vmodel.north_sps_power_sm_csu_role = '1';
			}
			addvalue(vmodel.north_sps_power_sm_values_recuce, d.data, "cfg_link_id");
			var array = [];
			for (var i in d.data) {
				if (d.data[i]['link type id'] == 'plat.ip_link') {
					array.push(d.data[i]);
				}
			}
			vmodel.north_sps_power_sm_ip_client_values = array;
			// vmodel.cfg_power_sm_client_link_id = array[0].ip_nms_id;
			var temp = vmodel.cfg_power_sm_client_link_id;
			vmodel.cfg_power_sm_client_link_id = "pdt.ip_power_sm_nms1";
			vmodel.cfg_power_sm_client_link_id = temp;
			vmodel.north_sps_power_sm_value_com = vmodel.north_sps_power_sm_values[4];
		}
	},
	PUT_power_sm:function(index) {
		var self = this;
		if (index == '1') {
			vmodel.north_sps_power_sm_value_com['csu_now_role']=vmodel.north_sps_power_sm_csu_role;
			var paraval = vmodel.north_sps_power_sm_value_com;
		} else {
			vmodel.north_sps_power_sm_value['csu_now_role']=vmodel.north_sps_power_sm_csu_role;
			if (vmodel.north_sps_power_sm_value['csu_now_role'] == "0" &&
				vmodel.north_sps_power_sm_value["SSH Status"] == "1" &&
				false == check_north_sps_sshpswd(vmodel.north_sps_power_sm_value["SSH Passwd"])) {
				alert(mainvalue.i18nkeyword.north_protocol.ssh_passwd_tip)
				self.GET_power_sm();
				return ;
			}
			var paraval = vmodel.north_sps_power_sm_value;
		}
		var Rq = {data:{objectid:"pdt_sps_north_protocol_power_sm",type:"val_set",paranum:"1",paraval:JSON.stringify([paraval])},success:put_power_sm_north_sps_value_succ};
		request.addRequest([Rq]);
		showDataInit();

		//回调函数
		function put_power_sm_north_sps_value_succ(d,r) {
			hideDataInit();
			if(d.result == "ok"){
				mainvalue.controlsuccess ="success";
				if (index == '2'){
					var inform_port = vmodel.north_sps_power_sm_value['inform port'];
					var ip_link_num = vmodel.north_sps_power_sm_values.length - 1;
					for (var i =0; i < ip_link_num; i++) {
						vmodel.north_sps_power_sm_values[i]['inform port'] = inform_port;
					}
					if(vmodel.north_sps_power_sm_csu_role == 1){
						self.GET_1104();
						self.GET_1363();
						self.GET_sm();
						self.GET_a_inter();
					}
				}
			}
			else{
				mainvalue.controlsuccess ="failure";
				self.GET_power_sm();
			}
		}
	},
	GET_a_inter:function(obj) {
		var Rq = {data:{objectid:"pdt_sps_northpro_a_inter",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_a_inter_north_sps_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_a_inter_north_sps_value_succ(d,r){
			vmodel.north_sps_a_inter_values = d.data;
			//vmodel.north_sps_a_inter_inform_port = vmodel.north_sps_a_inter_values[3]['inform port'];
			vmodel.north_sps_a_inter_csu_role = vmodel.north_sps_a_inter_values[0].csu_now_role;
			if (vmodel.north_sps_a_inter_csu_role == '0') {
				vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[0];
				vmodel.cfg_a_inter_link_id = vmodel.north_sps_a_inter_values[0].cfg_link_id;
				vmodel.north_sps_a_inter_csu_role = '0';
			} else if (vmodel.north_sps_a_inter_csu_role == '1'){
				vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[3];
				vmodel.cfg_a_inter_link_id = vmodel.north_sps_a_inter_values[3].cfg_link_id;
				vmodel.north_sps_a_inter_csu_role = '1';
			}
			addvalue(vmodel.north_sps_a_inter_values_recuce, d.data, "cfg_link_id");
			var array = [];
			for (var i in d.data) {
				if (d.data[i]['link type id'] == 'plat.ip_link') {
					array.push(d.data[i]);
				}
			}
			vmodel.north_sps_a_inter_ip_client_values = array;
			// vmodel.cfg_a_inter_client_link_id = array[0].ip_nms_id;
			var temp = vmodel.cfg_a_inter_client_link_id;
			vmodel.cfg_a_inter_client_link_id = "pdt.ip_a_inter_nms1";
			vmodel.cfg_a_inter_client_link_id = temp;
			vmodel.north_sps_a_inter_value_com = vmodel.north_sps_a_inter_values[4];
		}
	},
	PUT_a_inter:function(index) {
		var self = this;
		if (index == '1') {
			vmodel.north_sps_a_inter_value_com['csu_now_role']=vmodel.north_sps_a_inter_csu_role;
			var paraval = vmodel.north_sps_a_inter_value_com;
		} else {
			vmodel.north_sps_a_inter_value['csu_now_role']=vmodel.north_sps_a_inter_csu_role;
			if (vmodel.north_sps_a_inter_value['csu_now_role'] == "0" &&
				vmodel.north_sps_a_inter_value["SSH Status"] == "1" &&
				false == check_north_sps_sshpswd(vmodel.north_sps_a_inter_value["SSH Passwd"])) {
				alert(mainvalue.i18nkeyword.north_protocol.ssh_passwd_tip)
				self.GET_a_inter();
				return ;
			}
			var paraval = vmodel.north_sps_a_inter_value;
		}
		var Rq = {data:{objectid:"pdt_sps_northpro_a_inter",type:"val_set",paranum:"1",paraval:JSON.stringify([paraval])},success:put_a_inter_north_sps_value_succ};
		request.addRequest([Rq]);
		showDataInit();

		//回调函数
		function put_a_inter_north_sps_value_succ(d,r) {
			hideDataInit();
			if(d.result == "ok"){
				mainvalue.controlsuccess = "success";
				if (index == '2'){
					var inform_port = vmodel.north_sps_a_inter_value['inform port'];
					var ip_link_num = vmodel.north_sps_a_inter_values.length - 1;
					for (var i =0; i < ip_link_num; i++) {
						vmodel.north_sps_a_inter_values[i]['inform port'] = inform_port;
					}
					if(vmodel.north_sps_a_inter_csu_role == 1){
						self.GET_1104();
						self.GET_1363();
						self.GET_sm();
						self.GET_power_sm();
					}
				}
			}
			else{
				mainvalue.controlsuccess = "failure";
				self.GET_a_inter();
			}
		}
	},
	COM_INFO_GET:function(obj) {
		var Rq = {data:{objectid:"plat.com",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:getcomvalue_succ};
		request.addRequest([Rq]);

		//回调函数
		function getcomvalue_succ(d,r){
			var data_use = [];
			for (var i in d.data) {
				if(/^plat\.COM\d+$/.test(d.data[i].instid) && d.data[i].instid != "plat.COM0")
					{
						data_use.push(d.data[i]);
					}
			}
			addvalue(vmodel.comvalues,data_use,"instid");
		}
	},
	IP_LINK_INFO_GET:function(obj) {
		var Rq = {data:{objectid:"plat.ip_link",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:get_ip_link_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_ip_link_value_succ(d,r){
			addvalue(vmodel.ip_link_values,d.data,"instid");
		}
	},
	CURRENT_PROTOCAL_GET:function(obj) {
		var Rq = {data:{objectid:"plat.north_protocol",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_north_protocol_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_north_protocol_value_succ(d,r){
			addvalue(vmodel.current_north_protocol,d.data,"instid");
			vmodel.init_current_north_protocol = vmodel.current_north_protocol[0]['current protocol'];
		}
	},
	CURRENT_PROTOCAL_SET:function(obj) {
		var self = this;
		var paraval = vmodel.current_north_protocol;
		var Rq = {data:{objectid:"plat.north_protocol",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)},success:put_north_protocol_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function put_north_protocol_value_succ(d,r) {
			if(d.result == "ok"){
				mainvalue.controlsuccess ="success";
			}
			else{
				mainvalue.controlsuccess ="failure";
				self.CURRENT_PROTOCAL_GET();
			}
		}
	}
};

function check_north_sps_sshpswd(password) {
	if (password!=WEB_SPECIFY_STR && password.length > 20) {
		return false;
	}
	return true;
}

function set_north_sps_1104_value(index) {
	north_sps_info.PUT_1104(index);
}
function set_north_sps_1363_value(index) {
	north_sps_info.PUT_1363(index);
}
function set_north_sps_sm_value(index) {
	north_sps_info.PUT_sm(index);
}
function set_north_sps_power_sm_value(index) {
	north_sps_info.PUT_power_sm(index);
}
function set_north_sps_a_inter_value(index) {
	north_sps_info.PUT_a_inter(index);
}

north_sps_info.COM_INFO_GET();
// north_sps_info.GET_1104();
// north_sps_info.GET_1363();
// north_sps_info.GET_sm();
// north_sps_info.GET_power_sm();
// north_sps_info.GET_a_inter();

function send_1104_request(){
	north_sps_info.GET_1104();
}

function clear_1104_request(){
	var getRq = {data:{objectid:"pdt_sps_north_protocol_1104",type:"val_get",paranum:"1",paraval:JSON.stringify([])}};
	request.clearRequest(getRq);
	var Rq = {data:{objectid:"pdt_sps_north_protocol_1104",type:"val_set",paranum:"1"}};
	request.clearRequest(Rq);
}

function send_1363_request(){
	north_sps_info.GET_1363();
}

function clear_1363_request(){
	var getRq = {data:{objectid:"pdt_sps_north_protocol_1363",type:"val_get",paranum:"1",paraval:JSON.stringify([])}};
	request.clearRequest(getRq);
	var setReq = {data:{objectid:"pdt_sps_north_protocol_1363",type:"val_set",paranum:"1"}};
	request.clearRequest(setReq);
}

function send_sm_request(){
	north_sps_info.GET_sm();
}

function clear_sm_request(){
	var smGetReq = {data:{objectid:"pdt_sps_north_protocol_sm",type:"val_get",paranum:"1",paraval:JSON.stringify([])}};
	request.clearRequest(smGetReq);
	var smSetReq = {data:{objectid:"pdt_sps_north_protocol_sm",type:"val_set",paranum:"1"}};
	request.addRequest(smSetReq);
}

function send_power_sm_request(){
	north_sps_info.GET_power_sm();
}

function clear_power_sm_request(){
	var powersmGetRq = {data:{objectid:"pdt_sps_north_protocol_power_sm",type:"val_get",paranum:"1",paraval:JSON.stringify([])}};
	request.clearRequest(powersmGetRq);
	var powersmSetRq = {data:{objectid:"pdt_sps_north_protocol_power_sm",type:"val_set",paranum:"1"}};
	request.clearRequest(powersmSetRq);
}

function send_a_inter_request(){
	north_sps_info.GET_a_inter();
}

function clear_a_inter_request(){
	var aGetReq = {data:{objectid:"pdt_sps_northpro_a_inter",type:"val_get",paranum:"1",paraval:JSON.stringify([])}};
	request.clearRequest(aGetReq);
	var aSetReq = {data:{objectid:"pdt_sps_northpro_a_inter",type:"val_set",paranum:"1"}};
	request.clearRequest(aSetReq);
}

vmodel.$watch("north_sps_1104_csu_role", function(a) {
	//vmodel.north_sps_1104_value['inform port'] = vmodel.north_sps_1104_inform_port;
	if (vmodel.north_sps_1104_csu_role == '0') {
		//vmodel.north_sps_1104_values[0]['inform port'] = vmodel.north_sps_1104_inform_port;
		vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[0];
	} else {
		vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[3];
		vmodel.north_sps_1104_csu_role = '1';
	}
	//vmodel.north_sps_1104_value['inform port'] = "";
	//vmodel.north_sps_1104_value['inform port'] = vmodel.north_sps_1104_inform_port;
});

vmodel.$watch("cfg_1104_link_id", function(a) {
	for (var i in vmodel.north_sps_1104_values) {
		if (vmodel.north_sps_1104_values[i].cfg_link_id == vmodel.cfg_1104_link_id) {
			vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[i];
			break;
		}
	}
});

vmodel.$watch("cfg_1104_client_link_id", function(a) {
	for (var i in vmodel.north_sps_1104_values) {
		if (vmodel.north_sps_1104_values[i].ip_nms_id == vmodel.cfg_1104_client_link_id) {
			vmodel.north_sps_1104_value = vmodel.north_sps_1104_values[i];
			break;
		}
	}
});

vmodel.$watch("north_sps_1363_csu_role", function(a) {
	//vmodel.north_sps_1363_value['inform port'] = vmodel.north_sps_1363_inform_port;
	if (vmodel.north_sps_1363_csu_role == '0') {
		//vmodel.north_sps_1363_values[0]['inform port'] = vmodel.north_sps_1363_inform_port;
		vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[0];
	} else {
		vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[3];
		vmodel.north_sps_1363_csu_role = '1';
	}
	//vmodel.north_sps_1363_value['inform port'] = "";
	//vmodel.north_sps_1363_value['inform port'] = vmodel.north_sps_1363_inform_port;
});

vmodel.$watch("cfg_1363_link_id", function(a) {
	for (var i in vmodel.north_sps_1363_values) {
		if (vmodel.north_sps_1363_values[i].cfg_link_id == vmodel.cfg_1363_link_id) {
			vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[i];
			break;
		}
	}
});

vmodel.$watch("cfg_1363_client_link_id", function(a) {
	for (var i in vmodel.north_sps_1363_values) {
		if (vmodel.north_sps_1363_values[i].ip_nms_id == vmodel.cfg_1363_client_link_id) {
			vmodel.north_sps_1363_value = vmodel.north_sps_1363_values[i];
			break;
		}
	}
});

vmodel.$watch("north_sps_sm_csu_role", function(a) {
	//vmodel.north_sps_sm_value['inform port'] = vmodel.north_sps_sm_inform_port;
	if (vmodel.north_sps_sm_csu_role == '0') {
		//vmodel.north_sps_sm_values[0]['inform port'] = vmodel.north_sps_sm_inform_port;
		vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[0];
	} else {
		vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[3];
		vmodel.north_sps_sm_csu_role = '1';
	}
	//vmodel.north_sps_sm_value['inform port'] = "";
	//vmodel.north_sps_sm_value['inform port'] = vmodel.north_sps_sm_inform_port;
});

vmodel.$watch("cfg_sm_link_id", function(a) {
	for (var i in vmodel.north_sps_sm_values) {
		if (vmodel.north_sps_sm_values[i].cfg_link_id == vmodel.cfg_sm_link_id) {
			vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[i];
			break;
		}
	}
});

vmodel.$watch("cfg_sm_client_link_id", function(a) {
	for (var i in vmodel.north_sps_sm_values) {
		if (vmodel.north_sps_sm_values[i].ip_nms_id == vmodel.cfg_sm_client_link_id) {
			vmodel.north_sps_sm_value = vmodel.north_sps_sm_values[i];
			break;
		}
	}
});

vmodel.$watch("north_sps_power_sm_csu_role", function(a) {
	//vmodel.north_sps_power_sm_value['inform port'] = vmodel.north_sps_power_sm_inform_port;
	if (vmodel.north_sps_power_sm_csu_role == '0') {
		//vmodel.north_sps_power_sm_values[0]['inform port'] = vmodel.north_sps_power_sm_inform_port;
		vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[0];
	} else {
		vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[3];
		vmodel.north_sps_power_sm_csu_role = '1';
	}
	//vmodel.north_sps_power_sm_value['inform port'] = "";
	//vmodel.north_sps_power_sm_value['inform port'] = vmodel.north_sps_power_sm_inform_port;
});

vmodel.$watch("cfg_power_sm_link_id", function(a) {
	for (var i in vmodel.north_sps_power_sm_values) {
		if (vmodel.north_sps_power_sm_values[i].cfg_link_id == vmodel.cfg_power_sm_link_id) {
			vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[i];
			break;
		}
	}
});

vmodel.$watch("cfg_power_sm_client_link_id", function(a) {
	for (var i in vmodel.north_sps_power_sm_values) {
		if (vmodel.north_sps_power_sm_values[i].ip_nms_id == vmodel.cfg_power_sm_client_link_id) {
			vmodel.north_sps_power_sm_value = vmodel.north_sps_power_sm_values[i];
			break;
		}
	}
});

vmodel.$watch("north_sps_a_inter_csu_role", function(a) {
	//vmodel.north_sps_a_inter_value['inform port'] = vmodel.north_sps_a_inter_inform_port;
	if (vmodel.north_sps_a_inter_csu_role == '0') {
		//vmodel.north_sps_a_inter_values[0]['inform port'] = vmodel.north_sps_a_inter_inform_port;
		vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[0];
	} else {
		vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[3];
		vmodel.north_sps_a_inter_csu_role = '1';
	}
	//vmodel.north_sps_a_inter_value['inform port'] = "";
	//vmodel.north_sps_a_inter_value['inform port'] = vmodel.north_sps_a_inter_inform_port;
});

vmodel.$watch("cfg_a_inter_link_id", function(a) {
	for (var i in vmodel.north_sps_a_inter_values) {
		if (vmodel.north_sps_a_inter_values[i].cfg_link_id == vmodel.cfg_a_inter_link_id) {
			vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[i];
			break;
		}
	}
});

vmodel.$watch("cfg_a_inter_client_link_id", function(a) {
	for (var i in vmodel.north_sps_a_inter_values) {
		if (vmodel.north_sps_a_inter_values[i].ip_nms_id == vmodel.cfg_a_inter_client_link_id) {
			vmodel.north_sps_a_inter_value = vmodel.north_sps_a_inter_values[i];
			break;
		}
	}
});


/*************************************************************************************************************************************/
/************************************************sps协议栈B接口参数设置接口**********************************************************************/
function send_b_inter_request() {
	//获取 instid
	var b_inter_list_Rq = {data:{objectid:"plat.b_interface",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:get_b_inter_list_succ};
	request.addRequest([b_inter_list_Rq]);

	// 获取 attr 信息
	var b_inter_attr_Rq = {data:{objectid:"plat.b_interface",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_b_inter_attr_succ};
	request.addRequest([b_inter_attr_Rq]);

	get_b_inter_value();
}

function clear_b_inter_request() {
	var b_inter_list_req = {data:{objectid:"plat.b_interface",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:get_b_inter_list_succ};
	request.clearRequest(b_inter_list_req);

	var b_inter_attr_req = {data:{objectid:"plat.b_interface",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_b_inter_attr_succ};
	request.clearRequest(b_inter_attr_req);
	
	var b_inter_value_req = {data:{objectid:"plat.b_interface",type:"val_get",paranum:"1",paraval:JSON.stringify(vmodel.north_sps_b_inter_ListData)},success:get_b_inter_value_succ};
	request.clearRequest(b_inter_value_req);
}

function get_b_inter_value() {
	// 获取 value 信息
	var b_inter_value_Rq = {data:{objectid:"plat.b_interface",type:"val_get",paranum:"1",paraval:JSON.stringify(vmodel.north_sps_b_inter_ListData)},success:get_b_inter_value_succ};
	request.addRequest([b_inter_value_Rq]);
}

function get_b_inter_list_succ(d,r) {
	if (d.result == 'ok') {
		vmodel.north_sps_b_inter_ListData = d.data;
	}
}

function get_b_inter_attr_succ(d,r) {
	let tmp = d.data;
	let operators = [];
	for (let i = 0; i < tmp.length; i++) {
		if (tmp[i].id == "login_user" || tmp[i].id == "login_password") {
			tmp.splice(i, 1);
			i--;
		} else if (tmp[i].id == "operators") {
			operators = tmp[i].convention.split(',');
			tmp[i].convention = operators[0] + ',' + operators[3];
		}
	}
	addvalue(vmodel.north_sps_b_inter_AttrData,d.data,"name");
}

function get_b_inter_value_succ(d,r) {
	addvalue(vmodel.north_sps_b_inter_ValueData,d.data,"instid");
}

function check_b_inter_device_id(id) {
	var regexp = /^\d+$/;
	if(id.length > b_inter_id_maxlen || !regexp.test(id)) {
		return false;
	}
	return true;
}

function set_north_sps_b_inter_value(){
	var legal_flag = true;
	var device_id_list = []; //存放处理过的监控点ID数据，用于比对是否有相同值

	// 筛选获取配置参数的校验数据
	vmodel.north_sps_b_inter_CheckData.clear();
	for(var i in vmodel.north_sps_b_inter_AttrData) {
		let attr = vmodel.north_sps_b_inter_AttrData[i];
		for(var name in vmodel.north_sps_b_inter_ValueData[0]) {
			if(name == attr.name) {
				let value = vmodel.north_sps_b_inter_ValueData[0][name];
				let data = {"name":name,"full_name":attr["full_name"],"value":value,"min":attr["min"],"max":attr["max"]}
				vmodel.north_sps_b_inter_CheckData.push(data);
			}
		}
	}

	// 校验B接口配置参数合法性
	for(var i in vmodel.north_sps_b_inter_CheckData) {
		let tmp = vmodel.north_sps_b_inter_CheckData[i];

		if(tmp["name"].indexOf("Password") != -1) {
			if(tmp["value"].length > 32) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
				legal_flag = false;
				break;
			}
		} 
		else if(tmp["name"].indexOf("ID") != -1 && tmp["value"].length != 0) {
			if(!check_b_inter_device_id(tmp["value"])) {
				alert(tmp["full_name"] + " : "  + mainvalue.i18nkeyword.north_protocol.device_id_tip);
				legal_flag = false;
				break;
			}

			let index = device_id_list.findIndex(item => item["value"] == tmp["value"]);
			if(index != -1) {
				alert(tmp["full_name"] + " , " + device_id_list[index]["full_name"] + " : "  + mainvalue.i18nkeyword.north_protocol.b_inter_samedeviceid);
				legal_flag = false;
				break;
			} else {
				let obj = {"full_name":tmp["full_name"],"value":tmp["value"]};
				device_id_list.push(obj);
			}
		} 
		else if(tmp["name"].indexOf("IP") != -1) {
			if(!checkIPAddress(0,tmp["value"])) {
				alert(tmp["full_name"] + " : "  + mainvalue.i18nkeyword.north_protocol.config_para_tip + IP_MIN + " ~~ " + IP_MAX);
				legal_flag = false;
				break;
			}
		}
		else if(tmp["name"].indexOf("Port") != -1 || tmp["name"].indexOf("Interval") != -1) {
			if(tmp["value"].length == 0 || !(tmp["value"] >= parseInt(tmp["min"])) || tmp["value"] > parseInt(tmp["max"])) {
				alert(tmp["full_name"] + " : "  + mainvalue.i18nkeyword.north_protocol.config_para_tip + tmp["min"] + " ~~ " + tmp["max"]);
				legal_flag = false;
				break;
			}
		}
	}

	if(!legal_flag) {
		get_b_inter_value();
		return ;
	}

	var set_b_inter_value_Rq = {data:{objectid:"plat.b_interface",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.north_sps_b_inter_ValueData)},success:set_b_inter_value_succ};
	request.addRequest([set_b_inter_value_Rq]);
}

function set_b_inter_value_succ(d,r){
	get_b_inter_value();
    if(d.result ==="ok"){
        mainvalue.controlsuccess = "success";
    }
    else{
        mainvalue.controlsuccess = "failure";
    }
}


/*************************************************************************************************************************************/
/************************************************SNMP参数设置接口**********************************************************************/
function listifnstructure(d,r){
	if (d.result == 'ok' && d.datanum >= 0) {
		//vmodel.structuredata = d.data;
		addvalue(vmodel.structuredata,d.data,"name");
	}
}

// var ifn_attr_Rq = {data:{objectid:"plat.snmp",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listifnstructure};
// request.addRequest([ifn_attr_Rq]);

function get_ifn_value(){
	var ifn_val_Rq = {data:{objectid:"plat.snmp",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_ifn_value_succ};
	request.addRequest([ifn_val_Rq]);
}

// get_ifn_value();

function get_ifn_value_succ(d,r){
	if (d.result == 'ok' && d.datanum >= 0)	{
		vmodel.ifnvalue = d.data[0];
		vmodel.ifnvalue_backup = d.data[0];
		vmodel.snmp_changeNo = 0;
		vmodel.snmp_duplexNo = 0;
		vmodel.snmp_trap_changeNo = 0;
		vmodel.snmp_trap_duplexNo = 0;
	}
}

function snmp_manager_attr_succ(d,r) {
	if (d.result == 'ok' && d.datanum >= 0) {
		addvalue(vmodel.snmp_manager_structre,d.data,"name");
	}
}

// var snmp_manager_attr_Rq = {data:{objectid:"plat.snmp_manager",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_manager_attr_succ};
// request.addRequest([snmp_manager_attr_Rq]);

function snmp_manager_val_get_succ(d,r){
	if (d.result == 'ok')	{
		vmodel.snmp_manager_infos = d.data;
	} else {
		vmodel.snmp_manager_infos = [];
	}
}

function snmp_manager_list(d,r) {
	var snmp_manager_val_req = {data:{objectid:"plat.snmp_manager",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:snmp_manager_val_get_succ};
	request.addRequest([snmp_manager_val_req]);
}

// snmp_manager_list();

function set_ifn_value_succ(d,r) {
	if (d.result == 'ok') {
		popupTipsDiv($("#setSuccessalart"), 2000);
	} else {
		popupTipsDiv($("#setFailurealart"), 2000);
	}
	get_ifn_value();
}

function set_ifn_value(type) {
	if(type == "snmp"){
		// if(vmodel.snmp_changeNo == 1 || vmodel.snmp_duplexNo % 2 == 1) {
			let read_community_changed = vmodel.ifnvalue['SNMP Read Community'] != vmodel.ifnvalue_backup['SNMP Read Community'];
			let write_community_changed = vmodel.ifnvalue['SNMP Write Community'] != vmodel.ifnvalue_backup['SNMP Write Community'];
			if (vmodel.ifnvalue['SNMP Community Strong Passwd Enable'] == '1'
				&& (( read_community_changed && !pswd_strong_check(vmodel.ifnvalue['SNMP Read Community']))
				   ||( write_community_changed && !pswd_strong_check(vmodel.ifnvalue['SNMP Write Community']))))  {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
				get_ifn_value();
				return;
			}
			if (vmodel.ifnvalue['SNMP Community Strong Passwd Enable'] == '0'
				&& (( read_community_changed && (vmodel.ifnvalue['SNMP Read Community'].length < snmp_pswd_minlen))
				  ||( write_community_changed && (vmodel.ifnvalue['SNMP Write Community'].length < snmp_pswd_minlen)))) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
				get_ifn_value();
				return;
			}
			if ((vmodel.ifnvalue['SNMP Read Community'] != WEB_SPECIFY_STR && vmodel.ifnvalue['SNMP Write Community'] != WEB_SPECIFY_STR) && (vmodel.ifnvalue['SNMP Read Community'].length > snmp_pswd_maxlen || vmodel.ifnvalue['SNMP Write Community'].length > snmp_pswd_maxlen)) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
				get_ifn_value();
				return;
			}
			if(vmodel.ifnvalue['SNMP Read Community'].indexOf("&") != -1 || vmodel.ifnvalue['SNMP Write Community'].indexOf("&") != -1){
				popupTipsDiv($("#setFailurealart"), 2000);
				get_ifn_value();
				return;
			}
			vmodel.ifnvalue['SNMP Notification Mode'] = vmodel.ifnvalue_backup['SNMP Notification Mode'];
			vmodel.ifnvalue['SNMP V3 Notification Name'] = vmodel.ifnvalue_backup['SNMP V3 Notification Name'];
			vmodel.ifnvalue['SNMP V3 Notification Severity Level'] = vmodel.ifnvalue_backup['SNMP V3 Notification Severity Level'];
			vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP trap V3 Strong Passwd Enable'];
			vmodel.ifnvalue['Authentication Protocol'] = vmodel.ifnvalue_backup['Authentication Protocol'];
			vmodel.ifnvalue['Authentication Key'] = vmodel.ifnvalue_backup['Authentication Key'];
			vmodel.ifnvalue['Privacy Protocol'] = vmodel.ifnvalue_backup['Privacy Protocol'];
			vmodel.ifnvalue['Privacy Key'] = vmodel.ifnvalue_backup['Privacy Key'];
			var set_ifn_val_Rq = {data:{objectid:"para_snmp",type:"val_set",paranum:"1",paraval:JSON.stringify([vmodel.ifnvalue])},success:set_ifn_value_succ};
			request.addRequest([set_ifn_val_Rq]);
		// }
		vmodel.snmp_changeNo = 0;
		vmodel.snmp_duplexNo = 0;
	} else if (type == "snmp_trap") {
		// if(vmodel.snmp_trap_changeNo == 1 || vmodel.snmp_trap_duplexNo % 2 == 1) {
			if(vmodel.ifnvalue['SNMP V3 Notification Name'].indexOf("&") != -1){
				popupTipsDiv($("#setFailurealart"), 2000);
				get_ifn_value();
				return;
			}
			var para = vmodel.snmp_v3user_infos;
			for (var i in para) {
				if(para[i]['SNMP User Name'] == vmodel.ifnvalue['SNMP V3 Notification Name']){
					vmodel.ifnvalue['SNMP V3 Notification Name'] = vmodel.ifnvalue_backup['SNMP V3 Notification Name'];
					alert(mainvalue.i18nkeyword.north_protocol.notifname_samewith_v3username);
					return;
				}
			}
			if(vmodel.ifnvalue['SNMP Notification Mode'] == 3) {
				if(vmodel.ifnvalue['SNMP V3 Notification Severity Level'] == 0) {
					vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP trap V3 Strong Passwd Enable'];
					vmodel.ifnvalue['Authentication Protocol'] = vmodel.ifnvalue_backup['Authentication Protocol'];
					vmodel.ifnvalue['Authentication Key'] = vmodel.ifnvalue_backup['Authentication Key'];
					vmodel.ifnvalue['Privacy Protocol'] = vmodel.ifnvalue_backup['Privacy Protocol'];
					vmodel.ifnvalue['Privacy Key'] = vmodel.ifnvalue_backup['Privacy Key'];
				}
				if(vmodel.ifnvalue['SNMP V3 Notification Severity Level'] == 1) {
					let authkey_changed = vmodel.ifnvalue['Authentication Key'] != vmodel.ifnvalue_backup['Authentication Key'];
					if(vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] == 1 && authkey_changed && !pswd_strong_check(vmodel.ifnvalue['Authentication Key'])) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
						return;
					}
					if(vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] == 0 && authkey_changed && vmodel.ifnvalue['Authentication Key'].length < snmp_pswd_minlen) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
						return;
					}
					if (vmodel.ifnvalue['Authentication Key'].length > snmp_pswd_maxlen) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
						return;
					}
					if(vmodel.ifnvalue['Authentication Key'].indexOf("&") != -1) {
						popupTipsDiv($("#setFailurealart"), 2000);
						return;
					}
					vmodel.ifnvalue['Privacy Protocol'] = vmodel.ifnvalue_backup['Privacy Protocol'];
					vmodel.ifnvalue['Privacy Key'] = vmodel.ifnvalue_backup['Privacy Key'];
				}
				if(vmodel.ifnvalue['SNMP V3 Notification Severity Level'] == 2) {
					let authkey_changed = vmodel.ifnvalue['Authentication Key'] != vmodel.ifnvalue_backup['Authentication Key'];
					let privkey_changed = vmodel.ifnvalue['Privacy Key'] != vmodel.ifnvalue_backup['Privacy Key'];
					if(vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] == 1
						&& ( (authkey_changed && !pswd_strong_check(vmodel.ifnvalue['Authentication Key']))
							|| (privkey_changed && !pswd_strong_check(vmodel.ifnvalue['Privacy Key'])))) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
						return;
					}
					if(vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] == 0
						&& ( (authkey_changed && (vmodel.ifnvalue['Authentication Key'].length < snmp_pswd_minlen))
							|| (privkey_changed && (vmodel.ifnvalue['Privacy Key'].length < snmp_pswd_minlen)))) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
						return;
					}
					if ((vmodel.ifnvalue['Authentication Key'] != WEB_SPECIFY_STR && vmodel.ifnvalue['Privacy Key'] != WEB_SPECIFY_STR) && (vmodel.ifnvalue['Authentication Key'].length > snmp_pswd_maxlen || vmodel.ifnvalue['Privacy Key'].length > snmp_pswd_maxlen)) {
						alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
						return;
					}
					if(vmodel.ifnvalue['Authentication Key'].indexOf("&") != -1 || vmodel.ifnvalue['Privacy Key'].indexOf("&") != -1) {
						popupTipsDiv($("#setFailurealart"), 2000);
						return;
					}
				}
			} else {
				vmodel.ifnvalue['SNMP V3 Notification Severity Level'] = vmodel.ifnvalue_backup['SNMP V3 Notification Severity Level'];
				vmodel.ifnvalue['SNMP trap V3 Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP trap V3 Strong Passwd Enable'];
				vmodel.ifnvalue['Authentication Protocol'] = vmodel.ifnvalue_backup['Authentication Protocol'];
				vmodel.ifnvalue['Authentication Key'] = vmodel.ifnvalue_backup['Authentication Key'];
				vmodel.ifnvalue['Privacy Protocol'] = vmodel.ifnvalue_backup['Privacy Protocol'];
				vmodel.ifnvalue['Privacy Key'] = vmodel.ifnvalue_backup['Privacy Key'];
			}
			
			vmodel.ifnvalue['SNMP Enable'] = vmodel.ifnvalue_backup['SNMP Enable'];
			vmodel.ifnvalue['SNMP Community Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP Community Strong Passwd Enable'];

			vmodel.ifnvalue['SNMP Write Community'] = vmodel.ifnvalue_backup['SNMP Write Community'];
			vmodel.ifnvalue['SNMP Read Community'] = vmodel.ifnvalue_backup['SNMP Read Community'];
			vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP V3 User Strong Passwd Enable'];
			var set_ifn_val_Rq = {data:{objectid:"para_snmp",type:"val_set",paranum:"1",paraval:JSON.stringify([vmodel.ifnvalue])},success:set_ifn_value_succ};
			request.addRequest([set_ifn_val_Rq]);
		// }
		vmodel.snmp_trap_changeNo = 0;
		vmodel.snmp_trap_duplexNo = 0;
	}
}

function set_snmp_manager_value_succ(d,r) {
	if (d.result == 'ok') {
		popupTipsDiv($("#setSuccessalart"), 2000);
	} else {
		popupTipsDiv($("#setFailurealart"), 2000);
	}
	snmp_manager_list();
}

function set_snmp_manager_value(obj) {
	if(vmodel.snmp_manager_changeNo == 1) {
		var para = vmodel.snmp_manager_infos;
		var arr = [];
		for (var i in para) {
			if (para[i]['SNMP Manager Name'] == '') {
				alert(mainvalue.i18nkeyword.user.username_empty_tip);
				snmp_manager_list();
				return;
			}
			if (!checkIPAddress(0,para[i]['SNMP Manager IP'])) {
				alert (para[i]['SNMP Manager IP'] + " : " + mainvalue.i18nkeyword.north_protocol.ip_illegal_tip);
				snmp_manager_list();
				return;
			}
			if(para[i]['SNMP Manager Name'].indexOf("&") != -1 || para[i]['SNMP Manager IP'].indexOf("&") != -1){
				popupTipsDiv($("#setFailurealart"), 2000);
				snmp_manager_list();
				return;
			}
			if (arr.indexOf(para[i]['SNMP Manager Name']) != -1) {
				alert(mainvalue.i18nkeyword.north_protocol.snmp_v3_sameusername);
				snmp_manager_list();
				return;
			}else {
				arr.push(para[i]['SNMP Manager Name']);
			}
		}
		var set_snmp_manager_val_Rq = {data:{objectid:"plat.snmp_manager",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:set_snmp_manager_value_succ};
		request.addRequest([set_snmp_manager_val_Rq]);
	}
	vmodel.snmp_manager_changeNo = 0;
}

function show_add_snmp_manager(obj) {
	$("#tr_default_manager_add").css("display", "");
}

function clear_add_snmp_manager() {
	$("#tr_default_manager_add").css("display", "none");
	vmodel.add_snmp_manager_info["SNMP Manager Name"] = "0";
	$("#snmp_manager_username").val("");
	$("#snmp_manager_ip").val("");
	$("#snmp_manager_port").val("");
}

function add_snmp_manager_confirm(obj) {
	vmodel.add_snmp_manager_info["SNMP Manager Name"] = $("#snmp_manager_username").val();
	vmodel.add_snmp_manager_info["SNMP Manager IP"] = $("#snmp_manager_ip").val();
	vmodel.add_snmp_manager_info["SNMP Notification Port"] = $("#snmp_manager_port").val();
	if (vmodel.snmp_manager_infos.length>=10) {
		popupTipsDiv($("#usernumbermax"), 3000);
		clear_add_snmp_manager();
		return;
	}
	var para = vmodel.add_snmp_manager_info;
	for (var i in vmodel.snmp_manager_infos) {
		if(para["SNMP Manager Name"] == vmodel.snmp_manager_infos[i]["SNMP Manager Name"]) {
			alert(mainvalue.i18nkeyword.north_protocol.snmp_v3_sameusername);
			clear_add_snmp_manager();
			return;
		}
	}
	if (para['SNMP Manager Name'] == '') {
		alert(mainvalue.i18nkeyword.user.username_empty_tip);
		return;
	}
	if(para['SNMP Manager Name'].indexOf("&") != -1 || para['SNMP Manager IP'].indexOf("&") != -1){
		popupTipsDiv($("#setFailurealart"), 2000);
		return;
	}
	var add_snmp_manager_Rq = {data:{objectid:"plat.snmp_manager",type:"inst_add",paranum:"1",paraval:JSON.stringify([para])},success:set_snmp_manager_value_succ};
	request.addRequest([add_snmp_manager_Rq]);
	clear_add_snmp_manager();
	vmodel.add_snmp_manager_info = default_snmp_manager;
}

function add_snmp_manager_cancel(obj) {
	clear_add_snmp_manager();
	vmodel.add_snmp_manager_info = default_snmp_manager;
}

function snmp_v3user_attr_succ(d,r) {
	if (d.result == 'ok' && d.datanum >= 0) {
		addvalue(vmodel.snmp_v3user_structre,d.data,"name");
		// 配置一个默认用户
		var key = "";
		for (var i in  vmodel.snmp_v3user_structre) {
			key = vmodel.snmp_v3user_structre[i].name;
			vmodel.add_snmp_v3user_info[key] = (["SNMP Authentication Protocol", "SNMP Privacy Protocol"].includes(key))?"1":"0";
		}
	}
}

// var snmp_v3user_attr_Rq = {data:{objectid:"plat.snmp_v3user",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_v3user_attr_succ};
// request.addRequest([snmp_v3user_attr_Rq]);

function snmp_v3user_val_get_succ(d,r){
	if (d.result == 'ok')	{
		vmodel.snmp_v3user_infos = d.data;
	} else {
		vmodel.snmp_v3user_infos = [];
	}
	vmodel.snmp_v3user_infos_backup = JSON.parse(JSON.stringify(vmodel.snmp_v3user_infos)) ;
	vmodel.snmp_v3user_attr_changed = {};
	vmodel.snmp_v3user_attr_ifchanged = false;

}

function snmp_v3user_list() {
	var snmp_v3user_val_req = {data:{objectid:"plat.snmp_v3user",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:snmp_v3user_val_get_succ};
	request.addRequest([snmp_v3user_val_req]);
}

// snmp_v3user_list();

function set_snmp_v3user_value_succ(d,r) {
	if (d.result == 'ok') {
		popupTipsDiv($("#setSuccessalart"), 2000);
	} else {
		popupTipsDiv($("#setFailurealart"), 2000);
	}
	snmp_v3user_list();
}

function pswd_strong_check(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

function check_snmp_v3user_strong_pswd(username, authkey, privkey, authkey_changed, privkey_changed){
	if(vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] == 0 ){
		return true;
	}

	if(!check_paswd_include_username_rev(username, authkey) || !check_paswd_include_username_rev(username, privkey)){
		popupTipsDiv($("#sameuserandpwd"), 2000);
		return false;
	}

	if (((authkey_changed && !pswd_strong_check(authkey)) || (privkey_changed && !pswd_strong_check(privkey)))) {
		alert(username + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
		snmp_v3user_list();
		return false;
	}

	return true;
}


function set_snmp_v3user_value(obj) {
	vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP V3 User Strong Passwd Enable'];
	// if (vmodel.snmp_v3user_changeNo == 1 || vmodel.snmp_v3user_duplexNo%2 == 1) {
		var para = vmodel.snmp_v3user_infos;
		let para_backup = vmodel.snmp_v3user_infos_backup;
		var arr = [];
		for (var i in para) {
			if (para[i]['SNMP User Name'] == '') {
				alert(mainvalue.i18nkeyword.user.username_empty_tip);
				snmp_v3user_list();
				return;
			}
			let authkey_changed = para[i]['SNMP Authentication Key'] != para_backup[i]['SNMP Authentication Key'];
			let privkey_changed = para[i]['SNMP Privacy Key'] != para_backup[i]['SNMP Privacy Key'];

			if(!check_snmp_v3user_strong_pswd(para[i]['SNMP User Name'], para[i]['SNMP Authentication Key'], para[i]['SNMP Privacy Key'], authkey_changed, privkey_changed)){
				return;
			}

			if (vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] == 0
				&& ((authkey_changed && (para[i]['SNMP Authentication Key'].length < snmp_pswd_minlen))
					|| (privkey_changed && (para[i]['SNMP Privacy Key'].length < snmp_pswd_minlen)))) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
				snmp_v3user_list();
				return;
			}
			if((para[i]['SNMP Authentication Key'] != WEB_SPECIFY_STR && para[i]['SNMP Privacy Key'] != WEB_SPECIFY_STR) && (para[i]['SNMP Authentication Key'].length > snmp_pswd_maxlen || para[i]['SNMP Privacy Key'].length > snmp_pswd_maxlen)) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
				snmp_v3user_list();
				return;
			}
			if(para[i]['SNMP User Name'].indexOf("&") != -1 || para[i]['SNMP Authentication Key'].indexOf("&") != -1 || para[i]['SNMP Privacy Key'].indexOf("&") != -1 ){
				popupTipsDiv($("#setFailurealart"), 2000);
				snmp_v3user_list();
				return;
			}
			if (para[i]['SNMP User Name'] == vmodel.ifnvalue['SNMP V3 Notification Name']) {
				alert(mainvalue.i18nkeyword.north_protocol.v3username_samewith_notifname);
				snmp_v3user_list();
				return;
			}
			if (arr.indexOf(para[i]['SNMP User Name']) != -1) {
				alert(mainvalue.i18nkeyword.north_protocol.snmp_v3_sameusername);
				snmp_v3user_list();
				return;
			}
			arr.push(para[i]['SNMP User Name']);
		}
		if (JSON.stringify(vmodel.snmp_v3user_infos) == JSON.stringify(vmodel.snmp_v3user_infos_backup)) {
			return;
		}
		var set_snmp_v3user_val_Rq = {data:{objectid:"plat.snmp_v3user",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:set_snmp_v3user_value_succ};
		request.addRequest([set_snmp_v3user_val_Rq]);
	// }
	// vmodel.snmp_v3user_changeNo = 0;
	// vmodel.snmp_v3user_duplexNo = 0;
}

function show_add_snmp_v3user(obj) {
	vmodel.snmp_v3user_add = 1;
}

function clear_add_snpm_v3user_info() {
	vmodel.snmp_v3user_add = 0;
	vmodel.add_snmp_v3user_info['SNMP User Name'] = '0';
	vmodel.add_snmp_v3user_info['SNMP Authentication Protocol'] = '1';
	vmodel.add_snmp_v3user_info['SNMP Authentication Key'] = '0';
	vmodel.add_snmp_v3user_info['SNMP Privacy Protocol'] = '1';
	vmodel.add_snmp_v3user_info['SNMP Privacy Key'] = '0';
	vmodel.add_snmp_v3user_info['SNMP Permission Scope'] = '0';
	$("#snmp_v3_username").val("");
	$("#snmp_v3_Authentication").val("");
	$("#snmp_v3_Privacy").val("");
	$("#snmp_v3_auth_proto").get(0).selectedIndex = 1;
	$("#snmp_v3_priv_proto").get(0).selectedIndex = 1;
	$("#snmp_v3_perm_scope").get(0).selectedIndex = 0;
}

function add_snmp_v3user_confirm(obj) {
	vmodel.add_snmp_v3user_info['SNMP User Name'] = $("#snmp_v3_username").val();
	vmodel.add_snmp_v3user_info['SNMP Authentication Key'] = $("#snmp_v3_Authentication").val();
	vmodel.add_snmp_v3user_info['SNMP Privacy Key'] = $("#snmp_v3_Privacy").val();
	vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] = vmodel.ifnvalue_backup['SNMP V3 User Strong Passwd Enable'];

	if (vmodel.snmp_v3user_infos.length >= 10) {
		popupTipsDiv($("#usernumbermax"), 3000);
		clear_add_snpm_v3user_info();
		return;
	}
	var para = vmodel.add_snmp_v3user_info;
	if (para["SNMP User Name"] == '') {
		alert(mainvalue.i18nkeyword.user.username_empty_tip);
		clear_add_snpm_v3user_info();
		return;
	}
	if (para["SNMP User Name"] == vmodel.ifnvalue['SNMP V3 Notification Name']) {  //新增用户名与trap用户名相同
		alert(mainvalue.i18nkeyword.north_protocol.v3username_samewith_notifname);
		clear_add_snpm_v3user_info();
		return;
	}
	for (var i in vmodel.snmp_v3user_infos) {
		if(para["SNMP User Name"] == vmodel.snmp_v3user_infos[i]["SNMP User Name"]) {
			alert(mainvalue.i18nkeyword.north_protocol.snmp_v3_sameusername);
			clear_add_snpm_v3user_info();
			return;
		}
	}
	if(vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable']== 1 && (!check_paswd_include_username_rev(para["SNMP User Name"], para['SNMP Authentication Key']) || !check_paswd_include_username_rev(para["SNMP User Name"], para['SNMP Privacy Key']))){
		popupTipsDiv($("#sameuserandpwd"), 2000);
		return false;
	}
	if (vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable']== 1 && (!pswd_strong_check(para['SNMP Authentication Key']) || !pswd_strong_check(para['SNMP Privacy Key']))) {
		alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
		clear_add_snpm_v3user_info();
		return;
	}
	if (vmodel.ifnvalue['SNMP V3 User Strong Passwd Enable'] == 0 && (para['SNMP Authentication Key'].length < snmp_pswd_minlen || para['SNMP Privacy Key'].length < snmp_pswd_minlen)) {
		alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
		clear_add_snpm_v3user_info();
		return;
	}
	if (para['SNMP Authentication Key'].length > snmp_pswd_maxlen || para['SNMP Privacy Key'].length > snmp_pswd_maxlen) {
		alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
		clear_add_snpm_v3user_info();
		return;
	}
	if(para['SNMP User Name'].indexOf("&") != -1 || para['SNMP Authentication Key'].indexOf("&") != -1 || para['SNMP Privacy Key'].indexOf("&") != -1 ){
		popupTipsDiv($("#setFailurealart"), 2000);
		clear_add_snpm_v3user_info();
		return;
	}
	if (para["SNMP User Name"] == para['SNMP Authentication Key'] || para["SNMP User Name"] == para['SNMP Privacy Key']) {
		alert(mainvalue.i18nkeyword.north_protocol.v3username_samewith_key);
		clear_add_snpm_v3user_info();
		return;
	}
	var add_snmp_v3user_Rq = {data:{objectid:"plat.snmp_v3user",type:"inst_add",paranum:"1",paraval:JSON.stringify([para])},success:set_snmp_v3user_value_succ};
	request.addRequest([add_snmp_v3user_Rq]);
	clear_add_snpm_v3user_info();
}

function add_snmp_v3user_cancel(obj) {
	vmodel.snmp_v3user_add = 0;
}

function send_snmp_request(){
	var ifn_attr_Rq = {data:{objectid:"plat.snmp",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listifnstructure};
	request.addRequest([ifn_attr_Rq]);
	get_ifn_value();
	var snmp_manager_attr_Rq = {data:{objectid:"plat.snmp_manager",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_manager_attr_succ};
	request.addRequest([snmp_manager_attr_Rq]);
	snmp_manager_list();
	var snmp_v3user_attr_Rq = {data:{objectid:"plat.snmp_v3user",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_v3user_attr_succ};
	request.addRequest([snmp_v3user_attr_Rq]);
	snmp_v3user_list();
}

function clear_snmp_request(){
	var ifn_attr_req = {data:{objectid:"plat.snmp",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:listifnstructure};
	request.clearRequest(ifn_attr_req);

	var ifn_val_req = {data:{objectid:"plat.snmp",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_ifn_value_succ};
	request.clearRequest(ifn_val_req);

	var snmp_manager_attr_req = {data:{objectid:"plat.snmp_manager",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_manager_attr_succ};
	request.clearRequest(snmp_manager_attr_req);

	var snmp_manager_val_req = {data:{objectid:"plat.snmp_manager",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:snmp_manager_val_get_succ};
	request.clearRequest(snmp_manager_val_req);

	var snmp_v3user_attr_req = {data:{objectid:"plat.snmp_v3user",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:snmp_v3user_attr_succ};
	request.clearRequest(snmp_v3user_attr_req);

	var snmp_v3user_val_req = {data:{objectid:"plat.snmp_v3user",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:snmp_v3user_val_get_succ};
	request.clearRequest(snmp_v3user_val_req);
}
/*************************************************************************************************************************************/
/************************************************MQTT参数设置接口**********************************************************************/
var mqtt_config = {
	GET:function() {
		var para = {"instid":""};
		var Rq = {data:{objectid:"plat.mqtt",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_mqtt_value_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_mqtt_value_succ(d,r){
			if (d.result == 'ok') {
				addvalue(vmodel.mqtt_infos,d.data,"instid");
			}
		}
	},
	HEAD:function() {
		var Rq = {data:{objectid:"plat.mqtt",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_mqtt_head_succ};
		request.addRequest([Rq]);

		//回调函数
		function get_mqtt_head_succ(d,r) {
			if (d.result == 'ok') {
				addvalue(vmodel.mqtt_structre,d.data,"name");
			}
		}
	},
	PUT:function() {
		var self = this;
		var paraval = vmodel.mqtt_infos;
		var Rq = {data:{objectid:"plat.mqtt",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)},success:put_mqtt_val_succ};
		request.addRequest([Rq]);

		//回调函数
		function put_mqtt_val_succ(d,r) {
			if(d.result ==="ok"){
				mainvalue.controlsuccess = "success";
			}
			else{
				mainvalue.controlsuccess = "failure";
				self.GET();
			}
		}
	}
};

function set_mqtt_value(obj) {
	let password = vmodel.mqtt_infos[0]["MQTT Password"];
	let mqtt_password_len = password.length;
	if (password != WEB_SPECIFY_STR && mqtt_password_len > 32) {
		alert(mainvalue.i18nkeyword.north_protocol.mqtt_pswd_len_tip);
		send_mqtt_request();
		return ;
	}
	mqtt_config.PUT();
}

function send_mqtt_request(){
	mqtt_config.HEAD();
	mqtt_config.GET();
}

function clear_mqtt_request(){
	var attr_rq = {data:{objectid:"plat.mqtt",type:"attr_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(attr_rq);
	var val_rq = {data:{objectid:"plat.mqtt",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])}};
	request.clearRequest(val_rq);
}

/*************************************************************************************************************************************/
/************************************************页面跳转函数**************************************************************************/
function init_tab_select() {
    var tab = Cookies.get("tab_list");
    var tab2 = Cookies.get("tab_list2");
	if (tab == "sps_config_1104_tab" || tab == "sps_config_1363_tab" || tab == "sps_config_sm_tab" ||
		tab == "sps_config_power_sm_tab" || tab == "sps_config_a_inter_tab" || tab == "sps_config_b_inter_tab" ||
		tab == "SNMP_config_tab" || tab == "mqtt_config_tab") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "sps_config_1104_tab";
		set_cookie_with_path("tab_list", vmodel.tab_list);
    }
    if (tab2 == "snmpuser_tab" || tab2 == "snmp_trap_tab") {
		vmodel.tab_list2 = tab2;
	} else {
		vmodel.tab_list2 = "snmpuser_tab";
		set_cookie_with_path("tab_list2", vmodel.tab_list2);
	}
}
init_tab_select();
send_request_by_tab(vmodel.tab_list);