﻿<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        <div class="main-container container-fluid" >
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="userdemo">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.user.usermanage}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span9">
                            <div id="pswnotmatchalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.pswnotmatchalert}}
                            </div>
                            <div id="insertrighttest_empty" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.username_empty_tip}}
                            </div>
                            <div id="insertrighttest_toolong" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.username_toolong_tip}}
                            </div>
                            <div id="usernamehavespaces" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.usernamehavespaces}}
                            </div>
                            <div id="usernumbermax" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.usernumbermax}}
                            </div>
                            <div id="sameuserandpwd" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.sameuserandpwd}}
                            </div>
                            <div id="addfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.addfailedalert}}
                            </div>
                            <div id="addsuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.user.addsuccessalert}}
                            </div>
                            <div id="alterfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.alterfailedalert}}
                            </div>
                            <div id="altersuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.user.altersuccessalert}}
                            </div>
                            <div id="delfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.delfailedalert}}
                            </div>
                            <div id="delsuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                 {{i18nkeyword.user.delsuccessalert}}
                            </div>
                            <div id="opfailedalert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                 {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="opsuccessalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                 {{i18nkeyword.operate_successfully}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li class="active">
                                        <a data-toggle="tab" href="#super">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            Web {{i18nkeyword.user.usermanage}}
                                        </a>
                                    </li>
                                </ul>

                                <div class="tab-content">
                                    <div class="tab-pane active" >
                                        <div ms-if="userlevel === '2'">
                                            <label class="col-sm-3 control-label no-padding-right" style="display:inline">{{i18nkeyword.user.max_login_wrong_times}}</label>
                                            <span class="input-icon" style="padding-right:20px">
                                                <input id="form-control input-medium" type="text" autocomplete="off" ms-duplex= "loginpara_val[0]['Max wrong times']"/>
                                                <i class="icon-leaf blue"></i>
                                            </span>
                                            <button class="button button-small button-flat-primary" onClick="set_loginpara()">{{i18nkeyword.set}}</button>
                                        </div>
                                        <table  ms-if="(userlevel === '1'|| userlevel === '2') && radius_status == '0' " class="table table-striped table-bordered table-hover" id="alterusertable">
                                            <thead>
                                                <tr class="info">
                                                    <th>{{i18nkeyword.user.alterpswd}}</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                    <tr class="">
                                                        <td id="usernameinfo">{{username}}</td>
                                                        <td ><!--[if IE]>当前密码：&nbsp;&nbsp;&nbsp;<![endif]-->
                                                            <input id="currpsw" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.oldpswd}"  class="form-control input-medium" onChange="changedInput(this);pushParaList(this)">
                                                            </input></td>
                                                    </tr>
                                                    <tr class="">
                                                        <td ><!--[if IE]>新密码：<![endif]-->
                                                            <input id="alterpsw" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.newpswd,'title':i18nkeyword.user.pswd_tip}"  class="form-control input-medium" onchange="check_alter_pswd_strength(this)" oninput="check_alter_pswd_strength(this)" >
                                                            </input>
                                                            <div id="pw_level2"  class="pw-strength" style="display:none">
                                                                <div class="pw-bar"></div>
                                                                <div class="pw-bar-on"></div>
                                                                <div class="pw-txt">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td ><!--[if IE]>确认新密码：<![endif]-->
                                                            <input id="alterpsw2" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.newpswd2}"  class="form-control input-medium" >
                                                            </input>
                                                        </td>
                                                    </tr>
                                                    <tr class="">
                                                        <td ><button class="button button-small button-flat-primary" onClick="alteruser()" id="alteruserbt">{{i18nkeyword.submit}}</button></td>
                                                        <td></td>
                                                    </tr>
                                            </tbody>
                                        </table> <!--修改密码table-->
                                        <table ms-if="userlevel == '2'" class="table table-striped table-bordered table-hover" id="newusertable">
                                            <thead>
                                                <tr class="info">
                                                    <th >{{i18nkeyword.user.adduser}}</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                    <tr class="">
                                                        <td><!--[if IE]>用户名：<![endif]-->
                                                            <input id="newuser" type="text" autocomplete="off" ms-attr="{'placeholder':i18nkeyword.user.addusername}" class="form-control input-medium">
                                                            </input>
                                                        </td>
                                                        <td ><!--[if IE]>用户类型：<![endif]-->
                                                            <select id="superlevel" class = "input-medium" style="width: 164px;">
                                                               <option value="1" selected/>{{i18nkeyword.user.general_user}}
                                                               <option i18n="user.administrator" value="2"/>
                                                               {{i18nkeyword.user.administrator}}
                                                            </select> </td>
                                                    </tr>
                                                    <tr class="">
                                                        <td><!--[if IE]>密&nbsp;&nbsp;&nbsp;码：<![endif]-->
                                                            <input id="newpsw" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.adduserpsw1,'title':i18nkeyword.user.pswd_tip}" class="form-control input-medium" onchange="check_pswd_strength(this)" oninput="check_pswd_strength(this)" >
                                                            </input>
                                                            <div id="pw_level"  class="pw-strength" style="display:none">
                                                                <div class="pw-bar"></div>
                                                                <div class="pw-bar-on"></div>
                                                                <div class="pw-txt">
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td ><!--[if IE]>确认密码：<![endif]-->
                                                            <input id="newpsw2" type="password" autocomplete="new-password" ms-attr="{'placeholder':i18nkeyword.user.adduserpsw2}" class="form-control input-medium">
                                                            </input>
                                                        </td>
                                                    </tr>
                                                    <tr class="">
                                                        <td ><button class="button button-small button-flat-primary" onClick="addNewUser()" id="addnewbt">{{i18nkeyword.submit}}</button></td>
                                                        <td></td>
                                                    </tr>
                                            </tbody>
                                        </table> <!--新增用户table-->
                                        <table class="table table-striped table-bordered table-hover" ms-if="userlevel=='2'">
                                            <thead>
                                                <tr class="info">
                                                    <th>{{i18nkeyword.user.username}}</th>
                                                    <th>{{i18nkeyword.user.last_time}}</th>
                                                    <th>{{i18nkeyword.user.is_admin}}</th>
                                                    <th ms-if="delete_flag == '1'"></th>
                                                </tr>
                                            </thead>
                                            <tbody id="userinfo">
                                                <tr ms-for="(num,user) in userdata" ms-if="userlevel=='2'">
                                                    <td>{{user["username"]}}</td>
                                                    <td>{{user["logintime"]}}</td>
                                                    <td>{{userlevels_dic[user["userlevel"]].type}}</td>
                                                    <td ms-if = "delete_flag == '1' && user['del_no'] != '1' && user['userlevel'] < '3' && user['username'] != username">
                                                        <a href="#" class="red" ms-click="@delete_user_event(user['username'])">{{i18nkeyword.user.deleteuser}}</a>
                                                    </td>
                                                    <td ms-if = "delete_flag == '1' && (user['del_no'] == '1' || user['userlevel'] >= '3'|| user['username'] == username)"></td>
                                                </tr>
                                            </tbody>
                                        </table><!--用户信息table-->
                                    </div>
                                </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span9-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <link rel="stylesheet" href="/page//html/plug_pswd/css/plug_pswd.css" />
        <script src="/page/js/user.js"></script>
        <script src="/page//html/plug_pswd/js/plug_pswd.js"></script>
    </body>
</html>
