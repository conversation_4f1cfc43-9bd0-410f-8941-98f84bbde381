---

char:
    data_lenth: 1
    enum_type: 0
    struct_type: char
    invalid_value: 0x80
    python_type_format: b
    default_value: 0
    xml_handler: int
    sqlite_type_format: TEXT
unsigned char:
    data_lenth: 1
    enum_type: 1
    struct_type: unsigned char
    invalid_value: 0xff
    python_type_format: B
    default_value: 0
    xml_handler: int
    sqlite_type_format: TEXT
short:
    data_lenth: 2
    enum_type: 2
    struct_type: short
    invalid_value: 0x8000
    python_type_format: h
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
unsigned short:
    data_lenth: 2
    enum_type: 3
    struct_type: unsigned short
    invalid_value: 0xffff
    python_type_format: H
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
int:
    data_lenth: 4
    enum_type: 4
    struct_type: int
    invalid_value: -2147483648
    python_type_format: i
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
unsigned int:
    data_lenth: 4
    enum_type: 5
    struct_type: unsigned int
    invalid_value: 4294967295
    python_type_format: I
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
float:
    data_lenth: 4
    enum_type: 6
    struct_type: float
    invalid_value: 0xffffffff
    python_type_format: f
    default_value: 0
    xml_handler: float
    sqlite_type_format: REAL
double:
    data_lenth: 8
    enum_type: 7
    struct_type: double
    invalid_value: 0xffffffffffffffff
    python_type_format: d
    default_value: 0
    xml_handler: float
    sqlite_type_format: REAL
sid:
    data_lenth: 8
    enum_type: 8
    struct_type: sid
    invalid_value: 0xffffffffffffffff
    python_type_format: Q
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
time_t:
    data_lenth: 4
    enum_type: 9
    struct_type: time_t
    invalid_value: 0
    python_type_format: i
    default_value: 0
    xml_handler: int
    sqlite_type_format: NONE
longlong:
    data_lenth: 8
    enum_type: 10
    struct_type: long long
    invalid_value: 0xffffffffffffffff
    python_type_format: Q
    default_value: 0
    xml_handler: int
    sqlite_type_format: INTEGER
time:
    data_lenth: 7
    enum_type: 11
    struct_type: time_base_t
    invalid_value: 0
    python_type_format: HBBBBB
    default_value: 0
    xml_handler: time
    sqlite_type_format: NONE
date:
    data_lenth: 4
    enum_type: 12
    struct_type: date_base_t
    invalid_value: 0
    python_type_format: HBB
    default_value: 0
    xml_handler: date
    sqlite_type_format: NONE
process_time:
    data_lenth: 14
    enum_type: 13
    struct_type: process_time_base_t
    invalid_value: ''
    python_type_format: HBBBBBHBBBBB
    default_value: ''
    xml_handler: process_time
    sqlite_type_format: NONE
hour_minute:
    data_lenth: 2
    enum_type: 14
    struct_type: hour_minute_t
    invalid_value: 0
    python_type_format: BB
    default_value: 0
    xml_handler: hour_minute
    sqlite_type_format: NONE
month_day:
    data_lenth: 2
    enum_type: 15
    struct_type: month_day_t
    invalid_value: 0
    python_type_format: BB
    default_value: 0
    xml_handler: month_day
    sqlite_type_format: NONE
process_month_day:
    data_lenth: 4
    enum_type: 16
    struct_type: process_month_day_t
    invalid_value: 0
    python_type_format: BBBB
    default_value: 0
    xml_handler: process_month_day
    sqlite_type_format: NONE
process_hour_minute:
    data_lenth: 4
    enum_type: 17
    struct_type: process_hour_minute_t
    invalid_value: 0
    python_type_format: BBBB
    default_value: 0
    xml_handler: process_hour_minute
    sqlite_type_format: NONE
string32:
    data_lenth: 32
    enum_type: 32
    struct_type: str32
    invalid_value: ''
    python_type_format: 32s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string64:
    data_lenth: 64
    enum_type: 64
    struct_type: str64
    invalid_value: ''
    python_type_format: 64s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string128:
    data_lenth: 128
    enum_type: 128
    struct_type: str128
    invalid_value: ''
    python_type_format: 128s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string256:
    data_lenth: 256
    enum_type: 256
    struct_type: str256
    invalid_value: ''
    python_type_format: 256s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string512:
    data_lenth: 512
    enum_type: 512
    struct_type: str512
    invalid_value: ''
    python_type_format: 512s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string1024:
    data_lenth: 1024
    enum_type: 1024
    struct_type: str1024
    invalid_value: ''
    python_type_format: 1024s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
string2048:
    data_lenth: 2048
    enum_type: 2048
    struct_type: str2048
    invalid_value: ''
    python_type_format: 2048s
    default_value: ''
    xml_handler: str
    sqlite_type_format: TEXT
