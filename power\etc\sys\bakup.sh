#!/bin/sh
#
# This script will be executed *after* all the other init scripts.
# You can put your own initialization stuff in here if you don't
# want to do the full Sys V style init stuff.

echo "bakup old programs..."
date
rm -rf /mnt/data/app_backup/*
cd /root
cp -f power/bin/SysMonitor_run power/bin/sysmonitor
tar -czf /mnt/data/app_backup/powernewbak.tar.gz power --exclude power/bin/SysMonitor_run
#zip -1  -r /mnt/data/app_backup/powernewbak.zip power -x power/bin/SysMonitor_run >/dev/null
rm -f power/bin/sysmonitor
date
echo "bakup old programs ok!"

