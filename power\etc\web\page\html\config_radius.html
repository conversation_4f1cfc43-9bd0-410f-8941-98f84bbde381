<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="radius">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.RADIUS_config}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>

                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                            <!--PAGE CONTENT BEGINS-->
                            <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="file_empty_tip" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.system.fileempty}}
                            </div>
                            <div id="upcacert_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.radius.uploadcacert_file_tip}}
                            </div>
                            <div id="upclicert_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.radius.uploadclicert_file_tip}}
                            </div>
                            <div id="upprikey_name_err" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.radius.uploadprikey_file_tip}}
                            </div>
                            <div class="tabbable">
                                <div class="tab-content">
                                    <div id="wired" class="tab-pane active">
                                    	<div class="tabbable tabs-left" id="wiredtable">
                                            <ul class="nav nav-tabs">
                                                <li ms-class="'config_data' == tab_list?'active':''" ms-click="tabChange('config_data')">
                                                    <a data-toggle="tab" href="#config_data">
                                                        <i class="pink icon-flag bigger-110">
                                                        </i>
                                                        {{i18nkeyword.radius.para}}
                                                    </a>
                                                </li>
                                                <li ms-class="'cipher_data' == tab_list?'active':''" ms-click="tabChange('cipher_data')">
                                                    <a data-toggle="tab" href="#cipher_data">
                                                        <i class="blue icon-fire bigger-110">
                                                        </i>
                                                        {{i18nkeyword.radius.file}}
                                                    </a>
                                                </li>
                                            </ul>
                                            <div class="tab-content">
                                                <div class="tab-pane" id ="config_data" ms-class="'config_data' == tab_list?'active':''">
                                                    <table class="table table-striped table-bordered table-hover">
                                                        <tr ms-for = "(index,radiusAttr) in radiusAttrData"  ms-if="radiusAttr.visible!=='NO'">
                                                            <td>{{radiusAttr.full_name}}</td>
                                                            <td ms-if="getconventionlength(radiusAttr.convention)>0" >
                                                                <select ms-duplex="radiusValueData[0][radiusAttr.name]" ms-attr="{name:@radiusAttr.name}" ms-change="changeshow" style="width:165px">
                                                                        <option ms-for="(id,name) in radiusAttr.convention" ms-attr="{value:id}">{{name}}</option>
                                                                </select>
                                                            </td>
                                                            <td ms-if="getconventionlength(radiusAttr.convention)===0 && radiusAttr.id !=='radius_secret' && radiusAttr.id !=='private_key_password'">
                                                                <input class="form-control input-medium pull-left" ms-duplex="radiusValueData[0][radiusAttr.name]">
                                                            </td>
                                                            <td ms-if="getconventionlength(radiusAttr.convention)===0 && (radiusAttr.id =='radius_secret' || radiusAttr.id =='private_key_password')">
                                                                <input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="radiusValueData[0][radiusAttr.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <button class="button button-small button-flat-primary" onClick="setRadiusData()" id="setRadiusDataBT"><i class="icon-pencil bigger-100"></i>
                                                    {{i18nkeyword.set}}
                                                    </button>
                                                </div>
                                                <div class="tab-pane" id ="cipher_data" ms-class="'cipher_data' == tab_list?'active':''">
                                                    <form id="ca_cert_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                                    enctype="multipart/form-data" onsubmit="return upload_ca_cert();">
                                                        <fieldset>
                                                            <legend>
                                                               {{i18nkeyword.radius.ca_cert}}
                                                            </legend>
                                                            <table>
                                                                <tr>
                                                                    <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.radius.uploadcacert_file_tip}">
                                                                        <input type="file" id="ca-cert-file" name="uploadfile" class="file" data-max-file-size="10"/> <!--升级文件的最大值定位10K-->
                                                                        <input type="hidden" id ="up_type" name="type" value="inst_add" />
                                                                        <input type="hidden" id ="up_objid" name="objectid" value="plat.radius" />
                                                                        <input type="hidden" id ="fileno" name="fileno" value="1" />
                                                                    </td>
                                                                    <td style="padding-bottom:10px;padding-left:20px">
                                                                        <button id="ca_cert_up_bt" type="submit" class="button button-small button-flat-primary">
                                                                            {{i18nkeyword.upload_file}}
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </fieldset>
                                                    </form>
                                                    <form id="client_cert_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                                    enctype="multipart/form-data" onsubmit="return upload_client_cert();">
                                                        <fieldset>
                                                            <legend>
                                                               {{i18nkeyword.radius.client_cert}}
                                                            </legend>
                                                            <table>
                                                                <tr>
                                                                    <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.radius.uploadclicert_file_tip}">
                                                                        <input type="file" id="client-cert-file" name="uploadfile" class="file" data-max-file-size="10"/> <!--升级文件的最大值定位10K-->
                                                                        <input type="hidden" id ="up_type" name="type" value="inst_add" />
                                                                        <input type="hidden" id ="up_objid" name="objectid" value="plat.radius" />
                                                                        <input type="hidden" id ="fileno" name="fileno" value="2" />
                                                                    </td>
                                                                    <td style="padding-bottom:10px;padding-left:20px">
                                                                        <button id="client_cert_up_bt" type="submit" class="button button-small button-flat-primary">
                                                                            {{i18nkeyword.upload_file}}
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </fieldset>
                                                    </form>
                                                    <form id="private_key_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                                    enctype="multipart/form-data" onsubmit="return upload_private_key();">
                                                        <fieldset>
                                                            <legend>
                                                               {{i18nkeyword.radius.privite_key}}
                                                            </legend>
                                                            <table>
                                                                <tr>
                                                                    <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.radius.uploadprikey_file_tip}">
                                                                        <input type="file" id="private-key-file" name="uploadfile" class="file" data-max-file-size="10"/> <!--升级文件的最大值定位10K-->
                                                                        <input type="hidden" id ="up_type" name="type" value="inst_add" />
                                                                        <input type="hidden" id ="up_objid" name="objectid" value="plat.radius" />
                                                                        <input type="hidden" id ="fileno" name="fileno" value="3" />
                                                                    </td>
                                                                    <td style="padding-bottom:10px;padding-left:20px">
                                                                        <button id="private_key_up_bt" type="submit" class="button button-small button-flat-primary">
                                                                            {{i18nkeyword.upload_file}}
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </fieldset>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div><!--radius参数配置-->
                                </div>
                            </div><!--/tabbable-->
                        </div><!--/span10-->
                        <!--PAGE CONTENT ENDS-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-content-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/js/private/opensrc_i18n.js"></script>
        <script src="/js/opensrc/jquery.form.min.js"></script>
        <script src="/js/opensrc/fileinput.js"></script>
        <script src="/page/js/config_radius.js"></script>
         <!--[if !IE]> -->
        <script type="text/javascript">
            var empty =  get_opensrc_i18n_word("empty");
            var choose = get_opensrc_i18n_word("choose_file");
            var change = get_opensrc_i18n_word("choose_again");
            var choose_tip_prex = get_opensrc_i18n_word("select_tips");
            $(function() {
                $('#ca-cert-file').ace_file_input({
                    no_file: choose_tip_prex + " " + "radius_rootca.pem",
                    btn_choose: choose,
                    btn_change: change,
                    droppable: false,
                    onchange: null,
                    thumbnail: false
                });
                $('#client-cert-file').ace_file_input({
                    no_file: choose_tip_prex + " " + "radius_user.pem",
                    btn_choose: choose,
                    btn_change: change,
                    droppable: false,
                    onchange: null,
                    thumbnail: false
                });
                $('#private-key-file').ace_file_input({
                    no_file: choose_tip_prex + " " + "radius_userkey.pem",
                    btn_choose: choose,
                    btn_change: change,
                    droppable: false,
                    onchange: null,
                    thumbnail: false
                });
            });
        </script>
        <!-- <![endif]-->
</body>

</html>