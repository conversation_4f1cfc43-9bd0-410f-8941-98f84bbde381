#######################################################################
##
##  Output Compression 
## --------------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/docs_modcompress
##
server.modules += ( "mod_compress" )

##
## where should the compressed files be cached?
## see the base config for the declaration of the variable.
##
## This directory should be changed per vhost otherwise you can
## run into trouble with overlapping filenames
##
compress.cache-dir = cache_dir + "/compress"

##
## FileTypes to compress.
## 
compress.filetype          = ("text/plain", "text/html")

##
## Maximum filesize that will be compressed.
## Default is 0, which means unlimited file size.
## 
#compress.max-filesize = 0

##
#######################################################################
