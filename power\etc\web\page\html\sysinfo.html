 <!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/prompt.html" -->
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="system">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.system_maintain}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                              <div id="time_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="time_successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="do_control_successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.control_cmd_success}}
                            </div>
                            <div id="file_empty_tip" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.system.fileempty}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'systemstate' == tab_list?'active':''" ms-click="tabChange('systemstate')">
                                        <a data-toggle="tab" href="#systemstate" ms-if="userlevel > '1'">
                                            <i class="blue icon-align-left bigger-110">
                                            </i>
                                            {{i18nkeyword.system.system_state}}
                                        </a>
                                    </li>
                                    <li ms-class="'systemsinfo' == tab_list?'active':''" ms-click="tabChange('systemsinfo')">
                                        <a data-toggle="tab" href="#systemsinfo" ms-if="userlevel > '1'">
                                            <i class="blue icon-list-alt bigger-110">
                                            </i>
                                            {{i18nkeyword.system.sys_time}}
                                        </a>
                                    </li>
                                    <li ms-class="'status' == tab_list?'active':''" ms-click="tabChange('status')">
                                        <a data-toggle="tab" href="#status">
                                            <i class="blue icon-signal bigger-110"></i>
                                            {{i18nkeyword.northnet.connection_status}}
                                        </a>
                                    </li>
                                    <li ms-class="'netmanage_config_tab' == tab_list?'active':''" ms-click="tabChange('netmanage_config_tab')">
                                        <a data-toggle="tab" href="#netmanage_config_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            {{i18nkeyword.menu.north_netmanage_connect_status}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div id="systemstate" class="tab-pane" ms-class="'systemstate' == tab_list?'active':''">
                                        <div class="widget-box" style="margin-bottom:5px;" id="buzzercontrol" ms-if="userlevel > '1'">
                                            <div class="widget-header widget-header-flat widget-header-small">
                                                <h5 class="lighter">
                                                    <i class="icon-cog"></i>
                                                    {{i18nkeyword.system.buzzer_control}}
                                                </h5>
                                                <label style="padding-top: 5px;">
                                                    <input name="switch-field-1"  id="buzzer_switch" class="ace ace-switch" type="checkbox" onclick="buzz_input_click()"/>
                                                    <span class="lbl"></span>
                                                </label>
                                            </div>
                                        </div><!--蜂鸣器控制-->
                                        <div class="widget-box" style="margin-bottom:5px;" id="buzzercontrol" ms-if="userlevel > '2'"> <!--厂家维护用户使用此功能-->
                                            <div class="widget-header widget-header-flat widget-header-small">
                                                <h5 class="lighter">
                                                    <i class="icon-cog"></i>
                                                    {{i18nkeyword.system.outrelay_control}}
                                                </h5>
                                                <div>
                                                    <label>
                                                        <input name="switch-field-1" id="outrelay_ctrl" class="ace ace-switch" type="checkbox" ms-attr="{'checked':outrelay_ctrl_curr_state}" onclick="set_outrelay_ctrl_mode(this)"/>
                                                        <span class="lbl"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div><!--干接点强制控制-->
                                        <div class="widget-box" style="margin-bottom:5px;" id="iostate">
                                            <div class="widget-header widget-header-flat widget-header-small" onClick="channelSlide(this)">
                                                <h5 class="lighter">
                                                    <i class="icon-cog"></i>
                                                    {{i18nkeyword.system.dry_contact_state}}
                                                </h5>
                                                <div class="widget-toolbar pull-right">
                                                    <a href="#">
                                                        <i class="icon-chevron-down"></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="widget-body" style="overflow-y:auto; height:400px;margin-bottom:5px" ms-css="{'display':userlevel > '1'?'none':'block'}">
                                                <div class="widget-body-inner" style="display: block;">
                                                    <div class="widget-main no-padding">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr class="info">
                                                                    <th>
                                                                    {{i18nkeyword.system.dry_contact_name}}
                                                                    </th>
                                                                    <th>
                                                                    {{i18nkeyword.devlist.signame}}
                                                                    </th>
                                                                    <th>
                                                                    {{i18nkeyword.system.dry_contact_state}}
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody >
                                                                <tr ms-for="(i,ioinf) in system_IOstate">
                                                                    <td>{{ioinf.full_name}}</td>
                                                                    <td>
                                                                        <span ms-if="ioinf.short_name === ''">{{i18nkeyword.null}}</span>
                                                                        <span ms-if="ioinf.short_name != ''">{{ioinf.short_name}}</span>
                                                                    </td>
                                                                    <td><img ms-if="ioinf.value ==='1'" src="/page/assets/img/button_open.png"><img ms-if="ioinf.value !=='1'" src="/page/assets/img/button_close.png"></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!--干接点状态-->
                                        <div class="widget-box" style="margin-bottom:5px;" id="iocontrol" ms-if="userlevel > '2'"> <!--厂家维护用户使用此功能-->
                                            <div class="widget-header widget-header-flat widget-header-small" onClick="channelSlide(this)">
                                                <h5 class="lighter">
                                                    <i class="icon-cog"></i>
                                                    {{i18nkeyword.system.dry_contact_control}}
                                                </h5>
                                                <div class="widget-toolbar pull-right">
                                                    <a href="#">
                                                        <i class="icon-chevron-down"></i>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="widget-body" style="overflow-y:auto; height:400px;margin-bottom:5px;display:none;">
                                                <div class="widget-body-inner" style="display: block;">
                                                    <div class="widget-main no-padding">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr class="info">
                                                                    <th>
                                                                    {{i18nkeyword.system.dry_contact_name}}
                                                                    </th>
                                                                    <th>
                                                                    {{i18nkeyword.system.control_operate}}
                                                                    </th>
                                                                    <th>
                                                                    {{i18nkeyword.system.control_state}}
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody >
                                                                <tr ms-for= "(i,data) in system_DO_infos"  ms-if="data['Ctrl Mode'] == '2'">  <!--下电-->
                                                                    <td>{{data['Channel Name']}}</td>
                                                                    <td>
                                                                        <button class="btn-xs btn-info center" ms-click="changeIOValue(0,data['Board Type'],data['Ctrl Mode'],data['Channel NO.'])">{{i18nkeyword.recover}}</button>
                                                                    </td>
                                                                    <td >
                                                                        <img ms-if="@do_set_status_get(data['Board Type'],data['Channel Type'],data['Channel NO.']) =='1'" src="/page/assets/img/button_open.png"><img ms-if="@do_set_status_get(data['Board Type'],data['Channel Type'],data['Channel NO.']) =='0'" src="/page/assets/img/button_close.png">
                                                                    </td>
                                                                </tr>
                                                                <tr ms-for= "(i,data) in system_DO_infos" ms-if="data['Ctrl Mode'] == '1' || data['Ctrl Mode'] == '3' || data['Ctrl Mode'] == '4'" >  <!--输出干接点、告警-->
                                                                    <td>{{data['Channel Name']}}</td>
                                                                    <td>
                                                                        <button class="btn-xs btn-info center" ms-click="changeIOValue(0,data['Board Type'],data['Ctrl Mode'],data['Channel NO.'])">{{i18nkeyword.recover}}</button>
                                                                        <button class="btn-xs btn-info center" ms-click="changeIOValue(1,data['Board Type'],data['Ctrl Mode'],data['Channel NO.'])">{{i18nkeyword.active}}</button>
                                                                    </td>
                                                                    <td>
                                                                        <img ms-if="@do_set_status_get(data['Board Type'],data['Channel Type'],data['Channel NO.']) =='1'" src="/page/assets/img/button_open.png"><img ms-if="@do_set_status_get(data['Board Type'],data['Channel Type'],data['Channel NO.']) =='0'" src="/page/assets/img/button_close.png">
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div><!--干节点控制-->
                                    </div>
                                    <!-- 系统时间 -->
                                    <div id="systemsinfo" class="tab-pane" ms-class="'systemsinfo' == tab_list?'active':''">
                                        <div class="widget-header widget-header-flat widget-header-small" style="height:40px">
                                            <h5 class="lighter pull-left" style="margin-top:5px">
                                            {{i18nkeyword.system.set_time}}&nbsp;
                                                <i class="icon-hand-right">
                                                </i>
                                            </h5>
                                            <input id="timeinput" type="text" autocomplete="off" class="form-control input-medium pull-left"
                                            style="margin-top:6px; margin-left:15px;" onFocus="getSysTime()" />
                                            <button id="timesync" class="button button-small button-flat-primary pull-right"
                                            style="margin-top:5px; margin-right:10px" onClick="systimeTask.synchro()">
                                            {{i18nkeyword.system.synchro_local_time}}
                                            </button>
                                            <button id="submitTime" class="button button-small button-flat-primary pull-left"
                                            style="margin-top:5px; margin-left:10px" onClick="setSysTime($('#timeinput').val())">
                                            {{i18nkeyword.set}}
                                            </button>
                                        </div>
                                        <div class="widget-content">
                                            <table class="table table-striped table-bordered table-hover" >
                                                <tbody>
                                                    <tr ms-for="(el,al) in timezonestruc" ms-if="timezonestruc[el]['visible']!='NO'">
                                                    <td>{{al.full_name}}</td>
                                                    <td ms-if="getconventionlength(al.convention)>0">
                                                        <select ms-duplex="timezonevalue[0][al.name]" class="input-medium" style="width:auto">
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}" ms-if="al.name == 'timezone select'" >{{timezoneUTC[index]}} {{name}}</option>
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}" ms-if="al.name != 'timezone select'" >{{name}}</option>
                                                    </td>
                                                    <td ms-if="getconventionlength(al.convention)==0">
                                                        <input class="form-control input-medium pull-left" ms-duplex="timezonevalue[0][al.name]">
                                                    </td>
                                                    </tr>
                                                    <td colspan="2">
                                                        <button class="button button-small button-flat-primary" onClick="set_tz_Data()" id="set_tz_Data"><i class="icon-pencil bigger-100"></i>
                                                            {{i18nkeyword.set}}
                                                        </button>
                                                    </td>
                                                </tbody>
                                            </table>
                                        </div><!--子页面-->
                                    </div><!--时间等信息-->
                                    <!-- 连接状态 -->
                                    <div class="tab-pane" id="status" ms-class="'status' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover">
                                            <tbody id="status_data">
                                                <tr ms-for="(index,attrname) in northnet_NetconstatusAttrData">
                                                    <td style="color:#707070; font-weight:bold">{{attrname.full_name}}</td>
                                                    <td ms-for="valuename in northnet_NetconstatusValueData"><!--数组里已处理，此处不重复处理-->
                                                        <span ms-if = "getconventionlength(attrname.convention)>0">{{attrname.convention[valuename[attrname.name]]}}</span>
                                                        <span ms-if = "getconventionlength(attrname.convention)===0">{{valuename[attrname.name]}}</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div ms-if="'1'=='1'"> <!--无线信息-->
                                            <span>
                                            {{i18nkeyword.northnet.wireless_network_sig_info}}
                                            </span>
                                            <table class="table table-striped table-bordered table-hover">
                                                <tbody id="wireless_sig_data">
                                                    <tr>
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.northnet.wireless_network_operator}}</td>
                                                        <td>{{northnet_WirelessSigData[0]["network_operator"]}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.northnet.wireless_module_status}}</td>
                                                        <td>{{northnet_WirelessSigData[0]["network_status"]}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.northnet.wireless_sig_strlength}}</td>
                                                        <td class="bars" style="display: flex;">
                                                            <ul style="display:none; position: relative; margin:0;height: 20px;width: 60px;">
                                                                <li style="width: 5px; position: absolute; bottom: 0; border-radius: 10px; background: rgba(0,0,0,.5);height: 4px; left: 0px;list-style: none"></li>
                                                                <li style="width: 5px; position: absolute; bottom: 0; border-radius: 10px; background: rgba(0,0,0,.5);height: 8px; left: 10px;list-style: none"></li>
                                                                <li style="width: 5px; position: absolute; bottom: 0; border-radius: 10px; background: rgba(0,0,0,.5);height: 12px; left: 20px;list-style: none"></li>
                                                                <li style="width: 5px; position: absolute; bottom: 0; border-radius: 10px; background: rgba(0,0,0,.5);height: 16px; left: 30px;list-style: none"></li>
                                                                <li style="width: 5px; position: absolute; bottom: 0; border-radius: 10px; background: rgba(0,0,0,.5);height: 20px; left: 40px;list-style: none"></li>
                                                            </ul>
                                                            <span id="sig_strlength_val" style="width: 70px;">{{sig_strlength}}</span>
                                                            <div style="display:none">{{northnet_WirelessSigData[0]["sig_strlength"]}}</div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div><!--无线信息-->
                                    </div><!--网络状态-->
                                    <div class="tab-pane" id="netmanage_config_tab" ms-class="'netmanage_config_tab' == tab_list?'active':''">
                                        <div class="alert alert-block alert-success">{{i18nkeyword.menu.north_link_num}}：<p style="display:inline;">{{north_protocol_num}}</p>
                                        <!--ms-for:(i,nms_sta) in north_protocol_status-->
                                        <div>
                                            <span>
                                            {{nms_sta["nms_inst_name"]}}
                                            </span>
                                            <table class="table table-striped table-bordered table-hover">
                                                <tbody id="netmanage_status_data">
                                                    <tr>
                                                        <td style="color:#707070; font-weight:bold;width:30%">{{i18nkeyword.menu.north_protocol_name}}</td>
                                                        <td>{{nms_sta["protocal_name"]}}</td>
                                                    </tr>
                                                    <tr ms-if="nms_sta['nms_inst_name'].indexOf('ip')!=-1">
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.menu.north_ip_name}}</td>
                                                        <td>{{nms_sta["link_name"]}}</td>
                                                    </tr>
                                                    <tr ms-if="nms_sta['nms_inst_name'].indexOf('com')!=-1">
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.menu.north_com_name}}</td>
                                                        <td>{{nms_sta["link_name"]}}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style="color:#707070; font-weight:bold">{{i18nkeyword.menu.north_link_status}}</td>
                                                        <td ms-if="nms_sta['status']=='0'" >
                                                            <i class="icon-ok green" style="margin-right: 5px;"></i>
                                                            {{i18nkeyword.menu.north_link_status0}}
                                                        </td>
                                                        <td ms-if="nms_sta['status']=='1'" >
                                                            <i class="icon-ban-circle red" style="margin-right: 5px;"></i>
                                                            {{i18nkeyword.menu.north_link_status1}}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    <!--ms-for-end:-->
                                </div><!--北向网管连接状态tab结束-->
                            </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/js/opensrc/jquery.form.min.js"></script>
        <script src="/js/opensrc/fileinput.js"></script>
        <script src="/page/js/sysinfo.js"></script>
    </body>

</html>