 <!DOCTYPE html>
<html>
	<head>
		<!--#include virtual="/page/html/include.html" -->
		<style>
			#myTab>li{
				height: 33.6px;
			}
		</style>
	</head>

	<body class="navbar-fixed ms-controller" ms-controller="container">
 		<!--#include virtual="/page/html/header.html" -->

		 <div class="main-container container-fluid" ms-controller="traceinfo">
			<!--#include virtual="/page/html/menu.html" -->
			<div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.trance.tranceinfo}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
				<div class="page-content">
					<div class="row-fluid">
						<div class="span10">
							<!--PAGE CONTENT BEGINS-->
							<div class="tabbable">
								<ul class="nav nav-tabs" id="myTab">
									<!--ms-for:(i,trace_title) in traceinfos-->
									<li ms-class="i==tab_list?'active':''" ms-click="tab_change(i)">
										<a data-toggle="tab"  ms-attr="{'href':'#'+trace_title.title}">
											<i class="blue icon-tint bigger-110">
											</i>
											{{trace_title.title}}
										</a>
									</li>
									<!--ms-for-end:-->
									<li class="pull-right">{{i18nkeyword.devlist.Data_refresh_interval}}
										<select id="fleshselect" class = "input-small" onchange="change_trace_fleshtime(this.value)">
											<option value="10" selected>{{i18nkeyword.second_10}}</option>
											<option value="1" >{{i18nkeyword.second_1}}</option>
										</select>
									</li>
								</ul>
								<div class="tab-content">
									<div ms-for="(i,trace) in traceinfos" ms-attr="{'id':trace.title}" class="tab-pane" ms-class="i==tab_list?'active':''"  style="height:600px">
										<table id="sample-table-1" class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.devlist.trace_info}}</th>
													<th>
														<i class="icon-time bigger-110 hidden-480"></i>
														{{i18nkeyword.devlist.value}}
													</th>
												</tr>
											</thead>
											<tbody>
												<!--ms-for:(i,info) in trace.infos-->
												<tr>
													<td> &nbsp;&nbsp;&nbsp;{{info.name}}</td>
													<td class="hidden-480"> &nbsp;&nbsp;&nbsp;{{info.value}}</td>
												</tr>
												<!--ms-for-end:-->
											</tbody>
										</table>
									</div>
								</div>
							</div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
						</div><!--/span10-->
					</div><!--/row-fluid-->
				</div><!--/page-content-->
			</div><!--/main-contain-->
		</div><!--/main-container-->
		<!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/traceinfo.js"></script>
	</body>

</html>