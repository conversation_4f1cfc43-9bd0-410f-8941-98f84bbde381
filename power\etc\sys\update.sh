#!/bin/sh
#
# This script will be executed *after* all the other init scripts.
# You can put your own initialization stuff in here if you don't
# want to do the full Sys V style init stuff.

echo "setup new packet"
SOUTH_PATH="/root/power/etc/dictionary/south"

#kill process
top -b -d 1 -n 1 >> /tmp/log/message
cd /root
APP=`ls /root/power/bin | sed -e 's/SysMonitor_run//g'`
killall -9 $APP


#delete south file
if [ -d $SOUTH_PATH ] ; then 
	rm -rf $SOUTH_PATH/*.*
	echo "rm south files"
fi

df -h >> /tmp/log/message
# top -b -d 1 -n 1 >> /tmp/log/message
#uncompress package
tar zxf /mnt/memdisk/updating/powernew.tar.gz >> /tmp/log/message
if [ $? -ne 0 ] ; then
    df -h >> /tmp/log/message
    top -b -d 1 -n 1 >> /tmp/log/message
else
    rm -rf /mnt/memdisk/updating/powernew.tar.gz
	#top -b -d 1 -n 1 >> /tmp/log/message
    sync
    echo "update ok!"
fi
