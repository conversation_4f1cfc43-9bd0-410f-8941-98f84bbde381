<?xml version="1.0" encoding="utf-8"?>
<config>
	<cfgobject id="plat.snmp" name="SNMP Settings" type="single">
		<para id="read_community" name="SNMP Read Community" encrypt="yes" type="string128">
			<full_name>SNMP Read Community</full_name>
		</para>
		<para id="write_community" name="SNMP Write Community" encrypt="yes" type="string128">
			<full_name>SNMP Write Community</full_name>
		</para>
		<para id="mode" name="SNMP Notification Mode" type="int">
			<min>0</min>
			<max>3</max>
			<full_name>SNMP Notification Mode</full_name>
			<convention>0:Inform V2,1:Trap V1,2:Trap V2,3:Trap V3</convention>
		</para>
		<para id="name" name="SNMP V3 Notification Name" type="string32">
			<full_name>SNMP V3 Notification Name</full_name>
		</para>
		<para id="level" name="SNMP V3 Notification Severity Level" type="int">
			<min>0</min>
			<max>2</max>
			<precision>1</precision>
			<step>1</step>
			<visible>YES</visible>
			<full_name>SNMP V3 Notification Severity Level</full_name>
			<short_name>SNMP V3 Notification Severity Level</short_name>
			<convention>0:NoAuth NoPriv,1:Auth NoPriv,2:Auth Priv</convention>
			<default>0</default>
		</para>
		<para id="auth_proto" name="Authentication Protocol" type="int">
			<min>0</min>
			<max>1</max>
			<full_name>Authentication Protocol</full_name>
			<convention>0:MD5,1:SHA</convention>
		</para>
		<para id="auth_key" name="Authentication Key" encrypt="yes" type="string128">
			<full_name>Authentication Key</full_name>
		</para>
		<para id="priv_proto" name="Privacy Protocol" type="int">
			<min>0</min>
			<max>1</max>
			<full_name>Privacy Protocol</full_name>
			<convention>0:DES,1:AES</convention>
		</para>
		<para id="priv_Key" name="Privacy Key" encrypt="yes" type="string128">
			<full_name>Privacy Key</full_name>
		</para>	
        <para id="snmp_enable" name="SNMP Enable" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SNMP Enable</full_name>
            <short_name>SNMP Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>0</default>
        </para>
        <para id="community_strong_passwd_enable" name="SNMP Community Strong Passwd Enable" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SNMP Community Strong Passwd Enable</full_name>
            <short_name>SNMP Community Strong Passwd Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>0</default>
        </para>
        <para id="trapv3_strong_passwd_enable" name="SNMP trap V3 Strong Passwd Enable" type="int">
            <min>0</min>
            <max>1</max>
            <full_name>SNMP trap V3 Strong Passwd Enable</full_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>0</default>
        </para>	
		<para id="v3user_strong_passwd_enable" name="SNMP V3 User Strong Passwd Enable" type="int">
            <min>0</min>
            <max>1</max>
            <full_name>SNMP V3 User Strong Passwd Enable</full_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>0</default>
        </para>			
	</cfgobject>
	<cfgobject id="plat.snmp_v3user" name="SNMP V3 User" type="multi">
		<para id="user_name" name="SNMP User Name" type="string32">
			<full_name>SNMP User Name</full_name>
		</para>
		<para id="auth_proto" name="SNMP Authentication Protocol" type="int">
			<min>0</min>
			<max>1</max>
			<full_name>SNMP Authentication Protocol</full_name>
			<convention>0:MD5,1:SHA</convention>
		</para>
		<para id="auth_key" name="SNMP Authentication Key" encrypt="yes" type="string128">
			<full_name>SNMP Authentication Key</full_name>
		</para>
		<para id="priv_proto" name="SNMP Privacy Protocol" type="int">
			<min>0</min>
			<max>1</max>
			<full_name>SNMP Privacy Protocol</full_name>
			<convention>0:DES,1:AES</convention>
		</para>
		<para id="priv_Key" name="SNMP Privacy Key" encrypt="yes" type="string128">
			<full_name>SNMP Privacy Key</full_name>
		</para>
		<para id="perm_scope" name="SNMP Permission Scope" type="int">
			<min>0</min>
			<max>1</max>
			<full_name>SNMP Permission Scope</full_name>
			<convention>0:read only,1:read and write</convention>
			<default>1</default>
		</para>
	</cfgobject>
	<cfgobject id="plat.snmp_manager" name="SNMP Manager" type="multi">
		<para id="name" name="SNMP Manager Name" type="string32">
			<full_name>SNMP Manager Name</full_name>
			<short_name>SNMP Manager Name</short_name>
		</para>
		<para id="ip" name="SNMP Manager IP" type="string32">
			<full_name>SNMP Manager IP</full_name>
			<short_name>SNMP Manager IP</short_name>
			<default>127.0.0.1</default>
		</para>
		<para id="port" name="SNMP Notification Port" type="int">
			<full_name>SNMP Notification Port</full_name>
			<default>161</default>
			<min>1</min>
			<max>65535</max>
		</para>
	</cfgobject>
	<cfgobject id="plat.com" name="Serial Configure Information" type="multi">
		<para id="serialname" name="Serial Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>NO</visible>
			<full_name>Serial Name</full_name>
			<short_name>Serial Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="serialnum" name="Serial NO." type="int">
			<min>0</min>
			<max>5</max>
			<precision></precision>
			<step></step>
			<visible>NO</visible>
			<full_name>Serial NO.</full_name>
			<short_name>Serial NO.</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="devicename" name="Device Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>NO</visible>
			<full_name>Device Name</full_name>
			<short_name>Device Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="baudrate" name="Baud Rate" type="int">
			<min>0</min>
			<max>7</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Baud Rate</full_name>
			<short_name>Baud Rate</short_name>
			<convention>0:1200,1:2400,2:4800,3:9600,4:19200,5:38400,6:57600,7:115200</convention>
			<default></default>
		</para>
		<para id="bits" name="Data Bits" type="int">
			<min>7</min>
			<max>8</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Data Bits</full_name>
			<short_name>Data Bits</short_name>
			<convention>7:7,8:8</convention>
			<default></default>
		</para>
		<para id="stop" name="Stop Bits" type="int">
			<min>1</min>
			<max>2</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Stop Bits</full_name>
			<short_name>Stop Bits</short_name>
			<convention>1:1,2:2</convention>
			<default></default>
		</para>
		<para id="parity" name="Parity Bits" type="int">
			<min>0</min>
			<max>2</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Parity Bits</full_name>
			<short_name>Parity Bits</short_name>
			<convention>0:None,1:odd,2:even</convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.can" name="CAN Configure Information" type="multi">
		<para id="can_name" name="CAN Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>can name</full_name>
			<short_name>can name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="can_no" name="CAN NO." type="int">
			<min>0</min>
			<max>5</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>CAN NO.</full_name>
			<short_name>CAN NO.</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="device_name" name="Device Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>no</visible>
			<full_name>Device Name</full_name>
			<short_name>Device Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="baudrate" name="Baud Rate" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Baud Rate</full_name>
			<short_name>Baud Rate</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="can_id" name="Can ID" type="unsigned int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>no</visible>
			<full_name>Can ID</full_name>
			<short_name>Can ID</short_name>
			<convention></convention>
			<default>2415935104</default>
		</para>
		<para id="can_mask" name="Can Mask" type="unsigned int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>no</visible>
			<full_name>Can Mask</full_name>
			<short_name>Can Mask</short_name>
			<convention></convention>
			<default>2415933696</default>
		</para>
	</cfgobject>
	<cfgobject id="plat.apconnection" name="AP Connection" type="single">
		<para id="ap_name" name="AP Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AP Name</full_name>
			<short_name>AP Name</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ap_password" name="AP Password" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AP Password</full_name>
			<short_name>AP Password</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
        <para id="ap_strong_passwd_enable" name="AP Strong Passwd Enable" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>AP Strong Passwd Enable</full_name>
            <short_name>AP Strong Passwd Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
	</cfgobject>
	<cfgobject id="plat.wiredconnection" name="Wired Connection" type="single">
		<para id="defaultrouter" name="Default Router" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Router</full_name>
			<short_name>Default Router</short_name>
			<convention>0:No,1:Yes</convention>
			<default></default>
		</para>
		<para id="allocationmode" name="IP Allocation Mode" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IP Allocation Mode</full_name>
			<short_name>IP Allocation Mode</short_name>
			<convention>0:Static,1:Dynamic</convention>
			<default></default>
		</para>
		<para id="ipaddress" name="IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IP Address</full_name>
			<short_name>IP Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="subnetmask" name="Subnet Mask" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subnet Mask</full_name>
			<short_name>Subnet Mask</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="gateway" name="Gateway" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Gateway</full_name>
			<short_name>Gateway</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.wiredconnectionv6" name="Wired Connection V6" type="single">
		<para id="ipv6address" name="IPv6 Address" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IPv6 Address</full_name>
			<short_name>IPv6 Address</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.wirelessconnection" name="Wireless Connection" type="single">
		<para id="defaultrouter" name="Default Router" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Router</full_name>
			<short_name>Default Router</short_name>
			<convention>0:No,1:Yes</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="APN" name="APN" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>APN</full_name>
			<short_name>APN</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="dialcode" name="Dial Code" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Dial Code</full_name>
			<short_name>Dial Code</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="username" name="User Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>User Name</full_name>
			<short_name>User Name</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="password" name="Password" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Password</full_name>
			<short_name>Password</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="backupAPN" name="Backup APN" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Backup APN</full_name>
			<short_name>Backup APN</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="backupdialcode" name="Backup Dial Code" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Backup Dial Code</full_name>
			<short_name>Backup Dial Code</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="backupusername" name="Backup User Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Backup User Name</full_name>
			<short_name>Backup User Name</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="backuppassword" name="Backup Password" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Backup Password</full_name>
			<short_name>Backup Password</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="nomanagermode" name="No Manager Mode" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>No Manager Mode</full_name>
			<short_name>No Manager Mode</short_name>
			<convention>0:NotSwitchSimcard,1:KeepConnection</convention>
			<visible>no</visible>
			<default>0</default>
		</para>
	</cfgobject>
	<cfgobject id="plat.wirelessnetdevs" name="Wireless Net Devices" type="multi">
		<para id="vendorid" name="Vendor ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Vendor ID</full_name>
			<short_name>Vendor ID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="productid" name="Product ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Product ID</full_name>
			<short_name>Product ID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="modemdevice" name="Modem Device" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Modem Device</full_name>
			<short_name>Modem Device</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="smsdevice" name="SMS Device" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SMS Device</full_name>
			<short_name>SMS Device</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="netname" name="Net Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Net Name</full_name>
			<short_name>Net Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="dialcmd" name="Dial Cmd" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Dial Cmd</full_name>
			<short_name>Dial Cmd</short_name>
			<convention></convention>
			<default>0</default>
		</para>
	</cfgobject>
	<cfgobject id="plat.b_interface" name="B Interface Connection" type="single">
		<para id="operators" name="Operators" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Operators</full_name>
			<short_name>Operators</short_name>
			<convention>0:None,1:CHN-UNICOM,2:CHINA MOBILE,3:CHINA TOWER</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fsu_id" name="FSU ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FSU ID</full_name>
			<short_name>FSU ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="login_user" name="Login user" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Login user</full_name>
			<short_name>Login user</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="login_password" name="Login Password" encrypt="yes" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Login Password</full_name>
			<short_name>Login Password</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="sc_ip" name="SC IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SC IP Address</full_name>
			<short_name>SC IP Address</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="sc_port" name="SC Port" type="int">
			<min>0</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>SC Port</full_name>
			<short_name>SC Port</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="inform_ip" name="Inform IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Inform IP Address</full_name>
			<short_name>Inform IP Address</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="inform_port" name="Inform Port" type="int">
			<min>0</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>Inform Port</full_name>
			<short_name>Inform Port</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="fsu_port" name="FSU Port" type="int">
			<min>0</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>FSU Port</full_name>
			<short_name>FSU Port</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="power_id" name="POWER ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>POWER ID</full_name>
			<short_name>POWER ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="nfbbms_id1" name="FBBMS ID1" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FBBMS ID1</full_name>
			<short_name>FBBMS ID1</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="nfbbms_id2" name="FBBMS ID2" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FBBMS ID2</full_name>
			<short_name>FBBMS ID2</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="door_id" name="DOOR ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>DOOR ID</full_name>
			<short_name>DOOR ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="humiture_sensor_id" name="Humiture Sensor ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Humiture Sensor ID</full_name>
			<short_name>Humiture Sensor ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="smog_sensor_id" name="Smog Sensor ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Smog Sensor ID</full_name>
			<short_name>Smog Sensor ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="flood_sensor_id" name="Flood Sensor ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Flood Sensor ID</full_name>
			<short_name>Flood Sensor ID</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="sc_heartbeat_interval" name="SC Heartbeat Interval" type="int">
			<min>1</min>
			<max>3600</max>
			<precision></precision>
			<step></step>
			<full_name>SC Heartbeat Interval(s)</full_name>
			<short_name>SC Heartbeat Interval(s)</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="ftp_user" name="FTP User" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>FTP User</full_name>
			<short_name>FTP User</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="ftp_password" name="FTP Password" encrypt="yes" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Password</full_name>
			<short_name>Password</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.ECMstatus" name="ECM Status" type="single">
		<para id="status" name="status" type="int">
			<min>0</min>
			<max>4</max>
			<precision></precision>
			<step></step>
			<full_name>status</full_name>
			<short_name>status</short_name>
			<convention>0:Down,1:Breakdown,2:dialing,3:Up,4:Waiting</convention>
			<default>0</default>
		</para>
		<para id="ecm_ip" name="ECM Ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ECM Ip</full_name>
			<short_name>ECM Ip</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.VPNconnection" name="VPN Connection" type="single">
		<para id="defaultrouter" name="Default Router" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Router</full_name>
			<short_name>Default Router</short_name>
			<convention>0:No,1:Yes</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="phyconnectiontype" name="Physical Connection Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Physical Connection Type</full_name>
			<short_name>Physical Connection Type</short_name>
			<convention>0:Wired,1:Wireless</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="VPNtype" name="VPN Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>VPN Type</full_name>
			<short_name>VPN Type</short_name>
			<convention>0:L2TP</convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="serverip" name="Server Ip" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Server Ip</full_name>
			<short_name>Server Ip</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="username" name="User Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>User Name</full_name>
			<short_name>User Name</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="password" name="Password" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Password</full_name>
			<short_name>Password</short_name>
			<convention></convention>
			<visible>yes</visible>
			<default></default>
		</para>
		<para id="presharedkey" name="Pre-shared Key" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Pre-shared Key</full_name>
			<short_name>Pre-shared Key</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="ipsec_usercertificate" name="IPSec User Certificate" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IPSec User Certificate</full_name>
			<short_name>IPSec User Certificate</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="ipsec_cacertificate" name="IPSec CA Certificate" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IPSec CA Certificate</full_name>
			<short_name>IPSec CA Certificate</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="ipsec_servercertificate" name="IPSec Server Certificate" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IPSec Server Certificate</full_name>
			<short_name>IPSec Server Certificate</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="mppe" name="MPPE" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>MPPE</full_name>
			<short_name>MPPE</short_name>
			<convention>0:No,1:Yes</convention>
			<visible>no</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.routesettings" name="Route Settings Configure Information" type="multi">
		<para id="connectionid" name="Connection ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Connection ID</full_name>
			<short_name>Connection ID</short_name>
			<convention></convention>
			<visible>no</visible>
			<default></default>
		</para>
		<para id="networkdest" name="Network Dest" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Network Dest</full_name>
			<short_name>Network Dest</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="networkmask" name="Network Mask" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Network Mask</full_name>
			<short_name>Network Mask</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.wiredconnection_sp" name="Wired Connection Slave Params" type="single">
		<para id="cutovermode" name="Cut Over Mode" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Cut Over Mode</full_name>
			<short_name>Cut Over Mode</short_name>
			<convention>0:Network manager cut over,1:Network parameters cut over,2:Network manager and parameters cut over</convention>
			<default></default>
		</para>
		<para id="northmanagerip" name="North Manager IP" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>North Manager IP</full_name>
			<short_name>North Manager IP</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="allocationmode" name="IP Allocation Mode" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IP Allocation Mode</full_name>
			<short_name>IP Allocation Mode</short_name>
			<convention>0:Static,1:dynamic</convention>
			<default></default>
		</para>
		<para id="ipaddress" name="IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IP Address</full_name>
			<short_name>IP Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="subnetmask" name="Subnet Mask" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subnet Mask</full_name>
			<short_name>Subnet Mask</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="gateway" name="Gateway" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Gateway</full_name>
			<short_name>Gateway</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="applytime" name="Apply Time" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Apply Time</full_name>
			<short_name>Apply Time</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.northinterfaceset" name="Northbound Interface Settings" type="single">
		<para id="northinterfacetype" name="Northbound Interface Type" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Northbound Interface Type</full_name>
			<short_name>Northbound Interface Type</short_name>
			<convention>0:Interface B,1:Interface N</convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.netconstatus" name="Net Connection Status" type="multi">
		<para id="conname" name="Connection Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Connection Name</full_name>
			<short_name>Connection Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="constatus" name="Connection Status" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Connection Status</full_name>
			<short_name>Connection Status</short_name>
			<convention>0:Down,1:dialing,2:Up</convention>
			<default></default>
		</para>
		<para id="interfacename" name="Interface Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Interface Name</full_name>
			<short_name>Interface Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="ipaddress" name="IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IP Address</full_name>
			<short_name>IP Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="subnetmask" name="Subnet Mask" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Subnet Mask</full_name>
			<short_name>Subnet Mask</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="gateway" name="Gateway" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Gateway</full_name>
			<short_name>Gateway</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="ipv6addr" name="IPv6 Address" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>IPv6 Address</full_name>
			<short_name>IPv6 Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="llaipv6addr" name="Local Link IPv6 Address" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Local Link IPv6 Address</full_name>
			<short_name>Local Link IPv6 Address</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.ai.factory_config" name="AI Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltypeno" name="Channel Type NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type NO.</full_name>
			<short_name>Channel Type NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="majorchannelno" name="Major Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Major Channel NO.</full_name>
			<short_name>Major Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="minorchannelno" name="Minor Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Minor Channel NO.</full_name>
			<short_name>Minor Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="adprecision" name="AdPrecision" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AdPrecision</full_name>
			<short_name>AdPrecision</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="basevoltage" name="BaseVoltage" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>BaseVoltage</full_name>
			<short_name>BaseVoltage</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumsamplevalue" name="Maximum Sample Value" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Sample Value</full_name>
			<short_name>Maximum Sample Value</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumsamplevalue" name="Mininum Sample Value" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Sample Value</full_name>
			<short_name>Mininum Sample Value</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumsersordata" name="Maximum Sensor Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Sensor Data</full_name>
			<short_name>Maximum Sensor Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumsersordata" name="Mininum Sensor Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Sensor Data</full_name>
			<short_name>Mininum Sensor Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumanalogdata" name="Maximum Analog Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Analog Data</full_name>
			<short_name>Maximum Analog Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumanalogdata" name="Mininum Analog Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Analog Data</full_name>
			<short_name>Mininum Analog Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="offset" name="Offset" type="float">
			<min>-60</min>
			<max>60</max>
			<precision></precision>
			<step></step>
			<full_name>Offset</full_name>
			<short_name>Offset</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="slope" name="Slope" type="float">
			<min>-10</min>
			<max>10</max>
			<precision></precision>
			<step></step>
			<full_name>Slope</full_name>
			<short_name>Slope</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:Low,1:High</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="displaymask" name="DisplayMask" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>DisplayMask</full_name>
			<short_name>DisplayMask</short_name>
			<convention>0:Visible,1:Invisible</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelalias" name="Channel Alias" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Alias</full_name>
			<short_name>Channel Alias</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Alarm/Abnormal Status</full_name>
			<short_name>Alarm/Abnormal Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
        <para id="statsid" name="Stat SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>STATE SID</full_name>
			<short_name>STATE SID</short_name>
			<convention></convention>
            <visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.ai.site_config" name="AI Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltypeno" name="Channel Type NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type NO.</full_name>
			<short_name>Channel Type NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="majorchannelno" name="Major Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Major Channel NO.</full_name>
			<short_name>Major Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="minorchannelno" name="Minor Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Minor Channel NO.</full_name>
			<short_name>Minor Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="adprecision" name="AdPrecision" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AdPrecision</full_name>
			<short_name>AdPrecision</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="basevoltage" name="BaseVoltage" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>BaseVoltage</full_name>
			<short_name>BaseVoltage</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumsamplevalue" name="Maximum Sample Value" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Sample Value</full_name>
			<short_name>Maximum Sample Value</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumsamplevalue" name="Mininum Sample Value" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Sample Value</full_name>
			<short_name>Mininum Sample Value</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumsersordata" name="Maximum Sensor Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Sensor Data</full_name>
			<short_name>Maximum Sensor Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumsersordata" name="Mininum Sensor Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Sensor Data</full_name>
			<short_name>Mininum Sensor Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="maximumanalogdata" name="Maximum Analog Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Maximum Analog Data</full_name>
			<short_name>Maximum Analog Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="mininumanalogdata" name="Mininum Analog Data" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Mininum Analog Data</full_name>
			<short_name>Mininum Analog Data</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="offset" name="Offset" type="float">
			<min>-60</min>
			<max>60</max>
			<precision></precision>
			<step></step>
			<full_name>Offset</full_name>
			<short_name>Offset</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="slope" name="Slope" type="float">
			<min>-10</min>
			<max>10</max>
			<precision></precision>
			<step></step>
			<full_name>Slope</full_name>
			<short_name>Slope</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:Low,1:High</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="displaymask" name="DisplayMask" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>DisplayMask</full_name>
			<short_name>DisplayMask</short_name>
			<convention>0:Visible,1:Invisible</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelalias" name="Channel Alias" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Alias</full_name>
			<short_name>Channel Alias</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Alarm/Abnormal Status</full_name>
			<short_name>Alarm/Abnormal Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
        <para id="statsid" name="Stat SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>STATE SID</full_name>
			<short_name>STATE SID</short_name>
			<convention></convention>
            <visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.di.factory_config" name="DI Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>	
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelno" name="Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>	
			<full_name>Channel NO.</full_name>
			<short_name>Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:Low,1:High</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="channelalias" name="Channel Alias" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Alias</full_name>
			<short_name>Channel Alias</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Alarm/Abnormal Status</full_name>
			<short_name>Alarm/Abnormal Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
        <para id="statsid" name="Stat SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>STATE SID</full_name>
			<short_name>STATE SID</short_name>
			<convention></convention>
            <visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.di.site_config" name="DI Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>	
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelno" name="Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>	
			<full_name>Channel NO.</full_name>
			<short_name>Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:Low,1:High</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="channelalias" name="Channel Alias" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Alias</full_name>
			<short_name>Channel Alias</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Alarm/Abnormal Status</full_name>
			<short_name>Alarm/Abnormal Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
        <para id="statsid" name="Stat SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>STATE SID</full_name>
			<short_name>STATE SID</short_name>
			<convention></convention>
            <visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.do.factory_config" name="DO Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="ctrlmode" name="Ctrl Mode" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Ctrl Mode</full_name>
			<short_name>Ctrl Mode</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelno" name="Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel NO.</full_name>
			<short_name>Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="spscmdid" name="Sps Cmd ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Sps Cmd ID</full_name>
			<short_name>Sps Cmd ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:normal close,1:normally open</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Output Status</full_name>
			<short_name>Default Output Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.do.site_config" name="DO Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="ctrlmode" name="Ctrl Mode" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Ctrl Mode</full_name>
			<short_name>Ctrl Mode</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channeltype" name="Channel Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Type</full_name>
			<short_name>Channel Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelno" name="Channel NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel NO.</full_name>
			<short_name>Channel NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="spscmdid" name="Sps Cmd ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Sps Cmd ID</full_name>
			<short_name>Sps Cmd ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="defaultstatus" name="Default Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>Default Status</full_name>
			<short_name>Default Status</short_name>
			<convention>0:normal close,1:normally open</convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="presetstatus" name="Preset Status" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Output Status</full_name>
			<short_name>Default Output Status</short_name>
			<convention>0:close,1:open</convention>
			<visible>yes</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.ao.factory_config" name="AO Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtypeno" name="Board Type NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type NO.</full_name>
			<short_name>Board Type NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="outputtype" name="Output Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Output Type</full_name>
			<short_name>Output Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="adprecision" name="AdPrecision" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AdPrecision</full_name>
			<short_name>AdPrecision</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="basevoltage" name="BaseVoltage" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>BaseVoltage</full_name>
			<short_name>BaseVoltage</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="spscmdid" name="Sps Cmd ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Sps Cmd ID</full_name>
			<short_name>Sps Cmd ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="displaymask" name="DisplayMask" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>DisplayMask</full_name>
			<short_name>DisplayMask</short_name>
			<convention>0:Visible,1:Invisible</convention>
			<visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.ao.site_config" name="AO Channel Configure" type="multi">
		<para id="boardid" name="Board ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board ID</full_name>
			<short_name>Board ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtype" name="Board Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type</full_name>
			<short_name>Board Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="boardtypeno" name="Board Type NO." type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Board Type NO.</full_name>
			<short_name>Board Type NO.</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="channelname" name="Channel Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Channel Name</full_name>
			<short_name>Channel Name</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="outputtype" name="Output Type" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Output Type</full_name>
			<short_name>Output Type</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="adprecision" name="AdPrecision" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>AdPrecision</full_name>
			<short_name>AdPrecision</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="basevoltage" name="BaseVoltage" type="float">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>BaseVoltage</full_name>
			<short_name>BaseVoltage</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="sid" name="SID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>SID</full_name>
			<short_name>SID</short_name>
			<convention></convention>
			<visible>YES</visible>
			<default></default>
		</para>
		<para id="relateddeviceid" name="Related Device ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Related Device ID</full_name>
			<short_name>Related Device ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="spscmdid" name="Sps Cmd ID" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Sps Cmd ID</full_name>
			<short_name>Sps Cmd ID</short_name>
			<convention></convention>
			<visible>NO</visible>
			<default></default>
		</para>
		<para id="displaymask" name="DisplayMask" type="int">
			<min>0</min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>DisplayMask</full_name>
			<short_name>DisplayMask</short_name>
			<convention>0:Visible,1:Invisible</convention>
			<visible>NO</visible>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.sps_listen" name="sps listen configure" type="multi">
		<para id="name" name="listen name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>listen name</full_name>
			<short_name>listen name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.device" name="device configure" type="multi">
		<para id="name" name="device name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device name</full_name>
			<short_name>device name</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="dev_type_code" name="device type code" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type code</full_name>
			<short_name>device type code</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.acq_unit" name="acquisition unit configure" type="multi">
		<para id="name" name="acquisition unit name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>acquisition unit name</full_name>
			<short_name>acq unit name</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
		<para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>yes</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.smart_board" name="smart extend board configure" type="multi">
		<para id="name" name="smart board name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>smart board name</full_name>
			<short_name>smart board name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.local_addr" name="local link address configure" type="multi">
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance address</full_name>
			<short_name>link inst addr</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="protocol_id" name="protocol id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>protocol id</full_name>
			<short_name>protocol id</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.local_devtype_code" name="local device type code" type="multi">
		<para id="protocol_id" name="protocol id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>protocol id</full_name>
			<short_name>protocol id</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="code" name="device type code" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type code</full_name>
			<short_name>dev type code</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.device_type" name="device type" type="multi">
		<para id="code" name="device type code" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type code</full_name>
			<short_name>dev type code</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="type_name" name="device type name" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device type name</full_name>
			<short_name>dev type name</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.language_category" name="Language Category Configure Information" type="multi">
		<para id="languagename" name="Language Name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Language Name</full_name>
			<short_name>Language Name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="languageinfo" name="Language Encode Info" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Language Encode Info</full_name>
			<short_name>Language Encode Info</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="defaultlanguage" name="Default Language" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Default Language</full_name>
			<short_name>Default Language</short_name>
			<convention>0:No,1:Yes</convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.parser_fidinfo" name="parser field id infomation" type="multi">
		<para id="fid" name="field id" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<language lang_code="en">
				<full_name>field id</full_name>
				<short_name>fid</short_name>
				<convention></convention>
			</language>
			<language lang_code="cn">
				<full_name>字段id</full_name>
				<short_name>字段id</short_name>
				<convention></convention>
			</language>
			<default></default>
		</para>
		<para id="sid" name="signal id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<language lang_code="en">
				<full_name>signal id</full_name>
				<short_name>sid</short_name>
				<convention></convention>
			</language>
			<language lang_code="cn">
				<full_name>信号id</full_name>
				<short_name>信号id</short_name>
				<convention></convention>
			</language>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.buzz_ctrl" name="buzz ctrl" type="single">
		<para id="ctrl_switch" name="ctrl switch" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ctrl switch</full_name>
			<short_name>ctrl switch</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="ctrl_period" name="ctrl period" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ctrl period</full_name>
			<short_name>ctrl period</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
    <cfgobject id="plat.outrelay_ctrl" name="out relay ctrl" type="single">
		<para id="ctrl_outrelay" name="ctrl out relay" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>ctrl out relay</full_name>
			<short_name>ctrl out relay</short_name>
			<convention>0:Output Relay NonForce Control,1:Output Relay Force Control</convention>
			<default>0</default>
		</para>
	</cfgobject>
    <cfgobject id="plat.acq_2dev" name="acquisition bind to device inst" type="multi">
        <para id="acq_inst_id" name="acquisition inst id" type="string32">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>acquisition id</full_name>
            <short_name>acquisition id</short_name>
            <convention></convention>
            <default></default>
        </para>
        <para id="dev_sid" name="device sid" type="sid">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>device sid</full_name>
            <short_name>dev sid</short_name>
            <convention></convention>
            <default></default>
        </para>
    </cfgobject>
    <cfgobject id="plat.acq_2sid" name="acquisition bind to signal inst" type="multi">
        <para id="acq_inst_id" name="acquisition inst id" type="string32">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>acquisition id</full_name>
            <short_name>acquisition id</short_name>
            <convention></convention>
            <default></default>
        </para>
        <para id="cmd_id" name="command id" type="string32">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>command id</full_name>
            <short_name>cmd id</short_name>
            <convention></convention>
            <default></default>
        </para>
        <para id="field_id" name="field id" type="string64">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>field id</full_name>
            <short_name>field id</short_name>
            <convention></convention>
            <default></default>
        </para>
        <para id="sig_id" name="signal id" type="sid">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>signal id</full_name>
            <short_name>signal id</short_name>
            <convention></convention>
            <default></default>
        </para>
    </cfgobject>
    <cfgobject id="plat.system_time" name="system time" type="single">
        <para id="timezone_select" name="timezone select" type="int">
            <min>0</min>
            <max>36</max>
            <precision></precision>
            <step></step>
            <full_name>timezone select</full_name>
            <short_name>timezone select</short_name>
            <convention>0:Azores,1:London,2:GMT-2,3:Athens,4:Moscow,5:Baku,6:Almaty,7:Bangkok,8:Beijing,9:Tokyo,10:Sydney,11:Vladivostok,12:Fiji,13:Iran,14:Calcutta,15:Adelaide,16:Cape_Verde,17:DeNoronha,18:Brazil_East,19:New_York,20:Chicago,21:Denver,22:Los_Angeles,23:Anchorage,24:GMT+9,25:Hawaii,26:Samoa,27:Kwajalein,28:Havana,29:Lima,30:Santiago,31:Asuncion,32:Newfoundland,33:Vienna,34:Jerusalem,35:Tashkent,36:Norfolk</convention>
            <visible>yes</visible>
            <default>8</default>
        </para>
        <para id="dst_enable" name="Daylight Saving Time Enable" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <full_name>Daylight Saving Time Enable</full_name>
            <short_name>DST Enable</short_name>
            <convention>0:On,1:Off</convention>
            <default></default>
        </para>
    </cfgobject>
    <cfgobject id="plat.history_num" name="history num" type="single">
        <para id="history_data_num" name="history data num" type="int">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>history data num</full_name>
            <short_name>history data num</short_name>
            <convention></convention>
            <visible>no</visible>
            <default>100000</default>
        </para>
        <para id="history_alarm_num" name="history alarm num" type="int">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>history alarm num</full_name>
            <short_name>history alarm num</short_name>
            <convention></convention>
            <visible>no</visible>
            <default>20000</default>
        </para>
        <para id="history_event_num" name="history event num" type="int">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>history event num</full_name>
            <short_name>history event num</short_name>
            <convention></convention>
            <visible>no</visible>
            <default>1000</default>
        </para>
    </cfgobject>
	<cfgobject id="plat.loginpara" name="login para" type="single">
        <para id="max_wrong_times" name="Max wrong times" type="int">
            <min>1</min>
            <max>100000</max>
            <precision></precision>
            <step></step>
            <full_name>max wrong times</full_name>
            <short_name>max wrong times</short_name>
            <convention></convention>
            <visible>yes</visible>
            <default>5</default>
        </para>
    </cfgobject>
	<cfgobject id="plat.mqtt" name="MQTT Settings" type="single">
		<para id="brokerip" name="Broker IP" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>no</visible>
			<full_name>Broker IP</full_name>
			<short_name>Broker IP</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="brokerport" name="Broker Port" type="int">
			<min>1</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<visible>no</visible>
			<full_name>Broker Port</full_name>
			<short_name>Broker Port</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="proxy_server_addr_mode" name="MQTT Proxy Server Addressing Mode" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>MQTT Proxy Server Addressing Mode</full_name>
            <short_name>MQTT Proxy Server Addressing Mode</short_name>
            <convention>0:Domain Name,1:IP Address</convention>
            <default>0</default>
		</para>	
		<para id="proxyserverip" name="MQTT Proxy Server IP" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>MQTT Proxy Server IP</full_name>
			<short_name>MQTT Proxy Server IP</short_name>
			<convention></convention>
			<default>127.0.0.1</default>
		</para>
		<para id="proxyserverport" name="MQTT Proxy Server Port" type="int">
			<min>1</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<full_name>MQTT Proxy Server Port</full_name>
			<short_name>MQTT Proxy Server Port</short_name>
			<convention></convention>
			<default>8883</default>
		</para>
		<para id="username" name="MQTT Username" type="string32">
			<full_name>MQTT Username</full_name>
		</para>
		<para id="password" name="MQTT Password" encrypt="yes" type="string128">
			<full_name>MQTT Password</full_name>
		</para>
		<para id="proxyserverdn" name="MQTT Proxy Server Domain Name" type="string64">
			<full_name>MQTT Proxy Server Domain Name</full_name>
		</para>	
		<para id="mqttdnsip" name="MQTT DNS IP" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>MQTT DNS IP</full_name>
			<short_name>MQTT DNS IP</short_name>
			<convention></convention>
			<default>127.0.0.1</default>
		</para>
        <para id="ssl_enable" name="MQTT SSL Security Enable" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>MQTT SSL Security Enable</full_name>
            <short_name>MQTT SSL Security Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default>1</default>
        </para>
        <para id="ssl_auth_mode" name="MQTT SSL Authentication Mode" type="int">
            <min>0</min>
            <max>0</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>MQTT SSL Authentication Mode</full_name>
            <short_name>MQTT SSL Authentication Mode</short_name>
            <convention>0:Encrypt NoAuth</convention>
            <default>0</default>
        </para>
		<para id="data_pub_period" name="MQTT Data Publish Period" type="int">
			<min>1</min>
			<max>3600</max>
			<precision></precision>
			<step></step>
			<full_name>MQTT Data Publish Period</full_name>
			<short_name>MQTT Data Publish Period</short_name>
			<convention></convention>
			<default>120</default>
		</para>
		<para id="pub_theme" name="MQTT Publish Theme" type="string64">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>MQTT Publish Theme</full_name>
			<short_name>MQTT Publish Theme</short_name>
			<convention></convention>
			<default>zabbix/pst</default>
		</para>		
	</cfgobject>
	<cfgobject id="plat.north_protocol" name="North Protocol" type="single">
		<para id="current_protocol" name="current protocol" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Current Protocol</full_name>
			<short_name>Current Protocol</short_name>
			<convention></convention>
			<default>SNMP</default>
		</para>
	</cfgobject>
    <cfgobject id="plat.sps_north" name="sps north device configure" type="multi">
		<para id="name" name="dev name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>device instance name</full_name>
			<short_name>dev inst name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="sps_dev_type_id" name="sps device type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>sps device type id</full_name>
			<short_name>sps device type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_type_id" name="link type id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link type id</full_name>
			<short_name>link type id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="link_inst_id" name="link inst id" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>link instance id</full_name>
			<short_name>link inst id</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
        <para id="addr" name="comm address" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>communication address</full_name>
			<short_name>comm addr</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.ip_link" name="IP Link Configure Information" type="multi">
		<para id="ip_link_name" name="IP link name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>IP link name</full_name>
			<short_name>IP link name</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="trs_protocol" name="transmission protocol" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>transmission protocol</full_name>
			<short_name>trs protocol</short_name>
			<convention>0:tcp,1:udp</convention>
			<default>0</default>
		</para>
		<para id="comm_role" name="communication role" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>communication role</full_name>
			<short_name>comm role</short_name>
			<convention>0:client,1:server</convention>
			<default></default>
		</para>
		<para id="ip_addr" name="IP address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>IP address</full_name>
			<short_name>IP address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="port" name="port" type="int">
			<min>0</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>port</full_name>
			<short_name>port</short_name>
			<convention></convention>
			<default></default>
		</para>
        <para id="ssh_status" name="SSH Status" type="int">
            <min>0</min>
            <max>1</max>
            <precision></precision>
            <step></step>
            <visible>YES</visible>
            <full_name>SSH Enable</full_name>
            <short_name>SSH Enable</short_name>
            <convention>0:Disabled,1:Enabled</convention>
            <default></default>
        </para>
        <para id="ssh_user" name="SSH UserName" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>SSH Username</full_name>
			<short_name>SSH Username</short_name>
			<convention></convention>
			<default></default>
		</para>
        <para id="ssh_passwd" name="SSH Passwd" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>SSH Password</full_name>
			<short_name>SSH Password</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.radius" name="RADIUS" type="single">
		<para id="radius_status" name="RADIUS Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>RADIUS Status</full_name>
			<short_name>RADIUS Status</short_name>
			<convention>0:Disabled,1:Enabled</convention>
			<default></default>
		</para>
		<para id="radius_prim_serv_ip_addr" name="RADIUS Primary Server IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>RADIUS Primary Server IP Address</full_name>
			<short_name>RADIUS Primary Server IP Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="radius_back_serv_ip_addr" name="RADIUS Backup Server IP Address" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>RADIUS Backup Server IP Address</full_name>
			<short_name>RADIUS Backup Server IP Address</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="port" name="RADIUS Server Authentication Port" type="int">
			<min>0</min>
			<max>65535</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>RADIUS Server Authentication Port</full_name>
			<short_name>RADIUS Server Authentication Port</short_name>
			<convention></convention>
			<default>0</default>
		</para>
		<para id="radius_secret" name="RADIUS Secret" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>RADIUS Secret</full_name>
			<short_name>RADIUS Secret</short_name>
			<convention></convention>
			<default></default>
		</para>
		<para id="expiration_time" name="Expiration Time" type="int">
			<min>10</min>
			<max>60</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Expiration Time</full_name>
			<short_name>Expiration Time</short_name>
			<convention></convention>
			<default>10</default>
		</para>
		<para id="retry_times" name="Retry Times" type="int">
			<min>1</min>
			<max>10</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Retry Times</full_name>
			<short_name>Retry Times</short_name>
			<convention></convention>
			<default>1</default>
		</para>
		<para id="private_key_status" name="Private Key Status" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Private Key Status</full_name>
			<short_name>Private Key Status</short_name>
			<convention>0:Unencrypted,1:Encrypted</convention>
			<default>0</default>
		</para>
		<para id="private_key_password" name="Private Key Password" encrypt="yes" type="string128">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<visible>YES</visible>
			<full_name>Private Key Password</full_name>
			<short_name>Private Key Password</short_name>
			<convention></convention>
			<default></default>
		</para>
	</cfgobject>
	<cfgobject id="plat.service_config" name="service config" type="multi">
		<para id="service_name" name="service name" type="string32">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>service name</full_name>
			<short_name>service name</short_name>
			<convention></convention>
			<default></default>
			<visible>no</visible>
		</para>
		<para id="wired_priority" name="wired priority" type="int">
			<min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>wired_priority</full_name>
			<short_name>wired_priority</short_name>
			<convention>0:NOT_CHOOSE,1:PRIORITY_FIRST,2:PRIORITY_SECOND,3:PRIORITY_THIRD</convention>
			<default>1</default>
			<visible>no</visible>
		</para>
		<para id="wireless_priority" name="wireless priority" type="int">
			<min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>wireless_priority</full_name>
			<short_name>wireless_priority</short_name>
			<convention>0:NOT_CHOOSE,1:PRIORITY_FIRST,2:PRIORITY_SECOND,3:PRIORITY_THIRD</convention>
			<default>2</default>
			<visible>no</visible>
		</para>
		<para id="vpn_priority" name="vpn priority" type="int">
			<min>0</min>
			<max>3</max>
			<precision></precision>
			<step></step>
			<full_name>vpn_priority</full_name>
			<short_name>vpn_priority</short_name>
			<convention>0:NOT_CHOOSE,1:PRIORITY_FIRST,2:PRIORITY_SECOND,3:PRIORITY_THIRD</convention>
			<default>0</default>
			<visible>no</visible>
		</para>
		<para id="link_detection_mechanism" name="link detection mechanism" type="int">
			<min>0</min>
			<max>1</max>
			<precision></precision>
			<step></step>
			<full_name>link_detection_mechanism</full_name>
			<short_name>link_detection_mechanism</short_name>
			<convention>0:serice_reachable,1:host_reachable</convention>
			<default>1</default>
			<visible>no</visible>
		</para>
	</cfgobject>
	<cfgobject id="plat.operators" name="operators" type="single">
		<para id="operators" name="Operators" type="int">
			<min></min>
			<max></max>
			<precision></precision>
			<step></step>
			<full_name>Operators</full_name>
			<short_name>Operators</short_name>
			<convention>0:None,1:CHN-UNICOM,2:CHINA MOBILE,3:TOWER</convention>
			<visible>yes</visible>
			<default></default>
		</para>
	</cfgobject>
    <cfgobject id="plat.dmu" name="dmu" type="single">
        <para id="dmu_type" name="dmu type" type="int">
            <min></min>
            <max></max>
            <precision></precision>
            <step></step>
            <full_name>dmu type</full_name>
            <short_name>dmu type</short_name>
            <convention>0:UNKNOWN_BASE_PLATE,1:CSU602_BASE_PLATE,2:CSU603_BASE_PLATE,3:CSU604_BASE_PLATE,4:EDU_BASE_PLATE,5:CT_BASE_PLATE</convention>
            <visible>yes</visible>
            <default>2</default>
        </para>
    </cfgobject>
</config>
