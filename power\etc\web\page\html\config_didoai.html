<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        <div class="main-container container-fluid" >
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="didoai">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            <span i18n="com.serial_">
                            {{i18nkeyword.menu.ai_di_config}}
                            </span>
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span9">
                        <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_successfully}}
                        </div>
                        <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_failure}}
                        </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'diconfig' == tab_list?'active':''">
                                        <a data-toggle="tab" href="#diconfig" onclick="gotoaidipage(1,'diconfig')">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            DI
                                        </a>
                                    </li>
                                    <li ms-class="'aiconfig' == tab_list?'active':''">
                                        <a data-toggle="tab" href="#aiconfig" onclick="gotoaidipage(2,'aiconfig')">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            AI
                                        </a>
                                    </li>
                                    <li ms-class="'doconfig' == tab_list?'active':''">
                                        <a data-toggle="tab" href="#doconfig" onclick="gotoaidipage(3,'doconfig')">
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            DO
                                        </a>
                                    </li>
                                    <li ms-class="'temp_humid_config' == tab_list?'active':''">
										<a data-toggle="tab"  href="#temp_humid_config" onclick="gotoaidipage(5,'temp_humid_config')">
											<i class="blue icon-user bigger-110">
											</i>
											{{i18nkeyword.temp_humid_conf.tab_name}}
										</a>
									</li>
                                </ul>
                                <div class="tab-content">
                                    <div id="diconfig" class="tab-pane" ms-class="'diconfig' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover" ms-if="userlevel==='3'">
                                            <tr ms-for="(n,name) in di_structuredata" ms-if="di_structuredata[n]['name']=='Channel Name' && di_instidvalue.length != '0'">
                                                <td>{{di_structuredata[n]["full_name"]}}</td>
                                                <td>
                                                    <select class="editable input-medium" ms-duplex="di_selectexid" style="width:200px" id="selectserial">
                                                        <option ms-for="(ee,aa) in di_instidvalue" ms-attr="{value:aa.inst_id,selected:di_selectexid==aa.inst_id?true:false}">{{aa["Channel Name"]}}</option>
                                                    </select>
                                                    {{i18nkeyword.state}}:
                                                    <img ms-if="@get_select_channel_state(di_value['Channel Name']) ==='1'" src="/page/assets/img/button_open.png">
                                                    <img ms-if="@get_select_channel_state(di_value['Channel Name']) =='0'" src="/page/assets/img/button_close.png">
                                                </td>
                                            </tr>
                                            <tbody >
                                                <tr ms-for="(el,al) in di_structuredata" ms-if="di_structuredata[el]['visible'] !='NO'" ms-visible="di_value['Class Type'] < '2'">
                                                    <td>{{al.full_name}}</td>
                                                    <td ms-if="getconventionlength(al.convention)>0">
                                                        <select  ms-duplex="di_value[al.name]" disabled="true">
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
                                                        </select>
                                                    </td>
                                                    <td ms-if="(getconventionlength(al.convention)==0)&&(al.name!='SID')">
                                                        <input class="form-control input-medium pull-left" ms-duplex="di_value[al.name]" disabled="true">
                                                    </td>
                                                    <td ms-if="al.name=='SID'">
                                                        {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="di_relevance_device_sid"  style="width:140px" disabled="true">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in devsid_listvalue" ms-attr="{value:sidnum.sid,selected:di_relevance_device_sid==sidnum.sid?true:false}" >{{sidnum["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="di_relevance_signal_sid" style="width:160px" disabled="true">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in ai_di_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:di_relevance_signal_sid==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr ms-for="(el,al) in di_structuredata" ms-if="di_structuredata[el]['visible'] !='NO'" ms-visible="di_value['Class Type'] >= '2'">
                                                    <td>{{al.full_name}}</td>
                                                    <td ms-if="getconventionlength(al.convention)>0">
                                                        <select  ms-duplex="di_value[al.name]">
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
                                                        </select>
                                                    </td>
                                                    <td ms-if="(getconventionlength(al.convention)==0)&&(al.name!='SID')">
                                                        <input class="form-control input-medium pull-left" ms-duplex="di_value[al.name]">
                                                    </td>
                                                    <td ms-if="al.name=='SID'">
                                                        {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="di_relevance_device_sid"  style="width:140px">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in di_device_sid" ms-attr="{value:sidnum.dev_sid,selected:di_relevance_device_sid==sidnum.dev_sid?true:false}">{{sidnum["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="di_relevance_signal_sid" style="width:160px">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in di_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:di_relevance_signal_sid==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr ms-visible="@judge_inrelay(di_relevance_signal_sid) && @judge_aidi_channel(di_selectexid)">
                                                    <td>{{i18nkeyword.alias}}</td>
                                                    <td>
                                                        <input class="form-control input-medium pull-left" style="width:60%" ms-attr="{'placeholder':i18nkeyword.check_input_tip+','+i18nkeyword.input_max_length+':31'+i18nkeyword.char}" ms-duplex="di_value['Channel Alias']">
                                                    </td>
                                                <tr>
                                            </tbody>
                                        </table>
                                        <div class="tabbable" style="text-align:center" ms-if="di_instidvalue.length == '0' && di_title_show == '1'">
                                            {{i18nkeyword.no_channel_config}}
                                        </div>
                                        <button class="button button-small button-flat-primary" onclick="setDIValue()" ms-if="di_instidvalue.length != '0'">
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div><!--DI-->
                                    <div id="aiconfig" class="tab-pane" ms-class="'aiconfig' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover" ms-if="ai_instidvalue.length != '0'" >
                                            <tr ms-for="(n,name) in ai_structuredata" ms-if="ai_structuredata[n]['name']=='Channel Name'">
                                                <td>{{ai_structuredata[n]["full_name"]}}</td>
                                                <td>
                                                    <select class="editable input-medium" ms-duplex="@ai_selectexid" style="width:200px" id="selectserial">
                                                        <option ms-for="(ee,aa) in ai_instidvalue" ms-if="aa['Channel Type']!='2'" ms-attr="{value:aa.inst_id,selected:ai_selectexid == aa.inst_id}">{{aa["Channel Name"]}}</option>
                                                    </select>
                                                </td>
                                            </tr>
                                            <tbody >
                                                <tr ms-for="(el,al) in ai_structuredata" ms-if="ai_show(al.name,ai_structuredata[el]['visible'],ai_value['Channel Type'])">
                                                    <td>{{al.full_name}}</td>
                                                    <td ms-if="(getconventionlength(al.convention)==0)&&(al.name!='SID')"><!--���б��-->
                                                        <input class="form-control input-medium pull-left" ms-duplex="ai_value[al.name]" oninput="value=value.replace(/[^\0,\1,\2,\3,\4,\5,\6,\7,\8,\9,\-,\.]/g,'')">
                                                    </td>
                                                    <td ms-if="al.name=='SID'" ms-visible="ai_value['Class Type'] < '2'">
                                                        {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="ai_relevance_device_sid"  style="width:200px" disabled="true">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in devsid_listvalue" ms-attr="{value:sidnum.sid,selected:ai_relevance_device_sid==sidnum.sid}" >{{sidnum["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="ai_relevance_signal_sid" style="width:200px" disabled="true">
                                                            <option value = "" >{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in ai_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:ai_relevance_signal_sid==sidnum.sid?true:false}">
                                                                {{sidnum["full_name"]}}
                                                            </option>
                                                        </select>
                                                        <span style="float:right">{{i18nkeyword.devlist.value}}:&nbsp;&nbsp;<b class="green">{{ai_analogdata_value}}</b></span>
                                                    </td>
                                                    <td ms-if="al.name=='SID'" ms-visible="ai_value['Class Type'] >= '2'">
	                                                    {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="ai_relevance_device_sid"  style="width:200px" >
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in ai_device_sid" ms-attr="{value:sidnum.dev_sid,selected:ai_relevance_device_sid==sidnum.dev_sid}">{{sidnum["device_name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="ai_relevance_signal_sid" data-duplex-changed="changeChannelData()" style="width:200px">
                                                            <option value = "" >{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in ai_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:ai_relevance_signal_sid==sidnum.sid?true:false}">
                                                                {{sidnum["full_name"]}}
                                                            </option>
                                                        </select>
                                                        <span style="float:right">{{i18nkeyword.devlist.value}}:&nbsp;&nbsp;<b class="green">{{ai_analogdata_value}}</b></span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div class="tabbable" style="text-align:center" ms-if="ai_instidvalue.length == '0' && ai_title_show == '1'">
                                            {{i18nkeyword.no_channel_config}}
                                        </div>
                                        <button class="button button-small button-flat-primary" onclick="setAIValue()" ms-if="ai_instidvalue.length != '0'" >
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div><!--AI-->
                                    <div id="doconfig" class="tab-pane" ms-class="'doconfig' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover" ms-if="userlevel==='3' && do_instidvalue.length != '0'">
                                            <tr ms-for="(n,name) in do_structuredata" ms-if="do_structuredata[n]['name']=='Channel Name'">
                                                <td>{{do_structuredata[n]["full_name"]}}</td>
                                                <td>
                                                    <select class="editable input-medium" ms-duplex="do_selectexid" style="width:200px" id="selectserial">
                                                        <option ms-for="(ee,aa) in do_instidvalue"  ms-attr="{value:aa.inst_id,selected:do_selectexid == aa.inst_id?true:false}">{{aa["Channel Name"]}}</option>
                                                    </select>
                                                    {{i18nkeyword.state}}:
                                                    <img ms-if="@get_select_do_channel_state(do_value['Board Type'],do_value['Channel Type'],do_value['Channel NO.']) =='0'" src="/page/assets/img/button_close.png">
                                                    <img ms-if="@get_select_do_channel_state(do_value['Board Type'],do_value['Channel Type'],do_value['Channel NO.']) =='1'" src="/page/assets/img/button_open.png">
                                                </td>
                                            </tr>
                                            <tbody >
                                                <!--ms-for:(el,al) in do_structuredata-->
                                                <tr  ms-if="do_structuredata[el]['name'] =='Preset Status' && @judge_do_sid_special(do_relevance_signal_sid)">
                                                    <td>{{al.full_name}}</td>
                                                    <td>
                                                        <select  ms-duplex="do_value['Present Status']" style="width:200px" disabled="true">
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr  ms-if="do_structuredata[el]['name'] =='Preset Status' && !@judge_do_sid_special(do_relevance_signal_sid)">
                                                    <td>{{al.full_name}}</td>
                                                    <td>
                                                        <select  ms-duplex="do_value['Present Status']" style="width:200px" >
                                                            <option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr  ms-if="do_structuredata[el]['name'] =='SID'"  ms-visible="do_value['Ctrl Mode'] == '3'">
                                                    <td>{{al.full_name}}</td>
                                                    <td>
                                                        {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="do_relevance_device_sid"  style="width:200px" disabled="true">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in do_device_sid" ms-attr="{value:sidnum.dev_sid,selected:do_relevance_device_sid==sidnum.dev_sid?true:false}" >{{sidnum["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="do_relevance_signal_sid" style="width:200px" disabled="true">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in do_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:do_relevance_signal_sid==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr  ms-if="do_structuredata[el]['name'] =='SID'"  ms-visible="do_value['Ctrl Mode'] != '3'">
                                                    <td>{{al.full_name}}</td>
                                                    <td>
                                                        {{i18nkeyword.device}}
                                                        <select class="editable input-medium" ms-duplex="do_relevance_device_sid"  style="width:200px" >
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in do_device_sid" ms-attr="{value:sidnum.dev_sid,selected:do_relevance_device_sid==sidnum.dev_sid?true:false}" >{{sidnum["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="do_relevance_signal_sid"  data-duplex-changed="changeDoChannelData()" style="width:200px">
                                                            <option value = "">{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in do_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:do_relevance_signal_sid==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <!--ms-for-end:-->
                                            </tbody>
                                        </table>
                                        <div class="tabbable" style="text-align:center" ms-if="do_instidvalue.length == '0' && do_title_show == '1'">
                                            {{i18nkeyword.no_channel_config}}
                                        </div>
                                        <button class="button button-small button-flat-primary" onclick="setDOValue()" ms-if="do_instidvalue.length != '0'">
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div><!--DO-->
                                    <div id="temp_humid_config" class="tab-pane" ms-class="'temp_humid_config' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover" ms-if="userlevel==='3'">
                                            <tr>
                                                <td>{{i18nkeyword.temp_humid_conf.channel_name}}</td>
                                                <td>
                                                    <select class="editable input-medium"  ms-duplex="@temp_humid_selectexid" style="width:200px" id="selectserial">
                                                        <option value="1">{{i18nkeyword.temp_humid_conf.temp_sensor}}</option>
                                                        <option value="0">{{i18nkeyword.temp_humid_conf.humid_sensor}}</option>
                                                    </select>
                                                </td>
                                            </tr>
                                            <tbody >
                                                <tr>
                                                    <td>{{i18nkeyword.temp_humid_conf.sid}}</td>
                                                    <td>
	                                                    {{i18nkeyword.device}}
                                                        <select class="editable input-medium"  style="width:200px" disabled="true">
                                                            <option value="0" >{{temp_humid_relevance_device_signals[0]["device name"]}}</option>
                                                        </select>
                                                        {{i18nkeyword.signal}}
                                                        <select class="editable input-medium" ms-duplex="temp_humid_relevance_signal_sid" data-duplex-changed="changeTempHumidChannelData()" style="width:200px">
                                                            <option value = "0" >{{i18nkeyword.null}}</option>
                                                            <option ms-for="sidnum in temp_humid_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:temp_humid_relevance_signal_sid==sidnum.sid?true:false}" ms-if="temp_humid_judge(sidnum.sid)">
                                                                {{sidnum["full_name"]}}
                                                            </option>
                                                        </select>
                                                        <span style="float:right">{{i18nkeyword.devlist.value}}:&nbsp;&nbsp;<b class="green">{{temp_humid_analogdata_value}}</b></span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <button class="button button-small button-flat-primary" onclick="setTempHumidValue()" >
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div><!--温湿度通道-->
                                </div>
                            </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span9-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
              <!-- inline scripts related to this page -->
        <script src="/page/js/config_didoai.js"></script>
    </body>
</html>
