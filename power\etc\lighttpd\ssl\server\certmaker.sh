#!/bin/bash
#
# usage:certmaker.sh -d domain-name
# usage:certmaker.sh -i IP address

#stop once any error occurs
set -e

if [ $# -ne 2 ];then
    echo "error args"
    exit 1
fi

getopts "d:i:" opt
if [ $opt = 'd' ];then
    cname=$OPTARG
elif [ $opt = 'i' ];then
    cname=$OPTARG
else
    echo "error usage!"
	exit 1
fi

openssl genrsa -out lighttpd.key 2048

message="/C=CN/ST=HuBei/L=WuHan/O=ZTE/OU=Energy/CN=${cname}"
openssl req -new -key lighttpd.key -out lighttpd.csr -subj $message

rootCA=../client/energy.crt
if [ ! -f $rootCA ];then
    echo "error:root CA can't find!"
	exit 1
fi

CAKey=../client/energy.key
if [ ! -f $CAKey ];then
    echo "error:root key can't find!"
	exit 1
fi

openssl x509 -req -in lighttpd.csr -CA $rootCA -CAkey $CAKey -CAcreateserial -out lighttpd.crt -days 7300 -sha256
cat lighttpd.key lighttpd.crt > lighttpd.pem
