#######################################################################
##
##  FastCGI Module 
## --------------- 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModFastCGI
##
server.modules += ( "mod_fastcgi" )

fastcgi.debug = 1
fastcgi.server = (
	".fcgi" => (
	 "local" => (
	  "socket" => socket_dir + "/fcgi.socket",
	  "checklocal" => "disable",
	  "bin-path" => "/root/power/bin/main.fcgi",
	  "disable-timeout" => 10,
	  "max-procs" => 1
	 )
	)
)

alias.url += ( "/power/cgi-bin/" => "/root/power/bin/" )

##
## PHP Example
## For PHP don't forget to set cgi.fix_pathinfo = 1 in the php.ini.
##
## The number of php processes you will get can be easily calculated:
##
## num-procs = max-procs * ( 1 + PHP_FCGI_CHILDREN )
##
## for the php-num-procs example it means you will get 17*5 = 85 php
## processes. you always should need this high number for your very
## busy sites. And if you have a lot of RAM. :)
##
#fastcgi.server = ( ".php" =>
#                   ( "php-local" =>
#                     (
#                       "socket" => socket_dir + "/php-fastcgi-1.socket",
#                       "bin-path" => server_root + "/cgi-bin/php5",
#                       "max-procs" => 1,
#                       "broken-scriptfilename" => "enable",
#                     )
#                   ),
#                   ( "php-tcp" =>
#                     (
#                       "host" => "127.0.0.1",
#                       "port" => 9999,
#                       "check-local" => "disable",
#                       "broken-scriptfilename" => "enable",
#                     )
#                   ),
#
#                   ( "php-num-procs" =>
#                     (
#                       "socket" => socket_dir + "/php-fastcgi-2.socket",
#                       "bin-path" => server_root + "/cgi-bin/php5",
#                       "bin-environment" => (
#                         "PHP_FCGI_CHILDREN" => "16",
#                         "PHP_FCGI_MAX_REQUESTS" => "10000",
#                       ),
#                       "max-procs" => 5,
#                       "broken-scriptfilename" => "enable",
#                     )
#                   ),
#                )

##
## Ruby on Rails Example
##
## Normally you only run one Rails application on one vhost.
##
#$HTTP["host"] == "rails1.example.com" {
#  server.document-root  = server_root + "/rails/someapp/public"
#  server.error-handler-404 = "/dispatch.fcgi"
#  fastcgi.server = ( ".fcgi" =>
#    ("someapp" =>
#      ( "socket" => socket_dir + "/someapp-fcgi.socket",
#        "bin-path" => server_root + "/rails/someapp/public/dispatch.fcgi",
#        "bin-environment" => (
#              "RAILS_ENV" => "production",
#              "TMP" => home_dir + "/rails/someapp",
#        ),
#      )
#    )
#  )
#}

##
## Another example with multiple rails applications on one vhost.
##
## http://blog.lighttpd.net/articles/2005/11/23/lighttpd-1-4-8-and-multiple-rails-apps
##
#$HTTP["host"] == "rails2.example.com" {
#  $HTTP["url"] =~ "^/someapp1" {
#    server.document-root  = server_root + "/rails/someapp1/public"
#    server.error-handler-404 = "/dispatch.fcgi"
#    fastcgi.server = ( ".fcgi" =>
#      ("someapp1" =>
#        ( "socket" => socket_dir + "/someapp1-fcgi.socket",
#          "bin-path" => server_root + "/rails/someapp1/public/dispatch.fcgi",
#          "bin-environment" => (
#                "RAILS_ENV" => "production",
#                "TMP" => home_dir + "/rails/someapp1",
#          ),
#          "strip-request-uri" => "/someapp1/"
#        )
#      )
#    )
#  }
#
#  $HTTP["url"] =~ "^/someapp2" {
#    server.document-root  = server_root + "/rails/someapp2/public"
#    server.error-handler-404 = "/dispatch.fcgi"
#    fastcgi.server = ( ".fcgi" =>
#      ("someapp2" =>
#        ( "socket" => socket_dir + "/someapp2-fcgi.socket",
#          "bin-path" => server_root + "/rails/someapp2/public/dispatch.fcgi",
#          "bin-environment" => (
#                "RAILS_ENV" => "production",
#                "TMP" => home_dir + "/rails/someapp2",
#          ),
#          "strip-request-uri" => "/someapp2/"
#        )
#      )
#    )
#  }
#}

## chrooted webserver + external PHP
##
## $ spawn-fcgi -f /usr/bin/php-cgi -p 2000 -a 127.0.0.1 -C 8
##
## webserver chrooted to /srv/www/
## php running outside the chroot
#
#fastcgi.server = ( 
#  ".php" => (( 
#    "host" => "127.0.0.1",
#    "port" => "2000",
#    "docroot" => "/srv/www/servers/www.example.org/htdocs/"
#  )))
#
#server.chroot = "/srv/www"
#server.document-root = "/servers/wwww.example.org/htdocs/"
#

##
#######################################################################
