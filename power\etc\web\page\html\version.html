<!DOCTYPE html>
<html>    
    <head>
		<!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/header.html" -->
        
        <div class="main-container container-fluid" ms-controller="version_info">
			<!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
				<div class="breadcrumbs" id="breadcrumbs">
					<ul class="breadcrumb">
						<li>
							<i class="icon-home"></i>
							{{i18nkeyword.devlist.devices_data}}
						</li>
					</ul><!-- /.breadcrumb -->
				</div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
							<!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
								<div class="tab-content">
									<div id="version" class="tab-pane active"  style="height:600px">
										<table ms-if="version_infos.length != 0 && is_devinfo_empty == 0" id="sample-table-1" class="table table-striped table-bordered table-hover">
											<thead>
												<tr>
													<th>{{i18nkeyword.devlist.devicename}}</th>
													<th>{{i18nkeyword.devlist.signalname}}</th>
													<th>
														<i class="icon-time bigger-110 hidden-480"></i>
														{{i18nkeyword.devlist.value}}
													</th>
												</tr>
											</thead>

											<tbody>
												<!--ms-for:(i,version) in version_infos-->
												<tr>
													<td>
														<a href="#">{{version['device name']}}</a>
													</td>
													<td>{{version.full_name}}</td>
													<td class="hidden-480" style="white-space: pre;">{{@get_version_value(version)}}</td>
												</tr>
												<!--ms-for-end:-->
											</tbody>
										</table>
										<div class="content" ms-if = "is_devinfo_empty == 1">
											{{i18nkeyword.no_displayable_data}}
										</div>
									</div>
								</div>
                            </div><!--/tabbable-->
							<!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->        
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
		<!-- inline scripts related to this page -->
		<script src="/page/js/version.js"></script>
    </body>

</html>