#!/bin/sh

# wrote by KORI 20221206
# very poor version
# ver1.0

sysctl_file_path="/root/power/etc/sys/"
sysctl_file_name="sysctl.conf"
file=$sysctl_file_path$sysctl_file_name

echo OS Enhancement start............................

# judge if sysctl.conf exist
if [ ! -f $file ] ; then
	echo "file unexist, create conf file [$file]"
	touch $file
	chmod 755 $file
fi

function assign_config_value_invalid () {
	for one in $@
	do
		# echo ${one}
		grep ${one} $file > /dev/null
		if [ $? -eq 0 ] ; then

			echo ${one} has been configured
		else
			echo ${one}"=0" >> $file
		fi
	done
}

function assign_config_value_valid () {
	for one in $@
	do
		# echo ${one}
		grep ${one} $file > /dev/null
		if [ $? -eq 0 ] ; then

			echo ${one} has been configured
		else
			echo ${one}"=1" >> $file
		fi
	done
}

assign_config_value_invalid \
		"net.ipv4.conf.all.send_redirects" \
		"net.ipv4.conf.default.send_redirects" \
		"net.ipv4.conf.all.accept_redirects" \
		"net.ipv4.conf.default.accept_redirects" \
		"net.ipv6.conf.all.accept_redirects" \
		"net.ipv6.conf.default.accept_redirects" \
		"net.ipv4.conf.all.secure_redirects" \
		"net.ipv4.conf.default.secure_redirects" \
		"net.ipv4.ip_forward" \
		"net.ipv6.conf.all.forwarding" \


assign_config_value_valid \
		"net.ipv4.icmp_echo_ignore_broadcasts" \
		"net.ipv4.icmp_ignore_bogus_error_responses" \
		"net.ipv4.tcp_syncookies" \

echo OS Enhancement End..............................