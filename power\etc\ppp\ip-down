#!/bin/sh
#
# This sample code shows you one way to modify your setup to allow automatic
# configuration of your resolv.conf for peer supplied DNS addresses when using
# the `usepeerdns' option.
#
# In my case I just added this to my /etc/ppp/ip-down.local script. You may need to 
# create an executable script if one does not exist.
#
# <PERSON> (<EMAIL>)
#

if [ -n "$USEPEERDNS" -a -f /etc/ppp/resolv.conf ]; then
	if [ -f /etc/ppp/resolv.prev ]; then
		cp -f /etc/ppp/resolv.prev /etc/resolv.conf
	else
		rm -f /etc/resolv.conf
	fi
fi

#if [ -n "$defaultroute" ]; then
	ifconfig eth0 up
	/etc/init.d/rcS.network
#fi