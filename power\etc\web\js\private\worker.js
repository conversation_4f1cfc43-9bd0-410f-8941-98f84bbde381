var count = 0;
var timeout = 10*60;
var bPause = false;
self.addEventListener('connect', function(e) {
    var port = e.ports[0];
    port.start();

    setInterval(() => {
        port.postMessage("ifmousemove");
    }, 1000);

    port.addEventListener('message', function(e) {
        if (e.data == "mousemoved") {
            count = 0;
        }
        if (e.data == "reset") {
            count = 0;
            bPause = false;
        }
        if (e.data == "pause") {
            bPause = true;
        }
        if (e.data == "iftimeout") {
            if (count > timeout) {
                port.postMessage("timeout");
                count = 0;
            }
        }
    });
});

const counter = () =>{
    this.setInterval(() => {
        if (!bPause) {
            count++;
        }
    }, 1000);
};

counter();