#!/bin/sh
DATA_BLOCK="/dev/mtdblock10"
DATA_MTD="/dev/mtd10"
SYS_PARA_BAKUP="/mnt/backup/" #backup dir
SYS_PARA="/mnt/data/para_and_config" #running data dir
TMPFS="/mnt/tmpfs"
LOG_PATH="/tmp/log/"
IDFILE="/mnt/backup/CSU_ID_AppTest.bin"
PKG_PATH="$1"
USB_BIN_PACKAGE="$2"
USB_BIN="am335_usb.bin"
UPDATE_FAIL=$SYS_PARA_BAKUP"update_all_fail.txt"
UPDATE_SUCC=$SYS_PARA_BAKUP"update_all_succ.txt"

ERRFILE="/tmp/log/updateinfo"
DIFFFILE="/tmp/log/diff_failure"

sync

echo "all update start, log to $ERRFILE!"
if [ -f $ERRFILE ] ; then
	rm -rf $ERRFILE
fi
echo "all update start, all log to $ERRFILE!" >> "$ERRFILE"

#1.kill all proccess for memory
cd /root/power/etc/sys
chmod 755 kill_all_process.sh
sleep 1
./kill_all_process.sh  >>/dev/null
if [ $? -eq 0 ]; then
    echo "kill all proccess success"
	echo "kill all proccess success" >> "$ERRFILE"
else
	echo "kill all proccess failure!"
    echo "kill all proccess failure!" >> "$ERRFILE"
    echo "all update stop!"
    echo "all update stop!" >> "$ERRFILE"
    mv $UPDATE_SUCC  $UPDATE_FAIL
    reboot -f
fi

#2.creat tmpfs
if [ ! -d $TMPFS ] ; then
	mkdir $TMPFS
fi

mount -t tmpfs -o size=100m  tmpfs $TMPFS
if [ $? -eq 0 ]; then
	echo "mount tmpfs success"
	echo "mount tmpfs success" >> "$ERRFILE"
else
	echo "mount tmpfs failure!"
    echo "mount tmpfs failure!" >> "$ERRFILE"
    mv $UPDATE_SUCC  $UPDATE_FAIL
    reboot -f
fi

#3.uncompress the package to tmpfs
tar zxf $PKG_PATH/$USB_BIN_PACKAGE -C $TMPFS
if [ $? -eq 0 ]; then
	echo "tar bin file and tools success"
	echo "tar bin file and tools success" >> "$ERRFILE"
else
	echo "tar bin file and tools failure!"
    echo "tar bin file and tools failure!" >> "$ERRFILE"
    umount $TMPFS
    mv $UPDATE_SUCC  $UPDATE_FAIL
    reboot -f
fi


#4.check the sh256 of bin file
cd $TMPFS
sha256sum -s -c sh256.txt
if [ $? -eq 0 ]; then
	echo "file sh256 ok"
    echo "file sh256 ok" >> "$ERRFILE"
		chmod +x $TMPFS/flash_erase
		chmod +x $TMPFS/nandwrite
else
	echo "file sh256  failure!"
	echo "file sh256  failure!" >> "$ERRFILE"
	cd /root
    umount $TMPFS
    mv $UPDATE_SUCC  $UPDATE_FAIL
    reboot -f
fi

#5.save mac address
ifconfig eth0 up
ETHADDR=`ifconfig eth0 | grep HWaddr | sed -n  '1P' | awk '{print $5}'`
if [ -z $ETHADDR ] ; then
	echo "get eth addr failure, $ETHADDR!"
	echo "get eth addr failure, $ETHADDR!"  >> "$ERRFILE"
else
	echo "get eth addr success $ETHADDR"
    echo "get eth addr success $ETHADDR"  >> "$ERRFILE"
fi

#6.bakup para and config and Certificate
echo "bakup sys para from $SYS_PARA to $SYS_PARA_BAKUP"
echo "bakup sys para from $SYS_PARA to $SYS_PARA_BAKUP" >> "$ERRFILE"
cd $SYS_PARA_BAKUP && rm -rf para_and_config
cp -rf $SYS_PARA   $SYS_PARA_BAKUP
if [ -f $IDFILE ]; then
	cp -rf $IDFILE     $SYS_PARA_BAKUP
fi
#bakup sys account info
cp -rf /etc/group   $SYS_PARA_BAKUP
cp -rf /etc/gshadow $SYS_PARA_BAKUP
cp -rf /etc/passwd  $SYS_PARA_BAKUP
cp -rf /etc/shadow  $SYS_PARA_BAKUP


RADIUS_BAKUP_CRET="/mnt/backup/radius" #backup dir
RADIUS_SSL_CRET="/mnt/data/work/radius/ssl"
WEB_BAKUP_CRET="/mnt/backup/web" #backup dir
WEB_SSL_CRET="/mnt/data/work/web/ssl"
rm -rf $RADIUS_BAKUP_CRET
cp -rf $RADIUS_SSL_CRET   $RADIUS_BAKUP_CRET
rm -rf $WEB_BAKUP_CRET
cp -rf $WEB_SSL_CRET      $WEB_BAKUP_CRET

#backup auth_management info
WEBUSER_BIN="/mnt/data/work/auth_management/webuser.bin"
GUIUSER_BIN="/mnt/data/work/auth_management/guiuser.bin"
WEBWEAKUSER_BIN="/mnt/data/work/auth_management/webweakuser.bin"
AUTHMANAGEMENT_DIR="$SYS_PARA_BAKUP/auth_management"
if [ ! -d $AUTHMANAGEMENT_DIR ] ; then
    mkdir $AUTHMANAGEMENT_DIR
fi

echo "bakup auth_management info start!"
cp $WEBUSER_BIN $GUIUSER_BIN $WEBWEAKUSER_BIN $AUTHMANAGEMENT_DIR
echo "bakup auth_management info finish!"

#bakup sys stat comdmgr candmgr info
STATSAVE_BIN="/mnt/data/work/productapp/statsave.bin"
STATSAVEBAK_BIN="/mnt/data/work/productapp/statsavebak.bin"
SMRCODEAUTH_BIN="/mnt/data/work/productapp/smrcodeauth.bin"
SMRCODEAUTHBAK_BIN="/mnt/data/work/productapp/smrcodeauthbak.bin"
COMDMGR_BIN="/mnt/data/work/comdmgr/*.bin"
CANDMGR_BIN="/mnt/data/work/candmgr/*.bin"
COMDMGR_DIR="$SYS_PARA_BAKUP/comdmgr"
CANDMGR_DIR="$SYS_PARA_BAKUP/candmgr"
if [ ! -d $COMDMGR_DIR ] ; then
	mkdir $COMDMGR_DIR
fi

if [ ! -d $CANDMGR_DIR ] ; then
	mkdir $CANDMGR_DIR
fi

echo "bakup sys stat comdmgr candmgr info start!"
cp $STATSAVE_BIN $STATSAVEBAK_BIN  $SYS_PARA_BAKUP
cp $SMRCODEAUTH_BIN $SMRCODEAUTHBAK_BIN  $SYS_PARA_BAKUP
cp $COMDMGR_BIN  $COMDMGR_DIR
cp $CANDMGR_BIN  $CANDMGR_DIR
echo "bakup sys stat comdmgr candmgr info finish!"
sync

#7.umount /mnt/data
sleep 8
cd /root
umount -fl  $DATA_BLOCK
if [ $? -eq 0 ]; then
    echo "umount /mnt/data success"
	echo "umount /mnt/data success" >> "$ERRFILE"
else
	echo "umount /mnt/data failure!"
    echo "umount /mnt/data failure!" >> "$ERRFILE"
    echo "all update stop!"
    echo "all update stop!" >> "$ERRFILE"
    mv $UPDATE_SUCC $UPDATE_FAIL
    reboot -f
fi

AM335_USB_PATH="/mnt/tmpfs/am335_usb.bin"
PACKAGE_HEAD_PATH="/mnt/tmpfs/package_head.bin"
DATA_BLOCK_HEAD_PATH="/mnt/tmpfs/data_block_head.bin"

function update_firmware {
    echo "begin erase ..."
    echo "begin erase ..."  >> "$ERRFILE"
    cd $TMPFS
    flash_erase $DATA_MTD 0 0
    if [ $? -eq 0 ]; then
        echo "flash_erase $DATA_MTD success"
        echo "flash_erase $DATA_MTD success" >> "$ERRFILE"
    else
        echo "flash_erase $DATA_MTD failure!"
        echo "flash_erase $DATA_MTD failure!" >> "$ERRFILE"
        echo "all update stop!"
        echo "all update stop!" >> "$ERRFILE"
        mv $UPDATE_SUCC $UPDATE_FAIL
        reboot -f
    fi

    echo "begin write ..."
    echo "begin write ..." >> "$ERRFILE"
    nandwrite -p $DATA_MTD  $USB_BIN
    if [ $? -eq 0 ]; then
        echo "nandwrite $DATA_MTD success"
        echo "nandwrite $DATA_MTD success" >> "$ERRFILE"
    else
        echo "nandwrite $DATA_MTD failure!"
        echo "nandwrite $DATA_MTD failure!" >> "$ERRFILE"
        echo "all update stop!"
        echo "all update stop!" >> "$ERRFILE"
        mv $UPDATE_SUCC $UPDATE_FAIL
        reboot -f
    fi
    dd if=$AM335_USB_PATH of=$PACKAGE_HEAD_PATH bs=128K count=1
    nanddump -s 0 -l 0x20000 /dev/mtd10 -f $DATA_BLOCK_HEAD_PATH
}

#8.flash erase and write
update_firmware

echo "all update end!"

#9.Verifying the Upgrade Package
diff $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH
if [ $? -eq 0 ]; then
    echo "diff success 1"
    echo "diff success 1" >> "$ERRFILE"
    reboot -f
else
    echo "diff failure 1"
    echo "diff failure 1" >> "$ERRFILE"
    echo "diff failure 1" > "$DIFFFILE"
    cp -f $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH $LOG_PATH
    update_firmware
fi

diff $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH
if [ $? -eq 0 ]; then
    echo "diff success 2"
    echo "diff success 2" >> "$ERRFILE"
    reboot -f
else
    echo "diff failure 2"
    echo "diff failure 2" >> "$ERRFILE"
    echo "diff failure 2" > "$DIFFFILE"
    cp -f $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH $LOG_PATH
    update_firmware
fi

diff $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH
if [ $? -eq 0 ]; then
    echo "diff success 3"
    echo "diff success 3" >> "$ERRFILE"
else
    echo "diff failure 3"
    echo "diff failure 3" >> "$ERRFILE"
    echo "diff failure 3" > "$DIFFFILE"
    cp -f $PACKAGE_HEAD_PATH $DATA_BLOCK_HEAD_PATH $LOG_PATH
    mv $UPDATE_SUCC $UPDATE_FAIL
    flash_erase $DATA_MTD 0 0
fi

reboot -f
