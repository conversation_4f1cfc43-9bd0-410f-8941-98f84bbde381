﻿#!/bin/sh
# file name : clean_all_data.sh 
# usage : for default update to clean old data, before execute this script you should save some info to power/etc/xxx

# Do not rename the shell file name!!!
# Do not rename the shell file name!!!
# Do not rename the shell file name!!!

echo "clean para config record"

# delete history record
cd /mnt/data/database
rm -rf *

# delete para, keep dict_para.sqlite for inherit
cd  /mnt/data/para_and_config/para
cp  dict_para.sqlite ../
rm -rf *
mv ../dict_para.sqlite ./


# delete config
cd  /mnt/data/para_and_config/config
#rm -rf *

# delete backup para
cd  /mnt/data/para_and_config/back
rm -rf *

rm -rf  /mnt/backup/para_and_config

# ssl Certificate and radius Certificate need inherit, don't delete
# delete ssl Certificate
#rm -rf /mnt/data/work/web/ssl
#cp -rf /root/power/etc/lighttpd/ssl /mnt/data/work/web
#cd  /mnt/data/work/web/backup
#rm -rf *

# delete radius Certificate
#cd  /mnt/data/work/radius/ssl
#rm -rf *

#web
rm -f  /mnt/data/work/web/web_operator.bin
cd  /mnt/data/work/web/download/web_export
rm -rf *

#auth_management
cd  /mnt/data/work/auth_management
# ls | grep -v defaultuser.bin | xargs rm -rf
find ./ -type f ! -name defaultuser.bin -a ! -name secauthuser.bin|xargs rm -rf
# ls | grep -v secauthuser.bin | xargs rm -rf
#sysmonitor
#sys_update.bin use for update record, do not delete
#rm -f /mnt/data/work/sysmonitor/sys_update.bin

#smrmgr
cd  /mnt/data/work/smrmgr
rm -rf *

#productapp
cd  /mnt/data/work/productapp
mv smrcodeauth*.bin /mnt/backup/
rm -rf *
mv /mnt/backup/smrcodeauth*.bin ./

# comdmgr.bin  info
echo "delete comdmgr.bin"
cd  /mnt/data/work/comdmgr
rm -rf *

# candmgr.bin  info
echo "delete candmgr.bin"
cd  /mnt/data/work/candmgr
rm -rf *

# delete other record

echo "clean ok!"
