var vmodel = avalon.define({
	$id:'userdemo',
	tab_list:'web_tab',
	userlevels:["None", "General User", "Administrator"],
	userdata:[], //用户管理数据结构
});

// 定义用户信息类
function userinfo(username, pswd, level, logintime) {
	this.username  = username;
	this.pswd = pswd;
	this.userlevel = level;
	this.logintime = logintime;
}
//获取用户信息
var get_userinfo = {data:{objectid:"superuser",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getUserInfo};

request.addRequest([get_userinfo]);
function getUserInfo(d,r){
	if((d.result ==="ok")&&(d.datanum >0)){
		vmodel.userdata=d.data;
	}
}

var alter_userinfo = {data:{objectid:"superuser",type:"val_set",paranum:"0",paraval:JSON.stringify([{}])},success:altersuccess};

function alterwebuser() {
	var newpsw = $("#alterpsw").val();
	var newpsw2 = $("#alterpsw2").val();
	if(newpsw !=="" && newpsw2 !=="") {
			var username = vmodel.userdata[0].username;
			//约束密码在8位以上
			if (newpsw.length < 8) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
				$("#alterpsw").val('');
				$("#alterpsw2").val('');
				$("#pw_level2").css("display", "none");
				return;
			}
			//约束密码在32位以内
			if (newpsw.length > 32) {
				alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
				$("#alterpsw").val('');
				$("#alterpsw2").val('');
				$("#pw_level2").css("display", "none");
				return;
			}
			if(newpsw == newpsw2) {
				let para = {"username":username, "userlevel":"3", "user_type":"0", "newpswd":newpsw};
				let strong_pswd_enable_req = {data:{objectid:"strong_pswd_enable",type:"val_get",paranum:"0",paraval:JSON.stringify([para])},success:get_alert_strong_pswd_enable_success};
				request.addRequest([strong_pswd_enable_req]);
			}else{
				$("#alterpsw").val("");
				$("#alterpsw2").val("");
				$("#pw_level2").css("display", "none");
				popupTipsDiv($("#pswnotmatchalert"), 1000);
				return;
			}
	} else {
		$("#alterpsw").val("");
		$("#alterpsw2").val("");
		$("#pw_level2").css("display", "none");
		popupTipsDiv($("#insertrighttest"), 1000);
	}
}

function get_alert_strong_pswd_enable_success(d, r){
	if((d.result !="ok")){
		popupTipsDiv($("#addfailedalert"), 1000);
		return;
	} 
	let paraval = JSON.parse(r.data.paraval)[0];
	if(d.data[0].strong_pswd_enable == 1 && !is_super_strong_pswd(paraval.username, paraval.newpswd)){//强密码校验开关开启 且 强密码校验失败
		return false;
	}
	var para = {"username":paraval.username, "userlevel":paraval.userlevel, "user_type":paraval.user_type, "newpswd":paraval.newpswd};
	alter_userinfo.data.paraval= JSON.stringify([para]);
	request.addRequest([alter_userinfo]);
}

function is_super_strong_pswd(username, newpsw){
	// 检查密码强度是否符合要求
	if (alter_strong_pswd < 3) {   // 必须为强密码
		alert(mainvalue.i18nkeyword.north_protocol.pswd_tip);
		$("#alterpsw").val('');
		$("#alterpsw2").val('');
		$("#pw_level2").css("display", "none");
		return false;
	}
	
	var username_reverse = username.split('').reverse().join('');
	if(newpsw.includes(username) || newpsw.includes(username_reverse)) {
		popupTipsDiv($("#sameuserandpwd"), 2000);
		$("#alterpsw").val('');
		$("#alterpsw2").val('');
		$("#pw_level2").css("display", "none");
		return false;
	}

	return true;
}

function altersuccess(d, r) {
	$("#alterpsw").val("");
	$("#alterpsw2").val("");
	$("#pw_level2").css("display", "none");
	if(d.result != "ok") {
		popupTipsDiv($("#alterfailedalert"), 1000);
	} else {
		popupTipsDiv($("#altersuccessalert"), 3000);
	}
}
