var vmodel = avalon.define({
    $id:'password_reset',
    identify_code:"",
    time_valid:"",
    SN_code:"",
    user_name:"",
    pswd_new:"",
    pswd_new_2:"",
});

var alter_level_show = false;
//获取序列号及验证码
var get_identify_code = {data:{objectid:"password_reset",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])}, success:getIdentifyCode};
request.addRequest([get_identify_code]);

function getIdentifyCode(d, r){
    if((d.result ==="ok")&&(d.datanum >0)){
        vmodel.identify_code = d.data;
    }
}
//重新获取验证码
function codeReget(){
    var identify_code_reget = {data:{objectid:"password_reset",type:"val_set",paranum:"0",paraval:JSON.stringify([{}])},success:getIdentifyCode};
    request.addRequest([identify_code_reget]);
}
//获取剩余有效时间
var get_time_valid = {data:{objectid:"password_reset",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])}, refresh:0.5, success:getTimeValid};
request.addRequest([get_time_valid]);

function getTimeValid(d, r){
    if((d.result ==="ok")&&(d.datanum >0)){
        vmodel.time_valid = d.data;
    }
    if(vmodel.time_valid[0].time_valid == "00:00:00"){
        request.addRequest([get_identify_code]);
    }
}

//SN码校验
function SN_check(){
    var para = {username:"temp_user", pswd:vmodel.SN_code};
    var sn_check = {data:{objectid:"password_reset",type:"list",paraval:JSON.stringify([para])}, success:snCheck};
    request.addRequest([sn_check]);
}

function snCheck(d, r){
    if(d.result === "ok" && d.data[0].userlevel > 0){
        var obj = document.getElementById('pswd_reset');
        obj.style.display = 'none';
        var obj_2 = document.getElementById('pswd_alter');
        obj_2.style.display = 'block';
    }
    else{
        vmodel.SN_code = "";
        alert(mainvalue.i18nkeyword.pswdReset.check_failed);
    }
}

function pswd_alter(){
    let pswd_new = vmodel.pswd_new;
    let pswd_new_2 = vmodel.pswd_new_2;
    let user_name = vmodel.user_name;
    if(user_name =="" || pswd_new =="" && pswd_new_2 ==""){
        user_name = '';
        pswd_new = '';
        pswd_new_2 = '';
        return;
    }
    //约束密码在8位以上
    if (pswd_new.length < 8) {
        alert(mainvalue.i18nkeyword.north_protocol.pswd_tooshort_tip);
        return;
    }
    //约束密码在32位以内
    if (pswd_new.length > 32) {
        alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
        return;
    }
    if(pswd_new == pswd_new_2) {
        let para = {"username":user_name, newpswd:pswd_new};
        let strong_pswd_ebable_req = {data:{objectid:"strong_pswd_enable",type:"val_get",paranum:"0",paraval:JSON.stringify([para])},success:get_alter_strong_pswd_enable_success};
        request.addRequest([strong_pswd_ebable_req]);
    } else {
        alert(mainvalue.i18nkeyword.pswdReset.different_pswd);
        user_name = '';
        pswd_new = '';
        pswd_new_2 = '';
    }
    document.getElementById('pw_level2').style.display = 'none';
    return;
}

function get_alter_strong_pswd_enable_success(d, r){
    if((d.result !="ok")){
        alert(mainvalue.i18nkeyword.pswdReset.sn_input);
		return;
	}
    let paraval = JSON.parse(r.data.paraval)[0];
	if(d.data[0].strong_pswd_enable == 1 && !is_strong_pswd_alter(paraval.username, paraval.newpswd)){//强密码校验开关开启 且 强密码校验失败
		return false;
	}
//    var para = {username:paraval.username, newpswd:paraval.newpswd};
//    var pswd_alter = {data:{objectid:"password_reset",type:"inst_add",paraval:JSON.stringify([para])}, success:pswdAlter};
//    request.addRequest([pswd_alter]);
    
    var para = {username:"temp_user", pswd:vmodel.SN_code,newusername:vmodel.user_name, newpswd:vmodel.pswd_new, is_login:"0"};
    var pswd_alter = {data:{objectid:"password_reset",type:"list",paraval:JSON.stringify([para])}, success:pswdAlter};
    request.addRequest([pswd_alter]);
}

function is_strong_pswd_alter(username, newpsw){
    if (alter_strong_pswd < 3) {   // 必须为强密码
        alert(mainvalue.i18nkeyword.pswdReset.weak_pswd);
        vmodel.user_name = '';
        vmodel.pswd_new = '';
        vmodel.pswd_new_2 = '';
        alter_level_show = false;
        document.getElementById('pw_level2').style.display = 'none';
        return false;
    }
    var username_reverse = username.split('').reverse().join('');
	if(newpsw.includes(username) || newpsw.includes(username_reverse)) {
		popupTipsDiv($("#sameuserandpwd"), 2000);
		return false;
	}
    return true;
}

function pswdAlter(d,r){
    if(d.result == "ok"){
        // alert(mainvalue.i18nkeyword.pswdReset.alter_success);
        // gotopage("login.html");
        if (d.data[0] != undefined && (d.data[0].sn_valid == '0')) {
            vmodel.SN_code = "";
            alert(mainvalue.i18nkeyword.pswdReset.check_failed);
        } else {
            alert(mainvalue.i18nkeyword.pswdReset.alter_success);
            gotopage("login.html");
        }
    } else {
        alert(mainvalue.i18nkeyword.pswdReset.pswd_reset_fail);
        vmodel.user_name = '';
        vmodel.pswd_new = '';
        vmodel.pswd_new_2 = '';
    }
}

function sn_click_enter(){
    if(event.keyCode == 13){
        SN_check();
    }
}

function alert_enter(){
    if(event.keyCode == 13){
        pswd_alter();
    }
}