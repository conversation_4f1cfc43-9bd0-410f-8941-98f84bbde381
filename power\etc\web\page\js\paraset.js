//   当前选择的业务对象
var selectBoId = Cookies.get("pagelist");
//BusinessObject.GetParasetObject();  //  加载参数设置数据
let paraData = {};//暂存参数数据
let blockTemp = {};//暂存参数中间数据
let recordExpandModules = [];//记录展开的模块
var  paraset_vmodel = avalon.define({
	$id:'paraset_info',
	init_finished:0,
	tab_list:'parameter',
	hisdata_paras:[],			//存储历史数据相关参数
	alarm_paras:[],				//存储告警量
	parameters:[],              //存储参数量
	para_head_dic:{},           //存储属性的属性
	dev_changesiddata : [],     // 存储变化的数据
	dev_set_data:[],     //  存储设置的参数
	dev_setfailure_data:[],     //  存储设置失败的数据
	para_block_dic:{}, // 存储参数块数据
	alarm_block_dic:{}, // 存储告警块数据
	doconvention:{},	//告警可用的DO通道
	get_sid_type:function(sid) {
		return calSidToDevType(BigInt(sid)).sigType;
	},
	checkSidIndex:function(sid){
		/*
		var index = calSidToDevType(sid).devSigIndex;   //   屏蔽过滤
		if (index > 1) {
			return 0;
		}*/
		return 1;
	},
	getSidParentName:function(name, sid){
		if (judge_specail_sid_show(sid)) {
			return name;   //   输入干接点告警不进行处理
		}
		return name.replace(/\[([^)]+)\]/,'');
	},
	getDevSidParentName:function(name){
		var index = name.indexOf("_");
		if (index < 0) {
			return name;
		}
		return name.substring(0,index);
	},
	get_paraset_value:function(siddata){
		if (siddata.value == "val_invalid") {
			return mainvalue.i18nkeyword.value_invalid;
		} else {
			if (typeof(siddata.convention)=='undefined' || mainvalue.getconventionlength(siddata.convention) <=0) {
				return siddata.value;
			} else {
				if (siddata.value in siddata.convention) {
					return siddata.convention[siddata.value];
				} else {
					return mainvalue.i18nkeyword.value_invalid;
				}
			}
		}
	},
	get_paraset_rang_value:function(siddata){
		if (siddata.str_tag != '-1') {
			return siddata.str_tag - 1  + " " + mainvalue.i18nkeyword.char ;   //   "-1" 是保留字符串结束符
		}else if (typeof(siddata.min_value)=='undefined' || typeof(siddata.max_value)=='undefined') {
			return "";
		}else {
			return siddata.min_value + siddata.unit + " ~~ " + siddata.max_value + siddata.unit;
		}
	},
	changeDevData: function(sidvalue,changekey){
		var changedata = [];
		var changeobject = {};
		changeobject.sid = sidvalue;
		changeobject[changekey] = event.target.value;
		// 参数数据增加action属性，用于二次确认场景中区分是否需要取消释放文件锁
		if (changekey == 'value') {
			changeobject.action = "0";
		}
		changedata.push(changeobject);
		addvalue(this.dev_changesiddata,changedata,"sid");
	},
	jugge_setdata_failure:function(sid, attr_name){
		for (var i in paraset_vmodel.dev_setfailure_data) {
			if (sid == paraset_vmodel.dev_setfailure_data[i].sid) {
				if (typeof(paraset_vmodel.dev_setfailure_data[i][attr_name]) == "undefined") {
					return false;
				}
				if (paraset_vmodel.dev_setfailure_data[i][attr_name] == "failure") {
					return true;
				}
			}
		}
		return false;
	},
	show_title:function(siddata){
		if (siddata.str_tag != '-1') {
			return mainvalue.i18nkeyword.Disallow_input + " " + " ' ";
		}else {
			return siddata.constraint;
		}
	},
	show_disallow_input:function(siddata){
		if (siddata.str_tag != '-1') {
			return mainvalue.i18nkeyword.Disallow_input + " " + " ' ";
		}else {
			return "";
		}
	},
	tabChange : function(tab) {
		paraset_vmodel.tab_list = tab;
		set_cookie_with_path("tab_list", paraset_vmodel.tab_list);
    },
	showTableContent: function(ev, index){
		addParaDataToPage(index);
		paraset_vmodel.para_block_dic = blockTemp;
		changeSlide(ev.currentTarget);
	}
});

function addParaDataToPage(block_index){
	blockTemp[block_index] = [];
	for(let item of paraData){
		if(item["block_index"] === block_index){
			blockTemp[block_index].push(item);
		}
	}
	// 更新模块条目
	const keys = Object.keys(blockTemp)
	const updatedBlockTemp = {};
	for(let key of keys){
		if(blockTemp[key].length>0){
			updatedBlockTemp[key] = blockTemp[key];
		}
	}
	paraset_vmodel.para_block_dic = updatedBlockTemp;
}

// 展开或折叠
function changeSlide(obj){
	var id = $(obj).closest(".widget-box").attr("id");
	let widgetBody = document.getElementById(id).childNodes[1];
	let iLabel = document.getElementById(id).childNodes[0].childNodes[1].childNodes[0].childNodes[0];
	let lighter = document.getElementById(id).childNodes[0].childNodes[0];
	if(Array.from(widgetBody.classList).includes("user-dis")){//展开
		expandSlide(iLabel, lighter, widgetBody);
		recordExpandModules.push(id);
	}else{//折叠
		foldSlide(iLabel, lighter, widgetBody);
		if(recordExpandModules.indexOf(id) != -1){
			recordExpandModules.splice(recordExpandModules.indexOf(id),1); 
		}
	}
}

function expandSlide(iLabel, lighter, widgetBody){
	iLabel.setAttribute("class", "icon-chevron-up")
	lighter.classList.add("fontbold")
	widgetBody.classList.remove("user-dis")
}

function foldSlide(iLabel, lighter, widgetBody){
	iLabel.setAttribute("class", "icon-chevron-down");
	lighter.classList.remove("fontbold");
	widgetBody.classList.add("user-dis")
}

///////////////////////para_tag:1-告警,2-参数,3-存储属性////////////////////////////////////////////////////
function patch_para_change(paraval,para_tag){
	var local_para_tag = para_tag;
	var set_tag = true;
	var req_set = {data:{objectid:"signal",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
	var parameter_check = {data:{objectid:"parameter_v2",type:"attr_set",paraval:JSON.stringify(paraval)},success:dev_check_succ};

	if (local_para_tag*1 == 2) {   //  参数量
		showDataInit();
        request.addRequest([parameter_check]);  //先进行参数校验，判断是否需要用户二次确认
    } else {
        request.addRequest([req_set]);
    }

	paraset_vmodel.dev_changesiddata.clear();

	function dev_check_succ(d,r){
		hideDataInit();
		setTimeout(function(){
			paraset_vmodel.parameters.clear();
			paraset_value_refresh();
		}, 4000);     //  4秒以后再进行系统刷新
		if (d.result == "ok") {
			// 参数校验不通过，直接显示错误信息
			if (typeof(d.data[0]['error_para']) != "undefined") {
				alert( mainvalue.i18nkeyword.config_error + "\n" + d.data[0]['error_para']);
				return;
			}
			if (d.data[0]['change_para'].length > 0) {  //有场景配置参数，需要二次确认，弹出确认框
				var change_para = d.data[0]['change_para'].replace(/\n/g, '<br>');
				$.confirm({
					title: mainvalue.i18nkeyword.para_confirm_title,
					content: mainvalue.i18nkeyword.para_confirm_content + "<br>" + change_para ,
					boxWidth: '30%',
    				useBootstrap: false,
					autoClose: 'close|60000',
					buttons: {
						confirm: {
							text: mainvalue.i18nkeyword.seco_cert.confirm,
							btnClass: 'btn-info',
							action: function () {
								showDataInit();
								var paraval = JSON.parse(r.data.paraval);
								var parameter_set = {data:{objectid:"parameter_v2",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
								request.addRequest([parameter_set]);
							}
						},
						close: {
							text: mainvalue.i18nkeyword.seco_cert.close,
							action: function () {
								showDataInit();
								var paraval = JSON.parse(r.data.paraval);
								paraval[0].action = "1";  //用于二次场景中取消释放文件锁
								var parameter_set = {data:{objectid:"parameter_v2",type:"val_set",paraval:JSON.stringify(paraval)},success:dev_set_succ};
								request.addRequest([parameter_set]);
							}
						}
					}
				});
			}
			else {    //无场景配置参数，且校验通过，直接返回操作成功
				set_tag = true;
				alert(mainvalue.i18nkeyword.operate_successfully);
			}
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
			set_tag = false;
			var paraval = JSON.parse(r.data.paraval);
			var update_req =  {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},success:update_signal_para};
			request.addRequest([update_req]);
		}
	}

	function dev_set_succ(d,r){
		hideDataInit();
		setTimeout(function(){
			paraset_vmodel.parameters.clear();
			paraset_value_refresh();
		}, 4000);     //  4秒以后再进行系统刷新
		if (d.result == "ok") {
			set_tag = true;
			alert(mainvalue.i18nkeyword.operate_successfully);
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
			set_tag = false;
			var paraval = JSON.parse(r.data.paraval);
			var update_req =  {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},success:update_signal_para};
			request.addRequest([update_req]);
		}
	}
	function update_signal_para(d,r) {
		switch(local_para_tag*1){
			case 1:
				addvalue(paraset_vmodel.alarm_paras,d.data,"sid");
				break;
			case 2:
				addvalue(paraset_vmodel.parameters,d.data,"sid");
				break;
			case 3:
				addvalue(paraset_vmodel.hisdata_paras,d.data,"sid");
				break;
			default:
				break;
		}
		if (set_tag == false) {  //  没有设置成功
			get_failure_data(d.data);
		}
	}
	function get_failure_data(back_data) {
		var datas = [];
		for (var i in paraset_vmodel.dev_set_data) {
			if (paraset_vmodel.dev_set_data[i].sid == back_data[i].sid) {
				var para = {};
				para['sid'] = paraset_vmodel.dev_set_data[i].sid;
				for (var key in paraset_vmodel.dev_set_data[i]) {
					if (checkNumber(paraset_vmodel.dev_set_data[i][key])) {
						if (Math.abs(paraset_vmodel.dev_set_data[i][key] - back_data[i][key]) > 1e-6) {
							para[key] = "failure";
						}
					} else if (paraset_vmodel.dev_set_data[i][key] != back_data[i][key]) {  // 如果不相同则说明设置失败
						para[key] = "failure";
					}
				}
				datas.push(para);
			}
		}
		paraset_vmodel.dev_setfailure_data = datas;
	}
}


function setDevData(para_tag,attrtype){
	var setData = [];
	var num = [];
	paraset_vmodel.dev_setfailure_data.clear();
	for(var i in paraset_vmodel.dev_changesiddata){
		var sidData = calSidToDevType(BigInt(paraset_vmodel.dev_changesiddata[i].sid));
		setData.push(paraset_vmodel.dev_changesiddata[i]);
		num.push(i);
	}
	if(num.length > 0){
		for(var j in num){
			if((j===0)||(num[j]===0)){
				paraset_vmodel.dev_changesiddata.splice(num[j],1);
			}
			else{
				paraset_vmodel.dev_changesiddata.splice(num[j]-1,1);
			}
		}
	} else{
		return;
	}
	paraset_vmodel.dev_set_data = setData;
	if(attrtype === 'attr'){
		patch_para_change(setData,para_tag);
	} else if(attrtype === 'value'){
		patch_para_change(setData,para_tag);
	}
}

function paraset_head_attr_get(obj) {
	let boid = selectBoId.toString();
	if(selectBoId.toString().includes("-")){
		boid = boid.split("-")[1];
	}
	var paraval = [{"bo_id":boid}];
	// var paraval = [{"bo_id":selectBoId.toString()}];
	var req_head = {data:{objectid:"paraset",type:"attr_get",paraval:JSON.stringify(paraval)},success:para_head_succ};
	request.addRequest([req_head]);

	function para_head_succ(d,r){
		for (var i in d.data) {
			var sigtype = calSidToDevType(BigInt(d.data[i].sid));
			if (sigtype.sigType == 3) {
				var convention = transformToJson(d.data[i].alm_level);
				d.data[i].alm_level = convention;
			}
		}
		paraset_vmodel.para_head_dic = transformDataToDic(d.data, "sid");
	}
}

function paraset_value_refresh(obj) {
	// var paraval = [{"bo_id":selectBoId.toString()}];
	let boid = selectBoId.toString();
	if(selectBoId.toString().includes("-")){
		boid = boid.split("-")[1];
	}
	var paraval = [{"bo_id":boid}];
	var req_para = {data:{objectid:"parameter_block",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},success:paraset_value_refresh_succ};
	var req_alarm = {data:{objectid:"parameter_alarm",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},success:alarmset_value_refresh_succ};
	request.clearRequest(req_para);
	request.clearRequest(req_alarm);
	request.addRequest([req_para]);
	request.addRequest([req_alarm]);

	function paraset_value_refresh_succ(d,r) {
		if (d.result == 'ok') {
			var parameter_array = new Array();
			for (var i in d.data) {// 带有取值约定时需要额外处理
				var convention = transformToJson(d.data[i].convention);
				d.data[i].convention = convention;
				if (d.data[i].sid == "2251869878026241" && d.data[i].value == "1") {   // 整流系统防盗功能配置，当配置为“有”时，禁止可配置为“无”
					delete d.data[i].convention["0"];
				}
				parameter_array.push(d.data[i]);
			}
			paraset_vmodel.parameters = parameter_array;
			classsify_paraset(paraset_vmodel.parameters);
		}
	}

	function alarmset_value_refresh_succ(d,r) {
		if (d.result == 'ok') {
			var alarmpara_array = new Array();
			for (var i in d.data) {
				var convention = transformToJson(d.data[i].alm_level_list);
				d.data[i].alm_level_list = convention;
				alarmpara_array.push(d.data[i]);
			}
			paraset_vmodel.para_head_dic = transformDataToDic(d.data, "sid");
			paraset_vmodel.alarm_paras = alarmpara_array;
			classsify_alarmset(paraset_vmodel.alarm_paras);
		}
	}

	function classsify_paraset(paras) {
		paraData = paras;//更新缓存数据
		blockTemp = {};
		for(let item of paras){
			if(blockTemp[item.block_index] === undefined){
				blockTemp[item.block_index] = [];
				blockTemp[item.block_index].push({'block_name':item.block_name});
			}
		}

		// 更新记录已展开模块名数组recordExpandModules
		let updatedRecordExpandModules = updateRecord(paras);
		updateSlideStatus(recordExpandModules, updatedRecordExpandModules);
		recordExpandModules = updatedRecordExpandModules

		// 更新展开的模块数据
		for(let index of recordExpandModules){
			blockTemp[index] = [];
			for(let item of paras){
				if(item.block_index === index){
					blockTemp[item.block_index].push(item);
				}
			}
		}
		// 更新模块条目
		const keys = Object.keys(blockTemp)
		const updatedBlockTemp = {};
		for(let key of keys){
			if(blockTemp[key].length>0){
				updatedBlockTemp[key] = blockTemp[key];
			}
		}
		paraset_vmodel.para_block_dic = updatedBlockTemp;
	}

	function updateRecord(paras){
		let arrOfRefreshed = paras.map((item, index)=>item.block_index);
		const blockIndexsOfRefreshed = arrOfRefreshed.filter((item, index)=>arrOfRefreshed.indexOf(item)===index);
		let updatedRecordExpandModules = []
		for(let index of recordExpandModules){
			if(blockIndexsOfRefreshed.includes(index)){
				updatedRecordExpandModules.push(index)
			}
		}
		return updatedRecordExpandModules;
	}

	//折叠当前已经消失的模块(之前存在过)
	function updateSlideStatus(recordArr, updateRecordArr){
		let diff = recordArr.filter(v => !updateRecordArr.includes(v));
		for(let _id of diff){
			let widgetBody = document.getElementById(_id).childNodes[1];
			let iLabel = document.getElementById(_id).childNodes[0].childNodes[1].childNodes[0].childNodes[0];
			let lighter = document.getElementById(_id).childNodes[0].childNodes[0];
			foldSlide(iLabel, lighter, widgetBody);
		}
	}

	function classsify_alarmset(paras) {
		var block_index = paras[0]["device name"];
		var blocks = {};
		var datas = new Array();
		for (let i in paras) {
			if(block_index == paras[i]["device name"]) {
				datas.push(paras[i]);
			} else {
				blocks[block_index] = datas;
				block_index = paras[i]["device name"];
				datas = new Array();
				datas.push(paras[i]);
			}
		}
		blocks[block_index] = datas;
		paraset_vmodel.alarm_block_dic = {};
		paraset_vmodel.alarm_block_dic = blocks;
	}
}

paraset_value_refresh();
//paraset_head_attr_get();


//  获取DO信息
function get_alarm_do_list(){
	var req_set = {data:{objectid:"para_alarm_do",type:"val_get",paraval:JSON.stringify([{}])},success:get_alarm_do_succ};
	request.addRequest([req_set]);
}

function get_alarm_do_succ(d,r){
	if (d.result == 'ok') {
		var doctrlmode = d.data;
		paraset_vmodel.doconvention=transformArrayToObject(doctrlmode,"Channel NO.","Channel Name");
	}
}

get_alarm_do_list();
//listDoInstid();

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "parameter" || tab == "alarm_para") {
		paraset_vmodel.tab_list = tab;
	} else {
		paraset_vmodel.tab_list = "parameter";
		set_cookie_with_path("tab_list", paraset_vmodel.tab_list);
	}
}
init_tab_select();