#!/bin/sh
# file name : clean_all_data.sh 
# usage : for default update to clean old data, before execute this script you should save some info to power/etc/xxx

# Do not rename the shell file name!!!
# Do not rename the shell file name!!!
# Do not rename the shell file name!!!

echo "clean para config"


# delete para, keep dict_para.sqlite for inherit
cd  /mnt/data/para_and_config/para
rm -rf *


# delete config
cd  /mnt/data/para_and_config/config
rm -rf *

# delete backup para
cd  /mnt/data/para_and_config/back
rm -rf *

cd /mnt/data/work/
rm -rf *

cd /mnt/data/database
rm -rf *

rm -rf  /mnt/backup/para_and_config

echo "clean ok!"
