#######################################################################
##
##  Debug options
## ---------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/DebugVariables
##
## Enable those options for debugging the behavior
##
## The settings can be set per location/vhost.
## 

## 
## log-request-handling allows you to track the request
## handing inside lighttpd. 
##
#debug.log-request-handling        = "enable"

## 
## log all request headers. 
##
#debug.log-request-header          = "enable"

## 
## similar to log-request-header.
## but only logs if we encountered an error.
## (return codes 400 and 5xx)
##
#debug.log-request-header-on-error = "enable"

## 
## log the header we send out to the client.
##
#debug.log-response-header         = "enable"

## 
## log if a file wasnt found in the error log.
##
#debug.log-file-not-found          = "enable"

## 
## debug conditionals handling
##
#debug.log-condition-handling      = "enable"

#
#######################################################################


