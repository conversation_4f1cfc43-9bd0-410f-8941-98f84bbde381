#!/bin/sh
BAKUP_PATH="/mnt/backup"
SYS_PARA_BAKUP=$BAKUP_PATH/para_and_config #backup running data fir
SYS_PARA="/mnt/data" #running data dir 's parent dir
BAKUP_AUTH="/mnt/backup/auth"

# 拷贝备份区para_and_config操作需要放到创建/mnt/data/para_and_config之前
if [ ! -d /mnt/data/para_and_config ] ;then
    cp -rf $SYS_PARA_BAKUP $SYS_PARA
    echo "boot.sh restore bakup para_and_config"
fi

#create the main directory of non-root area
chmod 755 /root/power/etc/sys/create_dir.sh
/root/power/etc/sys/create_dir.sh

# set user default umask to 027
KEY_WORD="umask"
FILE_PATH="/etc/profile"
str="umask 027"
line=`sed -n '/'"$KEY_WORD"'/=' $FILE_PATH`
if [ "$line" == "" ]; then
    echo "$KEY_WORD is not found, set it!"
    echo "$str" >> $FILE_PATH
else
    echo "$KEY_WORD is found in $line"
    sed -i "${line}c ${str}" $FILE_PATH
fi

chmod 755 /root/power/etc/sys/osenhance.sh
/root/power/etc/sys/osenhance.sh

cp /root/power/etc/sys/sysctl.conf /etc/
/sbin/sysctl -p

# Started by AICoder, pid:974113cdb5vf9021425e08b2104c202471f2182d
# 定义一个函数，用于处理备份和恢复文件的逻辑
handle_file() {
    local file=$1
    if [ -f "$BAKUP_PATH/$file" ]; then
        if [ -s "$BAKUP_PATH/$file" ]; then
            mv "$BAKUP_PATH/$file" "/etc/"
        else
            rm "$BAKUP_PATH/$file"
        fi
    fi
    if [ -s "/etc/$file" ]; then
        cp "/etc/$file" "$BAKUP_AUTH"
    else
        cp "$BAKUP_AUTH/$file" "/etc/"
    fi
}

# 调用函数处理每个文件
handle_file group
handle_file gshadow
handle_file passwd
handle_file shadow
# Ended by AICoder, pid:974113cdb5vf9021425e08b2104c202471f2182d

rm -f /tmp/cgic*

# recover Certificate, after create_dir.sh
RADIUS_BAKUP_CRET="/mnt/backup/radius" #backup dir
RADIUS_SSL_CRET="/mnt/data/work/radius/ssl"
WEB_BAKUP_CRET="/mnt/backup/web" #backup dir
WEB_SSL_CRET="/mnt/data/work/web/ssl"
if [ -d $RADIUS_BAKUP_CRET ] ; then
    echo "this restart because of all update, recover the radius Certificate!"
    rm -rf $RADIUS_SSL_CRET
    mv  $RADIUS_BAKUP_CRET  $RADIUS_SSL_CRET
fi
if [ -d $WEB_BAKUP_CRET ] ; then
    echo "this restart because of all update, recover the http Certificate!"
    rm -rf $WEB_SSL_CRET
    mv  $WEB_BAKUP_CRET  $WEB_SSL_CRET
fi

# recover comdmgr candmgr statsave statsavebak auth_management, after create_dir.sh
WORK_DIR="/mnt/data/work/"
STATSAVE_CRET="$WORK_DIR/productapp"
COMDMGR_DIR="$BAKUP_PATH/comdmgr"
CANDMGR_DIR="$BAKUP_PATH/candmgr"
AUTH_MANAGEMENT_DIR="$BAKUP_PATH/auth_management"
if [ -f $BAKUP_PATH/statsave.bin ] ; then
    echo "this restart because of all update, recover the statsave.bin statsavebak.bin!"
    mv $BAKUP_PATH/statsave*.bin $STATSAVE_CRET
fi

if [ -f $BAKUP_PATH/smrcodeauth.bin ] ; then
    echo "this restart because of all update, recover the smrcodeauth.bin smrcodeauthbak.bin!"
    mv $BAKUP_PATH/smrcodeauth.bin $BAKUP_PATH/smrcodeauthbak.bin $STATSAVE_CRET
fi

if [ -d $COMDMGR_DIR ] ; then
    echo "this restart because of all update, recover the comdmgr!"
    mv $COMDMGR_DIR $WORK_DIR
fi

if [ -d $CANDMGR_DIR ] ; then
    echo "this restart because of all update, recover the candmgr!"
    mv $CANDMGR_DIR $WORK_DIR
fi

if [ -d $AUTH_MANAGEMENT_DIR ] ; then
    echo "this restart because of all update, recover the auth_management!"
    cp -rf $AUTH_MANAGEMENT_DIR $WORK_DIR
    rm -rf $AUTH_MANAGEMENT_DIR
fi

# update ssl, after create_dir.sh
SSL_CERT="/mnt/data/work/web/ssl"
SSL_CERT_PATH="/mnt/data/work/web"
SSL_CERT_SOURCE="/root/power/etc/lighttpd/ssl"
if [ ! -d $SSL_CERT ] ; then
    echo "syn ssl certificate ..."
    cp -rf  $SSL_CERT_SOURCE  $SSL_CERT_PATH
fi
if [ ! -f /mnt/data/work/web/ssl/dh2048.pem ] ; then
    echo "recover ssl certificate ..."
    rm -rf $SSL_CERT
    cp -rf  $SSL_CERT_SOURCE  $SSL_CERT_PATH
fi

#remove pmsa_ssh
if [ -f /root/power/data/pmsa_ssh ] ; then
    rm /root/power/data/pmsa_ssh
fi

#pkill syslogd
chmod 755 /root/power/bin/*

# 执行install.sh，区分芯片类型
TI_VERSION_PATH=/var/isAM335X
RXW_VERSION_PATH=/var/isRV1126
TIME=`date`
if [ -f $TI_VERSION_PATH ]; then
    chmod 755 /root/power/bsp/install.sh
    /root/power/bsp/install.sh
	echo "$TIME check core is TI, install ko success!" >> /tmp/log/message
elif [ -f $RXW_VERSION_PATH ]; then
    chmod 755 /bsp/RXW/RV1126/install.sh
    /bsp/RXW/RV1126/install.sh
	echo "$TIME check core is RXW, install ko success!" >> /tmp/log/message
else
	echo "$TIME check core is unknow, don't install ko!" >> /tmp/log/message
fi

# 软连接，radius使用
echo "/power/lib/ soft connect, used in radius!"
if [ ! -L /lib/libcaapi.so ]; then
    ln -s /root/power/lib/libcaapi.so /lib/libcaapi.so
    ln -s /root/power/lib/libcmapi.so /lib/libcmapi.so
    ln -s /root/power/lib/libdaapi.so /lib/libdaapi.so
    ln -s /root/power/lib/libdmapi.so /lib/libdmapi.so
    ln -s /root/power/lib/libhal.so /lib/libhal.so
    ln -s /root/power/lib/liboss.so /lib/liboss.so
    ln -s /root/power/lib/libsps.so /lib/libsps.so
    ln -s /root/power/lib/libutils.so /lib/libutils.so
    ln -s /root/power/lib/libweb.so /lib/libweb.so

    ln -s /root/power/lib/libCMServiceService.so /usr/axis2c/services/FSUService/libCMServiceService.so
    ln -s /usr/axis2c/lib/libaxis2_axiom.so /lib/libaxis2_axiom.so.0
    ln -s /usr/axis2c/lib/libaxis2_engine.so /lib/libaxis2_engine.so.0
    ln -s /usr/axis2c/lib/libaxis2_http_common.so /lib/libaxis2_http_common.so.0
    ln -s /usr/axis2c/lib/libaxis2_http_receiver.so /lib/libaxis2_http_receiver.so.0
    ln -s /usr/axis2c/lib/libaxis2_http_sender.so /lib/libaxis2_http_sender.so.0
    ln -s /usr/axis2c/lib/libaxis2_parser.so /lib/libaxis2_parser.so.0
    ln -s /usr/axis2c/lib/libaxis2_xpath.so /lib/libaxis2_xpath.so.0
    ln -s /usr/axis2c/lib/libaxutil.so /lib/libaxutil.so.0
    ln -s /usr/axis2c/lib/libguththila.so /lib/libguththila.so.0
    ln -s /usr/axis2c/lib/libneethi.so /lib/libneethi.so.0

    ln -s /usr/axis2c/modules/addressing/libaxis2_mod_addr.so /lib/libaxis2_mod_addr.so.0
    ln -s /usr/axis2c/modules/logging/libaxis2_mod_log.so /lib/libaxis2_mod_log.so.0

    ln -s /root/power/lib/libfapi.so /lib/libfapi.so
    ln -s /root/power/lib/libhardware.so /lib/libhardware.so
    ln -s /root/power/lib/libresourceModule.so /lib/libresourceModule.so
    ln -s /root/power/lib/libzte_slibc.so /lib/libzte_slibc.so
fi

##### start dbus-daemon #####
if test -z "$DBUS_SESSION_BUS_ADDRESS" ; then
## if not found, launch a new one
    eval `dbus-launch --sh-syntax`
    echo "D-Bus per-session daemon address is: $DBUS_SESSION_BUS_ADDRESS"
fi

##### update $DBUS_SESSION_BUS_ADDRESS in the profile for ssh user #####
KEY_WORD="DBUS_SESSION_BUS_ADDRESS"
FILE_PATH="/etc/profile"
str="export DBUS_SESSION_BUS_ADDRESS=$DBUS_SESSION_BUS_ADDRESS"
line=`sed -n '/'"$KEY_WORD"'/=' $FILE_PATH`
if [ "$line" == "" ]; then
    echo "$KEY_WORD is not found, set it!"
    echo "$str" >> $FILE_PATH
else
    echo "$KEY_WORD is found in $line"
    sed -i "${line}c ${str}" $FILE_PATH
fi

/etc/init.d/rcS.network&

#if nfs fs,must delete
if [ -f $TI_VERSION_PATH ]; then
    mount -t usbfs usbfs /proc/bus/usb/
else
    mount -t usbfs usbfs /dev/bus/usb/
fi

mkdir -p /root/power/etc/web/download
ln -s /mnt/data/work/web/download/web_export /root/power/etc/web/download/web_export

# start watchdog
if [ -f $TI_VERSION_PATH ]; then
   /sbin/watchdog -t 5 -T 120 /dev/watchdog
else
   echo 'A' > /dev/watchdog
   /sbin/watchdog -t 1 -T 20 /dev/watchdog
fi


#add by wx
#python /root/power/bin/newconf2bin.pyc
# python /root/power/bin/newconf2bin.pyc
mv -f /root/power/bin/sysmonitor /root/power/bin/SysMonitor_run
chmod 755 /root/power/bin/SysMonitor_run
/root/power/bin/SysMonitor_run&
#/root/power/bin/lighttpd -f /root/power/etc/lighttpd/config/lighttpd.conf -m /usr/lib/lighttpd


#for open sshd
chmod 755 /root/power/etc/sys/open_ssh.sh

chmod 750 -R /var/log
chmod 640 -R /tmp/log
chmod 640 /mnt/data/work/web/ssl/server/*.key
chmod 640 /mnt/data/work/web/ssl/client/*.key
chmod 640 /mnt/data/database/*.sqlite
chmod 640 /root/power/etc/lighttpd/ssl/server/*.key
chmod 640 /root/power/etc/lighttpd/ssl/client/*.key
chmod 600 /root/power/etc/config/ssh_config.cfg
chmod 600 /root/power/data/aes.bin
chmod 640 /etc/passwd
chmod 640 /etc/shadow
chmod 644 /etc/group
chmod 640 /etc/gshadow
chmod 640 /etc/shadow-
chmod 644 /etc/passwd-
chmod 644 /etc/group-
chmod 640 /etc/gshadow-
chmod 600 /etc/ssh/sshd_config
chmod 600 /root/.ash_history
chmod 600 /home/<USER>/.ash_history

# 限制系统pid上限为500
echo 500 > /proc/sys/kernel/pid_max

# 用户密码散列值应该设设置为不可读
chmod 600 /mnt/data/work/auth_management/defaultuser.bin
chmod 600 /mnt/data/work/auth_management/guiuser.bin
chmod 600 /mnt/data/work/auth_management/webuser.bin

#修改root目录权限
chmod 750 /root

# 删除无效用户
sed -ie '/^user:/d' /etc/group
sed -ie '/^user:/d' /etc/gshadow
sed -ie '/^user:/d' /etc/passwd
sed -ie '/^user:/d' /etc/shadow

sed -ie '/^tenglq:/d' /etc/group
sed -ie '/^tenglq:/d' /etc/gshadow
sed -ie '/^tenglq:/d' /etc/passwd
sed -ie '/^tenglq:/d' /etc/shadow