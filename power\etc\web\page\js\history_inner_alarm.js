var vmodel = avalon.define({
	$id:'history',
	init_time:{},
	historyalarm_time:{"start_time":"","end_time":""},
	historyalarm_counts:"10"
});



/************历史信息起止时间默认30天**************/
function get_start_end_time(timestr) {
	var now_time_str = timestr;
	var date = new Date(now_time_str);
	var startTime,endTime;

	var endDate = date;
	endTime=timeToStr(endDate);
	date.setDate(date.getDate()-30);
	var startDate = date;
	startTime=timeToStr(startDate);
	return {"start_time":startTime,"end_time":endTime};
}

function showHisInfoDate(){
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_init_time_succ};
	request.addRequest([gettimeRq]);

	function get_init_time_succ(d,r) {
		if (d.result == "ok") {
			var now_time_str = d.data[0].time;
			vmodel.init_time = get_start_end_time(now_time_str);
			get_init_time();
		}
	}
}
showHisInfoDate();

function get_init_time() {
	//初始化时间选择空间的显示内容，终止时间为浏览器当前时间，起始时间为该时间的前30天
	var initTime = vmodel.init_time;
	vmodel.historyalarm_time.start_time = initTime.start_time;
	vmodel.historyalarm_time.end_time = initTime.end_time;
}

//校验日期时间是否合法或者日期时间范围是否有效
function checkDateTime(obj) {
	var reg = /^(\d+)-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
	var valid = $(obj).val() == "" || reg.test($(obj).val());

	if(valid && vmodel.historyalarm_time.end_time != "" && vmodel.historyalarm_time.start_time > vmodel.historyalarm_time.end_time) {
		valid = false;
	}

	if(!valid) {
		alert(mainvalue.i18nkeyword.system.date_not_correct);
		$(obj).val("");
	}
}

//历史告警相关操作
//查询历史告警
function doAlarmsearch(){
	showDataInit();
	vmodel.historyalarm_time.start_time = $('#alarm_starttext').val();
	vmodel.historyalarm_time.end_time = $('#alarm_endtext').val();
	var alarmSearch = [{"start_time":vmodel.historyalarm_time.start_time,"end_time":vmodel.historyalarm_time.end_time, "alarm_tag":"1"}];
	request.addRequest([{data:{objectid:"history_alarm",type:"list",paranum:"0",paraval:JSON.stringify(alarmSearch)},success:getHisAlarmList}]);
}

function getHisAlarmList(d,r){
	if(d.result!=="ok"||(d.datanum===0)){
		hideDataInit();
		d.data = [{"counts":"0"}];
	}
	mainvalue.paperattr.totalnum = d.data[0].counts;
	mainvalue.paperattr.currentpage = "1";
	mainvalue.paperattr.id = d.objectid;
	mainvalue.paperattr.listcom = JSON.parse(r.data.paraval)[0];
	mainvalue.paperattr.counts = vmodel.historyalarm_counts;
	var listdata = JSON.parse(r.data.paraval)[0];
	// var offset = d.data[0].counts - mainvalue.paperattr.counts +1;
	var offset = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	if(offset <= 0){
		listdata.offset = "1";
		listdata.counts =  d.data[0].counts;
	}
	else{
		listdata.offset =offset.toString();
		// listdata.counts = mainvalue.paperattr.counts;
		listdata.counts = (parseInt(d.data[0].counts) > parseInt(mainvalue.paperattr.counts))? mainvalue.paperattr.counts: d.data[0].counts;
	}
	mainvalue.paperattr.getcom = listdata;
	var value = {};
	$.extend( true, value, mainvalue.paperattr );	
	addvalue(mainvalue.pagerattrall,[value],"id");
	seachdata(mainvalue.paperattr.id);
}
