#!/bin/sh

rm -rf /dev/ttyUSB0 /dev/ttyUSB1 /dev/ttyUSB2 /dev/ttyUSB3  /dev/ttyUSB4
mknod /dev/ttyUSB0 c 188 0
mknod /dev/ttyUSB1 c 188 1
mknod /dev/ttyUSB2 c 188 2
mknod /dev/ttyUSB3 c 188 3
mknod /dev/ttyUSB4 c 188 4

rmmod option
if [ "$?" != "0" ]
then
echo ...error ignored
fi
rmmod usb_wwan
if [ "$?" != "0" ]
then
echo ...error ignored
fi
lsmod
/sbin/insmod /root/esmu/bsp/usb_wwan.ko
/sbin/insmod /root/esmu/bsp/option.ko

/root/esmu/bin/usb_modeswitch -W -c /root/esmu/etc/ppp/3ginstall/conf.txt

echo "19d2 0079" > /sys/bus/usb-serial/drivers/option1/new_id

/root/esmu/bin/pppd call tdscdmattyUSB0 &

