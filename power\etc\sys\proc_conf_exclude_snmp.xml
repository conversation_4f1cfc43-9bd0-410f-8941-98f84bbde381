<?xml version="1.0" encoding="utf-8"?>
<config>
	<process path="/root/power/bin" name="mainapp" timeout="300" wait_msg="yes" is_pulse="1" arg=""></process>
    <process path="/root/power/bin" name="aidido" timeout="120" wait_msg="yes" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="netmgr" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="smartboard" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="usbmgr" timeout="300" wait_msg="" is_pulse="1" arg=""></process>
	
    <!-- product below -->
	<process path="/root/power/bin" name="candmgr" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
    <process path="/root/power/bin" name="comdmgr" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
    <process path="/root/power/bin" name="productapp" timeout="120" wait_msg="yes" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="bcmuapp" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="bsmuapp" timeout="120" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="northpro" timeout="300" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="northdownloadip" timeout="300" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="ctower_client" timeout="300" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/bin" name="mqtt" timeout="120" wait_msg=""></process>
	<process path="/root/power/bin" name="gui" timeout="300" wait_msg="" is_pulse="1" arg=""></process>
	<process path="/root/power/etc/lighttpd/bin" name="lighttpd" timeout="90" wait_msg="" is_pulse="0" arg="-f /root/power/etc/lighttpd/config/lighttpd.conf -m /root/power/etc/lighttpd/lib"></process>
</config>
