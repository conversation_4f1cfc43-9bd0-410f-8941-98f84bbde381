<div id="pageLock" class="lockDiv" style="background-image:url(/page/assets/img/transparent.jpg) ;display:none"></div>
<div id="dataInit" class="loading" style="background-image:url(/page/assets/img/loading.gif); display:none; top:400px">
	<strong><span id="prompt_tip">{{i18nkeyword.data_loading}}<span></strong>
</div>
<div id="dataImport" class="loading" style="background-image:url(/page/assets/img/loading.gif); display:none; top:400px">
	<strong><span id="prompt_tip_v2">{{i18nkeyword.data_importing}}<span></strong>
</div>
<div class="modal fade" id="appModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header" style="padding: 0px 15px;">
				<button type="button" class="close" onClick="clearPswdSecauth()" aria-hidden="true">
					&times;
				</button>
				<h4 class="modal-title" id="myModalLabel">
					{{i18nkeyword.seco_cert.modal_title}}
				</h4>
			</div>
			<table class="modal-body my-table">
				<tr>
					<td style="width:100px;"><span id="secauth-user" style="float:right">{{i18nkeyword.seco_cert.username}}：</span></td>
					<td>{{seco_username}}</td>
				</tr>
				<tr>
					<td style="width:100px;"><span style="float:right">{{i18nkeyword.seco_cert.password}}：</span></td>
					<td><input type="password" id="modal-input" style="width: 85%;margin-left:0;"></td>
				</tr>
			</table>
			<div class="modal-footer" style="margin-bottom:4px">
				<button type="button" class="button button-small button-flat-primary" style="margin-right: 13px;" onClick="confirmSecauth()"> {{i18nkeyword.seco_cert.confirm}}</button>
				<button type="button" class="button button-small button-flat-primary" onClick="clearPswdSecauth()"> {{i18nkeyword.seco_cert.close}}</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal -->
</div>
<style>
	.my-table{
		width: 100%;
	}
</style>