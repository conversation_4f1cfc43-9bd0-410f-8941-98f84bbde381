<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/prompt.html" -->
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" >
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content" ms-controller="plc">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li>
                            <i class="icon-home"></i>
                            {{i18nkeyword.menu.plc_config}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                            <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="zip_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.ziptype_failure}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->
                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'plc_data' == tab_list?'active':''" ms-click="tabChange('plc_data')">
                                        <a data-toggle="tab" href="#plc_data">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.menu.plc_config}}
                                        </a>
                                    </li>
                                    <li ms-class="'plcparaMaintain' == tab_list?'active':''" ms-click="tabChange('plcparaMaintain')">
                                        <a data-toggle="tab" href="#plcparaMaintain">
                                            <i class="blue icon-off bigger-110">
                                            </i>
                                            {{i18nkeyword.plc.para_maintain}}
                                        </a>
                                    </li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane" id="plc_data" ms-class="'plc_data' == tab_list?'active':''">
                                        <div class="widget-box" style="margin-bottom:5px;" id="plccontrol" ms-if="userlevel > '2'">
                                            <div class="widget-header widget-header-flat widget-header-small">
                                                <h5 class="lighter">
                                                    <i class="icon-cog"></i>
                                                    {{i18nkeyword.plc.enable_state}}
                                                </h5>
                                                <label style="padding-top: 5px;">
                                                    <input name="switch-field-1"  id="plc_switch" class="ace ace-switch" type="checkbox" onclick="plc_input_click()"/>
                                                    <span class="lbl"></span>
                                                </label>
                                            </div>
                                        </div><!--PLC使能开关控制-->
                                        <!--ms-for:(i,p1) in decplc_block_dic-->
                                        <div ms-attr="{'id':'plc'+ i}" class="widget-box" style="margin-bottom:5px;">
                                            <div class="widget-header widget-header-flat widget-header-small" style="overflow-y:auto;" onClick="channelSlide(this)">
                                                <h5 class="lighter" style="word-break:break-all;">
                                                    <i class="icon-cog"></i>
                                                    {{p1[0]["Group_description"]}}
                                                </h5>
                                                <div class="widget-toolbar pull-right">
                                                    <a href="#">
                                                        <i class="icon-chevron-down"></i>
                                                    </a>
                                                </div>
                                                <span ms-visible="p1[0]['Group_status'] == '1'" class="badge pull-right bg-green">
                                                    {{i18nkeyword.plc.active}}
                                                </span>
                                                <span ms-visible="p1[0]['Group_status'] == '0'" class="badge pull-right bg-red">
                                                    {{i18nkeyword.plc.not_active}}
                                                </span>
                                            </div>
                                            <div class="widget-body" style="margin-bottom:5px" ms-css="{'display':userlevel > '2'?'none':'block'}">
                                                <div class="widget-body-inner" style="display: block;">
                                                    <div class="widget-main no-padding">
                                                        <table class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>{{i18nkeyword.plc.NO}}</th>
                                                                    <th>{{i18nkeyword.plc.input_one}}</th>
                                                                    <th>{{i18nkeyword.plc.operator}}</th>
                                                                    <th>{{i18nkeyword.plc.input_two}}</th>
                                                                    <th>{{i18nkeyword.plc.output}}</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr ms-for="(j,p2) in senplc_block_dic[i]">
                                                                    <td>{{j+1}}</td>
                                                                    <td>{{p2["Preposition_input_str"]}}</td>
                                                                    <td>{{p2["Middle_operator_str"]}}</td>
                                                                    <td>{{p2["Postposition_input_str"]}}</td>
                                                                    <td>{{p2["Output_str"]}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <button ms-attr="{'id':'editPLCBT'+ i}" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px; display:none" onClick="editGPLC(this)">
                                                {{i18nkeyword.plc.edit}}
                                            </button>
                                            <button ms-attr="{'id':'delPLCBT'+ i}" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; display:none" onClick="delGPLC(this)">
                                                {{i18nkeyword.plc.delete}}
                                            </button>
                                        </div><!--PLC语句配置-->
                                        <!--ms-for-end:-->
                                        <button ms-if="userlevel > '2'" id="addPLCBT" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px" onClick="addPLC()" >
                                            {{i18nkeyword.plc.add_plc}}
                                        </button>

                                        <div class="hr hr8 hr-double"></div>

                                        <div class="widget-box" id="editPLC" style="display:none">
                                            <div class="widget-header widget-header-flat widget-header-small" style="overflow-y:auto;">
                                                <h5 id="plctitle" class="lighter pull-left" style="margin-top:5px">
                                                    {{i18nkeyword.plc.plc_desc}}
                                                </h5>
                                                <input id="PLCTitle" ms-duplex="add_decplc_datas['Group_description']" class="form-control input-medium pull-left" autocomplete="off" ms-attr="{title:@show_init_plctitle() , placeholder:@show_init_plctitle()}" style="width:60%; margin-top:8px; margin-left:15px;">
                                                <select id="PLCEnable" ms-duplex="add_decplc_datas['Group_status']" class="editable input-small pull-left" style="margin-top:6px; margin-left:15px">
                                                    <option value="0">{{i18nkeyword.plc.disabled}}</option>
                                                    <option value="1">{{i18nkeyword.plc.enabled}}</option>
                                                </select>
                                                </span>
                                                <button id="cancelBT" class="button button-small button-flat-primary pull-right" style="margin-top:5px; margin-right:10px" onClick="cancelPLC()">
                                                    {{i18nkeyword.plc.cancel}}
                                                </button>
                                                <button id="submitBT" class="button button-small button-flat-primary pull-right" style="margin-top:5px; margin-right:10px" onClick="submitPLC()">
                                                    {{i18nkeyword.plc.submit}}
                                                </button>
                                            </div>
                                            <div class="widget-body">
                                                <div class="widget-body-inner">
                                                    <div class="widget-main no-padding">
                                                    <table class="table table-bordered table-striped">
                                                        <thead class="thin-border-bottom">
                                                            <tr style="text-align:center">
                                                                <th></th>
                                                                <th>{{i18nkeyword.plc.NO}}</th>
                                                                <th>{{i18nkeyword.plc.input_one}}</th>
                                                                <th>{{i18nkeyword.plc.operator}}</th>
                                                                <th>{{i18nkeyword.plc.input_two}}</th>
                                                                <th>{{i18nkeyword.plc.output}}</th>
                                                            </tr>
                                                        </thead>

                                                        <tbody id="editPLC_data">
                                                            <tr ms-for="(index,addplc) in add_senplc_block_dic" style="text-align:center">
                                                                <td style='width:27px'>
                                                                    <div class='radio'>
                                                                        <input name="line" type='radio' onclick='radioClick(this)'>
                                                                    </div>
                                                                </td>
                                                                <td>{{index+1}}</td>
                                                                <td>{{addplc["Preposition_input_str"]}}</td>
                                                                <td>{{addplc["Middle_operator_str"]}}</td>
                                                                <td>{{addplc["Postposition_input_str"]}}</td>
                                                                <td>{{addplc["Output_str"]}}</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <button id="addLineBT" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px;" onClick="addLine()">
                                                {{i18nkeyword.plc.add}}
                                            </button>
                                            <button id="alterLineBT" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px;" onClick="alterLine()">
                                                {{i18nkeyword.plc.alter}}
                                            </button>
                                            <button id="delLineBT" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px;" onClick="delLine()">
                                                {{i18nkeyword.plc.delete}}
                                            </button>
                                        </div>

                                        <div class="widget-box" id="editLine" style="display:none; margin-top:30px">
                                            <div class="widget-header widget-header-flat widget-header-small">
                                                <h5 class="lighter">
                                                    {{i18nkeyword.plc.plc_edittable}}
                                                </h5>
                                            </div>

                                            <div class="widget-body">
                                                <div class="widget-body-inner" style="display:block;">
                                                    <div class="widget-main no-padding">
                                                        <table class="table table-bordered table-striped">
                                                            <tbody id="editLine_data">
                                                                <tr>
                                                                    <td>{{i18nkeyword.plc.input_one}}</td>
                                                                    <td>
                                                                        <select id="input1_Type" ms-duplex="add_sen_datas['Preposition_operand_type']" class="editable input-medium">
                                                                            <option value="0">{{i18nkeyword.plc.signal}}</option>
                                                                            <option value="1">{{i18nkeyword.plc.register}}</option>
                                                                            <option value="2">{{i18nkeyword.plc.constant}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Preposition_operand_type'] == '0'">
                                                                        {{i18nkeyword.device}}
                                                                        <select id="input1_Device" ms-duplex="add_sen_datas['Preposition_device_sid']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Preposition_device_sid']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in devsid_listvalue" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Preposition_device_sid']==sidnum.sid?true:false}" >{{sidnum["device name"]}}</option>
                                                                        </select>
                                                                        {{i18nkeyword.plc.type}}
                                                                        <select id="input1_Signal_Type" ms-duplex="add_sen_datas['Preposition_signal_type']" class="editable input-medium" style="width:auto; min-width: 120px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Preposition_signal_type']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="devtype in classify_devType" ms-attr="{value:devtype.value,selected:add_sen_datas['Preposition_signal_type']==devtype.value?true:false}">{{devtype["name"]}}</option>
                                                                        </select>
                                                                        {{i18nkeyword.signal}}
                                                                        <select id="input1_Signal" ms-duplex="add_sen_datas['Preposition_operand_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Preposition_operand_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in pre_plc_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Preposition_operand_value']==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                                        </select>
                                                                        <span ms-if="preposition_value != ''"style="margin-top:7px; float:right">{{i18nkeyword.devlist.value}}:&nbsp;&nbsp;<b class="green">{{preposition_value}}</b></span>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Preposition_operand_type'] == '1'">
                                                                        <select id="input1_Register" ms-duplex="add_sen_datas['Preposition_operand_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Preposition_operand_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in plc_register_info" ms-attr="{value:sidnum.register_no,selected:add_sen_datas['Preposition_operand_value']==sidnum.sid?true:false}">{{sidnum["register_name"]}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Preposition_operand_type'] == '2'">
                                                                        <input ms-duplex="add_sen_datas['Preposition_operand_value']" class="form-control input-medium" style="width: 165px;">
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td>{{i18nkeyword.plc.operator}}</td>
                                                                    <td>
                                                                        <select ms-duplex="add_sen_datas['Middle_operator']" id="operator_Type" class="editable input-medium">
                                                                            <option value="0">AND</option>
                                                                            <option value="1">OR</option>
                                                                            <option value="2">NOT</option>
                                                                            <option value="3">&gt;</option>
                                                                            <option value="4">&lt;</option>
                                                                            <option value="5">=</option>
                                                                            <option value="6">≠</option>
                                                                            <option value="7">PAR</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Middle_operator'] == '0' || add_sen_datas['Middle_operator'] == '1' || add_sen_datas['Middle_operator'] == '2'">
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Middle_operator'] == '3' || add_sen_datas['Middle_operator'] == '4'">
                                                                        <span>{{i18nkeyword.plc.backlash}}</span>
                                                                        <input ms-duplex="add_sen_datas['Operator_extra_attr']" class="form-control input-medium" style="width: 165px;">
                                                                    </td>
                                                                    <td style="word-break:break-all;" ms-if="add_sen_datas['Middle_operator'] == '5'">
                                                                        {{i18nkeyword.plc.EQ_check}}
                                                                    </td>
                                                                    <td style="word-break:break-all;" ms-if="add_sen_datas['Middle_operator'] == '6'">
                                                                        {{i18nkeyword.plc.NE_check}}
                                                                    </td>
                                                                    <td style="word-break:break-all;" ms-if="add_sen_datas['Middle_operator'] == '7'">
                                                                        {{i18nkeyword.plc.PAR_note}}<br/>
                                                                        {{i18nkeyword.plc.PAR_check}}
                                                                    </td>
                                                                </tr>

                                                                <tr ms-if= "add_sen_datas['Middle_operator'] != '2' && add_sen_datas['Middle_operator'] != '7'" >
                                                                    <td>{{i18nkeyword.plc.input_two}}</td>
                                                                    <td>
                                                                        <select id="input2_Type" ms-duplex="add_sen_datas['Postposition_operand_type']" class="editable input-medium">
                                                                            <option value="0">{{i18nkeyword.plc.signal}}</option>
                                                                            <option value="1">{{i18nkeyword.plc.register}}</option>
                                                                            <option value="2">{{i18nkeyword.plc.constant}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Postposition_operand_type'] == '0'">
                                                                        {{i18nkeyword.device}}
                                                                        <select id="input2_Device" ms-duplex="add_sen_datas['Postposition_device_sid']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Postposition_device_sid']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in devsid_listvalue" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Postposition_device_sid']==sidnum.sid?true:false}" >{{sidnum["device name"]}}</option>
                                                                        </select>
                                                                        {{i18nkeyword.plc.type}}
                                                                        <select id="input2_Signal_Type" ms-duplex="add_sen_datas['Postposition_signal_type']" class="editable input-medium" style="width:auto; min-width: 120px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Postposition_signal_type']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="devtype in classify_devType" ms-attr="{value:devtype.value,selected:add_sen_datas['Postposition_signal_type']==devtype.value?true:false}">{{devtype["name"]}}</option>
                                                                        </select>
                                                                        {{i18nkeyword.signal}}
                                                                        <select id="input2_Signal" ms-duplex="add_sen_datas['Postposition_operand_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Postposition_operand_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in post_plc_relevance_device_signals" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Postposition_operand_value']==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                                        </select>
                                                                        <span ms-if="postposition_value != ''" style="margin-top:7px; float:right">{{i18nkeyword.devlist.value}}:&nbsp;&nbsp;<b class="green">{{postposition_value}}</b></span>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Postposition_operand_type'] == '1'">
                                                                        <select id="input2_Register" ms-duplex="add_sen_datas['Postposition_operand_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Postposition_operand_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in plc_register_info" ms-attr="{value:sidnum.register_no,selected:add_sen_datas['Postposition_operand_value']==sidnum.sid?true:false}">{{sidnum["register_name"]}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Postposition_operand_type'] == '2'">
                                                                        <input ms-duplex="add_sen_datas['Postposition_operand_value']" class="form-control input-medium" style="width: 165px;">
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td>{{i18nkeyword.plc.output}}</td>
                                                                    <td>
                                                                        <select id="Output_type" ms-duplex="add_sen_datas['Output_type']" class="editable input-medium">
                                                                            <option value="0">{{i18nkeyword.plc.signal}}</option>
                                                                            <option value="1">{{i18nkeyword.plc.register}}</option>
                                                                            <option value="2">DO</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Output_type'] == '0'">
                                                                        <select id="Output_Signal" ms-duplex="add_sen_datas['Output_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Output_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in output_plc_custom_alarm_signals" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Output_value']==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Output_type'] == '1'">
                                                                        <select id="Output_Register" ms-duplex="add_sen_datas['Output_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Output_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in plc_register_info" ms-attr="{value:sidnum.register_no,selected:add_sen_datas['Output_value']==sidnum.sid?true:false}">{{sidnum["register_name"]}}</option>
                                                                        </select>
                                                                    </td>
                                                                    <td ms-if="add_sen_datas['Output_type'] == '2'">
                                                                        <select id="Output_Do" ms-duplex="add_sen_datas['Output_value']" class="editable input-medium" style="width:auto; min-width: 150px;">
                                                                            <option value = "" ms-attr="{selected:add_sen_datas['Output_value']==''?true:false}">{{i18nkeyword.null}}</option>
                                                                            <option ms-for="sidnum in output_plc_digital_do_signals" ms-attr="{value:sidnum.sid,selected:add_sen_datas['Output_value']==sidnum.sid?true:false}">{{sidnum["full_name"]}}</option>
                                                                        </select>
                                                                        <select id="Output_Do_status" ms-duplex="add_sen_datas['Output_default_status']" class="editable input-medium">
                                                                            <option value="0">{{i18nkeyword.plc.reset}}</option>
                                                                            <option value="1">{{i18nkeyword.plc.set}}</option>
                                                                        </select>
                                                                    </td>
                                                                </tr>

                                                                <tr ms-visible="@judge_plc_alarm_sig(add_sen_datas['Output_value'])">
                                                                    <td>{{i18nkeyword.alias}}</td>
                                                                    <td colspan="2">
                                                                        <input ms-duplex="add_sen_datas['Output_alias']" class="form-control input-medium pull-left" style="width:60%" ms-attr="{'placeholder':i18nkeyword.check_input_tip+','+i18nkeyword.input_max_length+':128'+i18nkeyword.char}" >
                                                                    </td>
                                                                <tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <button id="" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px;" onClick="editLineOK()">
                                                {{i18nkeyword.plc.ok}}
                                            </button>
                                            <button id="" class="button button-small button-flat-primary" style="margin-bottom:10px; margin-top:10px; margin-right:10px;" onClick="editLineCancel()">
                                                {{i18nkeyword.plc.cancel}}
                                            </button>
                                            <div ms-if="preposition_convention != '' || postposition_convention != ''">
                                                <div class="widget-header widget-header-flat widget-header-small"  style="overflow-y:auto;" ms-if="preposition_convention != ''">
                                                    <h5 style="word-break:break-all;">
                                                        {{preposition_convention}}
                                                    </h5>
                                                </div>
                                                <div class="widget-header widget-header-flat widget-header-small" style="overflow-y:auto;" ms-if="postposition_convention != ''">
                                                    <h5 style="word-break:break-all;">
                                                        {{postposition_convention}}
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane" id="plcparaMaintain" ms-class="'plcparaMaintain' == tab_list?'active':''">
                                        <form id="plcpara_down" onSubmit="return false;" ms-if="userlevel > '2'">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.plc.plc_config_export}}
                                                </legend>
                                                <div id="dl_tip" style="display:none" class="alert alert-block alert-success">
                                                    <small>
                                                        {{i18nkeyword.system.handling_export_later}}
                                                    </small>
                                                </div>
                                                <div id="dl_url" style="display:none" class="alert alert-block alert-success">
                                                </div>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="export_plc_para_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_export}}
                                                        <input id="export_plc_para_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char , 'title': i18nkeyword.user.pswd_tip }" ms-duplex="plc_para_export_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('export_plc_para_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <div style="display:inline;">
                                                    <button id="dl_para" onClick="doDL_para()" style="margin-left: 12px;"  class="button button-small button-flat-primary button-large">
                                                        {{i18nkeyword.system.download_para}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                        <form id="plcpara_up"  action="/power/cgi-bin/main.fcgi" method="post"
                                        enctype="multipart/form-data" onsubmit="return uploadplcpara();" ms-if="userlevel > '2'">
                                            <fieldset>
                                                <legend>
                                                    {{i18nkeyword.plc.plc_config_import}}
                                                </legend>
                                                <div style="display:block;">
                                                    <label style="display:inline;">
                                                        <input type="checkbox" name="up_plc_para_box" value="" style="margin-right: 0px; zoom: 140%;" onchange="whether_to_encrypt(this)"/>
                                                        {{i18nkeyword.encryption_password_import}}
                                                        <input id="up_plc_para_input" disabled="disabled" type="password" autocomplete="new-password" class="form-control input-medium" style="width:200px;padding:0px 20px 0px 0px;" ms-attr="{'placeholder': i18nkeyword.input_max_length + ':31' + i18nkeyword.char}"ms-duplex="plc_para_import_passwd">
                                                    </label>
                                                    <span style="margin-left:-20px; cursor: pointer;padding-right:10px" onclick="change_password_show('up_plc_para_input')"><i class="icon-eye-open" ></i></span>
                                                </div>
                                                <table>
                                                    <tr>
                                                        <td style="width:400px;padding-top:10px" ms-attr="{'title':i18nkeyword.ziptype_failure}">
                                                            <input type="file" id="id-plcpara-file" name="uploadfile" class="file" data-max-file-size="10240" accept=".zip"/>
                                                            <input type="hidden" id ="uppara_type" name="type" value="val_set" />
                                                            <input type="hidden" id ="uppara_objid" name="objectid" value="paramaintain_plc" />
                                                            <input type="hidden" id ="uppara_paraval" name="paraval" ms-attr="{'value': plc_para_paraval }"/>
                                                        </td>
                                                        <td style="padding-bottom:10px;padding-left:20px">
                                                            <button id="up_plcpara_bt" type="submit" class="button button-small button-flat-primary" onclick="change_butonflag()">
                                                                {{i18nkeyword.system.upload_para}}
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <div ms-if="para_verify_value.length != 0">
                                                    <td>
                                                        {{i18nkeyword.system.verify_para}}
                                                    </td>
                                                    <table>
                                                        <tr>
                                                            <table style="padding-top:10px; width:600px" id="sample-table-1" class="table table-striped table-bordered table-hover" >
                                                                <thead>
                                                                    <th>
                                                                        {{i18nkeyword.system.abnormal_data}}
                                                                    </th>
                                                                </thead>
                                                                <tbody>
                                                                    <tr ms-for="(n,al) in para_verify_value">
                                                                        <td>{{al["para verify"]}}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </tr>
                                                    </table>
                                                    <button id="para_verify_bt"  class="button button-small button-flat-primary" onclick="download_abnormal_data()">
                                                        {{i18nkeyword.system.download_abnormal_data}}
                                                    </button>
                                                </div>
                                            </fieldset>
                                        </form>
                                    </div>
                                </div>
                            </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <script src="/js/private/opensrc_i18n.js"></script>
        <script src="/js/opensrc/jquery.form.min.js"></script>
        <script src="/js/opensrc/fileinput.js"></script>
        <script src="/page/js/config_plc.js"></script>
        <!--[if !IE]> -->
        <script type="text/javascript">
            var empty =  get_opensrc_i18n_word("choose_file_tip");
            var choose = get_opensrc_i18n_word("choose_file");
            var change = get_opensrc_i18n_word("choose_again");
            $(function() {
                $('#id-plcpara-file').ace_file_input({
                    no_file: empty + "...",
                    btn_choose: choose,
                    btn_change: change,
                    droppable: false,
                    onchange: null,
                    thumbnail: false
                });
            });
        </script>
        <!-- <![endif]-->
    </body>

</html>