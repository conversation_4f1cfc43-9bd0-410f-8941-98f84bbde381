﻿var getBuzzState = {data:{objectid:"buzzer",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getBuzzStateSucc};
var vmodel = avalon.define({
	$id:'system',
	tab_list:'systemstate',
	system_IOstate:[],
	system_DOstate:[],
	system_DO_infos:[],
	di_ai_instidvalue:[],
	drycontactstate:{"0":"LOW","1":"HIGH"},
	buzzerstate:true,
	buzzstate:[],
	outrelay_ctrl_attr:[],
	outrelay_ctrl_val:[],
	outrelay_ctrl_curr_state:0,
	northnet_NetconstatusAttrData:[],
	northnet_NetconstatusValueData:[],
	northnet_WirelessSigData:[],
	timezonevalue:[],    //时区参数值
	timezonestruc:[],    //时区参数结构数据
	timezoneUTC:['(UTC-01:00)','(UTC+00:00)','(UTC+02:00)','(UTC+02:00)','(UTC+03:00)','(UTC+04:00)','(UTC+06:00)','(UTC+07:00)','(UTC+08:00)','(UTC+09:00)','(UTC+10:00)','(UTC+10:00)',
				 '(UTC+12:00)','(UTC+03:30)','(UTC+05:30)','(UTC+09:30)','(UTC-01:00)','(UTC-02:00)','(UTC-03:00)','(UTC-05:00)','(UTC-06:00)','(UTC-07:00)','(UTC-08:00)','(UTC-09:00)',
				 '(UTC-09:00)','(UTC-10:00)','(UTC-11:00)','(UTC+12:00)','(UTC-05:00)','(UTC-05:00)','(UTC-04:00)','(UTC-04:00)','(UTC-03:30)','(UTC+01:00)','(UTC+02:00)','(UTC+05:00)',
				 '(UTC+11:00)'],
	north_protocol_status:[],
	north_protocol_num:"0",
	sig_strlength:"",

	do_set_status_get:function(board_type,do_type,io_index){
		for (var i in vmodel.system_DOstate) {
			if (vmodel.system_DOstate[i].index ==String(io_index) &&
				vmodel.system_DOstate[i]['type'] == String(do_type) &&
				vmodel.system_DOstate[i].board_type ==String(board_type)) {
				return vmodel.system_DOstate[i].value;
			}
		}
		return "";
	},
	changeIOValue:function(d,board_type,tag,index){
		var paraval = [{"action":String(d),"board_type":String(board_type),"mode":String(tag),"index":String(index)}];
		var changeIO = {data:{objectid:"di_do",type:"val_set",paranum:"0",paraval:JSON.stringify(paraval)},success:changeIOValue_succ};
		request.addRequest([changeIO]);
		return false;
	},
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
		send_request_by_tab(tab);
    },
});

function send_request_by_tab(tab){
	switch(tab){
		case "systemstate":
			send_systemstate_request();
			clear_request_by_flag(["systemsinfo", "status", "netmanage_config_tab"]);
			break;
		case "systemsinfo":
			send_systemsinfo_request();
			clear_request_by_flag(["systemstate", "status", "netmanage_config_tab"]);
			break;
		case "status":
			send_status_request();
			clear_request_by_flag(["systemstate", "systemsinfo", "netmanage_config_tab"]);
			break;
		case "netmanage_config_tab":
			send_netmanage_config_tab_request();
			clear_request_by_flag(["systemstate", "systemsinfo", "status"]);
			break;
		default:
			clear_request_by_flag(["systemstate", "systemsinfo", "status", "netmanage_config_tab"]);
			break;
	}
}

function clear_request_by_flag(arr){
	var map = {
			"systemstate":clear_systemstate_request,
			"systemsinfo":clear_systemsinfo_request,
			"status":clear_status_request,
			"netmanage_config_tab":clear_netmanage_config_tab_request,
			};
	for(var i=0;i<arr.length;i++){
		if(map[arr[i]]){
			map[arr[i]]();
		}
	}
}
/***************************************************************系统状态**********************************************************************/
//   获取DO信息 干接点控制信息
function get_DO_infos(obj) {
	var Rq = {data:{objectid:"plat.do",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:listDOInstid};
	request.clearRequest(Rq);
	request.addRequest([Rq]);

	function listDOInstid(d,r){
		var Rq = {data:{objectid:"plat.do",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getlistDOidname};
		request.addRequest([Rq]);
	}

	function getlistDOidname(d,r){
		if (d.result != "ok") {
			return;
		}
		var array = [];
		for (var i in d.data) {
			//array.push(d.data[i]);

			if (d.data[i]['Ctrl Mode'] == '1' || d.data[i]['Ctrl Mode'] == '2' || d.data[i]['Ctrl Mode'] == '3' || d.data[i]['Ctrl Mode'] == '4') {
				array.push(d.data[i]);
			}
		}
		vmodel.system_DO_infos.removeAll();
		addvalue(vmodel.system_DO_infos,array,"instid");
		get_all_do_status();
	}
}

// get_DO_infos();

function get_do_status_succ(d,r) {
	if (d.result == 'ok') {
		addvalue(vmodel.system_DOstate,d.data,"id");
	}
}

function get_do_status(board_type,do_type,io_index){
	var paraval = [{"board_type":String(board_type),"type":String(do_type),"index":String(io_index)}];
	var do_sta_req = {data:{objectid:"do_status",type:"val_get",paraval:JSON.stringify(paraval)},success:get_do_status_succ};
	request.addRequest([do_sta_req]);
}

function get_all_do_status(obj) {
	for (var i in vmodel.system_DO_infos) {
		get_do_status(vmodel.system_DO_infos[i]['Board Type'],vmodel.system_DO_infos[i]['Channel Type'],vmodel.system_DO_infos[i]['Channel NO.']);
	}
}

function changeIOValue_succ(d,r) {
	if (d.result ==="ok"){

		popupTipsDiv($("#do_control_successalert"), 2000);
		var paraval = JSON.parse(r.data.paraval);
		if (paraval[0]['mode'] == "2") {
			setTimeout(function(){get_do_status(paraval[0].board_type,1,paraval[0].index);}, 3000);  //  下电
		} else {
			setTimeout(function(){get_do_status(paraval[0].board_type,2,paraval[0].index);}, 3000);
		}
	}
	else{
		popupTipsDiv($("#time_failurealert"), 2000);
	}
}


outrelay_ctrl_info = {
	GET:function(obj) {
		var attr_req = {data:{objectid:"plat.outrelay_ctrl",type:"val_get",paranum:"0",paraval:JSON.stringify([{"instid":""}])},success:OutrelayGetSucc};
		request.addRequest([attr_req]);

		function OutrelayGetSucc(d,r) {
			if (d.result == 'ok') {
				vmodel.outrelay_ctrl_val = d.data;
				vmodel.outrelay_ctrl_curr_state = !(!(vmodel.outrelay_ctrl_val[0]['mode'] * 1));
			}
		}
	},
	PUT:function(obj) {
		var self = this;
		var attr_req = {data:{objectid:"plat.outrelay_ctrl",type:"val_set",paranum:"0",paraval:JSON.stringify(vmodel.outrelay_ctrl_val)},success:OutrelayPutSucc};
		request.addRequest([attr_req]);

		function OutrelayPutSucc(d,r) {
			if(d.result ==="ok"){
				mainvalue.controlsuccess ="success";
			}
			else{
				mainvalue.controlsuccess ="failure";
			}
			self.GET();
		}
	}
}

//  干接点强制控制
// outrelay_ctrl_info.GET();

function set_outrelay_ctrl_mode(obj) {
	var state = (!vmodel.outrelay_ctrl_curr_state) * 1;
	vmodel.outrelay_ctrl_curr_state = !(!state);
	vmodel.outrelay_ctrl_val[0]['mode'] = state.toString();
	outrelay_ctrl_info.PUT();
}

function getBuzzStateSucc(d,r){
	var buzz_input_state = !Boolean(d.data[0].status*1); // 获取蜂鸣器状态
	$("#buzzer_switch").attr("checked", buzz_input_state);
}

function buzz_input_click() {
	var buzz_input_state = $("#buzzer_switch").prop('checked');
	var sta = (!buzz_input_state)* 1;
	var paraval = [{"action":String(sta)}];
	var buzeeReq = {data:{objectid:"buzzer",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)}};
	request.addRequest([buzeeReq]);
}
// $(document).ready(function(){
// 	request.addRequest([getBuzzState]);//蜂鸣器状态
// });


// getDI_AIChannel();
//获取所有用作DI的通道
function getDI_AIChannel(obj){
	var Rq = {data:{objectid:"para_config_di",type:"val_get",paraval:JSON.stringify([])},success:get_ai_di_channel_info_succ};
	request.clearRequest(Rq);
	request.addRequest([Rq]);
}

function get_ai_di_channel_info_succ(d,r) {
	if (d.result != 'ok') {
		return;
	}
	vmodel.di_ai_instidvalue.clear();
	addvalue(vmodel.di_ai_instidvalue, d.data, "inst_id");
}

// 获取干接点状态
var getIOState = {data:{objectid:"di_do",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getIOState,refresh:3};
// request.addRequest([getIOState]);

function getIOState(d,r){
	if (d.result != 'ok') {
		return;
	}
	for(var i in d.data) {
	 	for(var j in vmodel.di_ai_instidvalue){
			if (d.data[i]["full_name"] == vmodel.di_ai_instidvalue[j]["Channel Name"]) {
				if (vmodel.di_ai_instidvalue[j]["Is Valid"] == "InValid") {
					d.data[i]["short_name"] = "";
				}
			}
		}
		if (d.data[i]["Is Valid"]  == "InValid") {
			d.data[i]["short_name"] = "";
		}
	}
	addvalue(vmodel.system_IOstate,d.data,"full_name");
}

function send_systemstate_request(){
	request.addRequest([getBuzzState]);//蜂鸣器状态
	get_DO_infos();
	//  获取蜂鸣器状态信息
	outrelay_ctrl_info.GET();
	getDI_AIChannel();
	request.addRequest([getIOState]);
}

function clear_systemstate_request(){
	var Rq = {data:{objectid:"plat.do",type:"list",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq);
	var Rq1 = {data:{objectid:"plat.do",type:"val_get",paranum:"1"}};
	request.clearRequest(Rq1);
	var do_sta_req = {data:{objectid:"do_status",type:"val_get"}};
	request.clearRequest(do_sta_req);
	request.clearRequest(getBuzzState);
}

/*************************************************************************************************************************************/
/***************************************************************系统时间**********************************************************************/
// 获取时区信息
var rqTzList = {data:{objectid:"plat.system_time",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:get_tz_List};
// request.addRequest([rqTzList]);

function get_tz_List(d,r)
{
	// 获取 Attr 信息
	var rqTzAttr = {data:{objectid:"plat.system_time",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:get_tz_Attr};
	request.addRequest([rqTzAttr]);
	// 获取 value 信息
	var rqTzValue = {data:{objectid:"plat.system_time",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:get_tz_Value};
	request.addRequest([rqTzValue]);
}

function get_tz_Attr(d,r){
	addvalue(vmodel.timezonestruc,d.data,"name");
}
function get_tz_Value(d,r){
	addvalue(vmodel.timezonevalue,d.data,"instid");
}

function set_tz_Data(){
	var Rq = {data:{objectid:"plat.system_time",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.timezonevalue)},success:reget_tz_Value};
	request.addRequest([Rq]);
}

function reget_tz_Value(d,r){
	systimeTask.start();
	if (d.result ==="ok"){
	    popupTipsDiv($("#time_successalert"), 3000);
	}
	else{
		popupTipsDiv($("#time_failurealert"), 3000);
	}
}

function getSysTime(){
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:setTimeInput};
	request.addRequest([gettimeRq]);
}

function setTimeInput(d, r){
	$("#timeinput").val($.trim($("#systime").text()));
}

function setSysTime(timeStr) {
	if (timeStr == "") {
		alert(mainvalue.i18nkeyword.input_empty);
		return;
	}
	var rslt = systimeTask.settime(timeStr);
	if (rslt == TIME_OVER_MAX) {
		alert(mainvalue.i18nkeyword.system.time_out_range);
		return;
	}
	if (rslt == TIME_NOT_REG) {
		alert(mainvalue.i18nkeyword.system.time_out_range);
		return;
	}
}

function send_systemsinfo_request(){
	request.addRequest([rqTzList]);
}

function clear_systemsinfo_request(){
	var rqTzList = {data:{objectid:"plat.system_time",type:"list",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(rqTzList);
	var rqTzAttr = {data:{objectid:"plat.system_time",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])}};
	request.clearRequest(rqTzAttr);
	var rqTzValue = {data:{objectid:"plat.system_time",type:"val_get",paranum:"1"}};
	request.clearRequest(rqTzValue);
	var Rq = {data:{objectid:"plat.system_time",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.timezonevalue)}};
	request.clearRequest(Rq);
	var gettimeRq = {data:{objectid:"system_time",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(gettimeRq);

}
/*************************************************************************************************************************************/
/******************************************************************连接状态************************************************************/
//获取无线网络信号数据
var rqWirelessSigList = {data:{objectid:"wireless_sig_get",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},refresh:5,success:getWirelessSigList};
// request.addRequest([rqWirelessSigList]);
function getWirelessSigList(d,r){
    //addvalue(vmodel.northnet_WirelessSigData,d.data,"network_operator");
    if (d.result != 'ok' || d.datanum <= 0) {
        return;
    }
    vmodel.northnet_WirelessSigData = d.data;
    var str=vmodel.northnet_WirelessSigData[0].sig_strlength;
	vmodel.sig_strlength = "("+str+")";
    var len=str.length;
    var n=Number(str.substring(0,len-4));
    //var n=85.986;
    if(str.indexOf("dBm")==-1){
        $('.bars ul').css('display','none');
		$('#sig_strlength_val').css('display','none');
        $('.bars div').css('display','block');
    }else{
        $('.bars div').css('display','none');
        $('.bars ul').css('display','block');
		$('#sig_strlength_val').css('display','block');
        $('.bars ul li').css('background-color','rgba(0,0,0,0.5)');
        if(n<=-100){
            $('.bars ul li:lt(1)').css('background','limegreen');
        }else if(-100<n&&n<=-95){
            $('.bars ul li:lt(2)').css('background','limegreen');
        }else if(-95<n&&n<=-90){
            $('.bars ul li:lt(3)').css('background','limegreen');
        }else if(-90<n&&n<=-85){
            $('.bars ul li:lt(4)').css('background','limegreen');
        }else{
            $('.bars ul li').css('background','limegreen');
        }
    }
}

//获取Netconstatus instid
var rqNetconstatusList = {data:{objectid:"plat.netconstatus",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getNetconstatusList};
// request.addRequest([rqNetconstatusList]); 

function getNetconstatusList(d,r){
	//获取	Netconstatus value
	var rqNetconstatusValue = {data:{objectid:"plat.netconstatus",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},refresh:10,success:getNetconstatusValue};
	request.addRequest([rqNetconstatusValue]);
	//获取Netconstatus Attr
	var rqNetconstatusAttr = {data:{objectid:"plat.netconstatus",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:getNetconstatusAttr};
	request.addRequest([rqNetconstatusAttr]);
}

function getNetconstatusValue(d,r){
    var arr = [];
	addvalue(arr,d.data,"instid");
    vmodel.northnet_NetconstatusValueData = arr;
}

function getNetconstatusAttr(d,r){
	addvalue(vmodel.northnet_NetconstatusAttrData,d.data,"name");
}

function send_status_request(){
	request.addRequest([rqWirelessSigList]);
	request.addRequest([rqNetconstatusList]);
}

function clear_status_request(){
	var rqWirelessSigList = {data:{objectid:"wireless_sig_get",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},refresh:5};
	request.clearRequest(rqWirelessSigList);
	var rqNetconstatusList = {data:{objectid:"plat.netconstatus",type:"list",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(rqNetconstatusList);
	var rqNetconstatusValue = {data:{objectid:"plat.netconstatus",type:"val_get",paranum:"1"},refresh:10};
	request.clearRequest(rqNetconstatusValue);
	var rqNetconstatusAttr = {data:{objectid:"plat.netconstatus",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])}};
	request.clearRequest(rqNetconstatusAttr);
}

/*************************************************************************************************************************************/
/***********************************************************************北向网管连接状态**************************************************************/
// var Rq = {data:{objectid:"pdt_sps_north_protocol_status",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},refresh:4,success:get_north_protocol_status_succ};
// request.addRequest([Rq]);

//回调函数
function get_north_protocol_status_succ(d,r){
	if (d.result == 'ok') {
		if (d.data.length != 0) {
			vmodel.north_protocol_status = d.data;
			vmodel.north_protocol_num = d.datanum;
		}
	} else {
		vmodel.north_protocol_status = [];
		vmodel.north_protocol_num = "0";
	}
}

function send_netmanage_config_tab_request(){
	var Rq = {data:{objectid:"pdt_sps_north_protocol_status",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},refresh:4,success:get_north_protocol_status_succ};
	request.addRequest([Rq]);
}

function clear_netmanage_config_tab_request(){
	var pdt_sps_north_protocol_status_req = {data:{objectid:"pdt_sps_north_protocol_status",type:"val_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},refresh:4};
	request.clearRequest(pdt_sps_north_protocol_status_req);
}

function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "systemstate" || tab == "systemsinfo" || tab == "status" || tab == "netmanage_config_tab") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "systemstate";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();
send_request_by_tab(vmodel.tab_list);