﻿function Opensrc_i18n(lan_code,word) {
    this.word = word;
    this.lan_code = lan_code;
    Opensrc_i18n.prototype.translate=function() {
         //  用于存放语言的翻译，语言变更时需进行更改
        var word_list = {
            'en_US.UTF-8':{
                "choose_file":"Choose",
                "choose_again":"Change again",
                "empty":"Empty",
                "select_tips":"Please select",
                "choose_file_tip":"Please choose File",
            },
            'zh_CN.UTF-8':{
               "choose_file":"选择文件",
               "choose_again":"重新选择",
               "empty":"空",
               "select_tips":"请选择",
               "choose_file_tip":"请选择上传文件",
            },
            'es_ES.UTF-8':{
                "choose_file":"Escoger",
                "choose_again":"Cambiar",
                "empty":"Vacio",
                "select_tips":"Por favor seleccione",
                "choose_file_tip":"Por favor elija Archivo",
            }
        }
        if (typeof(word_list[lan_code])=="undefined") {
            return "";
        }
        var rslt = word_list[lan_code][word];
        if (typeof(rslt)=="undefined") {
            return "";
        }
        return rslt;
    }
}

function get_opensrc_i18n_word(word) {
    var lan_code = Cookies.get("lan");
    if (typeof(lan_code)=="undefined") {
        lan_code = 'en_US.UTF-8'; // 默认选择英文
    }
    var o18n = new Opensrc_i18n(lan_code,word);
    return o18n.translate();
}