<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <meta http-equiv ="proma" content = "no-cache"/>
        <meta http-equiv="cache-control" content="no cache" />
        <meta http-equiv="expires" content="0" />
        <!-- <title>ZTE Energy Management</title> -->
        <title></title>
        <link id="logo_icon" href="/page/assets/img/white.ico" type="image/x-icon" rel="shortcut icon"/>
        <meta name="description" content="overview &amp; stats" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <link rel="stylesheet" href="/page/assets/css/bootstrap.min.css" />
        <link rel="stylesheet" href="/page/assets/css/bootstrap-responsive.min.css" />
        <link rel="stylesheet" href="/page/assets/css/font-awesome.min.css" />
        <link rel="stylesheet" href="/page/assets/css/ace.min.css" />
        <link rel="stylesheet" href="/page/assets/css/ace-responsive.min.css" />
        <link rel="stylesheet" href="/page/assets/css/ace-skins.min.css" />
        <link rel="stylesheet" href="/page/assets/css/button.css" />

        <!--[if IE 7]>
          <link rel="stylesheet" href="/page/assets/css/font-awesome-ie7.min.css" />
        <![endif]-->

        <!--[if lte IE 8]>
          <link rel="stylesheet" href="/page/assets/css/ace-ie.min.css" />
        <![endif]-->

        <!--[if !IE]> -->
        <link rel="stylesheet" href="/page/assets/css/zte_min_other.css" />
        <script type="text/javascript">
            window.jQuery || document.write("<script src='/js/opensrc/jquery-3.6.3.min.js'>"+"<"+"/script>");
        </script>
        <!-- <![endif]-->

        <script src="/js/opensrc/bootstrap.min.js"></script>
        <script src="/js/opensrc/js.cookie.js"></script>
        <script src="/js/opensrc/jquery.sha256.js"></script>
        <script src="/js/opensrc/ace-elements.min.js"></script>
        <script src="/js/opensrc/ace.min.js"></script>
        <script src="/js/opensrc/avalon.js"></script>
        <script src="/js/opensrc/jqueryv1.i18n.js"></script>
        <script src="/js/opensrc/json2.js"></script>
        <script src="/js/opensrc/jquery-migrate-3.4.1.min.js"></script>
        <script src="/js/opensrc/acorn_interpreter.js"></script>
        <script>
            document.write('<script src="/js/private/request.js?v='+ new Date().getTime() +'"><\/script>');
        </script>
        <script>
            document.write('<script src="/js/private/common.js?v='+ new Date().getTime() +'"><\/script>');
        </script>
        <!-- end of basic styles -->
        <link rel="stylesheet" href="/page/assets/css/login.css" />
        <link rel="stylesheet" href="/page/assets/css/style.css" />
    </head>

    <body class="login-layout login" ms-controller="container">
        <div class="bg bg-blur"></div>
        <div class="main-container">
            <div class="main-content">
                <div class="logo">
                    <img ms-if="logo_flag=='1'" src="/page/assets/img/ztelogo.png" style="display: inline;float: left;width: 108px;height: 40px;" alt="">
                    <div ms-if="logo_flag=='0'" style="display: inline;float: left;width: 108px;height: 40px;"></div>
                    <h2 style="font-family: 'fantasy, serif, Arial';font-weight: 1 !important;position: relative;z-index: 10;font-size: 4vw;margin-top: 220px;line-height: 0.8;color: rgb(0,142,211);pointer-events: none">{{i18nkeyword.login.plat_name}}</h2>
                </div>
                <div class="content" style="background-color: rgba(255,255,255,0.22);">
                    <form class="login-form" autocomplete="off">
                        <h4 class="header white" style="font-size: 16px;">
                            <i class="icon-coffee green"></i>
                            {{i18nkeyword.login.empty}}
                            <ul class="nav ace-nav pull-right">
                                <li >
                                    <a data-toggle="dropdown" class="dropdown-toggle" href="#" style="background-color: rgba(255,255,255,0);">
                                    <i class="icon-globe green"></i>
                                        {{i18nkeyword.language}}
                                    <i class="icon-chevron-down"></i>
                                    </a>

                                    <ul class="user-menu pull-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close" style="background-color: rgba(255,255,255,0.22);">
                                        <li ms-for="(num,id) in languagedata">
                                            <a href="#" ms-click="changeLan(id['Language Encode Info'],'login.html')">
                                                {{id["Language Name"]}}
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </h4>

                        <div id="messagealert" style="display:none;" class="alert alert-block alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.login.login_error}}
                        </div>
                        <div id="message_overtimes" style="display:none;" class="alert alert-block alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.login.over_times_tip}}
                        </div>

                        <div class="form-group">
                            <div class="input-icon">
                                <i class="icon-user" style="top:-6px;left:-2px"></i>
                                <input id="loginname" type="text" autocomplete="off" style="background-color: rgba(255, 255, 255, 0.32);display: block;width: 83%;height: 20px;padding: 6px 12px;font-size: 14px;line-height: 1.42857143;color: #555;background-image: none;border: 0px;" class="placeholder-no-fix" ms-attr="{placeholder:i18nkeyword.login.fill_username}">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-icon">
                                <i class="icon-lock" style="top:-6px;left:-2px"></i>
                                <i class="icon-eye-open" style="cursor: pointer;top:-6px;left:89%" onclick="change_password_show('loginpsw')"></i>
                                <input id="loginpsw" type="password" autocomplete="new-password" style="background-color: rgba(255, 255, 255, 0.32);display: block;width: 83%;height: 20px;padding: 6px 12px;font-size: 14px;line-height: 1.42857143;color: #555;background-image: none;border: 0px;" class="placeholder-no-fix" ms-attr="{placeholder:i18nkeyword.login.fill_password}" onkeydown="click_enter()">
                            </div>
                        </div>
                        <div style="padding:0px 0px 40px 0px;font-size: 16px;">
                            <a href="#" onClick="gotopage('password_reset.html')" style="float: left;color: white;">{{i18nkeyword.login.pswd_reset}}</a>
                            <a href="#" onClick="get_session_num();return false;" style="float: right;color: white;">
                                <span>{{i18nkeyword.login.login}}</span><i class="icon-circle-arrow-right"></i>
                            </a>
                        </div>
                    </form>
                </div>
                <div class="copyright" style="margin-top:100px">
                    <span ms-if="logo_flag=='1'">{{i18nkeyword.login.copyright}}</span>
                </div>
            </div><!-- /.main-content -->
        </div><!-- /.main-container -->

        <!-- inline scripts related to this page -->
        <script type="text/javascript">
            var thisisloginpage = true;
        </script>
        <script src="/page/js/login.js"></script>
    </body>

</html>