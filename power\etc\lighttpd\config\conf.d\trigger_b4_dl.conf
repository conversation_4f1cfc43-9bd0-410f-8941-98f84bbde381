#######################################################################
##
##  Trigger before download 
## --------------- 
##
## - if user requests ''download-url'' directly, the request is denied
##   and he is redirected to ''deny-url'
## - if user visits ''trigger-url'' before requesting ''download-url'',
##   access is granted
## - if user visits ''download-url'' again after ''trigger-timeout'' has
##   elapsed, the request is denied and he is redirected to ''deny-url''
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModTriggerBeforeDownload
##
server.modules += ( "mod_mod_trigger_b4_dl" )

##
## To store the trigger state you can either use a local GDBM
## file or memcached(s). 
##

##
## Path to the local GDBM file.
##
trigger-before-download.gdbm-filename = home_dir + "/trigger.db"

##
## List of memcached servers.
##
#trigger-before-download.memcache-hosts = ( "127.0.0.1:11211" )

##
## URL prefix a visitor has to visit before downloading is allowed 
##
trigger-before-download.trigger-url = "^/trigger/"

##
## URL Prefix of the proteced area.
##
trigger-before-download.download-url = "^/download/"

##
## 
## The deny url.
##
trigger-before-download.deny-url = "http://www.example.com/index.html"

##
## How long the "ticket" of the user will be valid.
##
## Value in seconds.
##
trigger-before-download.trigger-timeout = 10

##
## Normally the memcached key will be the remote ip of the request
## If you store other data in the memcached aswell and want to avoid
## key collisions you can configure a memcache-namespace.
##
## The key for the request will be memcache-namespace + remote_ip than.
##
#trigger-before-download.memcache-namespace = "t4bdl_"

##
## If set to 1, the module will log some debug informations.
##
#trigger-before-download.debug = 0

##
#######################################################################
