#######################################################################
##
##  Authentication Module
## -----------------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/docs_modauth
## for more info.
##

#auth.backend                 = "plain"
#auth.backend.plain.userfile  = "/etc/lighttpd/lighttpd.user"
#auth.backend.plain.groupfile = "/etc/lighttpd/lighttpd.group"

#auth.backend.ldap.hostname = "localhost"
#auth.backend.ldap.base-dn  = "dc=my-domain,dc=com"
#auth.backend.ldap.filter   = "(uid=$)"

#auth.require               = ( "/server-status" =>
#                               (
#                                 "method"  => "digest",
#                                 "realm"   => "Server Status",
#                                 "require" => "valid-user"
#                               ),
#                             )

##
#######################################################################
