var default_add_sen_datas = {
	"Group_no":	"-1",
	"Preposition_operand_type":	"0",
	"Preposition_operand_value":	"",
	"Preposition_sig_str": "",
	"Preposition_device_sid": "",
	"Preposition_signal_type": "",
	"Middle_operator":	"0",
	"Operator_extra_attr":	"0",
	"Postposition_operand_type":	"0",
	"Postposition_operand_value":	"",
	"Postposition_sig_str": "",
	"Postposition_device_sid": "",
	"Postposition_signal_type": "",
	"Output_type":	"0",
	"Output_value":	"",
	"Output_sig_str":	"",
	"Output_alias":	"",
	"Output_default_status":	"1",
};

var default_add_decplc_datas = {
	"Group_no":	"-1",
	"Group_description":	"",
	"Group_status":	"1",
};

var editGobj;             //editG选择的对象

var vmodel = avalon.define({
	$id:'plc',
	tab_list:'plc_data',
	decplc_block_dic:{},		//plc描述信息
	senplc_block_dic:{},		//plc语句内容
	add_senplc_block_dic:[],	//新增的PLC组总描述语句
	add_decplc_datas:{},		//新增的PLC描述信息
	add_sen_datas:{},			//新增的PLC单条描述语句
	add_type:"0",				//新增语句类型（0为新增，1为修改）
	select_flag:"0",			//编辑框弹出时检测编辑框值得变化

	buttonflag:0,
	para_verify_value:[],
	plc_para_paraval:[],         //  PLC参数导入传参
    para_err_type:0,	// 参数导入错误文件类型，1表示参数文件错误，2表示配置文件错误
    show_more_info_tip:0,
	plc_para_export_passwd:"",   //  PLC参数导出密码
	plc_para_import_passwd:"",   //  PLC参数导入密码

	classify_devType:[],
	pre_plc_relevance_device_signals:[],	//存储输入1的PLC的所有信号
	post_plc_relevance_device_signals:[],	//存储输入2的PLC的所有信号
	output_plc_custom_alarm_signals:[],		//输出告警的PLC的所有信号
	output_plc_digital_do_signals:[],		//输出DO信号的PLC的所有信号
	plc_register_info:[],					//寄存器的所有选项
	preposition_value:"",					//输入1的数值
	postposition_value:"",					//输入2的数值
	preposition_convention:"",				//输入1的取值约定
	postposition_convention:"",				//输入2的取值约定

	show_init_plctitle:function(){
		return mainvalue.i18nkeyword.plc.input_plc_desc + ',' + mainvalue.i18nkeyword.check_input_tip +
			',' + mainvalue.i18nkeyword.input_max_length + ':128' + mainvalue.i18nkeyword.char;
	},
	judge_plc_alarm_sig:function(sid){		// 判断一个sid是否为PLC输入自定义告警
		var sigtype = calSidToDevType(sid);
		if (sigtype.devType == "2" && sigtype.devSn == "1" && sigtype.sigType == "3" && sigtype.devSigVar == "6") {       // PLC告警状态量
			return true;
		}
		return false;
	},
	tabChange : function(tab) {
		vmodel.tab_list = tab;
		set_cookie_with_path("tab_list", vmodel.tab_list);
    }
});

/*********************************************数据获取处理***********************************************************/
function getPlcdata(d,r){
	var delPlcGroup = {data:{objectid:"pdt_plc_v2",type:"attr_get",paranum:"1",paraval:JSON.stringify({})},success:getPlcdataSucc};
	request.addRequest([delPlcGroup]);
	showDataInit();

	function getPlcdataSucc(d,r){
		hideDataInit();
		if (d.result == "ok") {
			for (let i in d.data) {
				get_sen_data(d.data[i]["Group_no"],d.data[i]["Group_no"]);
			}
			classsify_plcgroup(d.data);
		} else {
			vmodel.senplc_block_dic = {};
			vmodel.decplc_block_dic = {};
		}
	}
}

function getPlcgroup(d,r){
	var delPlcGroup = {data:{objectid:"pdt_plc_v2",type:"attr_get",paranum:"1",paraval:JSON.stringify({})},success:getPlcgroupSucc};
	request.addRequest([delPlcGroup]);
	showDataInit();

	function getPlcgroupSucc(d,r){
		hideDataInit();
		if (d.result == "ok" && d.data.length != 0) {
			classsify_plcgroup(d.data);
		} else {
			vmodel.decplc_block_dic = {};
		}
	}
}

function classsify_plcgroup(plcgropudata){
	var dev_Group_no = plcgropudata[0]["Group_no"];
	var dec_blocks = {};
	var dec_datas = new Array();
	for (let i in plcgropudata) {
		if(typeof(plcgropudata[i]["Group_description"]) != 'undefined') {
			if (dev_Group_no == plcgropudata[i]["Group_no"]) {
				dec_datas.push(plcgropudata[i]);
			} else {
				dec_blocks[dev_Group_no] = dec_datas;
				dev_Group_no = plcgropudata[i]["Group_no"];
				dec_datas = new Array();
				dec_datas.push(plcgropudata[i]);
			}
		}
	}
	dec_blocks[dev_Group_no] = dec_datas;
	vmodel.decplc_block_dic = object_copy(dec_blocks);
}

function get_sen_data(group_num, old_num){
	var paraval = [{"Group_no": group_num}];
	var delPlcGroup = {data:{objectid:"pdt_plc_v2",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},success:get_sen_dataSucc};
	request.addRequest([delPlcGroup]);

	function get_sen_dataSucc(d,r){
		if (d.result == "ok" && d.data.length != 0) {
			classsify_sen_data(d.data, old_num);
		}
	}
}

function classsify_sen_data(plcsendata, old_num){
	var sen_Group_no = plcsendata[0]["Group_no"];
	var sen_datas = new Array();
	for (let i in plcsendata) {
		if (sen_Group_no == plcsendata[i]["Group_no"]) {
			getshowstring(plcsendata[i]);
			sen_datas.push(plcsendata[i]);
		}
	}
	Fix_display_issues();
	delete vmodel.senplc_block_dic[sen_Group_no];
	vmodel.senplc_block_dic[sen_Group_no] = sen_datas;
	if (old_num == "-1") {
		getPlcgroup();
	} else {
		vmodel.decplc_block_dic = object_copy(vmodel.decplc_block_dic);  // 刷新界面显示
	}
}

// function getPlcdata(d,r){
// 	var delPlcGroup = {data:{objectid:"pdt_plc",type:"val_get",paranum:"1",paraval:JSON.stringify({})},success:getPlcdataSucc};
// 	request.addRequest([delPlcGroup]);
// 	showDataInit();

// 	function getPlcdataSucc(d,r){
// 		hideDataInit();
// 		if (d.result == "ok"  && d.data.length != 0) {
// 			classsify_plcdata(d.data);
// 		} else {
// 			vmodel.senplc_block_dic = {};
// 			vmodel.decplc_block_dic = {};
// 		}
// 	}
// }

// function classsify_plcdata(plcdata){
// 	var dev_Group_no = plcdata[0]["Group_no"];
// 	var dec_blocks = {};
// 	var dec_datas = new Array();
// 	var sen_Group_no = plcdata[0]["Group_no"];
// 	var sen_blocks = {};
// 	var sen_datas = new Array();
// 	for (let i in plcdata) {
// 		if(typeof(plcdata[i]["Group_description"]) != 'undefined') {
// 			if (dev_Group_no == plcdata[i]["Group_no"]) {
// 				dec_datas.push(plcdata[i]);
// 			} else {
// 				dec_blocks[dev_Group_no] = dec_datas;
// 				dev_Group_no = plcdata[i]["Group_no"];
// 				dec_datas = new Array();
// 				dec_datas.push(plcdata[i]);
// 			}
// 		} else {
// 			if (sen_Group_no == plcdata[i]["Group_no"]) {
// 				getshowstring(plcdata[i]);
// 				sen_datas.push(plcdata[i]);
// 			} else {
// 				sen_blocks[sen_Group_no] = sen_datas;
// 				sen_Group_no = plcdata[i]["Group_no"];
// 				sen_datas = new Array();
// 				getshowstring(plcdata[i]);
// 				sen_datas.push(plcdata[i]);
// 			}
// 		}
// 	}
// 	sen_blocks[sen_Group_no] = sen_datas;
// 	dec_blocks[dev_Group_no] = dec_datas;
// 	vmodel.senplc_block_dic = object_copy(sen_blocks);
// 	vmodel.decplc_block_dic = object_copy(dec_blocks);
// }

function getshowstring(plcdata){
	switch (plcdata["Preposition_operand_type"]) {
		case "0" :
			plcdata["Preposition_input_str"] = plcdata["Preposition_sig_str"];
			if (plcdata["Preposition_operand_value"] != "") {
				var sidData = calSidToDevType(plcdata["Preposition_operand_value"]);
				var devsid = sidData.devType * 281474976710656 + sidData.devSn * 68719476736;
				plcdata["Preposition_device_sid"] = devsid;
				plcdata["Preposition_signal_type"] = sidData.sigType;
			} else {
				plcdata["Preposition_device_sid"] = "";
				plcdata["Preposition_signal_type"] = "";
			}
			break;
		case "1" :
			plcdata["Preposition_input_str"] = mainvalue.i18nkeyword.plc.register + (parseInt(plcdata["Preposition_operand_value"]) + 1).toString();
			plcdata["Preposition_device_sid"] = "";
			plcdata["Preposition_signal_type"] = "";
			break;
		case "2" :
			plcdata["Preposition_input_str"] = plcdata["Preposition_operand_value"];
			plcdata["Preposition_device_sid"] = "";
			plcdata["Preposition_signal_type"] = "";
			break;
		default :
			break;
	}
	switch (plcdata["Postposition_operand_type"]) {
		case "0" :
			plcdata["Postposition_input_str"] = plcdata["Postposition_sig_str"];
			if (plcdata["Postposition_operand_value"] != "") {
				var sidData = calSidToDevType(plcdata["Postposition_operand_value"]);
				var devsid = sidData.devType * 281474976710656 + sidData.devSn * 68719476736;
				plcdata["Postposition_device_sid"] = devsid;
				plcdata["Postposition_signal_type"] = sidData.sigType;
			} else {
				plcdata["Postposition_device_sid"] = "";
				plcdata["Postposition_signal_type"] = "";
			}
			break;
		case "1" :
			plcdata["Postposition_input_str"] = mainvalue.i18nkeyword.plc.register + (parseInt(plcdata["Postposition_operand_value"]) + 1).toString();
			plcdata["Postposition_device_sid"] = "";
			plcdata["Postposition_signal_type"] = "";
			break;
		case "2" :
			plcdata["Postposition_input_str"] = plcdata["Postposition_operand_value"];
			plcdata["Postposition_device_sid"] = "";
			plcdata["Postposition_signal_type"] = "";
			break;
		default :
			break;
	}
	switch (plcdata["Middle_operator"]) {
		case "0" :
			plcdata["Middle_operator_str"] = "AND";
			plcdata["Operator_extra_attr"] = "0";
			break;
		case "1" :
			plcdata["Middle_operator_str"] = "OR";
			plcdata["Operator_extra_attr"] = "0";
			break;
		case "2" :
			plcdata["Middle_operator_str"] = "NOT";
			plcdata["Postposition_input_str"] = "";
			plcdata["Postposition_operand_type"] = "0";
			plcdata["Postposition_device_sid"] = "";
			plcdata["Postposition_signal_type"] = "";
			plcdata["Postposition_operand_value"] = "";
			plcdata["Operator_extra_attr"] = "0";
			break;
		case "3" :
			plcdata["Middle_operator_str"] = ">" + "(" + mainvalue.i18nkeyword.plc.backlash + ":" + plcdata["Operator_extra_attr"] +")";
			break;
		case "4" :
			plcdata["Middle_operator_str"] = "<" + "(" + mainvalue.i18nkeyword.plc.backlash + ":" + plcdata["Operator_extra_attr"] +")";
			break;
		case "5" :
			plcdata["Middle_operator_str"] = "=";
			plcdata["Operator_extra_attr"] = "0";
			break;
		case "6" :
			plcdata["Middle_operator_str"] = "≠";
			plcdata["Operator_extra_attr"] = "0";
			break;
		case "7" :
			plcdata["Middle_operator_str"] = "PAR";
			plcdata["Postposition_input_str"] = "";
			plcdata["Postposition_operand_type"] = "0";
			plcdata["Postposition_device_sid"] = "";
			plcdata["Postposition_signal_type"] = "";
			plcdata["Postposition_operand_value"] = "";
			plcdata["Operator_extra_attr"] = "0";
			break;
		default :
			break;
	}
	switch (plcdata["Output_type"]) {
		case "0" :
			if (vmodel.judge_plc_alarm_sig(plcdata["Output_value"]) == true && plcdata["Output_alias"] != "") {
				plcdata["Output_str"] = plcdata["Output_alias"];
			} else {
				plcdata["Output_str"] = plcdata["Output_sig_str"];
			}
			break;
		case "1" :
			plcdata["Output_str"] = mainvalue.i18nkeyword.plc.register + (parseInt(plcdata["Output_value"]) + 1).toString();
			plcdata["Output_alias"]= "";
			break;
		case "2" :
			if (plcdata["Output_default_status"] == "0") {
				plcdata["Output_str"] = plcdata["Output_sig_str"] + "-" + mainvalue.i18nkeyword.plc.reset;
			} else {
				plcdata["Output_str"] = plcdata["Output_sig_str"] + "-" + mainvalue.i18nkeyword.plc.set;
			}
			plcdata["Output_alias"]= "";
			break;
		default :
			break;
	}
}

function geteditstring(plcdata){
	if (plcdata["Preposition_operand_type"] == '0') {
		for (var i in mainvalue.devsid_listvalue) {
			if (plcdata["Preposition_device_sid"] == mainvalue.devsid_listvalue[i].sid) {
				for (var j in vmodel.pre_plc_relevance_device_signals) {
					if (plcdata["Preposition_operand_value"] == vmodel.pre_plc_relevance_device_signals[j].sid) {
						plcdata["Preposition_sig_str"] = mainvalue.devsid_listvalue[i]["device name"] + ":" + vmodel.pre_plc_relevance_device_signals[j]["full_name"];
						break;
					}
				}
			}
		}
	}
	if (plcdata["Postposition_operand_type"] == '0') {
		for (var i in mainvalue.devsid_listvalue) {
			if (plcdata["Postposition_device_sid"] == mainvalue.devsid_listvalue[i].sid) {
				for (var j in vmodel.post_plc_relevance_device_signals) {
					if (plcdata["Postposition_operand_value"] == vmodel.post_plc_relevance_device_signals[j].sid) {
						plcdata["Postposition_sig_str"] = mainvalue.devsid_listvalue[i]["device name"] + ":" + vmodel.post_plc_relevance_device_signals[j]["full_name"];
						break;
					}
				}
			}
		}
	}
	if (plcdata["Output_type"] == '0') {
		for (var i in vmodel.output_plc_custom_alarm_signals) {
			if (plcdata["Output_value"] == vmodel.output_plc_custom_alarm_signals[i].sid) {
				plcdata["Output_sig_str"] =  vmodel.output_plc_custom_alarm_signals[i]["full_name"];
			}
		}
	}
	if (plcdata["Output_type"] == '2') {
		for (var i in vmodel.output_plc_digital_do_signals) {
			if (plcdata["Output_value"] == vmodel.output_plc_digital_do_signals[i].sid) {
				plcdata["Output_sig_str"] =  vmodel.output_plc_digital_do_signals[i]["full_name"];
			}
		}
	}
	getshowstring(plcdata);
}

/*********************************************RADIO控件功能***********************************************************/
function clear_radio_show() {
	$("input[name='line'][checked]").attr("checked", false);
}

function radioClick(obj){
	if($(obj).attr("checked") == "checked"){
		clear_radio_show();
	} else {
		$("input[name=" + $(obj).attr("name") +"][checked]").attr("checked", false);
		$(obj).attr("checked", true);

	}
}

/*********************************************PLC语句按钮功能***********************************************************/
function object_copy(obj) {
	var tmpobj = JSON.stringify(obj);
	var newobj = JSON.parse(tmpobj);
	return newobj;
}

function editGPLC(obj){
	var id = $(obj).closest(".widget-box").attr("id");
	var plcindex = parseInt(id.substring(3));
	var temp_decdata = object_copy(vmodel.decplc_block_dic[plcindex][0]);
	vmodel.add_decplc_datas = object_copy(temp_decdata);
	var temp_sendata = object_copy(vmodel.senplc_block_dic[plcindex]);
	vmodel.add_senplc_block_dic = object_copy(temp_sendata);
	$("#addPLCBT").css("display", "none");
	$("#editPLC").slideDown(200);
	$("#addLineBT").css("display", "");
	$("#alterLineBT").css("display", "");
	$("#delLineBT").css("display", "");
	$(obj).css("display", "none");
	editGobj = obj;
}

function delGPLC(obj){
	if(confirm(mainvalue.i18nkeyword.delete_confirm)){
		var id = $(obj).closest(".widget-box").attr("id");
		var plcindex = parseInt(id.substring(3));
		var group_num = vmodel.decplc_block_dic[plcindex][0]["Group_no"];
		var para = [{
			"Group_no":	group_num.toString(),
		}];
		var delPlcGroup = {data:{objectid:"pdt_plc",type:"inst_delete",paranum:"1",paraval:JSON.stringify(para)},success:delPlcGroupSucc};
		request.addRequest([delPlcGroup]);
		showDataInit();
	}

	function delPlcGroupSucc(d,r) {
		hideDataInit();
		if (d.result == 'ok') {
			$("#addPLCBT").css("display", "");
			$("#editPLC").slideUp(200);
			$("#editLine").slideUp(200);
			refresh_delete_plc_data(r);   //刷新数据
			popupTipsDiv($("#setSuccessalart"), 1000);
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
		}
	}
}

function Fix_display_issues() {
	// 该函数用于界面显示，数据不更新
	var tmp_obj = object_copy(vmodel.senplc_block_dic);
	vmodel.senplc_block_dic = object_copy(vmodel.senplc_block_dic);  // 触发数据刷新
	// 恢复原始数据
	for (let i in vmodel.senplc_block_dic) {
		vmodel.senplc_block_dic[i] = tmp_obj[i];
	}
}

function refresh_delete_plc_data(r) {
	var newobj = JSON.parse(r.data.paraval);
	var group_num = newobj[0]["Group_no"];
	delete vmodel.senplc_block_dic[group_num];
	delete vmodel.decplc_block_dic[group_num];
	Fix_display_issues();
	vmodel.decplc_block_dic = object_copy(vmodel.decplc_block_dic);  // 刷新界面显示
}

function cancelPLC(){
	var id = $(editGobj).closest(".widget-box").attr("id")
	if($("#" + id + ">.widget-body").css("display") != "none") {
		$(editGobj).css("display", "");
	}
	$("#addPLCBT").css("display", "");
	$("#editPLC").slideUp(200);
	$("#editLine").slideUp(200);
}

function is_arry_repeat(array) {
	var tmp_array = {};
	for (var i in array) {
		if (tmp_array[array[i]]){
			return true;
		} else {
			tmp_array[array[i]] = true;
		}
	}
	return false;
}

function is_include_array(arr1, arr2) {
	for (let i in arr1) {
		if (arr2.indexOf(arr1[i]) == -1) {
			return false;
		}
	}
	return true;
}

function submitPLC(){
	var para = new Array();
	var in_register = new Array();
	var out_register = new Array();
	var datas;
	var output_flag = 0;
	var sen_para = object_copy(vmodel.add_senplc_block_dic);
	if (sen_para.length == "0") {
		alert(mainvalue.i18nkeyword.plc.no_sen_para);
		return;
	}
	var temp_dec_data = object_copy(vmodel.add_decplc_datas);
	if ( 0 == check_plc_name_valid(temp_dec_data["Group_description"])){
		alert(mainvalue.i18nkeyword.plc.group_description_too_long);
		return;
	} else if ( 2 != check_plc_name_valid(temp_dec_data["Group_description"])){
		alert(mainvalue.i18nkeyword.plc.wrong_group_description);
		return;
	}

	para.push(temp_dec_data);
	var temp_para = {
		"Group_no":	"",
		"Preposition_operand_type":	"",
		"Preposition_operand_value":	"",
		"Middle_operator":	"",
		"Operator_extra_attr":	"",
		"Postposition_operand_type":	"",
		"Postposition_operand_value":	"",
		"Output_type":	"0",
		"Output_value":	"",
		"Output_alias":	"",
		"Output_default_status":	"",
	};

	for (let i in sen_para) {
		temp_para["Group_no"] = sen_para[i]["Group_no"];
		temp_para["Preposition_operand_type"] = sen_para[i]["Preposition_operand_type"];
		temp_para["Preposition_operand_value"] = sen_para[i]["Preposition_operand_value"];
		if (temp_para["Preposition_operand_type"] == "1") {
			in_register.push(temp_para["Preposition_operand_value"]);
		}
		temp_para["Middle_operator"] = sen_para[i]["Middle_operator"];
		temp_para["Operator_extra_attr"] = sen_para[i]["Operator_extra_attr"];
		temp_para["Postposition_operand_type"] = sen_para[i]["Postposition_operand_type"];
		temp_para["Postposition_operand_value"] = sen_para[i]["Postposition_operand_value"];
		if (temp_para["Postposition_operand_type"] == "1") {
			in_register.push(temp_para["Postposition_operand_value"]);
		}
		temp_para["Output_type"] = sen_para[i]["Output_type"];
		temp_para["Output_value"] = sen_para[i]["Output_value"];
		if (temp_para["Output_type"] != "1") {
			output_flag++;
		} else {
			out_register.push(temp_para["Output_value"]);
		}
		temp_para["Output_alias"] = sen_para[i]["Output_alias"];
		temp_para["Output_default_status"] = sen_para[i]["Output_default_status"];
		datas = object_copy(temp_para);
		para.push(datas);
	}

	if (output_flag == 0) {
		alert(mainvalue.i18nkeyword.plc.no_correct_output);
		return;
	} else if (output_flag > 1){
		alert(mainvalue.i18nkeyword.plc.too_much_output);
		return;
	}

	if (true == is_arry_repeat(out_register)) {
		alert(mainvalue.i18nkeyword.plc.output_register_check);
		return;
	}

	if (false == is_include_array(in_register, out_register)) {
		alert(mainvalue.i18nkeyword.plc.input_register_check);
		return;
	}

	var setPlcGroup = {data:{objectid:"pdt_plc",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:setPlcGroupSucc};
	request.addRequest([setPlcGroup]);
	showDataInit();

	function setPlcGroupSucc(d,r) {
		hideDataInit();
		if(d.result == "ok"){
			var newobj = JSON.parse(r.data.paraval);
			if (newobj[0]["Group_no"] != "-1") {
				var id = $(editGobj).closest(".widget-box").attr("id")
				if($("#" + id + ">.widget-body").css("display") != "none") {
					$(editGobj).css("display", "");
				}
			}
			$("#addPLCBT").css("display", "");
			$("#editPLC").slideUp(200);
			$("#editLine").slideUp(200);
			get_sen_data(d.data[0]["Group_no"], "-1");   //刷新数据
			popupTipsDiv($("#setSuccessalart"), 1000);
		} else {
			alert(mainvalue.i18nkeyword.operate_failure);
		}
	}
}

function addPLC(){
	$("#addPLCBT").css("display", "none");
	$("#editPLC").slideDown(200);
	$("#addLineBT").css("display", "");
	$("#alterLineBT").css("display", "none");
	$("#delLineBT").css("display", "none");
	vmodel.add_decplc_datas = object_copy(default_add_decplc_datas);
	vmodel.add_senplc_block_dic.clear();
}

function addLine(){
	clear_radio_show();

	vmodel.add_type = 0;
	vmodel.add_sen_datas = object_copy(default_add_sen_datas);
	vmodel.add_sen_datas["Group_no"] = vmodel.add_decplc_datas["Group_no"];

	$("#editLine").slideDown(200);
	$("#addLineBT").css("display", "none");
	$("#alterLineBT").css("display", "none");
	$("#delLineBT").css("display", "none");
	vmodel.select_flag = "1";
}

function alterLine(){
	var i = $("input[name='line'][checked]").closest("tr").index();
	if(i >= 0){
		vmodel.add_type = 1;
		vmodel.add_sen_datas = object_copy(vmodel.add_senplc_block_dic[i]);
		$("#editLine").slideDown(200);
		$("#addLineBT").css("display", "none");
		$("#alterLineBT").css("display", "none");
		$("#delLineBT").css("display", "none");
		vmodel.select_flag = "1";
		get_init_plc_relevancesid_info();
		get_input_value();
	}
}

function delLine(){
	var i = $("input[name='line'][checked]").closest("tr").index();
	if(i >= 0){
		if(confirm(mainvalue.i18nkeyword.delete_confirm)){
			var tmpdata = object_copy(vmodel.add_senplc_block_dic);
			tmpdata.splice(i,1);
			vmodel.add_senplc_block_dic = object_copy(tmpdata);
			$("#editLine").slideUp(200);
			if($("#editPLC_data").children("tr").length > 0){
				$("#alterLineBT").css("display", "");
				$("#delLineBT").css("display", "");
			}
			else{
				$("#alterLineBT").css("display", "none");
				$("#delLineBT").css("display", "none");
			}
			$("#addLineBT").css("display", "");
			clear_radio_show();
		}
	}
}

function check_plc_name_valid(str) {
    str = str.trim();
    if (getStrLeng_UTF8(str) > 128) {
        return 0;
    }
    var danger_char_list = ["/",";","&"," "];
    for (var i in danger_char_list) {
        if (checkStringStr(str, danger_char_list[i])) {
            return 1;
        }
    }
    return 2;
}

function editLineOK(){
	var i = $("input[name='line'][checked]").closest("tr").index();
	if (vmodel.add_sen_datas["Preposition_operand_value"] == "" || vmodel.add_sen_datas["Output_value"] == "" ||
		((vmodel.add_sen_datas["Middle_operator"] != "2" && vmodel.add_sen_datas["Middle_operator"] != "7") && vmodel.add_sen_datas["Postposition_operand_value"] == "") ||
		((vmodel.add_sen_datas["Middle_operator"] == "3" || vmodel.add_sen_datas["Middle_operator"] == "4") && vmodel.add_sen_datas["Operator_extra_attr"] == "")) {
		alert(mainvalue.i18nkeyword.plc.data_empty);
		return;
	}

	if (vmodel.add_sen_datas["Middle_operator"] == "5" &&
		((vmodel.add_sen_datas['Preposition_operand_type'] == '0' && vmodel.add_sen_datas['Preposition_signal_type'] == '1') ||
		(vmodel.add_sen_datas['Postposition_operand_type'] == '0' && vmodel.add_sen_datas['Postposition_signal_type'] == '1'))) {
			alert(mainvalue.i18nkeyword.plc.EQ_check);
		return;
	}

	if (vmodel.add_sen_datas["Middle_operator"] == "6" &&
		((vmodel.add_sen_datas['Preposition_operand_type'] == '0' && vmodel.add_sen_datas['Preposition_signal_type'] == '1') ||
		(vmodel.add_sen_datas['Postposition_operand_type'] == '0' && vmodel.add_sen_datas['Postposition_signal_type'] == '1'))) {
			alert(mainvalue.i18nkeyword.plc.NE_check);
		return;
	}

	if (vmodel.add_sen_datas["Middle_operator"] == "7" &&
		(vmodel.add_sen_datas['Preposition_operand_type'] != '0' || vmodel.add_sen_datas['Preposition_signal_type'] != '3')) {
		alert(mainvalue.i18nkeyword.plc.PAR_check);
		return;
	}

	if (!vmodel.judge_plc_alarm_sig(vmodel.add_sen_datas["Output_value"])) { //  如果关联信号不是PLC告警，则清除别名
		vmodel.add_sen_datas["Output_alias"] = "";
	} else if ( 0 == check_plc_name_valid(vmodel.add_sen_datas["Output_alias"])){
		alert(mainvalue.i18nkeyword.plc.alias_too_long);
		return;
	} else if ( 2 != check_plc_name_valid(vmodel.add_sen_datas["Output_alias"])){
		alert(mainvalue.i18nkeyword.plc.wrong_alias);
		return;
	}
	vmodel.select_flag = "0";				//该位置要早于geteditstring函数，以防数据处理过程中数据变化
	geteditstring(vmodel.add_sen_datas);
	var sen_datas = object_copy(vmodel.add_sen_datas);
	if (vmodel.add_type == "0") {
		vmodel.add_senplc_block_dic.push(sen_datas);
	} else {
		var tmpdata = object_copy(vmodel.add_senplc_block_dic);
		tmpdata.splice(i,1,sen_datas);
		vmodel.add_senplc_block_dic = object_copy(tmpdata);
	}

	$("#editLine").slideUp(200);
	if($("#editPLC_data").children("tr").length > 0){
		$("#alterLineBT").css("display", "");
		$("#delLineBT").css("display", "");
	}
	else{
		$("#alterLineBT").css("display", "none");
		$("#delLineBT").css("display", "none");
	}
	$("#addLineBT").css("display", "");
}

function editLineCancel(){
	$("#editLine").slideUp(200);
	if($("#editPLC_data").children("tr").length > 0){
		$("#alterLineBT").css("display", "");
		$("#delLineBT").css("display", "");
	}
	else{
		$("#alterLineBT").css("display", "none");
		$("#delLineBT").css("display", "none");
	}
	$("#addLineBT").css("display", "");
	clear_radio_show();
	vmodel.select_flag = "0";
}
/*********************************************PLC参数导入导出***********************************************************/
function pswd_strong_check(pswd) {
    var strongRegex = new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$", "g");
    if (strongRegex.test(pswd)) {
        return true;
    }
    return false;
}

function check_string_length(str) {
    str = str.trim();
    if (getStrLeng_UTF8(str) > 31) {
        return false;
    }
    return true;
}
function disableBT(flag) {
	if (flag) {
		$("#dl_tip").show();
		$("#dl_para").attr("disabled", "disabled");
	} else {
		$("#dl_tip").hide();
		$("#dl_para").removeAttr("disabled");
	}
}

function doDL_para() {
	if ($("input[name='export_plc_para_box']").prop("checked")) {
		var password = vmodel.plc_para_export_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.plc_para_export_passwd = "";
			return false;
		}
		if (!pswd_strong_check(password)) {
			alert(password + " " + mainvalue.i18nkeyword.north_protocol.pswd_tip);
			vmodel.plc_para_export_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	disableBT(true);
	var paras = [{"encrypswd":password}];
	var q = {data:{objectid:"paramaintain_plc",type:"val_get",paraval:JSON.stringify(paras)},success:paraexphandler};
	request.addRequest([q]);
}

function paraexphandler(d, r) {
    if (d.result == "ok" && d.data.length > 0) {
        download_file_by_url(d.data[0].fileurl, d.data[0].filename);
	} else {
		alert(mainvalue.i18nkeyword.system.export_failed);
	}
	disableBT(false);
	vmodel.plc_para_export_passwd = "";
}

function check_paraup_result(d,r) {
	var result;
	setTimeout(function () {
		$("#up_plcpara_bt").attr("disabled", false);
	}, 5000);
	vmodel.plc_para_import_passwd = "";
	$("#id-plcpara-file~.remove").trigger("click");
	if (typeof(d)=="undefined") {
		hideDataInit();
		normalheart.start();
		$('#id-plcpara-file').val("");
		alert(mainvalue.i18nkeyword.update_failure);
		return false;
	} else {
		result = JSON.parse(d);
	}
	if(result.result === 'error'|| result.objectid ==="para_inspect_er") {
		hideDataInit();
		normalheart.start();
		$('#id-plcpara-file').val("");
		vmodel.para_verify_value = [];
		vmodel.para_verify_value = result.data;
		vmodel.para_err_type = 0;
		vmodel.show_more_info_tip = 0;
		if (vmodel.para_verify_value.length != 0) {
			if (result.data[0].hasOwnProperty("para verify")) {
				vmodel.para_err_type = 1;
			} else {
				vmodel.para_err_type = 2;
			}
			if (vmodel.para_verify_value.length > 5) {
				vmodel.show_more_info_tip = 1;
			} else {
				vmodel.show_more_info_tip = 0;
			}
		}
		alert(mainvalue.i18nkeyword.abnormal_data_upload);
		return false;
	} else {
		return true;
	}
}

function uploadplcpara() {
	var upload_file_path = $("#id-plcpara-file").val();
	if(upload_file_path == "" || vmodel.buttonflag == 1){
		return false;
	}
	if(upload_file_path.indexOf(".zip") == -1) {
		$("#id-plcpara-file~.remove").trigger("click");
		popupTipsDiv($("#zip_failurealert"), 3000);
		return false;
	}
	if(!confirm(mainvalue.i18nkeyword.operate_reboot_confirm)) {
		return false;
	}
	// 增加导入加密功能
	if ($("input[name='up_plc_para_box']").prop("checked")) {
		var password = vmodel.plc_para_import_passwd;
		if (password == '') {
			alert(mainvalue.i18nkeyword.pswd_empty);
			return false;
		}
		if (!check_string_length(password)) {
			alert(mainvalue.i18nkeyword.north_protocol.pswd_toolong_tip);
			vmodel.plc_para_import_passwd = "";
			return false;
		}
	} else {
		var password = "";
	}
	var paras = [{"encrypswd":password}];
	vmodel.plc_para_paraval = JSON.stringify(paras);
	$("#up_plcpara_bt").attr("disabled", "disabled");
	showDataInit();
	normalheart.stop();
	importheart.start();
	$("#plcpara_up").ajaxSubmit(check_paraup_result);
	return false;   //必须返回false否则将跳转到新界面
}

function change_butonflag() {
	vmodel.buttonflag = 0;
}

function download_abnormal_data() {
	vmodel.buttonflag = 1;
	download_file_by_url(vmodel.para_verify_value[0].url,vmodel.para_verify_value[0].filename);
}

/*********************************************编辑框获取的数据*************************************************************/
//获取设备信号类型
function getinitDevType() {
	var devType = {data:{objectid:"signal_type",type:"attr_get",paranum:"0",paraval:JSON.stringify([{}])},success:getDevType};
	request.addRequest([devType]);
}

function getDevType(d,r){
	var array = [];
	if((d.result ==="ok")&&(d.datanum >0)){
		for (var i in d.data) {
			if (d.data[i].value == '1' || d.data[i].value == '2' || d.data[i].value == '3') {
				array.push(d.data[i]);
			}
		}
	}
	vmodel.classify_devType = array;
}

//   获取输入1的PLC关联信号的信息
function get_pre_plc_relevancesid_info(sid,usage) {
	var para = {"sid":sid.toString(), "usage":usage.toString()};
	var Rq = {data:{objectid:"signal_plc",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_pre_plc_relevancesid_succ};
	request.addRequest([Rq]);

	function get_pre_plc_relevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.pre_plc_relevance_device_signals.removeAll();
			addvalue(vmodel.pre_plc_relevance_device_signals,d.data,"sid");
		}
	}
}

function get_input_value(obj) {
	if (vmodel.add_sen_datas['Preposition_operand_type'] == '0' && vmodel.add_sen_datas['Preposition_operand_value'] != "") {
		get_preposition_signal_info(vmodel.add_sen_datas['Preposition_operand_value']);
	}
	if (vmodel.add_sen_datas['Postposition_operand_type'] == '0' && vmodel.add_sen_datas['Postposition_operand_value'] != "") {
		get_postposition_signal_info(vmodel.add_sen_datas['Postposition_operand_value']);
	}
}

function stop_input_value_refresh(obj) {
	var Rq = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}};
	request.clearRequest(Rq);
}

function start_preposition_value_refresh(obj) {
	if (vmodel.add_sen_datas['Preposition_operand_type'] == '0' && vmodel.add_sen_datas['Preposition_operand_value'] != ""){
		request.addRequest([input1_value]);
	}
}

// 初始化请求
var input1_value = {};

function get_preposition_signal_info(sid) {
	var para = {"sid":sid.toString()};
	input1_value = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},refresh:3,success:update_preposition_signal_info};
	request.clearRequest(input1_value);
	request.addRequest([input1_value]);
	start_postposition_value_refresh();

	function update_preposition_signal_info(d,r) {
		if (d.result == "ok") {
			var sigtype = calSidToDevType(d.data[0].sid);
			if (d.data[0].valid_tag == "0" || d.data[0].value == "val_invalid") {
				vmodel.preposition_value = mainvalue.i18nkeyword.value_invalid;
			} else if (typeof(d.data[0].value) != 'undefined') {
				if (d.data[0].unit != "" && typeof(d.data[0].unit) != 'undefined') {
					vmodel.preposition_value = d.data[0].value + d.data[0].unit;
				} else if ( sigtype.sigType == "3") {
					if (d.data[0].value == '0') {
						vmodel.preposition_value = d.data[0].value + '(' + mainvalue.i18nkeyword.plc.no_alarm + ')';
					} else {
						vmodel.preposition_value = d.data[0].value + '(' + mainvalue.i18nkeyword.plc.alarm + ')';
					}
				} else if (d.data[0].convention != "" && typeof(d.data[0].convention) != 'undefined') {
					var convention = transformToJson(d.data[0].convention);
					vmodel.preposition_value = d.data[0].value + '(' + convention[d.data[0].value] + ')';
				} else {
					vmodel.preposition_value = d.data[0].value;
				}
			} else {
				vmodel.preposition_value = "";
			}
		} else {
			vmodel.preposition_value = "";
		}
		// 重置取值约定
		if (d.data[0].convention == "" || typeof(d.data[0].convention) == 'undefined') {
			vmodel.preposition_convention = "";
		} else {
			vmodel.preposition_convention = mainvalue.i18nkeyword.plc.input_one + " " + mainvalue.i18nkeyword.devlist.value_convention + ":  " + d.data[0].convention;
		}
	}
}

vmodel.$watch("add_sen_datas['Preposition_device_sid']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.pre_plc_relevance_device_signals.removeAll();
		if (vmodel.add_sen_datas['Preposition_device_sid'] !== "" && vmodel.add_sen_datas['Preposition_signal_type'] !== "") {
			get_pre_plc_relevancesid_info(vmodel.add_sen_datas['Preposition_device_sid'], vmodel.add_sen_datas['Preposition_signal_type']);
		}
		vmodel.add_sen_datas['Preposition_operand_value'] = "";
	}
});

vmodel.$watch("add_sen_datas['Preposition_signal_type']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.pre_plc_relevance_device_signals.removeAll();
		if (vmodel.add_sen_datas['Preposition_device_sid'] !== "" && vmodel.add_sen_datas['Preposition_signal_type'] !== "") {
			get_pre_plc_relevancesid_info(vmodel.add_sen_datas['Preposition_device_sid'], vmodel.add_sen_datas['Preposition_signal_type']);
		}
		vmodel.add_sen_datas['Preposition_operand_value'] = "";
	}
});

vmodel.$watch("add_sen_datas['Preposition_operand_value']", function(a) {
	if (vmodel.select_flag == "1" && vmodel.add_sen_datas['Preposition_operand_type'] == '0') {
		if (vmodel.add_sen_datas['Preposition_operand_value'] != "") {
			get_preposition_signal_info(vmodel.add_sen_datas['Preposition_operand_value']);
		} else {
			stop_input_value_refresh();
			start_postposition_value_refresh();
			vmodel.preposition_value = "";
			vmodel.preposition_convention = "";
		}
	}
});

//   获取输入2的PLC关联信号的信息
function get_post_plc_relevancesid_info(sid,usage) {
	var para = {"sid":sid.toString(), "usage":usage.toString()};
	var Rq = {data:{objectid:"signal_plc",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},success:get_post_plc_relevancesid_succ};
	request.addRequest([Rq]);

	function get_post_plc_relevancesid_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.post_plc_relevance_device_signals.removeAll();
			addvalue(vmodel.post_plc_relevance_device_signals,d.data,"sid");
		}
	}
}

function start_postposition_value_refresh(obj) {
	if (vmodel.add_sen_datas['Postposition_operand_type'] == '0' && vmodel.add_sen_datas['Postposition_operand_value'] != ""){
		request.addRequest([input2_value]);
	}
}

// 初始化请求
var input2_value = {};

function get_postposition_signal_info(sid) {
	var para = {"sid":sid.toString()};
	input2_value = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify([para])},refresh:3,success:update_postposition_signal_info};
	request.clearRequest(input2_value);
	request.addRequest([input2_value]);
	start_preposition_value_refresh();

	function update_postposition_signal_info(d,r) {
		if (d.result == "ok") {
			var sigtype = calSidToDevType(d.data[0].sid);
			if (d.data[0].valid_tag == "0" || d.data[0].value == "val_invalid") {
				vmodel.postposition_value = mainvalue.i18nkeyword.value_invalid;
			} else if (typeof(d.data[0].value) != 'undefined') {
				if (d.data[0].unit != "" && typeof(d.data[0].unit) != 'undefined') {
					vmodel.postposition_value = d.data[0].value + d.data[0].unit;
				} else if ( sigtype.sigType == "3") {
					if (d.data[0].value == '0') {
						vmodel.postposition_value = d.data[0].value + '(' + mainvalue.i18nkeyword.plc.no_alarm + ')';
					} else {
						vmodel.postposition_value = d.data[0].value + '(' + mainvalue.i18nkeyword.plc.alarm + ')';
					}
				} else if (d.data[0].convention != "" && typeof(d.data[0].convention) != 'undefined') {
					var convention = transformToJson(d.data[0].convention);
					vmodel.postposition_value = d.data[0].value + '(' + convention[d.data[0].value] + ')';
				} else {
					vmodel.postposition_value = d.data[0].value;
				}
			} else {
				vmodel.postposition_value = "";
			}
		} else {
			vmodel.postposition_value = "";
		}
		// 重置取值约定
		if (d.data[0].convention == "" || typeof(d.data[0].convention) == 'undefined') {
			vmodel.postposition_convention = "";
		} else {
			vmodel.postposition_convention = mainvalue.i18nkeyword.plc.input_two + " " + mainvalue.i18nkeyword.devlist.value_convention + ":  " + d.data[0].convention;;
		}
	}
}

vmodel.$watch("add_sen_datas['Postposition_device_sid']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.post_plc_relevance_device_signals.removeAll();
		if (vmodel.add_sen_datas['Postposition_device_sid'] !== "" && vmodel.add_sen_datas['Postposition_signal_type'] !== "") {
			get_post_plc_relevancesid_info(vmodel.add_sen_datas['Postposition_device_sid'], vmodel.add_sen_datas['Postposition_signal_type']);
		}
		vmodel.add_sen_datas['Postposition_operand_value'] = "";
	}
});

vmodel.$watch("add_sen_datas['Postposition_signal_type']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.post_plc_relevance_device_signals.removeAll();
		if (vmodel.add_sen_datas['Postposition_device_sid'] !== "" && vmodel.add_sen_datas['Postposition_signal_type'] !== "") {
			get_post_plc_relevancesid_info(vmodel.add_sen_datas['Postposition_device_sid'], vmodel.add_sen_datas['Postposition_signal_type']);
		}
		vmodel.add_sen_datas['Postposition_operand_value'] = "";
	}
});

vmodel.$watch("add_sen_datas['Postposition_operand_value']", function(a) {
	if (vmodel.select_flag == "1" && vmodel.add_sen_datas['Postposition_operand_type'] == '0') {
		if (vmodel.add_sen_datas['Postposition_operand_value'] != "") {
			get_postposition_signal_info(vmodel.add_sen_datas['Postposition_operand_value']);
		} else {
			stop_input_value_refresh();
			start_preposition_value_refresh();
			vmodel.postposition_value = "";
			vmodel.postposition_convention = "";
		}
	}
});

function get_init_plc_relevancesid_info() {
	if (vmodel.add_sen_datas['Preposition_device_sid'] != "" && typeof(vmodel.add_sen_datas['Preposition_device_sid']) != 'undefined' &&
		vmodel.add_sen_datas['Preposition_signal_type'] != "" && typeof(vmodel.add_sen_datas['Preposition_signal_type']) != 'undefined') {
		get_pre_plc_relevancesid_info(vmodel.add_sen_datas['Preposition_device_sid'], vmodel.add_sen_datas['Preposition_signal_type']);
	}
	if (vmodel.add_sen_datas['Postposition_device_sid'] != "" && typeof(vmodel.add_sen_datas['Postposition_device_sid']) != 'undefined' &&
		vmodel.add_sen_datas['Postposition_signal_type'] != "" && typeof(vmodel.add_sen_datas['Postposition_signal_type']) != 'undefined') {
		get_post_plc_relevancesid_info(vmodel.add_sen_datas['Postposition_device_sid'], vmodel.add_sen_datas['Postposition_signal_type']);
	}
}

function get_output_plc_alarm_info() {
	var para = {"usage":"0"};
	var Rq = {data:{objectid:"signal_plc",type:"attr_get",paranum:"1",paraval:JSON.stringify([para])},success:get_output_plc_alarm_info_succ};
	request.addRequest([Rq]);

	function get_output_plc_alarm_info_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.plc_custom_alarm_signals.removeAll();
			addvalue(vmodel.plc_custom_alarm_signals,d.data,"sid");
		}
	}
}

function get_output_plc_alarm_info() {
	var para = {"usage":"0"};
	var Rq = {data:{objectid:"signal_plc",type:"attr_get",paranum:"1",paraval:JSON.stringify([para])},success:get_output_plc_alarm_info_succ};
	request.addRequest([Rq]);

	function get_output_plc_alarm_info_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.output_plc_custom_alarm_signals.removeAll();
			addvalue(vmodel.output_plc_custom_alarm_signals,d.data,"sid");
		}
	}
}

function get_output_plc_do_info() {
	var para = {"usage":"2"};
	var Rq = {data:{objectid:"signal_plc",type:"attr_get",paranum:"1",paraval:JSON.stringify([para])},success:get_output_plc_do_info_succ};
	request.addRequest([Rq]);

	function get_output_plc_do_info_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.output_plc_digital_do_signals.removeAll();
			addvalue(vmodel.output_plc_digital_do_signals,d.data,"sid");
		}
	}
}

function get_register_plc_info() {
	var Rq = {data:{objectid:"register_plc",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},success:get_register_plc_info_succ};
	request.addRequest([Rq]);

	function get_register_plc_info_succ(d,r) {
		if (d.result == 'ok') {
			vmodel.plc_register_info.removeAll();
			addvalue(vmodel.plc_register_info,d.data,"register_no");
		}
	}
}

//编辑框选择时清除数据
vmodel.$watch("add_sen_datas['Preposition_operand_type']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.add_sen_datas['Preposition_device_sid'] = "";
		vmodel.add_sen_datas['Preposition_signal_type'] = "";
		vmodel.add_sen_datas['Preposition_operand_value'] = "";
		vmodel.preposition_value = "";
	}
});

vmodel.$watch("add_sen_datas['Postposition_operand_type']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.add_sen_datas['Postposition_device_sid'] = "";
		vmodel.add_sen_datas['Postposition_signal_type'] = "";
		vmodel.add_sen_datas['Postposition_operand_value'] = "";
		vmodel.postposition_value = "";
	}
});

vmodel.$watch("add_sen_datas['Output_type']", function(a) {
	if (vmodel.select_flag == "1") {
		vmodel.add_sen_datas['Output_value'] = "";
		vmodel.add_sen_datas['Output_default_status'] = "1";
	}
});

// 编辑框消失后去除轮询请求数据
vmodel.$watch("select_flag", function(a) {
	if (vmodel.select_flag == "0") {
		stop_input_value_refresh();
		vmodel.preposition_value = "";
		vmodel.postposition_value = "";
		vmodel.preposition_convention = "";
		vmodel.postposition_convention = "";
	}
});

/************************************************PLC使能状态************************************************************/
function getPlcStatus() {
	var getPlcState = {data:{objectid:"status_plc",type:"val_get",paranum:"0",paraval:JSON.stringify([{}])},success:getPlcStateSucc};
	request.addRequest([getPlcState]);
}

function getPlcStateSucc(d,r){
	if (d.result == "ok") {
		var plc_input_state = Boolean(d.data[0].status*1);   // 获取PLC使能状态
		$("#plc_switch").attr("checked", plc_input_state);
	}
}

function plc_input_click() {
	var plc_input_state = $("#plc_switch").prop('checked');
	var sta = (plc_input_state)* 1;
	var paraval = [{"action":String(sta)}];
	var setPlcState = {data:{objectid:"status_plc",type:"val_set",paranum:"1",paraval:JSON.stringify(paraval)},success:setPlcStateSucc};
	request.addRequest([setPlcState]);
}

function setPlcStateSucc(d,r){
	if(d.result === "ok"){
		return;
	} else {
		popupTipsDiv($("#setFailurealart"), 2000);
		getPlcStatus();
		return;
	}
}

/************************************************初始化请求************************************************************/
getPlcStatus();					//PLC使能状态获取
getPlcdata();					//获取PLC组数据
get_dev_list_all();				//获取设备列表
getinitDevType();				//获取输入设备类型选择
get_output_plc_alarm_info();	//获取输出列表告警
get_output_plc_do_info();		//获取输出DO信号
get_register_plc_info();		//获取寄存器列表


/************************************************页面跳转函数**************************************************************************/
function init_tab_select() {
	var tab = Cookies.get("tab_list");
	if (tab == "plc_data" || tab == "plcparaMaintain") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "plc_data";
		set_cookie_with_path("tab_list", vmodel.tab_list);
	}
}
init_tab_select();
////////////////////////////***加密密码框是否展示***///////////////////////////////////
function whether_to_encrypt(obj) {
	if ($(obj).is(":checked")) {
		$(obj).next().removeAttr("disabled");
	} else {
		$(obj).next().attr("disabled","disabled");
		$(obj).next().val("");
	}
}
////////////////////////////***导出按钮布局***////////////////////////////////////////
function page_show() {
	if (language == "zh_CN.UTF-8") {
		$("#dl_para").width(305);
	} else {
		$("#dl_para").width(406);
	}
}
$(document).ready(function(){
	page_show();
})
