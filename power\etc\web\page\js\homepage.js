//线段及信息框颜色
var line_blue    = "rgb(78, 146, 223)";
var block_green  = "rgb(37, 155, 36)";

//设备图片
var normal_dg      = "/page/assets/img/dg.png";
var normal_mains   = "/page/assets/img/mains.png";
var normal_power   = "/page/assets/img/power.png";
var normal_load    = "/page/assets/img/load.png";
var normal_battery = "/page/assets/img/battery.png";
var normal_solar   = "/page/assets/img/solar.png"

var waring_dg      = "/page/assets/img/red.dg.png";
var waring_mains   = "/page/assets/img/red.mains.png";
var waring_power   = "/page/assets/img/red.power.png";
var waring_load    = "/page/assets/img/red.load.png";
var waring_battery = "/page/assets/img/red.battery.png";
var waring_solar   = "/page/assets/img/red.solar.png"

//设备及状态
var dg      = {"normal":normal_dg, "waring":waring_dg};
var mains   = {"normal":normal_mains, "waring":waring_mains};
var power   = {"normal":normal_power, "waring":waring_power};
var load    = {"normal":normal_load, "waring":waring_load};
var battery = {"normal":normal_battery, "waring":waring_battery};
var solar   = {"normal":normal_solar, "waring":waring_solar};

var dev_sta = [];

var hp_vmodel = avalon.define({
    $id : "homepage",
    csu_scan_info:{"csu_version":""},                   //CSU版本号
    env_scan_info:{"temperature":"", "humidity":""},    //环境信息
    battery_group_status:"",                            //电池管理状态
    ac_input:[{attr:"",value:""},{attr:"",value:""}],   //交流输入框信息
    pu_input:[{attr:"",value:""},{attr:"",value:""}],   //solar输入框信息
    dc_output:[{attr:"",value:""},{attr:"",value:""}],  //直流输出框信息
    dev_info:"",                                        //各个设备详细信息
});

var c = document.getElementById("plot_canvas");
var ctx = c.getContext("2d");

//设备类
function devinfo(devname, sta, x, y, w, h, line_len, info, sid) {
    this.devname  = devname;
    this.sta      = sta;
    this.x        = x;
    this.y        = y;
    this.w        = w;
    this.h        = h;
    this.line_len = line_len;  //线段长度
    this.info     = "";
}
var dev_mains_1_x = 120;
var dev_mains_1_y = 50;
//设备状态
var dev_mains_1   = new devinfo(mains, 'normal',   dev_mains_1_x,      dev_mains_1_y,     110, 110, 200);
var dev_mains_2   = new devinfo(mains, 'normal',   dev_mains_1_x+190,  dev_mains_1_y+0,   110, 110, 200);
var dev_power     = new devinfo(power, 'normal',   dev_mains_1_x+420,  dev_mains_1_y+170, 120, 120, 0);
var dev_load_1    = new devinfo(load, 'normal',    dev_mains_1_x+760,  dev_mains_1_y+0,   120, 120, 160);
var dev_battery_1 = new devinfo(battery, 'normal', dev_mains_1_x+600,  dev_mains_1_y+350, 85, 85, -50);
var dev_battery_2 = new devinfo(battery, 'normal', dev_mains_1_x+690,  dev_mains_1_y+350, 85, 85, -50);
var dev_battery_3 = new devinfo(battery, 'normal', dev_mains_1_x+780,  dev_mains_1_y+350, 85, 85, -50);
var dev_battery_4 = new devinfo(battery, 'normal', dev_mains_1_x+870,  dev_mains_1_y+350, 85, 85, -50);
var dev_battery_5 = new devinfo(battery, 'normal', dev_mains_1_x+960,  dev_mains_1_y+350, 85, 85, -50);
var dev_battery_6 = new devinfo(battery, 'normal', dev_mains_1_x+1050, dev_mains_1_y+350, 85, 85, -50);
var dev_battery_7 = new devinfo(battery, 'normal', dev_mains_1_x+1140, dev_mains_1_y+350, 85, 85, -50);
var dev_battery_8 = new devinfo(battery, 'normal', dev_mains_1_x+1230, dev_mains_1_y+350, 85, 85, -50);
var dev_solar     = new devinfo(solar, 'normal',   dev_mains_1_x,      dev_mains_1_y+330, 110, 110, -70);
var dev_dg_1      = new devinfo(dg, 'normal',      dev_mains_1_x+190,  dev_mains_1_y+330, 110, 110, -60);
var dev_dg_2      = new devinfo(dg, 'normal',      dev_mains_1_x+190,  dev_mains_1_y+330, 110, 110, -60);

//设备实例数组
var dev_arry = [[dev_mains_1, dev_mains_2], [dev_power], [dev_load_1],
                [dev_battery_1, dev_battery_2, dev_battery_3, dev_battery_4, dev_battery_5, dev_battery_6, dev_battery_7, dev_battery_8],
                [dev_solar], [dev_dg_1, dev_dg_2]];
//获取设备状态及信息
var Rq = {data:{objectid:"device_topology",type:"val_get",paraval:JSON.stringify(paraval)},refresh:10, success:get_dev_info};
request.addRequest([Rq]);

function get_dev_info(d,r) {
    ctx.clearRect(0, 0, 1300, 550);
    if (d.result == 'ok') {
        //dev_info_arry：每个设备实例信息
        var dev_info_arry = {'1':['','',''], '2':['','',''], '3':['','',''], '4':['','',''], '5':['','',''], '6':['','',''], '7':['','',''], '8':['','',''], '9':['','',''], '10':['','',''], '11':['','',''], '12':['','',''], '13':['','',''], '14':['','',''], '15':['','','']}; //'设备实例编号':['设备在线状态','设备数据信息','设备名']
        var dev_tmp = [dev_mains_1, dev_mains_2, dev_power, dev_battery_1, dev_battery_2, dev_battery_3, dev_battery_4, dev_battery_5, dev_battery_6, dev_battery_7, dev_battery_8, dev_load_1, dev_solar, dev_dg_1, dev_dg_2];
        hp_vmodel.dev_info = d.data;
        var value_unit;
        for(var i=0; i<d.data.length; i++) {
            //设备分类
            if(hp_vmodel.dev_info[i].valid == '1'){
                if( typeof(hp_vmodel.dev_info[i].full_name) != 'undefined') {
                    /* 值无效不显示 */
                    if (hp_vmodel.dev_info[i].value != 'val_invalid') {
                        /* 添加单位 */
                        if (typeof(hp_vmodel.dev_info[i].unit) != 'undefined') {
                            value_unit = hp_vmodel.dev_info[i].value + hp_vmodel.dev_info[i].unit;
                        } else {
                            value_unit = hp_vmodel.dev_info[i].value;
                        }
                    } else {
                        value_unit = '';
                    }
                    /* 存放每个sid的数据信息 */
                    dev_info_arry[hp_vmodel.dev_info[i].device_tag][1] += (hp_vmodel.dev_info[i].full_name + ':' + value_unit + '<br/>'); 
                }
                dev_info_arry[hp_vmodel.dev_info[i].device_tag][2] = hp_vmodel.dev_info[i]['device name'];  //存放设备实例设备名
                if(hp_vmodel.dev_info[i].alarm <= '0' && dev_info_arry[hp_vmodel.dev_info[i].device_tag][0] != 'waring'){
                    dev_info_arry[hp_vmodel.dev_info[i].device_tag][0] = 'normal';
                } else {
                    dev_info_arry[hp_vmodel.dev_info[i].device_tag][0] = 'waring';
                }
            } else {
                dev_info_arry[hp_vmodel.dev_info[i].device_tag][0] = 'invalid';
            }
        }

        //对设备实例赋值
        var a = 0;
        for(var key in dev_info_arry){
            dev_tmp[a].sta = dev_info_arry[key][0];
            dev_tmp[a].info = '#' + dev_info_arry[key][2] + '<br/>' + dev_info_arry[key][1];
            a++;
        }
        //绘制设备图片
        for (var i=0; i<6; i++){
            for (var dev in dev_arry[i]){
                dev_draw(dev_arry[i][dev]);
            }
        }

        //绘制市电接线
        for(var index_mains in dev_arry[0]){
            var draw_x = dev_mains_1_x+420;
            if(draw_x > dev_arry[0][index_mains].x && dev_arry[0][index_mains].sta != 'invalid' && dev_arry[0][index_mains].sta != '' )
                draw_x = dev_arry[0][index_mains].x + dev_arry[0][index_mains].w/2 - 1;
            line_draw(draw_x, dev_mains_1_y+200, dev_mains_1_x+420, dev_mains_1_y+200, line_blue);
        }

        //绘制solar接线
        var draw_x = dev_mains_1_x+420;
        if (dev_arry[4][0].sta != 'invalid' && dev_arry[4][0].sta != '') {
            draw_x = dev_arry[4][0].x + dev_arry[4][0].w/2 - 1;
        }
        line_draw(draw_x, dev_mains_1_y+260, dev_mains_1_x+420, dev_mains_1_y+260, line_blue);

        //绘制油机接线
        var draw_x = dev_mains_1_x+420;
        if((dev_arry[5][0].sta != 'invalid' && dev_arry[5][0].sta != '') ||
           (dev_arry[5][1].sta != 'invalid' && dev_arry[5][1].sta != '')) {
            draw_x = dev_arry[5][0].x + dev_arry[5][0].w/2 - 1;
        }
        line_draw(draw_x, dev_mains_1_y+270, dev_mains_1_x+420, dev_mains_1_y+270, line_blue);

        //绘制负载/电池接线
        line_draw(dev_mains_1_x+510, dev_mains_1_y+230, dev_mains_1_x+570, dev_mains_1_y+230, line_blue);
        line_draw(dev_mains_1_x+570, dev_mains_1_y+140, dev_mains_1_x+570, dev_mains_1_y+320, line_blue);

        //负载设备接线
        var draw_x = dev_mains_1_x+570;
        for(var index_load in dev_arry[2]){
            if(draw_x < dev_arry[2][index_load].x && dev_arry[2][index_load].sta != 'invalid' && dev_arry[2][index_load].sta != '')
                draw_x = dev_arry[2][index_load].x + dev_arry[2][index_load].w/2 + 1;
        }
        line_draw(dev_mains_1_x+570, dev_mains_1_y+160, draw_x, dev_mains_1_y+160, line_blue);

        //电池组设备接线
        var draw_x = dev_mains_1_x+570;
        for(var index_battery in dev_arry[3]){
            if(draw_x < dev_arry[3][index_battery].x && dev_arry[3][index_battery].sta != 'invalid' && dev_arry[3][index_battery].sta != '')
                draw_x = dev_arry[3][index_battery].x + dev_arry[3][index_battery].w/2 + 1;
        }
        line_draw(dev_mains_1_x+570, dev_mains_1_y+300, draw_x, dev_mains_1_y+300, line_blue);

        //刷新显示设备信息
        dev_info_refresh();
    }
}

/***************************获取显示信息******************************/
//获取版本信息
var paraval = [{'sid':'563020820447233'}];
var Rq = {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},refresh:10,success:get_csu_info_succ};
request.addRequest([Rq]);

function get_csu_info_succ(d,r) {
    if (d.result == 'ok') {
        hp_vmodel.csu_scan_info.csu_version = d.data[0].value;
    }
}

//获取环境信息
var paraval = [{'sid':'2533343778373633'}, {'sid':'2533343778439169'}];
var Rq = {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},refresh:10, success:get_env_info_succ};
request.addRequest([Rq]);

function get_env_info_succ(d,r) {
    if (d.result == 'ok') {
        if (d.data[0].value != 'val_invalid') {
            hp_vmodel.env_scan_info.temperature = d.data[0].value*1.0 + (d.data[0].unit || "");
        }
        if (d.data[1].value != 'val_invalid') {
            hp_vmodel.env_scan_info.humidity = d.data[1].value*1.0  + (d.data[1].unit || "");
        }

        if (d.data[0].value == 'val_invalid' && d.data[1].value == 'val_invalid') {
            $("#env_info").hide();
        } else {
            $("#env_info").show();
        }
    }
}

//获取电池组管理信息
var paraval = [{'sid':'3377768976941057'}];
var Rq = {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},refresh:10, success:get_battery_info_succ};
request.addRequest([Rq]);

function get_battery_info_succ(d,r) {
    if (d.result == 'ok') {
        var convention = transformToJson(d.data[0].convention);
        hp_vmodel.battery_group_status = convention[d.data[0].value];
    }
}

//获取交流输入信息
var para = [{"sid":"844493918109697"},{"sid":"844493918175233"}];
var input_dev = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify(para)}, refresh:1, success:get_ac_intput};
request.addRequest([input_dev]);

function get_ac_intput(d,r) {
    if(d.result != 'ok')
        return;
    if(mainvalue.i18nkeyword.hasOwnProperty("plot")){
        hp_vmodel.ac_input[0].attr = mainvalue.i18nkeyword.plot.vol_input;
        hp_vmodel.ac_input[0].value = d.data[0].value + d.data[0].unit;
        hp_vmodel.ac_input[1].attr = mainvalue.i18nkeyword.plot.ele_input;
        hp_vmodel.ac_input[1].value = d.data[1].value + d.data[1].unit;

        if ((dev_mains_1.sta != 'invalid' && dev_mains_1.sta != '') || (dev_mains_2.sta != 'invalid' && dev_mains_2.sta != '')) {
            msg_box(dev_mains_1_x+265, dev_mains_1_y+210, block_green, hp_vmodel.ac_input);
        } else {
            clear_msg_box(dev_mains_1_x+265, dev_mains_1_y+210);
        }
    }
}

//获取PU输入信息
//var para =  [{"sid":"4222193638637569"},{"sid":"4222193638703105"}];
//var input_dev = {data:{objectid:"signal",type:"val_get",paranum:"1",paraval:JSON.stringify(para)}, refresh:1, success:get_pu_intput};
//request.addRequest([input_dev]);

function get_pu_intput(d,r) {
    if(d.result != 'ok')
        return;
    hp_vmodel.pu_input[0].attr = mainvalue.i18nkeyword.plot.solar_pu_total_out_curr;
    hp_vmodel.pu_input[0].value = d.data[0].value + d.data[0].unit;
    hp_vmodel.pu_input[1].attr = mainvalue.i18nkeyword.plot.solar_pu_total_out_power;
    hp_vmodel.pu_input[1].value = d.data[1].value + d.data[1].unit;

    if ((dev_solar.sta != 'invalid' && dev_solar.sta != '')) {
        msg_box(dev_mains_1_x+265, dev_mains_1_y+245, block_green, hp_vmodel.pu_input);
    } else {
        clear_msg_box(dev_mains_1_x+265, dev_mains_1_y+245);
    }
}

//获取直流输出信息
var paraval = [{'sid':'2814818755084289'},{'sid':'2814818755149825'},{'sid':'563018942513153'}];
var Rq = {data:{objectid:"signal",type:"val_get",paraval:JSON.stringify(paraval)},refresh:1, success:get_dc_output};
request.addRequest([Rq]);

function get_dc_output(d,r) {
    if(d.result != 'ok')
        return;
    if(mainvalue.i18nkeyword.hasOwnProperty("plot")){
        hp_vmodel.dc_output[0].attr = mainvalue.i18nkeyword.plot.vol_output;
        hp_vmodel.dc_output[0].value = d.data[0].value + d.data[0].unit;
        hp_vmodel.dc_output[1].attr = mainvalue.i18nkeyword.plot.ele_output;
        // 叠光场景显示直流输出电流， 非叠光场景显示直流负载电流， 两个sid不会同时有效
        hp_vmodel.dc_output[1].value = d.data[1].value == 'val_invalid'?(d.data[2].value + d.data[2].unit):(d.data[1].value + d.data[1].unit);

        msg_box(dev_mains_1_x+580, dev_mains_1_y+210, block_green, hp_vmodel.dc_output);
    }
}

/******************************绘制功能函数******************************/

/*将图片绘制到canvas
**imgURL：图片路径
* x、y：图片位置坐标
* w、h：图片大小
*/
function img_draw(imgUrl, x, y, w, h) {
    var img_obj = new Image();
    img_obj.src = imgUrl;
    //处理图片模糊的问题
    var getPixelRatio = function(context) {
        var backingStore = context.backingStorePixelRatio ||
            context.webkitBackingStorePixelRatio ||
            context.mozBackingStorePixelRatio ||
            context.msBackingStorePixelRatio ||
            context.oBackingStorePixelRatio ||
            context.backingStorePixelRatio || 1;

        return (window.devicePixelRatio || 1) / backingStore;
    };
    var ratio = getPixelRatio(ctx);

    img_obj.onload = function (){
        ctx.drawImage(img_obj, x, y, w*ratio, h*ratio);
    }
}
//function clean_img(x, y, w, h) {
//    ctx.clearRect(x, y, w, h);
//}
/*绘制设备间连线
**x_begin、y_begin:线段起点
* x_end、y_end     线段终点
* line_color      :线段颜色
*/
function line_draw(x_begin, y_begin, x_end, y_end, line_color) {
    ctx.beginPath();
    ctx.lineWidth = 2;
    ctx.lineJoin = "round";
    ctx.strokeStyle = line_color;
    ctx.moveTo(x_begin, y_begin);
    ctx.lineTo(x_end, y_end);
    ctx.stroke();
}

/*绘制信息框
**x、y:框的坐标
* fill_color:填充颜色
* info_input：信息内容
*/
function msg_box(x, y, fill_color, info_input) {
    ctx.fillStyle = fill_color;
    ctx.fillRect(x, y, 150, 40);
    ctx.fillStyle = "white";
    ctx.font = "12px Arial";
    ctx.fillText(info_input[0].attr + ' : ' + info_input[0].value, x+3, y+15);
    ctx.fillText(info_input[1].attr + ' : ' + info_input[1].value, x+3, y+30);
}

/*清空信息框
*x\y:左上角坐标
*/
function clear_msg_box(x, y) {
    ctx.clearRect(x, y, 150, 40);
}

/*按设备状态绘制设备*/
function dev_draw(devinfo) {
    switch (devinfo.sta) {
        case "normal":
            img_draw(devinfo.devname.normal, devinfo.x, devinfo.y, devinfo.w, devinfo.h);
            line_draw(devinfo.x + devinfo.w/2, devinfo.y, devinfo.x + devinfo.w/2, devinfo.y+devinfo.line_len, line_blue);
            break;
        case "waring":
            img_draw(devinfo.devname.waring, devinfo.x, devinfo.y, devinfo.w, devinfo.h);
            line_draw(devinfo.x + devinfo.w/2, devinfo.y, devinfo.x + devinfo.w/2, devinfo.y+devinfo.line_len, line_blue);
            break;
        default:
//            clean_img(devinfo.x, devinfo.y, devinfo.w, devinfo.h);
            break;
    }
}

//图片提示框标识符
var box_index = -1;
var box_dev = -1;
var mouse_x = 0;
var mouse_y = 0;
var box_style = 0;

//鼠标事件
plot_canvas.addEventListener("mousemove", function(event){
            box_index = -1;
            dev_info_show(plot_canvas, event);
        })

function dev_info_show(plot_canvas, event){
    var rect = plot_canvas.getBoundingClientRect();
    var x = event.clientX - rect.left * (plot_canvas.width / rect.width);
    var y = event.clientY - rect.top * (plot_canvas.height / rect.height);
    mouse_x = event.clientX;
    mouse_y = event.clientY;

    var obj_box=document.getElementById('box');
    if (y <= 300) {
        box_style = 0;
        obj_box.style.top = event.clientY - 10 + "px";
        obj_box.style.left = event.clientX + 15 + "px";
    } else {
        box_style = 1;
        obj_box.style.top = event.clientY - 100 + "px";
        obj_box.style.left = event.clientX + 15 + "px";
    }

    for(var i=0; i<6; i++){
        for(var dev in dev_arry[i]) {
            if(x>dev_arry[i][dev].x && y>dev_arry[i][dev].y && x<dev_arry[i][dev].x+dev_arry[i][dev].w && y<dev_arry[i][dev].y+dev_arry[i][dev].h && dev_arry[i][dev].sta != 'invalid' && dev_arry[i][dev].sta != ''){
                box_index = i;
                box_dev = dev;
                obj_box.style.display = 'block';
                hp_vmodel.dev_info=dev_arry[i][dev].info;
                return ;
            } else {
                box_index = -1;
                obj_box.style.display = 'none';
            }
        }
    }
}

function dev_info_refresh(){
    var obj_box=document.getElementById('box');
    if (box_style == "0") {
        obj_box.style.top = mouse_y - 10 + "px";
        obj_box.style.left = mouse_x + 15 + "px";
    } else {
        obj_box.style.top = mouse_y - 100 + "px";
        obj_box.style.left = mouse_x + 15 + "px";
    }

    if (box_index != "-1") {
        if(dev_arry[box_index][box_dev].sta != 'invalid' && dev_arry[box_index][box_dev].sta != ''){
            obj_box.style.display = 'block';
            hp_vmodel.dev_info=dev_arry[box_index][box_dev].info;
        } else {
            obj_box.style.display = 'none';
        }
    }
}

function check_def_login_pswd(){
    var input_dev = {data:{objectid:"is_login_def_pswd",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])}, success:check_def_login_pswd_succ};
    request.addRequest([input_dev]);
}

function check_def_login_pswd_succ(d, r){
    //使用了默认密码
    if((d.result =="ok") && (d.data[0].is_def_pswd == 1)){
        $("#def_pswd_tip").show();
        return;
	}
}

check_def_login_pswd();

/********************************* EEPROM配置确认 *********************************/
// EEPROM配置确认弹窗
var eeprom_popup1 = $.confirm({
    lazyOpen: true,
    title: false,
	content: "",
	boxWidth: '30%',
	useBootstrap: false,
    onOpenBefore: function () {
        this.setContent(mainvalue.i18nkeyword.eeprom.syn_csu_and_eeprom);
        this.buttons.confirm.setText(mainvalue.i18nkeyword.eeprom.confirm);
    },
	buttons: {
		confirm: {
			text: "",
			btnClass: 'btn-info',
			action: function () {
				eeprom_popup1.close();
                eeprom_popup2.open();
			}
		}
	},
});

var eeprom_popup2 = $.confirm({
    lazyOpen: true,
    title: false,
	content: "",
	boxWidth: '30%',
	useBootstrap: false,
    onOpenBefore: function () {
        this.setContent(mainvalue.i18nkeyword.eeprom.mac_address_change);
        this.buttons.confirm.setText(mainvalue.i18nkeyword.eeprom.confirm);
    },
	buttons: {
		confirm: {
			text: "",
			btnClass: 'btn-info',
			action: function () {
				eeprom_popup2.close();
				eeprom_popup3.open();
			}
		}
	},
});

var eeprom_popup3 = $.confirm({
    lazyOpen: true,
    title: false,
	content: "",
	boxWidth: '30%',
	useBootstrap: false,
    onOpenBefore: function () {
        this.setContent(mainvalue.i18nkeyword.eeprom.csu_and_eeprom_not_match);
        this.buttons.eeprom_to_csu.setText(mainvalue.i18nkeyword.eeprom.eeprom_to_csu);
        this.buttons.csu_to_eeprom.setText(mainvalue.i18nkeyword.eeprom.csu_to_eeprom);
        this.buttons.cancel_syn.setText(mainvalue.i18nkeyword.eeprom.cancel_syn);
        if (mainvalue.eeprom_select === "1") {
            this.buttons.eeprom_to_csu.hide();  // 隐藏EEPROM配置导入CSU按钮
        }
    },
	buttons: {
		eeprom_to_csu: {
			text: "",
			btnClass: 'btn-info',
			action: function () {
				var Rq1	= {data:{objectid:"eeprom_info",type:"val_set",paraval:JSON.stringify([{"action":"0","usage":"0"}])},success:eeprom_to_csu_result};
				request.addRequest([Rq1]);
				eeprom_popup3.close();
				showDataImport();
				normalheart.stop();
				importheart.start();
                FlagRq.data.paraval = JSON.stringify([{"eeprom_comfirm_flag": "1"}]);
                request.addRequest([FlagRq]); // 保存标记到后端，web进程重启，标记也相应被清除
			}
		},
		csu_to_eeprom: {
			text: "",
			btnClass: 'btn-info',
			action: function () {
				var Rq2	= {data:{objectid:"eeprom_info",type:"val_set",paraval:JSON.stringify([{"action":"1","usage":"0"}])},success:csu_to_eeprom_result};
				request.addRequest([Rq2]);
				eeprom_popup3.close();
				showDataInit();
                FlagRq.data.paraval = JSON.stringify([{"eeprom_comfirm_flag": "1"}]);
                request.addRequest([FlagRq]); // 保存标记到后端，web进程重启，标记也相应被清除
			}
		},
		cancel_syn: {
			text:"",
			btnClass: 'btn-info',
			action: function () {
				var Rq3	= {data:{objectid:"eeprom_info",type:"val_set",paraval:JSON.stringify([{"action":"2","usage":"0"}])}};
				request.addRequest([Rq3]);
				eeprom_popup3.close();
                FlagRq.data.paraval = JSON.stringify([{"eeprom_comfirm_flag": "2"}]);
                request.addRequest([FlagRq]); // 保存标记到后端，web进程重启，标记也相应被清除
			}
		}
	},
});

function eeprom_to_csu_result(d,r) {
    if (d.result === "ok") {
		if (d.data.length !== 0) {
			alert(d.data[0]['error_info']);
		}
	} else {
        hideDataImport();
		normalheart.start();
		alert(mainvalue.i18nkeyword.operate_failure);
    }
}

function csu_to_eeprom_result(d,r) {
	hideDataInit();
	if (d.result === "ok") {
		alert(mainvalue.i18nkeyword.operate_successfully);
	} else {
		alert(mainvalue.i18nkeyword.operate_failure);
	}
}

/* 记录已进行EEPROM配置确认、已保持一致、已进入apptest模式，后续不再弹窗直至CSU重启 */
/* 记录取消同步，后续不再弹窗直至下次登录 */
var FlagRq	= {data:{objectid:"eeprom_info",type:"attr_set",paraval:JSON.stringify([{}])}};

/* 定时获取EEPROM配置信息 */
function get_eeprom_info() {
	var Rq = {data:{objectid:"eeprom_info",type:"val_get",paranum:"1",paraval:JSON.stringify([{}])},refresh:3,success:save_eeprom_info};
	// request.clearRequest(Rq);
	request.addRequest([Rq]);
}
get_eeprom_info();

function save_eeprom_info(d,r) {
	if (d.result === "ok") {
        mainvalue.eeprom_select = d.data[0]['eeprom_select'];
        mainvalue.eeprom_comfirm_flag = d.data[0]['eeprom_comfirm_flag'];
        mainvalue.apptest_model = d.data[0]['apptest_model'];
        if (mainvalue.eeprom_comfirm_flag === "0") {
			mainvalue.eeprom_comfirm = d.data[0]['eeprom_comfirm'];  // 如果未进行EEPROM配置确认，获取更新实际状态信息
		} else {
			mainvalue.eeprom_comfirm = "0";  // 如果已进行EEPROM配置确认/取消同步，不再重新弹窗，直至csu重启/下次登录
		}
	}
}

mainvalue.$watch("eeprom_comfirm", function(a) {
	if (mainvalue.eeprom_comfirm === "0") {
        if (eeprom_popup1.isOpen()) eeprom_popup1.close();
        else if(eeprom_popup2.isOpen()) eeprom_popup2.close();
        else if(eeprom_popup3.isOpen()) eeprom_popup3.close();  // 关闭弹窗
        // 识别关闭弹窗有两种来源
        // 第一种是web进行了操作，eeprom_comfirm_flag已在点击按钮时设置
        // 第二种是系统的状态值发生了变化，eeprom_comfirm_flag需要在此额外设置，保证之后系统状态即使再变化也不弹窗
        if (mainvalue.eeprom_comfirm_flag === "0") {
            // 如果系统状态值发生变化的原因不是进入apptest模式，则需要提示是其他媒介用户进行的确认
            if (mainvalue.apptest_model === "0") {
                $.confirm({
                    title: false,
                    content: mainvalue.i18nkeyword.eeprom.others_confirmed_tip,
                    boxWidth: '30%',
                    useBootstrap: false,
                    autoClose: 'confirm|5000',
                    buttons: {
                        confirm: {
                            text: mainvalue.i18nkeyword.eeprom.confirm,
                            btnClass: 'btn-info',
                        },
                    }
                });
            }
            FlagRq.data.paraval = JSON.stringify([{"eeprom_comfirm_flag": "1"}]);
            request.addRequest([FlagRq]); // 保存标记到后端，web进程重启，标记也相应被清除
        }
	}
	if (mainvalue.eeprom_comfirm === "1") {
        setTimeout(function () {
            eeprom_popup1.open();  // 打开弹窗
        }, 2000);  // 延时2s,防止刚进入页面时翻译还未完全加载
	}
});