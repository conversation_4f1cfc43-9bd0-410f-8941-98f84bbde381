<!DOCTYPE html>
<html>
    <head>
              <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/prompt.html" -->
        <!--#include virtual="/page/html/header.html" -->

        <div class="main-container container-fluid" ms-controller="config_basic">
             <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                       <ul class="breadcrumb">
                              <li>
                                <i class="icon-home"></i>
                              {{i18nkeyword.menu.config_guide}}
                              </li>
                       </ul><!-- /.breadcrumb -->
                </div>
                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span11">
                        <!--PAGE CONTENT BEGINS-->
                        <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_successfully}}
                        </div>
                        <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                            <i class="icon-warning-sign  icon-animated-bell">
                            </i>
                            {{i18nkeyword.operate_failure}}
                        </div>
                        <div id="time_failurealert" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="time_successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="do_control_successalert" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.control_cmd_success}}
                            </div>
                            <div id="file_empty_tip" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-thumbs-up icon-animated-vertical">
                                </i>
                                {{i18nkeyword.system.fileempty}}
                            </div>
                          <div class="tabbable">
                              <div class="tab-content" style="width:1300px">
                                <div class="tabbable tabs-left" id="gui_para">
                                    <ul class="nav nav-tabs">
                                        <li ms-class="'basic_para' == tab_list?'active':''" ms-click="tabChange('basic_para')">
                                            <a data-toggle="tab" href="#basic_para">
                                                <i class="blue icon-fire bigger-110">
                                                </i>
                                                {{i18nkeyword.config_guide.system_time}}
                                            </a>
                                        </li>
                                        <li ms-class="'ac_para' == tab_list?'active':''" ms-click="tabChange('ac_para')">
                                            <a data-toggle="tab" href="#ac_para">
                                                <i class="blue icon-fire bigger-110">
                                                </i>
                                                {{i18nkeyword.config_guide.ac_para}}
                                            </a>
                                        </li>
                                        <li ms-class="'batt_para' == tab_list?'active':''" ms-click="tabChange('batt_para')">
                                            <a data-toggle="tab" href="#batt_para">
                                                <i class="blue icon-fire bigger-110">
                                                </i>
                                                {{i18nkeyword.config_guide.batt_para}}
                                            </a>
                                        </li>
                                        <li ms-class="'net_para' == tab_list?'active':''" ms-click="tabChange('net_para')">
                                            <a data-toggle="tab" href="#net_para">
                                                <i class="blue icon-fire bigger-110">
                                                </i>
                                                {{i18nkeyword.config_guide.net_para}}
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">
                                        <div class="tab-pane" id ="basic_para" ms-class="'basic_para' == tab_list?'active':''">
                                            <div class="widget-header widget-header-flat widget-header-small" style="height:40px">
                                                <h5 class="lighter pull-left" style="margin-top:5px">
                                                {{i18nkeyword.system.set_time}}&nbsp;
                                                    <i class="icon-hand-right">
                                                    </i>
                                                </h5>
                                                <input id="timeinput" type="text" autocomplete="off" class="form-control input-medium pull-left"
                                                style="margin-top:6px; margin-left:15px;" onFocus="getSysTime()" />
                                                <button id="timesync" class="button button-small button-flat-primary pull-right"
                                                style="margin-top:5px; margin-right:10px" onClick="systimeTask.synchro()">
                                                {{i18nkeyword.system.synchro_local_time}}
                                                </button>
                                                <button id="submitTime" class="button button-small button-flat-primary pull-left"
                                                style="margin-top:5px; margin-left:10px" onClick="setSysTime($('#timeinput').val())">
                                                {{i18nkeyword.set}}
                                                </button>
                                            </div>
                                            <div class="widget-content">
                                                <table class="table table-striped table-bordered table-hover" >
                                                    <tbody>
                                                        <tr ms-for="(el,al) in timezonestruc" ms-if="timezonestruc[el]['visible']!='NO'">
                                                        <td>{{al.full_name}}</td>
                                                        <td ms-if="getconventionlength(al.convention)>0">
                                                            <select ms-duplex="timezonevalue[0][al.name]" class="input-medium" style="width:auto">
                                                                <option ms-for="(index,name) in al.convention" ms-attr="{value:index}" ms-if="al.name == 'timezone select'" >{{timezoneUTC[index]}} {{name}}</option>
                                                                <option ms-for="(index,name) in al.convention" ms-attr="{value:index}" ms-if="al.name != 'timezone select'" >{{name}}</option>
                                                        </td>
                                                        <td ms-if="getconventionlength(al.convention)==0">
                                                            <input class="form-control input-medium pull-left" ms-duplex="timezonevalue[0][al.name]">
                                                        </td>
                                                        </tr>
                                                        <td colspan="2">
                                                            <button class="button button-small button-flat-primary" onClick="set_tz_Data()" id="set_tz_Data"><i class="icon-pencil bigger-100"></i>
                                                                {{i18nkeyword.set}}
                                                            </button>
                                                        </td>
                                                    </tbody>
                                                </table>
                                            </div><!--��ҳ��-->
                                        </div>
                                        <div class="tab-pane" id ="ac_para" ms-class="'ac_para' == tab_list?'active':''">
                                            <table id="sample-table-1" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>{{i18nkeyword.devlist.signalname}}</th>
                                                        <th>
                                                            <i class="icon-time bigger-110 hidden-480"></i>
                                                            {{i18nkeyword.devlist.value}}
                                                        </th>
                                                        <th>{{i18nkeyword.devlist.range}}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!--ms-for:(i,parameter) in ac_paras-->
                                                    <tr ms-if="getconventionlength(parameter.convention)<=0">
                                                        <td >{{parameter.full_name}}</td>
                                                        <td >
                                                            <input ms-if="@jugge_setdata_failure(@parameter.sid,'value')" ms-css="{'color':'#ff0000'}"
                                                            ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
                                                            ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
                                                            <input ms-if="!@jugge_setdata_failure(@parameter.sid,'value')"
                                                            ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
                                                            ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
                                                        </td>
                                                        <td style="font-family:none;">{{@get_paraset_rang_value(parameter)}}</td>
                                                    </tr>
                                                    <tr ms-if="getconventionlength(parameter.convention)>0">
                                                        <td >{{parameter.full_name}}</td>
                                                        <td >
                                                            <select  class="editable input-medium" ms-duplex="parameter.value" data-duplex-changed="changeDevData(@parameter.sid,'value')">
                                                                <option ms-for="(num,name) in parameter.convention" ms-attr="{value:num,selected:num==parameter.value?true:false}">{{name}}</option>
                                                            </select>
                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                    <!--ms-for-end:-->
                                                </tbody>
                                            </table>
                                            <button style="position: fixed ! important; right: 200px; top: 100px;" class="button button-small button-flat-primary" onClick="setDevData(2,'value')"<i class="icon-pencil bigger-100"></i>
                                            {{i18nkeyword.set}}
                                            </button>
                                        </div>
                                        <div class="tab-pane" id ="batt_para" ms-class="'batt_para' == tab_list?'active':''">
                                            <table id="sample-table-1" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>{{i18nkeyword.devlist.signalname}}</th>
                                                        <th>
                                                            <i class="icon-time bigger-110 hidden-480"></i>
                                                            {{i18nkeyword.devlist.value}}
                                                        </th>
                                                        <th>{{i18nkeyword.devlist.range}}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!--ms-for:(i,parameter) in batt_paras-->
                                                    <tr ms-if="getconventionlength(parameter.convention)<=0">
                                                        <td >{{parameter.full_name}}</td>
                                                        <td >
                                                            <input ms-if="@jugge_setdata_failure(@parameter.sid,'value')" ms-css="{'color':'#ff0000'}"
                                                            ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
                                                            ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
                                                            <input ms-if="!@jugge_setdata_failure(@parameter.sid,'value')"
                                                            ms-attr="{title:@parameter.constraint}" class="form-control input-medium pull-left"
                                                            ms-duplex="parameter.value" ms-change="changeDevData(@parameter.sid,'value')" />
                                                        </td>
                                                        <td style="font-family:none;">{{@get_paraset_rang_value(parameter)}}</td>
                                                    </tr>
                                                    <tr ms-if="getconventionlength(parameter.convention)>0">
                                                        <td >{{parameter.full_name}}</td>
                                                        <td >
                                                            <select  class="editable input-medium" ms-duplex="parameter.value" data-duplex-changed="changeDevData(@parameter.sid,'value')">
                                                                <option ms-for="(num,name) in parameter.convention" ms-attr="{value:num,selected:num==parameter.value?true:false}">{{name}}</option>
                                                            </select>
                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                    <!--ms-for-end:-->
                                                </tbody>
                                            </table>
                                            <button style="position: fixed ! important; right: 200px; top: 100px;" class="button button-small button-flat-primary" onClick="setDevData(2,'value')"<i class="icon-pencil bigger-100"></i>
                                            {{i18nkeyword.set}}
                                            </button>
                                        </div>
                                        <div class="tab-pane" id ="net_para" ms-class="'net_para' == tab_list?'active':''">
                                            <table class="table table-striped table-bordered table-hover">
                                                <tr ms-for = "(WiredAttrindex,WiredAttr) in northnet_WiredAttrData" ms-if="northnet_show[WiredAttrindex].show == 1">
                                                    <td>{{WiredAttr.full_name}}</td>
                                                    <td ms-if="getconventionlength(WiredAttr.convention)>0">
                                                        <select ms-duplex="northnet_WiredValueData[0][WiredAttr.name]" ms-attr="{name:@WiredAttr.name}" ms-change="changeshow">
                                                                <option ms-for="(index,name) in WiredAttr.convention" ms-attr="{value:index}">{{name}}</option>
                                                        </select>
                                                    </td>
                                                    <td ms-if="getconventionlength(WiredAttr.convention)===0">
                                                        <input class="form-control input-medium pull-left" ms-duplex="northnet_WiredValueData[0][WiredAttr.name]">
                                                    </td>
                                                </tr>
                                            </table>
                                            <button class="button button-small button-flat-primary" onClick="setWiredData()" id="setWireddataBT"><i class="icon-pencil bigger-100"></i>
                                            {{i18nkeyword.set}}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                              </div>
                          </div><!--/tabbable-->
                        <!--PAGE CONTENT ENDS-->
                        </div><!--/span11-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-contain-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
              <!-- inline scripts related to this page -->
        <script src="/page/js/config_basic.js"></script>
    </body>

</html>