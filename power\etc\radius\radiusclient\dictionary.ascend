#
# Ascend dictionary.
#
#		Enable by putting the line "$INCLUDE dictionary.ascend" into
#		the main dictionary file.
#
# Version:	1.00  21-Jul-1997  <PERSON><PERSON> <<EMAIL>>
#


#
#	Ascend specific extensions
#	Used by ASCEND MAX/Pipeline products
#
ATTRIBUTE	Ascend-FCP-Parameter		119	string
ATTRIBUTE	Ascend-Modem-<PERSON><PERSON><PERSON>		120	integer
ATTRIBUTE	Ascend-Modem-<PERSON>lotNo		121	integer
ATTRIBUTE	Ascend-Modem-ShelfNo		122	integer
ATTRIBUTE	Ascend-Call-Attempt-Limit	123	integer
ATTRIBUTE	Ascend-Call-Block-Duration	124	integer
ATTRIBUTE	Ascend-Maximum-Call-Duration	125	integer
ATTRIBUTE	Ascend-Temporary-Rtes		126	integer
ATTRIBUTE       Tunneling-Protocol              127     integer
ATTRIBUTE       Ascend-Shared-Profile-Enable    128     integer
ATTRIBUTE	Ascend-Primary-Home-Agent	129	string
ATTRIBUTE	Ascend-Secondary-Home-Agent	130	string
ATTRIBUTE	Ascend-Dialout-Allowed		131	integer
ATTRIBUTE	Ascend-Client-Gateway		132	ipaddr
ATTRIBUTE	Ascend-BACP-Enable		133	integer
ATTRIBUTE	Ascend-DHCP-Maximum-Leases	134	integer
ATTRIBUTE	Ascend-Client-Primary-<PERSON>NS	135	ipaddr
ATTRIBUTE	Ascend-Client-Secondary-DNS	136	ipaddr
ATTRIBUTE	Ascend-Client-Assign-DNS	137	integer
ATTRIBUTE	Ascend-User-Acct-Type		138	integer
ATTRIBUTE	Ascend-User-Acct-Host		139	ipaddr
ATTRIBUTE	Ascend-User-Acct-Port		140	integer
ATTRIBUTE	Ascend-User-Acct-Key		141	string
ATTRIBUTE	Ascend-User-Acct-Base		142	integer
ATTRIBUTE	Ascend-User-Acct-Time		143	integer
ATTRIBUTE	Ascend-Assign-IP-Client		144	ipaddr
ATTRIBUTE	Ascend-Assign-IP-Server		145	ipaddr
ATTRIBUTE	Ascend-Assign-IP-Global-Pool	146	string
ATTRIBUTE	Ascend-DHCP-Reply		147	integer
ATTRIBUTE	Ascend-DHCP-Pool-Number		148	integer
ATTRIBUTE	Ascend-Expect-Callback		149	integer
ATTRIBUTE	Ascend-Event-Type		150	integer
ATTRIBUTE	Ascend-Session-Svr-Key		151	string
ATTRIBUTE	Ascend-Multicast-Rate-Limit	152	integer
ATTRIBUTE	Ascend-IF-Netmask		153	ipaddr
ATTRIBUTE	Ascend-Remote-Addr		154	ipaddr
ATTRIBUTE	Ascend-Multicast-Client		155	integer
ATTRIBUTE	Ascend-FR-Circuit-Name		156	string
ATTRIBUTE	Ascend-FR-LinkUp		157	integer
ATTRIBUTE	Ascend-FR-Nailed-Grp		158	integer
ATTRIBUTE	Ascend-FR-Type			159	integer
ATTRIBUTE	Ascend-FR-Link-Mgt		160	integer
ATTRIBUTE	Ascend-FR-N391			161	integer
ATTRIBUTE	Ascend-FR-DCE-N392		162	integer
ATTRIBUTE	Ascend-FR-DTE-N392		163	integer
ATTRIBUTE	Ascend-FR-DCE-N393		164	integer
ATTRIBUTE	Ascend-FR-DTE-N393		165	integer
ATTRIBUTE	Ascend-FR-T391			166	integer
ATTRIBUTE	Ascend-FR-T392			167	integer
ATTRIBUTE	Ascend-Bridge-Address  	 	168	string
ATTRIBUTE       Ascend-TS-Idle-Limit            169     integer
ATTRIBUTE       Ascend-TS-Idle-Mode             170     integer
ATTRIBUTE	Ascend-DBA-Monitor	 	171	integer
ATTRIBUTE	Ascend-Base-Channel-Count 	172	integer
ATTRIBUTE	Ascend-Minimum-Channels		173	integer
ATTRIBUTE	Ascend-IPX-Route		174	string
ATTRIBUTE	Ascend-FT1-Caller		175	integer
ATTRIBUTE	Ascend-Backup			176	string
ATTRIBUTE	Ascend-Call-Type		177	integer
ATTRIBUTE	Ascend-Group			178	string
ATTRIBUTE	Ascend-FR-DLCI			179	integer
ATTRIBUTE	Ascend-FR-Profile-Name		180	string
ATTRIBUTE	Ascend-Ara-PW			181	string
ATTRIBUTE	Ascend-IPX-Node-Addr		182	string
ATTRIBUTE	Ascend-Home-Agent-IP-Addr	183	ipaddr
ATTRIBUTE	Ascend-Home-Agent-Password	184	string
ATTRIBUTE	Ascend-Home-Network-Name	185	string
ATTRIBUTE	Ascend-Home-Agent-UDP-Port	186	integer
ATTRIBUTE	Ascend-Multilink-ID		187	integer
ATTRIBUTE	Ascend-Num-In-Multilink		188	integer
ATTRIBUTE	Ascend-First-Dest		189	ipaddr
ATTRIBUTE	Ascend-Pre-Input-Octets		190	integer
ATTRIBUTE	Ascend-Pre-Output-Octets	191	integer
ATTRIBUTE	Ascend-Pre-Input-Packets	192	integer
ATTRIBUTE	Ascend-Pre-Output-Packets	193	integer
ATTRIBUTE	Ascend-Maximum-Time		194	integer
ATTRIBUTE	Ascend-Disconnect-Cause		195	integer
ATTRIBUTE	Ascend-Connect-Progress		196	integer
ATTRIBUTE	Ascend-Data-Rate		197	integer
ATTRIBUTE	Ascend-PreSession-Time		198	integer
ATTRIBUTE	Ascend-Token-Idle		199	integer
ATTRIBUTE	Ascend-Token-Immediate		200	integer
ATTRIBUTE	Ascend-Require-Auth		201	integer
ATTRIBUTE	Ascend-Number-Sessions		202	string
ATTRIBUTE	Ascend-Authen-Alias		203	string
ATTRIBUTE	Ascend-Token-Expiry		204	integer
ATTRIBUTE	Ascend-Menu-Selector		205	string
ATTRIBUTE	Ascend-Menu-Item		206	string
ATTRIBUTE	Ascend-PW-Warntime		207	integer
ATTRIBUTE	Ascend-PW-Lifetime		208	integer
ATTRIBUTE	Ascend-IP-Direct		209	ipaddr
ATTRIBUTE	Ascend-PPP-VJ-Slot-Comp		210	integer
ATTRIBUTE	Ascend-PPP-VJ-1172		211	integer
ATTRIBUTE	Ascend-PPP-Async-Map		212	integer
ATTRIBUTE	Ascend-Third-Prompt		213	string
ATTRIBUTE	Ascend-Send-Secret		214	string
ATTRIBUTE	Ascend-Receive-Secret		215	string
ATTRIBUTE	Ascend-IPX-Peer-Mode		216	integer
ATTRIBUTE	Ascend-IP-Pool-Definition	217	string
ATTRIBUTE	Ascend-Assign-IP-Pool		218	integer
ATTRIBUTE	Ascend-FR-Direct		219	integer
ATTRIBUTE	Ascend-FR-Direct-Profile	220	string
ATTRIBUTE	Ascend-FR-Direct-DLCI		221	integer
ATTRIBUTE	Ascend-Handle-IPX		222	integer
ATTRIBUTE	Ascend-Netware-timeout		223	integer
ATTRIBUTE	Ascend-IPX-Alias		224	integer
ATTRIBUTE	Ascend-Metric			225	integer
ATTRIBUTE	Ascend-PRI-Number-Type		226	integer
ATTRIBUTE	Ascend-Dial-Number		227	string
ATTRIBUTE	Ascend-Route-IP			228	integer
ATTRIBUTE	Ascend-Route-IPX		229	integer
ATTRIBUTE	Ascend-Bridge			230	integer
ATTRIBUTE	Ascend-Send-Auth		231	integer
ATTRIBUTE	Ascend-Send-Passwd		232	string
ATTRIBUTE	Ascend-Link-Compression		233	integer
ATTRIBUTE	Ascend-Target-Util		234	integer
ATTRIBUTE	Ascend-Maximum-Channels		235	integer
ATTRIBUTE	Ascend-Inc-Channel-Count	236	integer
ATTRIBUTE	Ascend-Dec-Channel-Count	237	integer
ATTRIBUTE	Ascend-Seconds-Of-History	238	integer
ATTRIBUTE	Ascend-History-Weigh-Type	239	integer
ATTRIBUTE	Ascend-Add-Seconds		240	integer
ATTRIBUTE	Ascend-Remove-Seconds		241	integer
ATTRIBUTE	Ascend-Idle-Limit		244	integer
ATTRIBUTE	Ascend-Preempt-Limit		245	integer
ATTRIBUTE	Ascend-Callback			246	integer
ATTRIBUTE	Ascend-Data-Svc			247	integer
ATTRIBUTE	Ascend-Force-56			248	integer
ATTRIBUTE	Ascend-Billing-Number		249	string
ATTRIBUTE	Ascend-Call-By-Call		250	integer
ATTRIBUTE	Ascend-Transit-Number		251	string
ATTRIBUTE	Ascend-Host-Info		252	string
ATTRIBUTE	Ascend-PPP-Address		253	ipaddr
ATTRIBUTE	Ascend-MPP-Idle-Percent		254	integer
ATTRIBUTE	Ascend-Xmit-Rate		255	integer



# Ascend protocols
VALUE		Service-Type		Dialout-Framed-User	5
VALUE		Framed-Protocol		ARA			255
VALUE		Framed-Protocol		MPP			256
VALUE		Framed-Protocol		EURAW			257
VALUE		Framed-Protocol		EUUI			258
VALUE		Framed-Protocol		X25			259
VALUE		Framed-Protocol		COMB			260
VALUE		Framed-Protocol		FR			261
VALUE		Framed-Protocol		MP			262
VALUE		Framed-Protocol		FR-CIR			263


#
#	Ascend specific extensions
#	Used by ASCEND MAX/Pipeline products (see above)
#

VALUE		Ascend-FR-Direct	FR-Direct-No		0
VALUE		Ascend-FR-Direct	FR-Direct-Yes		1
VALUE		Ascend-Handle-IPX	Handle-IPX-None		0
VALUE		Ascend-Handle-IPX	Handle-IPX-Client	1
VALUE		Ascend-Handle-IPX	Handle-IPX-Server	2
VALUE		Ascend-IPX-Peer-Mode	IPX-Peer-Router		0
VALUE		Ascend-IPX-Peer-Mode	IPX-Peer-Dialin		1
VALUE		Ascend-Call-Type	Nailed			1
VALUE		Ascend-Call-Type	Nailed/Mpp		2
VALUE		Ascend-Call-Type	Perm/Switched		3
VALUE		Ascend-FT1-Caller	FT1-No			0
VALUE		Ascend-FT1-Caller	FT1-Yes			1
VALUE		Ascend-PRI-Number-Type	Unknown-Number		0
VALUE		Ascend-PRI-Number-Type	Intl-Number		1
VALUE		Ascend-PRI-Number-Type	National-Number		2
VALUE		Ascend-PRI-Number-Type	Local-Number		4
VALUE		Ascend-PRI-Number-Type	Abbrev-Number		5
VALUE		Ascend-Route-IPX	Route-IPX-No		0
VALUE		Ascend-Route-IPX	Route-IPX-Yes		1
VALUE		Ascend-Bridge		Bridge-No		0
VALUE		Ascend-Bridge		Bridge-Yes		1
VALUE  		Ascend-TS-Idle-Mode     TS-Idle-None		0
VALUE	  	Ascend-TS-Idle-Mode     TS-Idle-Input		1
VALUE  		Ascend-TS-Idle-Mode     TS-Idle-Input-Output	2
VALUE		Ascend-Send-Auth	Send-Auth-None		0
VALUE		Ascend-Send-Auth	Send-Auth-PAP		1
VALUE		Ascend-Send-Auth	Send-Auth-CHAP		2
VALUE		Ascend-Send-Auth	Send-Auth-MS-CHAP	3
VALUE		Ascend-Link-Compression	Link-Comp-None		0
VALUE		Ascend-Link-Compression	Link-Comp-Stac		1
VALUE		Ascend-Link-Compression	Link-Comp-Stac-Draft-9	2
VALUE		Ascend-Link-Compression	Link-Comp-MS-Stac	3
VALUE		Ascend-History-Weigh-Type	History-Constant	0
VALUE		Ascend-History-Weigh-Type	History-Linear		1
VALUE		Ascend-History-Weigh-Type	History-Quadratic	2
VALUE		Ascend-Callback		Callback-No		0
VALUE		Ascend-Callback		Callback-Yes		1
VALUE		Ascend-Expect-Callback	Expect-Callback-No	0
VALUE		Ascend-Expect-Callback	Expect-Callback-Yes	1
VALUE		Ascend-Data-Svc		Switched-Voice-Bearer	0
VALUE		Ascend-Data-Svc		Switched-56KR		1
VALUE		Ascend-Data-Svc		Switched-64K		2
VALUE		Ascend-Data-Svc		Switched-64KR		3
VALUE		Ascend-Data-Svc		Switched-56K		4
VALUE		Ascend-Data-Svc		Switched-384KR		5
VALUE		Ascend-Data-Svc		Switched-384K		6
VALUE		Ascend-Data-Svc		Switched-1536K		7
VALUE		Ascend-Data-Svc		Switched-1536KR		8
VALUE		Ascend-Data-Svc		Switched-128K		9
VALUE		Ascend-Data-Svc		Switched-192K		10
VALUE		Ascend-Data-Svc		Switched-256K		11
VALUE		Ascend-Data-Svc		Switched-320K		12
VALUE		Ascend-Data-Svc		Switched-384K-MR	13
VALUE		Ascend-Data-Svc		Switched-448K		14
VALUE		Ascend-Data-Svc		Switched-512K		15
VALUE		Ascend-Data-Svc		Switched-576K		16
VALUE		Ascend-Data-Svc		Switched-640K		17
VALUE		Ascend-Data-Svc		Switched-704K		18
VALUE		Ascend-Data-Svc		Switched-768K		19
VALUE		Ascend-Data-Svc		Switched-832K		20
VALUE		Ascend-Data-Svc		Switched-896K		21
VALUE		Ascend-Data-Svc		Switched-960K		22
VALUE		Ascend-Data-Svc		Switched-1024K		23
VALUE		Ascend-Data-Svc		Switched-1088K		24
VALUE		Ascend-Data-Svc		Switched-1152K		25
VALUE		Ascend-Data-Svc		Switched-1216K		26
VALUE		Ascend-Data-Svc		Switched-1280K		27
VALUE		Ascend-Data-Svc		Switched-1344K		28
VALUE		Ascend-Data-Svc		Switched-1408K		29
VALUE		Ascend-Data-Svc		Switched-1472K		30
VALUE		Ascend-Data-Svc		Switched-1600K		31
VALUE		Ascend-Data-Svc		Switched-1664K		32
VALUE		Ascend-Data-Svc		Switched-1728K		33
VALUE		Ascend-Data-Svc		Switched-1792K		34
VALUE		Ascend-Data-Svc		Switched-1856K		35
VALUE		Ascend-Data-Svc		Switched-1920K		36
VALUE		Ascend-Data-Svc		Switched-inherited		37
VALUE		Ascend-Data-Svc		Switched-restricted-bearer-x30  38
VALUE		Ascend-Data-Svc		Switched-clear-bearer-v110	39
VALUE		Ascend-Data-Svc		Switched-restricted-64-x30	40
VALUE		Ascend-Data-Svc		Switched-clear-56-v110		41
VALUE		Ascend-Data-Svc		Switched-modem			42
VALUE		Ascend-Data-Svc		Switched-atmodem		43
VALUE		Ascend-Data-Svc		Nailed-56KR		1
VALUE		Ascend-Data-Svc		Nailed-64K		2
VALUE		Ascend-Force-56		Force-56-No		0
VALUE		Ascend-Force-56		Force-56-Yes		1
VALUE		Ascend-PW-Lifetime	Lifetime-In-Days	0
VALUE		Ascend-PW-Warntime	Days-Of-Warning		0
VALUE		Ascend-PPP-VJ-1172	PPP-VJ-1172		1
VALUE		Ascend-PPP-VJ-Slot-Comp	VJ-Slot-Comp-No		1
VALUE		Ascend-Require-Auth	Not-Require-Auth	0
VALUE		Ascend-Require-Auth	Require-Auth		1
VALUE		Ascend-Token-Immediate	Tok-Imm-No		0
VALUE		Ascend-Token-Immediate	Tok-Imm-Yes		1
VALUE		Ascend-DBA-Monitor		DBA-Transmit		0
VALUE 		Ascend-DBA-Monitor	DBA-Transmit-Recv	1
VALUE		Ascend-DBA-Monitor	DBA-None		2
VALUE		Ascend-FR-Type		Ascend-FR-DTE		0
VALUE		Ascend-FR-Type		Ascend-FR-DCE		1
VALUE		Ascend-FR-Type		Ascend-FR-NNI		2
VALUE		Ascend-FR-Link-Mgt	Ascend-FR-No-Link-Mgt	0
VALUE		Ascend-FR-Link-Mgt	Ascend-FR-T1-617D	1
VALUE		Ascend-FR-Link-Mgt	Ascend-FR-Q-933A	2
VALUE		Ascend-FR-LinkUp	Ascend-LinkUp-Default	0
VALUE		Ascend-FR-LinkUp	Ascend-LinkUp-AlwaysUp	1
VALUE		Ascend-Multicast-Client	Multicast-No		0
VALUE		Ascend-Multicast-Client	Multicast-Yes		1
VALUE		Ascend-User-Acct-Type	Ascend-User-Acct-None	0
VALUE		Ascend-User-Acct-Type	Ascend-User-Acct-User	1
VALUE		Ascend-User-Acct-Type	Ascend-User-Acct-User-Default	2
VALUE		Ascend-User-Acct-Base	Base-10			0
VALUE		Ascend-User-Acct-Base	Base-16			1
VALUE		Ascend-DHCP-Reply	DHCP-Reply-No		0
VALUE		Ascend-DHCP-Reply	DHCP-Reply-Yes		1
VALUE		Ascend-Client-Assign-DNS	DNS-Assign-No		0
VALUE		Ascend-Client-Assign-DNS	DNS-Assign-Yes		1
VALUE		Ascend-Event-Type	Ascend-ColdStart	1
VALUE		Ascend-Event-Type	Ascend-Session-Event	2
VALUE		Ascend-BACP-Enable	BACP-No			0
VALUE		Ascend-BACP-Enable	BACP-Yes		1
VALUE		Ascend-Dialout-Allowed	Dialout-Not-Allowed	0
VALUE		Ascend-Dialout-Allowed	Dialout-Allowed		1
VALUE		Ascend-Shared-Profile-Enable    Shared-Profile-No       0
VALUE		Ascend-Shared-Profile-Enable    Shared-Profile-Yes      1
VALUE		Ascend-Temporary-Rtes	Temp-Rtes-No		0
VALUE		Ascend-Temporary-Rtes	Temp-Rtes-Yes		1
