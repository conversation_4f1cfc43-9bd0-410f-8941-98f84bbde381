//   当前选择的业务对象
var selectBoId = Cookies.get("pagelist");
//BusinessObject.GetRealdataObject();   // 加载实时数据对象
var  realdata_vmodel = avalon.define({
	$id:'realdata_info',
	realdata_device_table:[],
	current_devname:[],
	current_devsid:[],
	current_devinfo:[],
	is_devinfo_empty:0,
	refresh_time:10,
	devinfo_show:function(devsid, devname){
		realdata_vmodel.current_devname = devname;
		realdata_vmodel.current_devsid = devsid;
		object_device_realdata_get();
		realdata_vmodel.current_devinfo.clear();
	},
	get_sid_show_value(value,unit) {
		if (typeof(value)=='undefined' || value =='val_invalid'){
				return mainvalue.i18nkeyword.value_invalid;
		} else {
			if (typeof(unit)!='undefined') {
				return value + " "+ unit;
			} else {
				return value;
			}
		}
	}
});

function get_realdata_refesh_time(obj) {
    var refresh_time = Cookies.get("realdata_period");
    if (typeof(refresh_time) =='undefined') {
        realdata_vmodel.refresh_time = 10;   //  默认值为10
    } else {
        realdata_vmodel.refresh_time = refresh_time * 1;
    }
    $("#fleshselect").val(realdata_vmodel.refresh_time);
}
get_realdata_refesh_time();

function object_device_list(obj) {
	let boid = selectBoId.toString();
	if(selectBoId.toString().includes("-")){
		boid = boid.split("-")[1];
	}
	var paraval = [{"bo_id":boid}];
	var req_list = {data:{objectid:"business_object_dev",type:"list",paranum:"1",paraval:JSON.stringify(paraval)},success:object_device_list_succ};
	request.clearRequest(req_list);
	request.addRequest([req_list]);

	function object_device_list_succ(d,r) {
		if (d.datanum>0) {
			realdata_vmodel.realdata_device_table = d.data;
			realdata_vmodel.current_devsid = d.data[0].sid;
			realdata_vmodel.current_devname = d.data[0]['device name'];
			object_device_realdata_get();
		}
	}
}

function object_device_realdata_get() {
	var paraval = [{"sid":realdata_vmodel.current_devsid}];
	var req_get = {data:{objectid:"business_object_dev",type:"val_get",paranum:"1",paraval:JSON.stringify(paraval)},refresh:realdata_vmodel.refresh_time, success:object_device_get_succ};
	request.clearRequest(req_get);
	request.addRequest([req_get]);

	function object_device_get_succ(d,r) {
		realdata_vmodel.current_devinfo.clear();
		if (d.datanum>0) {
			if (d.data[0]["device name"] == realdata_vmodel.current_devname) {
				realdata_vmodel.current_devinfo = d.data;
				realdata_vmodel.is_devinfo_empty=0;
			}
		}else{
			realdata_vmodel.is_devinfo_empty=1;
		}
	}
}

function changeFleshtime(time) {
	realdata_vmodel.refresh_time = time;
	object_device_realdata_get();
    set_cookie_with_path('realdata_period', realdata_vmodel.refresh_time);
}

object_device_list();
