#######################################################################
##
##  CGI modules
## --------------- 
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/docs_modcgi
##
server.modules += ( "mod_cgi" )

##
## Plain old CGI handling
##
## For PHP don't forget to set cgi.fix_pathinfo = 1 in the php.ini.
##
cgi.assign  = (".cgi"=>"")               
#= ( ".pl"  => "/usr/bin/perl",
#                              ".cgi" => "/usr/bin/perl",
#                             ".rb"  => "/usr/bin/ruby",
#                            ".erb" => "/usr/bin/eruby",
#                           ".py"  => "/usr/bin/python" )

##
## to get the old cgi-bin behavior of apache
##
## Note: make sure that mod_alias is loaded if you uncomment the
##       next line. (see modules.conf)
##
alias.url += ( "/power/cgi-bin" => "/root/power/bin" )
#$HTTP["url"] =~ "^/cgi-bin" {
#   cgi.assign = ( "" => "" )
#}

##
#######################################################################
