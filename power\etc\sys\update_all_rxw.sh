#!/bin/sh
SYS_PARA_BAKUP="/mnt/backup/" #backup dir
SYS_PARA="/mnt/data/para_and_config" #running data dir
IDFILE="/mnt/backup/CSU_ID_AppTest.bin"
PKG_PATH="$1"
USB_BIN_PACKAGE="$2"
TIME=`date`

ERRFILE="/tmp/log/updateinfo"

echo "all update start, log to $ERRFILE!"
if [ -f $ERRFILE ] ; then
	rm -rf $ERRFILE
fi
echo "all update start, all log to $ERRFILE!" >> "$ERRFILE"

#bakup para and config and Certificate
echo "bakup sys para from $SYS_PARA to $SYS_PARA_BAKUP"
echo "bakup sys para from $SYS_PARA to $SYS_PARA_BAKUP" >> "$ERRFILE"
cd $SYS_PARA_BAKUP && rm -rf para_and_config
cp -rf $SYS_PARA   $SYS_PARA_BAKUP
if [ -f $IDFILE ]; then
	cp -rf $IDFILE     $SYS_PARA_BAKUP
fi

#bakup sys account info
cp -rf /etc/group   $SYS_PARA_BAKUP
cp -rf /etc/gshadow $SYS_PARA_BAKUP
cp -rf /etc/passwd  $SYS_PARA_BAKUP
cp -rf /etc/shadow  $SYS_PARA_BAKUP

RADIUS_BAKUP_CRET="/mnt/backup/radius" #backup dir
RADIUS_SSL_CRET="/mnt/data/work/radius/ssl"
WEB_BAKUP_CRET="/mnt/backup/web" #backup dir
WEB_SSL_CRET="/mnt/data/work/web/ssl"
rm -rf $RADIUS_BAKUP_CRET
cp -rf $RADIUS_SSL_CRET   $RADIUS_BAKUP_CRET
rm -rf $WEB_BAKUP_CRET
cp -rf $WEB_SSL_CRET      $WEB_BAKUP_CRET

#backup auth_management info
WEBUSER_BIN="/mnt/data/work/auth_management/webuser.bin"
GUIUSER_BIN="/mnt/data/work/auth_management/guiuser.bin"
WEBWEAKUSER_BIN="/mnt/data/work/auth_management/webweakuser.bin"
AUTHMANAGEMENT_DIR="$SYS_PARA_BAKUP/auth_management"
if [ ! -d $AUTHMANAGEMENT_DIR ] ; then
    mkdir $AUTHMANAGEMENT_DIR
fi

echo "bakup auth_management info start!"
cp $WEBUSER_BIN $GUIUSER_BIN $WEBWEAKUSER_BIN $AUTHMANAGEMENT_DIR
echo "bakup auth_management info finish!"

#bakup sys stat comdmgr candmgr info
STATSAVE_BIN="/mnt/data/work/productapp/statsave.bin"
STATSAVEBAK_BIN="/mnt/data/work/productapp/statsavebak.bin"
COMDMGR_BIN="/mnt/data/work/comdmgr/*.bin"
CANDMGR_BIN="/mnt/data/work/candmgr/*.bin"
COMDMGR_DIR="$SYS_PARA_BAKUP/comdmgr"
CANDMGR_DIR="$SYS_PARA_BAKUP/candmgr"
if [ ! -d $COMDMGR_DIR ] ; then
	mkdir $COMDMGR_DIR
fi

if [ ! -d $CANDMGR_DIR ] ; then
	mkdir $CANDMGR_DIR
fi

echo "bakup sys stat comdmgr candmgr info start!"
cp $STATSAVE_BIN $STATSAVEBAK_BIN  $SYS_PARA_BAKUP
cp $COMDMGR_BIN  $COMDMGR_DIR
cp $CANDMGR_BIN  $CANDMGR_DIR
echo "bakup sys stat comdmgr candmgr info finish!"
sync

touch /mnt/data/all_update.flag
tar zxf $PKG_PATH/$USB_BIN_PACKAGE -C /mnt/data/
echo "all update end!"
reboot -f
