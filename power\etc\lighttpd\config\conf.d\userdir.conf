#######################################################################
##
##  Userdir Module 
## ----------------
##
## See http://redmine.lighttpd.net/projects/lighttpd/wiki/Docs_ModUserDir
##
server.modules += ( "mod_userdir" )

##
## usually it should be set to "public_html" to take ~/public_html/ as
## the document root
## Default: empty (document root is the home directory)
##
userdir.path = "public_html"

##
## If set, don't check /etc/passwd for homedir
## Default: empty
#userdir.basepath = server_root + "/users/"

##
## list of usernames which may not use this feature
## Default: empty (all users may use it)
##
#userdir.exclude-user = ( "root", "postmaster" )

##
## if set, only users from this list may use the feature
## Default: empty (all users may use it) 
##
#userdir.include-user = ("user1", "user2")

##
#######################################################################

