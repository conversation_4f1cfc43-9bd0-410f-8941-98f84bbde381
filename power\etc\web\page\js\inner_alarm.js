﻿var vmodel = avalon.define({
	$id:'inneralarm',
	inneralarmdata:[],
	doconvention:{},	//告警可用的DO通道
});

//  获取DO接口
function get_alarm_do_list(){
	var req_set = {data:{objectid:"para_alarm_do",type:"val_get",paraval:JSON.stringify([{}])},success:get_alarm_do_succ};
	request.addRequest([req_set]);
}

function get_alarm_do_succ(d,r){
	if (d.result == 'ok') {
		var doctrlmode = d.data;
		vmodel.doconvention=transformArrayToObject(doctrlmode,"Channel NO.","Channel Name");
	}
}

var para = {sort_style:"3",alarm_tag:"1"};

function name_sort() {
    if($('#name_sort').attr('class') == "arrow asc") {
        $('#name_sort').removeClass('asc');
        $('#name_sort').addClass('dsc');
		para.sort_style = "0";
    } else {
        $('#name_sort').removeClass('dsc');
        $('#name_sort').addClass('asc');
		para.sort_style = "1";
    }
    alarm_sort();
}

function time_sort() {
    if($('#time_sort').attr('class') == "arrow dsc") {
        $('#time_sort').removeClass('dsc');
        $('#time_sort').addClass('asc');
		para.sort_style = "2";
    } else {
        $('#time_sort').removeClass('asc');
        $('#time_sort').addClass('dsc');
		para.sort_style = "3";
    }
    alarm_sort();
}

function level_sort() {
    if($('#level_sort').attr('class') == "arrow dsc") {
        $('#level_sort').removeClass('dsc');
        $('#level_sort').addClass('asc');
		para.sort_style = "5";
    } else {
        $('#level_sort').removeClass('asc');
        $('#level_sort').addClass('dsc');
		para.sort_style = "4";
    }
    alarm_sort();
}

function alarm_sort() {
    var Rq = {data:{objectid:"real_alarm",type:"val_get",paraval:JSON.stringify([para])}, success:get_inner_alarm_sorted};
    request.addRequest([Rq]);
    
    function get_inner_alarm_sorted(d,r) {
        if (d.result == 'ok') {
            vmodel.inneralarmdata = d.data;
        } else {
            vmodel.inneralarmdata = [];
        }
    }
}

get_alarm_do_list();
//listDoInstid();
alarm_sort();
// setTimeout(alarm_sort(), 5000);
/* alarm_info.GET(); */