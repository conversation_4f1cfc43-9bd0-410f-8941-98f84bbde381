<!DOCTYPE html>
<html>
    <head>
        <!--#include virtual="/page/html/include.html" -->
    </head>

    <body class="navbar-fixed ms-controller" ms-controller="container">
        <!--#include virtual="/page/html/prompt.html" -->
        <!--#include virtual="/page/html/header.html" -->
        <div class="main-container container-fluid" ms-controller="northprotacal">
            <!--#include virtual="/page/html/menu.html" -->
            <div class="main-content">
                <div class="breadcrumbs" id="breadcrumbs">
                    <ul class="breadcrumb">
                        <li >
                            <i class="icon-home active"></i>
                            {{i18nkeyword.north_protocol.north_protocol_cfg}}
                        </li>
                    </ul><!-- /.breadcrumb -->
                </div>

                <div class="page-content">
                    <div class="row-fluid">
                        <div class="span10">
                            <div id="setSuccessalart" style="display:none;" class="alert alert-block alert-success">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_successfully}}
                            </div>
                            <div id="setFailurealart" style="display:none;" class="alert alert-block .alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.operate_failure}}
                            </div>
                            <div id="usernumbermax" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.usernumbermax}}
                            </div>
							<div id="sameuserandpwd" style="display:none;" class="alert alert-block alert-danger">
                                <i class="icon-warning-sign  icon-animated-bell">
                                </i>
                                {{i18nkeyword.user.sameuserandpwd}}
                            </div>
                            <!--PAGE CONTENT BEGINS-->

                            <div class="tabbable">
                                <ul class="nav nav-tabs" id="myTab">
                                    <li ms-class="'sps_config_1104_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_1104_tab')">
                                        <a data-toggle="tab" href="#sps_config_1104_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            YD/T 1104
                                        </a>
                                    </li>
                                    <li ms-class="'sps_config_1363_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_1363_tab')">
                                        <a data-toggle="tab" href="#sps_config_1363_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            YD/T 1363
                                        </a>
                                    </li>
                                    <li ms-class="'sps_config_sm_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_sm_tab')">
                                        <a data-toggle="tab" href="#sps_config_sm_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            {{i18nkeyword.north_protocol.Wireless_SC}}
                                        </a>
                                    </li>
                                    <li ms-class="'sps_config_power_sm_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_power_sm_tab')">
                                        <a data-toggle="tab" href="#sps_config_power_sm_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            {{i18nkeyword.north_protocol.Energy_SC}}
                                        </a>
                                    </li>
									<li ms-class="'sps_config_a_inter_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_a_inter_tab')">
                                        <a data-toggle="tab" href="#sps_config_a_inter_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
											{{i18nkeyword.north_protocol.A_INTER}}
                                        </a>
									</li>
									<li ms-class="'sps_config_b_inter_tab' == tab_list?'active':''" ms-click="tabChange('sps_config_b_inter_tab')">
                                        <a data-toggle="tab" href="#sps_config_b_inter_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
											{{i18nkeyword.north_protocol.B_INTER}}
                                        </a>
									</li>
                                    <li ms-class="'SNMP_config_tab' == tab_list?'active':''" ms-click="tabChange('SNMP_config_tab')">
                                        <a data-toggle="tab" href="#SNMP_config_tab" >
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            SNMP
                                        </a>
                                    </li>
                                    <li ms-class="'mqtt_config_tab' == tab_list?'active':''" ms-click="tabChange('mqtt_config_tab')">
                                        <a data-toggle="tab" href="#mqtt_config_tab"> <!--暂时屏蔽-->
                                            <i class="blue icon-user bigger-110">
                                            </i>
                                            MQTT
                                        </a>
                                    </li>
                                </ul>
                               <div class="tab-content">
									<div class="tab-pane" id="sps_config_1104_tab" ms-class="'sps_config_1104_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon  icon-cog orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_COM}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table class="table table-striped table-bordered table-hover">
																				<tbody id="interfaceN_data">
																					<tr>
																						<td style="width:25%">
																							{{i18nkeyword.north_protocol.link_name}}
																						</td>
																						<td style="width:75%">
																							<select class="editable input-medium" ms-duplex="@north_sps_1104_value_com['link inst id']" style="width:165px">
																								<option ms-attr="{value:''}">{{i18nkeyword.select}}</option>
																								<option ms-for="(cc,vv) in comvalues" ms-attr="{value:vv.instid,selected:vv.instid==north_sps_1104_value_com['link inst id']?true:false}">{{vv["Serial Name"]}}</option>
																							</select>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_1104_value(1)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon icon-cogs orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_IP}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																			<tbody>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.csu_role}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@north_sps_1104_csu_role" style="width:165px">
																							<option ms-attr="{value:'0'}">{{i18nkeyword.client}}</option>
																							<option ms-attr="{value:'1'}">{{i18nkeyword.server}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='0'">
																					<td style="width:25%">
																						NMS
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@cfg_1104_client_link_id" style="width:165px">
																							<!--ms-for:(i,ip_client) in north_sps_1104_ip_client_values-->
																							<option ms-attr="{value:ip_client['ip_nms_id']}">{{ip_client['ip_nms_name']}}</option>
																							<!--ms-for-end:-->
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ip_addr}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1104_value['IP address']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.data_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1104_value['data port']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.listen_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1104_value['listen port']">
																					</td>
																				</tr>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_enable}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="north_sps_1104_value['SSH Status']" style="width:165px">
																							<option ms-attr="{value:'0',selected:'0'==north_sps_1104_value['SSH Status']?true:false}">{{i18nkeyword.Disabled}}</option>
																							<option ms-attr="{value:'1',selected:'1'==north_sps_1104_value['SSH Status']?true:false}">{{i18nkeyword.Enabled}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='0' && north_sps_1104_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_username}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1104_value['SSH UserName']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1104_csu_role=='0' && north_sps_1104_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_password}}</td>
																						<td style="width:75%">
																						<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="north_sps_1104_value['SSH Passwd']">
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_1104_value(2)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--1104 参数结束-->
									<div class="tab-pane" id="sps_config_1363_tab" ms-class="'sps_config_1363_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon  icon-cog orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_COM}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table class="table table-striped table-bordered table-hover">
																				<tbody id="interfaceN_data">
																					<tr>
																						<td style="width:25%">
																							{{i18nkeyword.north_protocol.link_name}}
																						</td>
																						<td style="width:75%">
																							<select class="editable input-medium" ms-duplex="@north_sps_1363_value_com['link inst id']" style="width:165px">
																								<option ms-attr="{value:''}">{{i18nkeyword.select}}</option>
																								<option ms-for="(cc,vv) in comvalues" ms-attr="{value:vv.instid,selected:vv.instid==north_sps_1363_value_com['link inst id']?true:false}">{{vv["Serial Name"]}}</option>
																							</select>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_1363_value(1)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon icon-cogs orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_IP}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																			<tbody>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.csu_role}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@north_sps_1363_csu_role" style="width:165px">
																							<option ms-attr="{value:'0'}">{{i18nkeyword.client}}</option>
																							<option ms-attr="{value:'1'}">{{i18nkeyword.server}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='0'">
																					<td style="width:25%">
																						NMS
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@cfg_1363_client_link_id" style="width:165px">
																							<!--ms-for:(i,ip_client) in north_sps_1363_ip_client_values-->
																							<option ms-attr="{value:ip_client['ip_nms_id']}">{{ip_client['ip_nms_name']}}</option>
																							<!--ms-for-end:-->
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ip_addr}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1363_value['IP address']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.data_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1363_value['data port']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.listen_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1363_value['listen port']">
																					</td>
																				</tr>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_enable}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="north_sps_1363_value['SSH Status']" style="width:165px">
																							<option ms-attr="{value:'0',selected:'0'==north_sps_1363_value['SSH Status']?true:false}">{{i18nkeyword.Disabled}}</option>
																							<option ms-attr="{value:'1',selected:'1'==north_sps_1363_value['SSH Status']?true:false}">{{i18nkeyword.Enabled}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='0' && north_sps_1363_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_username}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_1363_value['SSH UserName']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_1363_csu_role=='0' && north_sps_1363_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_password}}</td>
																						<td style="width:75%">
																						<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="north_sps_1363_value['SSH Passwd']">
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_1363_value(2)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--1363 参数结束-->
									<div class="tab-pane" id="sps_config_sm_tab" ms-class="'sps_config_sm_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon  icon-cog orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_COM}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table class="table table-striped table-bordered table-hover">
																				<tbody id="interfaceN_data">
																					<tr>
																						<td style="width:25%">
																							{{i18nkeyword.north_protocol.link_name}}
																						</td>
																						<td style="width:75%">
																							<select class="editable input-medium" ms-duplex="@north_sps_sm_value_com['link inst id']" style="width:165px">
																								<option ms-attr="{value:''}">{{i18nkeyword.select}}</option>
																								<option ms-for="(cc,vv) in comvalues" ms-attr="{value:vv.instid,selected:vv.instid==north_sps_sm_value_com['link inst id']?true:false}">{{vv["Serial Name"]}}</option>
																							</select>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_sm_value(1)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon icon-cogs orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_IP}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																			<tbody>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.csu_role}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@north_sps_sm_csu_role" style="width:165px">
																							<option ms-attr="{value:'0'}">{{i18nkeyword.client}}</option>
																							<option ms-attr="{value:'1'}">{{i18nkeyword.server}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='0'">
																					<td style="width:25%">
																						NMS
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@cfg_sm_client_link_id" style="width:165px">
																							<!--ms-for:(i,ip_client) in north_sps_sm_ip_client_values-->
																							<option ms-attr="{value:ip_client['ip_nms_id']}">{{ip_client['ip_nms_name']}}</option>
																							<!--ms-for-end:-->
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ip_addr}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_sm_value['IP address']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.data_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_sm_value['data port']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.listen_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_sm_value['listen port']">
																					</td>
																				</tr>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_enable}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="north_sps_sm_value['SSH Status']" style="width:165px">
																							<option ms-attr="{value:'0',selected:'0'==north_sps_sm_value['SSH Status']?true:false}">{{i18nkeyword.Disabled}}</option>
																							<option ms-attr="{value:'1',selected:'1'==north_sps_sm_value['SSH Status']?true:false}">{{i18nkeyword.Enabled}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='0' && north_sps_sm_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_username}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_sm_value['SSH UserName']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_sm_csu_role=='0' && north_sps_sm_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_password}}</td>
																						<td style="width:75%">
																						<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="north_sps_sm_value['SSH Passwd']">
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_sm_value(2)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--SM 参数结束-->
									<div class="tab-pane" id="sps_config_power_sm_tab" ms-class="'sps_config_power_sm_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon  icon-cog orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_COM}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table class="table table-striped table-bordered table-hover">
																				<tbody id="interfaceN_data">
																					<tr>
																						<td style="width:25%">
																							{{i18nkeyword.north_protocol.link_name}}
																						</td>
																						<td style="width:75%">
																							<select class="editable input-medium" ms-duplex="@north_sps_power_sm_value_com['link inst id']" style="width:165px">
																								<option ms-attr="{value:''}">{{i18nkeyword.select}}</option>
																								<option ms-for="(cc,vv) in comvalues" ms-attr="{value:vv.instid,selected:vv.instid==north_sps_power_sm_value_com['link inst id']?true:false}">{{vv["Serial Name"]}}</option>
																							</select>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_power_sm_value(1)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon icon-cogs orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_IP}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																			<tbody>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.csu_role}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@north_sps_power_sm_csu_role" style="width:165px">
																							<option ms-attr="{value:'0'}">{{i18nkeyword.client}}</option>
																							<option ms-attr="{value:'1'}">{{i18nkeyword.server}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='0'">
																					<td style="width:25%">
																						NMS
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@cfg_power_sm_client_link_id" style="width:165px">
																							<!--ms-for:(i,ip_client) in north_sps_power_sm_ip_client_values-->
																							<option ms-attr="{value:ip_client['ip_nms_id']}">{{ip_client['ip_nms_name']}}</option>
																							<!--ms-for-end:-->
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ip_addr}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_power_sm_value['IP address']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.data_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_power_sm_value['data port']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.listen_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_power_sm_value['listen port']">
																					</td>
																				</tr>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_enable}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="north_sps_power_sm_value['SSH Status']" style="width:165px">
																							<option ms-attr="{value:'0',selected:'0'==north_sps_power_sm_value['SSH Status']?true:false}">{{i18nkeyword.Disabled}}</option>
																							<option ms-attr="{value:'1',selected:'1'==north_sps_power_sm_value['SSH Status']?true:false}">{{i18nkeyword.Enabled}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='0' && north_sps_power_sm_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_username}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_power_sm_value['SSH UserName']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_power_sm_csu_role=='0' && north_sps_power_sm_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_password}}</td>
																						<td style="width:75%">
																						<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="north_sps_power_sm_value['SSH Passwd']">
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_power_sm_value(2)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--POWER_SM 参数结束-->
									<div class="tab-pane" id="sps_config_a_inter_tab" ms-class="'sps_config_a_inter_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon  icon-cog orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_COM}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table class="table table-striped table-bordered table-hover">
																				<tbody id="interfaceN_data">
																					<tr>
																						<td style="width:25%">
																							{{i18nkeyword.north_protocol.link_name}}
																						</td>
																						<td style="width:75%">
																							<select class="editable input-medium" ms-duplex="@north_sps_a_inter_value_com['link inst id']" style="width:165px">
																								<option ms-attr="{value:''}">{{i18nkeyword.select}}</option>
																								<option ms-for="(cc,vv) in comvalues" ms-attr="{value:vv.instid,selected:vv.instid==north_sps_a_inter_value_com['link inst id']?true:false}">{{vv["Serial Name"]}}</option>
																							</select>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_a_inter_value(1)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
															<li class="dd-item dd2-item">
																<div class="dd-handle dd2-handle">
																	<i class="normal-icon icon-cogs orange bigger-130"></i>
																	<i class="drag-icon icon-move bigger-125"></i>
																</div>
																<div class="dd2-content">{{i18nkeyword.north_protocol.link_IP}}
																</div>
																<ol class="dd-list">
																	<li class="dd-item dd2-item">
																		<table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																			<tbody>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.csu_role}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@north_sps_a_inter_csu_role" style="width:165px">
																							<option ms-attr="{value:'0'}">{{i18nkeyword.client}}</option>
																							<option ms-attr="{value:'1'}">{{i18nkeyword.server}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='0'">
																					<td style="width:25%">
																						NMS
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="@cfg_a_inter_client_link_id" style="width:165px">
																							<!--ms-for:(i,ip_client) in north_sps_a_inter_ip_client_values-->
																							<option ms-attr="{value:ip_client['ip_nms_id']}">{{ip_client['ip_nms_name']}}</option>
																							<!--ms-for-end:-->
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ip_addr}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_a_inter_value['IP address']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='0'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.data_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_a_inter_value['data port']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.listen_port}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_a_inter_value['listen port']">
																					</td>
																				</tr>
																				<tr>
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_enable}}
																					</td>
																					<td style="width:75%">
																						<select class="editable input-medium" ms-duplex="north_sps_a_inter_value['SSH Status']" style="width:165px">
																							<option ms-attr="{value:'0',selected:'0'==north_sps_a_inter_value['SSH Status']?true:false}">{{i18nkeyword.Disabled}}</option>
																							<option ms-attr="{value:'1',selected:'1'==north_sps_a_inter_value['SSH Status']?true:false}">{{i18nkeyword.Enabled}}</option>
																						</select>
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='0' && north_sps_a_inter_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_username}}
																					</td>
																					<td style="width:75%">
																						<input class="form-control input-medium pull-left" ms-duplex="north_sps_a_inter_value['SSH UserName']">
																					</td>
																				</tr>
																				<tr ms-if="north_sps_a_inter_csu_role=='0' && north_sps_a_inter_value['SSH Status'] =='1'">
																					<td style="width:25%">
																						{{i18nkeyword.north_protocol.ssh_password}}</td>
																						<td style="width:75%">
																						<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="north_sps_a_inter_value['SSH Passwd']">
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</li>
																	<button class="button button-small button-flat-primary" onclick="set_north_sps_a_inter_value(2)">
																		{{i18nkeyword.north_protocol.modofy_link_para}}
																	</button>
																</ol>
															</li>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--a_inter 参数结束-->
									<div class="tab-pane" id="sps_config_b_inter_tab" ms-class="'sps_config_b_inter_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<div class="tab-content">
												<div class="col-sm-6">
													<div class="dd-draghandle">
														<ol class="dd-list">
															<table class="table table-striped table-bordered table-hover">
																	<tr ms-for = "(Attrindex,Attr) in north_sps_b_inter_AttrData">
																		<td ms-visible="Attr.visible && Attr.visible!='no'" style="width:35%">{{Attr.full_name}}</td>
																		<td ms-if="getconventionlength(Attr.convention)>0 && Attr.visible && Attr.visible!='no'" style="width:65%">
																			<select ms-duplex="north_sps_b_inter_ValueData[0][Attr.name]" style="width: 164px; margin-left: 5px;">
																					<option ms-for="(index,name) in Attr.convention" ms-attr="{value:index}">{{name}}</option>
																			</select>
																		</td>
																		<td ms-if="getconventionlength(Attr.convention)===0 && Attr.visible && Attr.visible!='no' && Attr.id !='login_password' &&  Attr.id !='ftp_password'">
																			<input class="form-control input-medium pull-left" ms-duplex="north_sps_b_inter_ValueData[0][Attr.name]" ms-attr="{disabled:@judge_b_inter_para(Attr.name)}">
																		</td>
																		<td ms-if="getconventionlength(Attr.convention)===0 && Attr.visible && Attr.visible!='no' && ( Attr.id =='login_password' || Attr.id =='ftp_password')">
																			<input type="Password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-duplex="north_sps_b_inter_ValueData[0][Attr.name]">
																		</td>
																	</tr>
															</table>
															<button class="button button-small button-flat-primary" onclick="set_north_sps_b_inter_value()">
																	{{i18nkeyword.north_protocol.modofy_link_para}}
															</button>
														</ol>
													</div>
												</div>
											</div>
										</div>
									</div><!--b_inter 参数结束-->
									<div class="tab-pane" id="SNMP_config_tab" ms-class="'SNMP_config_tab' == tab_list?'active':''">
										<div class="tabbable tabs-left">
											<ul class="nav nav-tabs">
												<li ms-class="'snmpuser_tab' == tab_list2?'active':''" ms-click="tabChange2('snmpuser_tab')">
													<a data-toggle="tab" href="#snmpuser_tab">
														<i class="blue icon-user bigger-110">
														</i>
														{{i18nkeyword.north_protocol.snmp_user_cfg}}
													</a>
												</li>
												<li ms-class="'snmp_trap_tab' == tab_list2?'active':''" ms-click="tabChange2('snmp_trap_tab')">
													<a data-toggle="tab" href="#snmp_trap_tab">
														<i class="blue icon-book bigger-110">
														</i>
														{{i18nkeyword.north_protocol.snmp_para_trap}}
													</a>
												</li>
											</ul>
											<div class="tab-content">
												<div class="tab-pane" id="snmpuser_tab" ms-class="'snmpuser_tab' == tab_list2?'active':''">
													<div class="col-sm-6">
														<div class="dd dd-draghandle">
															<ol class="dd-list">
																<li class="dd-item dd2-item">
																	<div class="dd-handle dd2-handle">
																		<i class="normal-icon  icon-cog orange bigger-130"></i>
																		<i class="drag-icon icon-move bigger-125"></i>
																	</div>
																	<div class="dd2-content">{{i18nkeyword.north_protocol.snmp_para_self}}
																	</div>
																	<ol class="dd-list">
																		<li class="dd-item dd2-item">
																			   <table class="table table-striped table-bordered table-hover">
																					<tbody id="interfaceN_data">
																						<!--ms-for:(i,ifn_para) in structuredata-->
																						<tr ms-if = "ifn_para.id == 'snmp_enable'">
																							<td>{{ifn_para.full_name}}</td>
																							<td>
																								<select  ms-duplex="ifnvalue['SNMP Enable']" style="width:163px" data-duplex-changed="changecanmake()" ms-change="changeChannelData(1, 'snmp')">
																										<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index,selected:index==ifnvalue['SNMP Enable']?true:false}">{{name}}</option>
																								</select>
																							</td>
																						</tr>
																						<tr ms-if = "ifn_para.id == 'community_strong_passwd_enable'">
																							<td>{{ifn_para.full_name}}</td>
																							<td>
																								<select  ms-duplex="ifnvalue['SNMP Community Strong Passwd Enable']" style="width:163px" data-duplex-changed="changecanmake2()" ms-change="changeChannelData(1, 'snmp')">
																										<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index,selected:index==ifnvalue['SNMP Community Strong Passwd Enable']?true:false}">{{name}}</option>
																								</select>
																							</td>
																						</tr>
																						<!--ms-for-end:-->
																						<!--ms-for:(i,ifn_para) in structuredata-->
																						<tr  ms-if="ifn_para.visible!=='NO' && @judge_snmp_trap_para(ifn_para.name) ==='snmp_self' ">
																							<td>{{ifn_para.full_name}}</td>
																							<td ms-if="getconventionlength(ifn_para.convention)>0">
																								<select  ms-duplex="ifnvalue[ifn_para.name]" style="width:163px">
																									<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																								</select>
																							</td>
																							<td ms-if="getconventionlength(ifn_para.convention)==0">
																									<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-change="changeChannelData(0, 'snmp')" ms-duplex="ifnvalue[ifn_para.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
																							</td>
																						</tr>
																						<!--ms-for-end:-->
																						<!--ms-for:(i,ifn_para) in structuredata-->
																						<tr ms-if = "ifn_para.id == 'v3user_strong_passwd_enable'">
																							<td>{{ifn_para.full_name}}</td>
																							<td>
																								<select  ms-duplex="ifnvalue['SNMP V3 User Strong Passwd Enable']" style="width:163px" data-duplex-changed="changecanmake3()" ms-change="changeChannelData(1, 'snmp')">
																										<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index,selected:index==ifnvalue['SNMP V3 User Strong Passwd Enable']?true:false}">{{name}}</option>
																								</select>
																							</td>
																						</tr>
																						<!--ms-for-end:-->
																					</tbody>
																				</table>
																		</li>
																		<button class="button button-small button-flat-primary"  onclick="set_ifn_value('snmp')"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}
																		</button>
																	</ol>
																</li>
																<li class="dd-item dd2-item">
																	<div class="dd-handle dd2-handle">
																		<i class="normal-icon icon-cogs orange bigger-130"></i>
																		<i class="drag-icon icon-move bigger-125"></i>
																	</div>
																	<div class="dd2-content">{{i18nkeyword.north_protocol.snmp_v3user}}
																	</div>
																	<ol class="dd-list">
																		<li class="dd-item dd2-item">
																			 <table id="snmp_v3user_cfg" class="table table-striped table-bordered table-hover">
																				<thead>
																					<tr>
																						<th class="center" style="display:none">
																							<label>
																								<input type="checkbox" class="ace" />
																								<span class="lbl"></span>
																							</label>
																						</th>
																						<!--ms-for:(i,para) in snmp_v3user_structre-->
																						<th>{{para.full_name}}</th>
																						<!--ms-for-end:-->
																					</tr>
																				</thead>
																				<tbody>
																					<!--ms-for:(i,snmp_v3user) in snmp_v3user_infos-->
																						<tr>
																							<td>
                                                                                                    <input class="form-control input-medium pull-left" ms-blur="checkSnmpNameAndKey()" ms-input="changeSnmpNameOrKey(i, 'SNMP User Name')" ms-change="changeChannelData(0, 'snmp_v3user')" ms-duplex="snmp_v3user['SNMP User Name']" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px"/>
                                                                                            </td>
                                                                                            <td>
                                                                                                <select  ms-duplex="snmp_v3user['SNMP Authentication Protocol']" style="width:160px" ms-change="changeChannelData(1, 'snmp_v3user')">
                                                                                                    <option ms-for="(index,name) in snmp_v3user_structre[1].convention" ms-attr="{value:index,selected:index==snmp_v3user['SNMP Authentication Protocol']?true:false}">{{name}}</option>
                                                                                                </select>
                                                                                            </td>
                                                                                             <td>
                                                                                                    <input type="password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-blur="checkSnmpNameAndKey()" ms-input="changeSnmpNameOrKey(i, 'SNMP Authentication Key')" ms-change="changeChannelData(0, 'snmp_v3user')"  ms-duplex="snmp_v3user['SNMP Authentication Key']" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px"/>
                                                                                            </td>
                                                                                            <td>
                                                                                                <select  ms-duplex="snmp_v3user['SNMP Privacy Protocol']" style="width:160px" ms-change="changeChannelData(1, 'snmp_v3user')">
                                                                                                    <option ms-for="(index,name) in snmp_v3user_structre[3].convention" ms-attr="{value:index,selected:index==snmp_v3user['SNMP Privacy Protocol']?true:false}">{{name}}</option>
                                                                                                </select>
                                                                                            </td>
                                                                                            <td>
                                                                                                    <input type="password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-blur="checkSnmpNameAndKey()" ms-input="changeSnmpNameOrKey(i, 'SNMP Privacy Key')" ms-change="changeChannelData(0, 'snmp_v3user')" ms-duplex="snmp_v3user['SNMP Privacy Key']" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px"/>
																							</td>
																							<td>
																								<select  ms-duplex="snmp_v3user['SNMP Permission Scope']" style="width:160px" ms-change="changeChannelData(1, 'snmp_v3user')">
																									<option ms-for="(index,name) in snmp_v3user_structre[5].convention" ms-attr="{value:index}">{{name}}</option>
																								</select>
																							</td>
																							<td>
																								<div class="visible-md visible-lg hidden-sm hidden-xs action-buttons" style="width:100px">
																									<a class="red" href="#" ms-click="delete_snmp_v3user(snmp_v3user.instid,snmp_v3user['SNMP User Name'])">
																										<i class="icon-trash bigger-110"><span style="font-size: 100%">{{i18nkeyword.delete}}</span></i>
																									</a>
																								</div>
																								<div class="visible-xs visible-sm hidden-md hidden-lg">
																									<div class="inline position-relative">
																										<ul class="dropdown-menu dropdown-only-icon dropdown-yellow pull-right dropdown-caret dropdown-close">
																											<li>
																												<a href="#" class="tooltip-error" data-rel="tooltip" title="Delete">
																													<span class="red">
																														<i class="icon-trash bigger-120"></i>
																													</span>
																												</a>
																											</li>
																										</ul>
																									</div>
																								</div>
																							</td>
																						</tr>
																					<!--ms-for-end:-->
																					<tr ms-visible="snmp_v3user_add == '1'">
																						<!-- <td class="center" style="display:none">
																							<label>
																								<input type="checkbox" class="ace" />
																								<span class="lbl"></span>
																							</label>
																						</td> -->
                                                                                        <td>
																							<input id= "snmp_v3_username" autocomplete="off" class="form-control input-medium pull-left" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px">
																						    </input>
                                                                                        </td>
                                                                                        <td>
																							<select id="snmp_v3_auth_proto" ms-duplex="add_snmp_v3user_info['SNMP Authentication Protocol']" style="width:160px">
                                                                                                <option ms-for="(index,name) in snmp_v3user_structre[1].convention" ms-attr="{value:index}">{{name}}</option>
                                                                                            </select>
                                                                                        </td>
                                                                                         <td>
																							<input id="snmp_v3_Authentication" type="password" autocomplete="new-password" class="form-control input-medium pull-left"  ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px">
																							</input>
                                                                                        </td>
                                                                                        <td>
																							<select id="snmp_v3_priv_proto" ms-duplex="add_snmp_v3user_info['SNMP Privacy Protocol']" style="width:160px">
                                                                                                <option ms-for="(index,name) in snmp_v3user_structre[3].convention" ms-attr="{value:index}">{{name}}</option>
                                                                                            </select>
                                                                                        </td>
                                                                                        <td>
																							<input id="snmp_v3_Privacy" type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:100px">
																							</input>
                                                                                        </td>
																						<td>
                                                                                            <select id="snmp_v3_perm_scope" ms-duplex="add_snmp_v3user_info['SNMP Permission Scope']" style="width:160px">
                                                                                                <option ms-for="(index,name) in snmp_v3user_structre[5].convention" ms-attr="{value:index,selected:add_snmp_v3user_info['SNMP Permission Scope']==index?true:false}">{{name}}</option>
                                                                                            </select>
                                                                                        </td>
																						<td>
																							<div class="visible-md visible-lg hidden-sm hidden-xs btn-group">
																									<button class="btn btn-xs btn-success" onclick="add_snmp_v3user_confirm()">
																										<i class="icon-ok bigger-100"></i>
																									</button>
																									<button class="btn btn-xs btn-danger" onclick="add_snmp_v3user_cancel()">
																										<i class="icon-trash bigger-100"></i>
																									</button>
																								</div>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</li>
																		<div style="padding-right:50px;display:inline">
																			 <button class="button button-small button-flat-primary"  onclick="set_snmp_v3user_value()"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}</button>
																		</div>
																		<div style="padding-right:50px;display:inline">
																			 <button class="button button-small button-flat-primary" onclick="show_add_snmp_v3user()"><i class="icon-plus bigger-100">{{i18nkeyword.add}}</i></button>
																		</div>
																	</ol>
																</li>
															</ol>
														</div>
													</div>
												</div><!--tab snmp用户参数结束-->
												<div class="tab-pane" id="snmp_trap_tab" ms-class="'snmp_trap_tab' == tab_list2?'active':''">
													<div class="col-sm-6">
														<div class="dd dd-draghandle">
															<ol class="dd-list">
																<li class="dd-item dd2-item">
																	<div class="dd-handle dd2-handle">
																		<i class="normal-icon icon-cog orange bigger-130"></i>
																		<i class="drag-icon icon-move bigger-125"></i>
																	</div>
																	<div class="dd2-content">{{i18nkeyword.north_protocol.snmp_trap}}
																	</div>
																	<ol class="dd-list">
																		<li class="dd-item dd2-item">
																			   <table class="table table-striped table-bordered table-hover">
																				<tbody id="snmp_data">
																					<!--ms-for:(i,ifn_para) in structuredata-->
																					<tr ms-if="ifn_para.id==='mode'">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<select  ms-duplex="ifnvalue[ifn_para.name]"  ms-change="changeChannelData(1, 'snmp_trap')" style="width:163px" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name)}">
																								<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																							</select>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='name'">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<input class="form-control input-medium pull-left" ms-change="changeChannelData(0, 'snmp_trap')" ms-duplex="ifnvalue[ifn_para.name]" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name) , title:@show_disallow_input() , placeholder:@show_disallow_input()}">
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='level' && !judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name) ">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<select  ms-duplex="ifnvalue[ifn_para.name]"  ms-change="changeChannelData(1, 'snmp_trap')" style="width:163px" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name)}">
																								<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																							</select>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='trapv3_strong_passwd_enable' && ifnvalue['SNMP V3 Notification Severity Level']>=1">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<select  ms-duplex="ifnvalue[ifn_para.name]"  ms-change="changeChannelData(1, 'snmp_trap')" style="width:163px" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name)}">
																								<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																							</select>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='auth_proto' &&ifnvalue['SNMP Notification Mode']==3 && (ifnvalue['SNMP V3 Notification Severity Level']>=1) ">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<select  ms-duplex="ifnvalue[ifn_para.name]"  ms-change="changeChannelData(1, 'snmp_trap')" style="width:163px" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name)}">
																								<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																							</select>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='auth_key' &&ifnvalue['SNMP Notification Mode']==3 && (ifnvalue['SNMP V3 Notification Severity Level']>=1) ">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-change="changeChannelData(0, 'snmp_trap')"  ms-duplex="ifnvalue[ifn_para.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:152px"/>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='priv_proto' &&ifnvalue['SNMP Notification Mode']==3 && (ifnvalue['SNMP V3 Notification Severity Level']==2) ">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<select  ms-duplex="ifnvalue[ifn_para.name]"  ms-change="changeChannelData(1, 'snmp_trap')" style="width:163px" ms-attr="{disabled:@judge_snmp_v3user_para(ifnvalue['SNMP Notification Mode'],ifn_para.name)}">
																								<option ms-for="(index,name) in ifn_para.convention" ms-attr="{value:index}">{{name}}</option>
																							</select>
																						</td>
																					</tr>
																					<tr ms-if="ifn_para.id==='priv_Key' &&ifnvalue['SNMP Notification Mode']==3 && (ifnvalue['SNMP V3 Notification Severity Level']==2) ">
																						<td>{{ifn_para.full_name}}</td>
																						<td>
																							<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-change="changeChannelData(0, 'snmp_trap')"  ms-duplex="ifnvalue[ifn_para.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:152px"/>
																						</td>
																					</tr>
																					<!--ms-for-end:-->
																				</tbody>	
																				</table> 
																		</li>
																		<button class="button button-small button-flat-primary"  onclick="set_ifn_value('snmp_trap')"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}
																		</button>
																	</ol>
																</li>
																<li class="dd-item dd2-item">
																	<div class="dd-handle dd2-handle">
																		<i class="normal-icon icon-cogs orange bigger-130"></i>
																		<i class="drag-icon icon-move bigger-125"></i>
																	</div>
																	<div class="dd2-content">{{i18nkeyword.north_protocol.snmp_manager_cfg}}
																	</div>
																	<ol class="dd-list">
																		<li class="dd-item dd2-item">
																			<table id="snmp_manager_cfg" class="table table-striped table-bordered table-hover">
																				<thead>
																					<tr>
																						<th class="center" style="display:none">
																							<label>
																								<input type="checkbox" class="ace" />
																								<span class="lbl"></span>
																							</label>
																						</th>
																						<!--ms-for:(i,para) in snmp_manager_structre-->
																						<th>{{para.full_name}}</th>
																						<!--ms-for-end:-->
																					</tr>
																				</thead>
																				<tbody>
																					<!--ms-for:(i,snmp_manager) in snmp_manager_infos-->
																						<tr>
																							<!--ms-for:(i,para) in snmp_manager_structre-->
																								<td>
																									<input class="form-control input-medium pull-left"  ms-change="changeChannelData(0, 'snmp_manager')" ms-duplex="snmp_manager[para.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}" style="width:200px">
																								</td>
																							<!--ms-for-end:-->
																							<td>
																								<div class="visible-md visible-lg hidden-sm hidden-xs action-buttons" style="width:100px">
																									<a class="red" href="#" ms-click="delete_snmp_manager(snmp_manager.instid,snmp_manager['SNMP Manager Name'])">
																										<i class="icon-trash bigger-110"><span style="font-size: 100%">{{i18nkeyword.delete}}</span></i>
																									</a>
																								</div>
																								<div class="visible-xs visible-sm hidden-md hidden-lg">
																									<div class="inline position-relative">
																										<ul class="dropdown-menu dropdown-only-icon dropdown-yellow pull-right dropdown-caret dropdown-close">
																											<li>
																												<a href="#" class="tooltip-error" data-rel="tooltip" title="Delete">
																													<span class="red">
																														<i class="icon-trash bigger-120"></i>
																													</span>
																												</a>
																											</li>
																										</ul>
																									</div>
																								</div>
																							</td>
																						</tr>
																					<!--ms-for-end:-->
																					<tr id="tr_default_manager_add" style ="display:none">
																						<td class="center" style="display:none">
																							<label>
																								<input type="checkbox" class="ace" />
																								<span class="lbl"></span>
																							</label>
																						</td>
																							<td>
																								<input id="snmp_manager_username" autocomplete="off" class="form-control input-medium pull-left"  ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
																							</td>
																							<td>
																								<input id="snmp_manager_ip" autocomplete="off" class="form-control input-medium pull-left"  ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
																							</td>
																							<td>
																								<input id="snmp_manager_port" autocomplete="off" class="form-control input-medium pull-left"  ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
																							</td>
																						<td>
																							<div class="visible-md visible-lg hidden-sm hidden-xs btn-group">
																									<button class="btn btn-xs btn-success" onclick="add_snmp_manager_confirm()">
																										<i class="icon-ok bigger-100"></i>
																									</button>
																									<button class="btn btn-xs btn-danger" onclick="add_snmp_manager_cancel()">
																										<i class="icon-trash bigger-100"></i>
																									</button>
																								</div>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																			<div style="padding-right:50px;display:inline">
																				 <button class="button button-small button-flat-primary"  onclick="set_snmp_manager_value()"><i class="icon-pencil bigger-100"></i>{{i18nkeyword.set}}</button>
																			</div>
																			<div style="padding-right:50px;display:inline">
																				 <button class="button button-small button-flat-primary" onclick="show_add_snmp_manager()"><i class="icon-plus bigger-100">{{i18nkeyword.add}}</i></button>
																			</div>
																		</li>
																	</ol>
																</li>
															</ol>
														</div>
													</div>
												</div><!--Tab snmp Trap参数结束-->
											</div>
										</div>
                                    </div><!--Tab_snmp结束-->
									<div class="tab-pane" id="mqtt_config_tab" ms-class="'mqtt_config_tab' == tab_list?'active':''">
                                        <table class="table table-striped table-bordered table-hover" >
											<tr>
												<th>{{i18nkeyword.attr_name}}</th>
												<th>{{i18nkeyword.attr_value}}</th>
											</tr>
                                            <tbody >
												<!--ms-for:(el,al) in mqtt_structre-->
												<tr ms-if="al.id =='proxy_server_addr_mode'">
													<td>{{al.full_name}}</td>
													<td>
														<select  ms-duplex="mqtt_infos[0][al.name]" style="width:163px" >
															<option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
														</select>
													</td>
												</tr>
												<tr ms-if="al.id =='proxyserverip'">
													<td>{{al.full_name}}</td>
													<td >
														<input class="form-control input-medium pull-left" id="proxyserverip-inp" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<tr ms-if="al.id =='proxyserverport'">
													<td>{{al.full_name}}</td>
													<td >
														<input class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<tr ms-if="al.id =='username'">
													<td>{{al.full_name}}</td>
													<td >
														<input class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<tr ms-if="al.id =='password'">
													<td>{{al.full_name}}</td>
													<td>
														<input type="password" autocomplete="new-password" class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]" ms-attr="{title:@show_disallow_input() , placeholder:@show_disallow_input()}">
													</td>
												</tr>
												<tr ms-if="al.id =='proxyserverdn'">
													<td>{{al.full_name}}</td>
													<td>
														<input class="form-control input-medium pull-left" id="proxyserverdn-inp" ms-duplex="mqtt_infos[0][al.name]" >
													</td>
												</tr>
												<tr ms-if="al.id =='mqttdnsip'">
													<td>{{al.full_name}}</td>
													<td>
														<input class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<tr ms-if="al.id =='ssl_enable'">
													<td>{{al.full_name}}</td>
													<td>
														<select  ms-duplex="mqtt_infos[0][al.name]" style="width:163px" >
															<option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
														</select>
													</td>
												</tr>

												<tr ms-if="al.id =='ssl_auth_mode' && mqtt_infos[0]['MQTT SSL Security Enable'] == 1">
													<td>{{al.full_name}}</td>
													<td>
														<select  ms-duplex="mqtt_infos[0][al.name]" style="width:163px" >
															<option ms-for="(index,name) in al.convention" ms-attr="{value:index}">{{name}}</option>
														</select>
													</td>
												</tr>
												<tr ms-if="al.id =='data_pub_period'">
													<td>{{al.full_name}}</td>
													<td>
														<input class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<tr ms-if="al.id =='pub_theme'">
													<td>{{al.full_name}}</td>
													<td>
														<input class="form-control input-medium pull-left" ms-duplex="mqtt_infos[0][al.name]">
													</td>
												</tr>
												<!--ms-for-end:-->
                                            </tbody>
                                        </table>
                                        <button class="button button-small button-flat-primary" onclick="set_mqtt_value(this)">
                                            {{i18nkeyword.set}}
                                        </button>
                                    </div><!--Tab_Mqtt结束-->
                                </div>
                            </div><!--/tabbable-->
                            <!--PAGE CONTENT ENDS-->
                        </div><!--/span10-->
                    </div><!--/row-fluid-->
                </div><!--/page-content-->
            </div><!--/main-content-->
        </div><!--/main-container-->
        <!--#include virtual="/page/html/foot.html" -->
        <!-- inline scripts related to this page -->
        <link rel="stylesheet" href="/page//html/plug_pswd/css/plug_pswd.css" />
        <script src="/page//html/plug_pswd/js/plug_pswd.js"></script>
        <script src="/page/js/config_northprotocol.js"></script>
</body>

</html>