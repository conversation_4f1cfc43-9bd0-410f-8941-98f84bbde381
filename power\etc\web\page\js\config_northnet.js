var vmodel = avalon.define({
	$id:'northnet',
	listactive:"",
	tab_list:'wired',
	tab_list2:'ipv4_data',
	northnet_WiredAttrData:[],
	northnet_WiredValueData:[],
    northnet_Wiredv6AttrData:[],
    northnet_Wiredv6ValueData:[],
	northnet_WirelessAttrData:[],
    northnet_WirelessValueData:[],
    northnet_VPNAttrData:[],
    northnet_VPNValueData:[],
    northnet_show:[],
	northnet_NetconstatusAttrData:[],
	northnet_NetconstatusValueData:[],
	northnet_WirelessSigData:[],

    northnet_WiredRouteData:[],
    northnet_WirelessRouteData:[],
    northnet_VPNRouteData:[],
	getconventionlength : function(con) {
        var length = 0;
        for(var i in con){
        	length++;
        }
        return length;
    },
    changeshow :function(d){
        if(event.path[0].name ==="IP Allocation Mode")
        {
            if(event.path[0].value === "1"){
                vmodel.northnet_show[2].show = 0;
                vmodel.northnet_show[3].show = 0;
                vmodel.northnet_show[4].show = 0;
            }
            else if(event.path[0].value === "0"){
                vmodel.northnet_show[2].show = 1;
                vmodel.northnet_show[3].show = 1;
                vmodel.northnet_show[4].show = 1;
            }
        }
    },
    tabChange : function(tab) {
        vmodel.tab_list = tab;
        set_cookie_with_path("tab_list", vmodel.tab_list);
    },
    tabChange2 : function(tab) {
        vmodel.tab_list2 = tab;
        set_cookie_with_path("tab_list2", vmodel.tab_list2);
    },
    changeVPNData:function(attr, value) {
        if ((attr == "VPN Type") && (value == "1" || value == "2")) {
            for (let i = 0; i < vmodel.northnet_VPNAttrData.length; i++) {
                if (vmodel.northnet_VPNAttrData[i].id == "ipsec_cacertificate"
                    || vmodel.northnet_VPNAttrData[i].id == "ipsec_usercertificate"
                    || vmodel.northnet_VPNAttrData[i].id == "ipsec_servercertificate") {
                        vmodel.northnet_VPNAttrData[i].visible = 'yes';
                    }
            }
        } else if ((attr == "VPN Type") && (value == "0" || value == "3")) {
            for (let i = 0; i < vmodel.northnet_VPNAttrData.length; i++) {
                if (vmodel.northnet_VPNAttrData[i].id == "ipsec_cacertificate"
                    || vmodel.northnet_VPNAttrData[i].id == "ipsec_usercertificate"
                    || vmodel.northnet_VPNAttrData[i].id == "ipsec_servercertificate") {
                        vmodel.northnet_VPNAttrData[i].visible = 'no';
                    }
            }
        } else {}
    },
    show_disallow_input:function(){
		return mainvalue.i18nkeyword.Disallow_input + " & ";
	},
});

// 展开或折叠
function changeSlide(obj) {
	var id = $(obj).closest(".widget-box").attr("id");
	let widgetBody = document.getElementById(id).childNodes[1];
	let iLabel = document.getElementById(id).childNodes[0].childNodes[1].childNodes[0].childNodes[0];
	let lighter = document.getElementById(id).childNodes[0].childNodes[0];
	if(Array.from(widgetBody.classList).includes("user-dis")) { //展开
        iLabel.setAttribute("class", "icon-chevron-up");
        lighter.classList.add("fontbold");
        widgetBody.classList.remove("user-dis");
	} else { //折叠
        iLabel.setAttribute("class", "icon-chevron-down");
        lighter.classList.remove("fontbold");
        widgetBody.classList.add("user-dis");
	}
}

// 校验静态路由值
function checkRouteValue(para) {
    for(var i in para) {
        let ip1 = para[i]["Network Dest"];
        let ip2 = para[i]["Network Mask"];
        if(ip1 == "" && ip2 == "") {
            continue;
        } else if(ip1 == "0.0.0.0" || !checkIPAddress(0,ip1)) {
            alert (ip1 + " : " + mainvalue.i18nkeyword.northnet.ip_illegal_tip + "," + mainvalue.i18nkeyword.northnet.static_route_tip);
            return false;
        } else if(ip2 == "0.0.0.0" || !checkIPAddress(1,ip2)) {
            alert (ip2 + " : " + mainvalue.i18nkeyword.northnet.netmask_illegal_tip  + "," + mainvalue.i18nkeyword.northnet.static_route_tip);
            return false;
        }
    }

    return true;
}

// 校验VPN用户名/密码
function checkVPNValue(index,para) {
    var regexp = /^[a-z0-9A-Z\~\!\@\#\$\%\^\&\*\(\)\_\+\{\}\|\:\"\<\>\?\`\-\=\[\]\\;\'\,\.\/]*$/;
    var maxlen = index ? 32 : 31;
    
    if(para.length > maxlen || !regexp.test(para)) {
        return false;
    }
	return true;
}

//获取instid
var rqWiredList = {data:{objectid:"plat.wiredconnection",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getNorthnetWiredList};
request.addRequest([rqWiredList]);

function getNorthnetWiredList(d,r)
{
	// 获取 Attr 信息
	var rqWiredAttr = {data:{objectid:"plat.wiredconnection",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:getNorthnetWiredAttr};
	request.addRequest([rqWiredAttr]);
	// 获取value信息
	var rqWiredValue = {data:{objectid:"plat.wiredconnection",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getNorthnetWiredValue};
	request.addRequest([rqWiredValue]);
}

function getNorthnetWiredAttr(d,r){
	addvalue(vmodel.northnet_WiredAttrData,d.data,"name");
    var showattr = [];
    for(var i in d.data){
       var unit = {};
       unit.name = d.data[i].name;
       unit.show = 1;
       showattr.push(unit);
    }
    addvalue(vmodel.northnet_show,showattr,"name");
}

function getNorthnetWiredValue(d,r){
	addvalue(vmodel.northnet_WiredValueData,d.data,"instid");
    if(d.data[0]["IP Allocation Mode"] === "0"){
        vmodel.northnet_show[2].show = 1;
        vmodel.northnet_show[3].show = 1;
        vmodel.northnet_show[4].show = 1;
    }
    else{
        vmodel.northnet_show[2].show = 0;
        vmodel.northnet_show[3].show = 0;
        vmodel.northnet_show[4].show = 0;
    }
}

//获取instid
var rqWirelessList = {data:{objectid:"plat.wirelessconnection",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getNorthnetWirelessList};
request.addRequest([rqWirelessList]);

function getNorthnetWirelessList(d,r)
{
	// 获取 Attr 信息
	var rqWirelessAttr = {data:{objectid:"plat.wirelessconnection",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:getNorthnetWirelessAttr};
	request.addRequest([rqWirelessAttr]);
	// 获取value信息
	var rqWirelessValue = {data:{objectid:"plat.wirelessconnection",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getNorthnetWirelessValue};
	request.addRequest([rqWirelessValue]);
}

function getNorthnetWirelessAttr(d,r){
	addvalue(vmodel.northnet_WirelessAttrData,d.data,"name");
}

function getNorthnetWirelessValue(d,r){
	addvalue(vmodel.northnet_WirelessValueData,d.data,"instid");
}

//获取instid
var rqVPNList = {data:{objectid:"plat.VPNconnection",type:"list",paranum:"1",paraval:JSON.stringify([{}])},success:getNorthnetVPNList};
request.addRequest([rqVPNList]);

function getNorthnetVPNList(d,r)
{
	// 获取 Attr 信息
	var rqVPNAttr = {data:{objectid:"plat.VPNconnection",type:"attr_get",paranum:"1",paraval:JSON.stringify([{"instid":""}])},success:getNorthnetVPNAttr};
	request.addRequest([rqVPNAttr]);
	// 获取 value 信息
	var rqVPNValue = {data:{objectid:"plat.VPNconnection",type:"val_get",paranum:"1",paraval:JSON.stringify(d.data)},success:getNorthnetVPNValue};
	request.addRequest([rqVPNValue]);
}

function getNorthnetVPNAttr(d,r){
    for (let i = 0; i < d.data.length; i++) {
        if (d.data[i].id == "VPNtype") {
            d.data[i].convention = "0:L2TP,";
        }
    }
	addvalue(vmodel.northnet_VPNAttrData,d.data,"name");
}

function getNorthnetVPNValue(d,r){
	addvalue(vmodel.northnet_VPNValueData,d.data,"instid");
}


function setVPNData(){
    let ip = vmodel.northnet_VPNValueData[0]["Server Ip"];
    let username = vmodel.northnet_VPNValueData[0]["User Name"];
    let password = vmodel.northnet_VPNValueData[0]["Password"];
    if(!checkIPAddress(0,ip)) {
        alert (mainvalue.i18nkeyword.northnet.ip_illegal_tip);
        request.addRequest([rqVPNList]);
        return ;
    }
    if (!checkVPNValue(0,username)) {
        alert (mainvalue.i18nkeyword.northnet.username_illegal_tip);
        request.addRequest([rqVPNList]);
        return ;
    }
    if (password != WEB_SPECIFY_STR && !checkVPNValue(1,password)) {
        alert (mainvalue.i18nkeyword.northnet.pswd_illegal_tip);
        request.addRequest([rqVPNList]);
        return ;
    }
    if (username.indexOf("&") != -1 || password.indexOf("&") != -1) {
        alert(mainvalue.i18nkeyword.Disallow_input + "&");
        request.addRequest([rqVPNList]);
        return;
    }
	var setVPNvalue = {data:{objectid:"plat.VPNconnection",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.northnet_VPNValueData)},success:regetVPNNetconstatusValue};
	request.addRequest([setVPNvalue]);
}

function regetVPNNetconstatusValue(d,r){
	request.addRequest([rqVPNList]);
    // 默认路由为是时，重新刷新其他网络参数配置
    if (vmodel.northnet_VPNValueData[0]["Default Router"]) {
        request.addRequest([rqWiredList]);
        request.addRequest([rqWirelessList]);
    }
    if(d.result ==="ok"){
        mainvalue.controlsuccess = "success";
    }
    else{
        mainvalue.controlsuccess = "failure";
    }
}


function setWirelessData(){
    let password = vmodel.northnet_WirelessValueData[0]["Password"];
    let backup_password = vmodel.northnet_WirelessValueData[0]["Backup Password"];
    let tmp = vmodel.northnet_WirelessValueData[0];
    if ((password != WEB_SPECIFY_STR && backup_password != WEB_SPECIFY_STR) && ( password.length > 32 || backup_password.length >32)) {
        alert (mainvalue.i18nkeyword.northnet.pswd_len_tip);
        request.addRequest([rqWirelessList]);
        return ;
    }
    if (tmp["APN"].indexOf("&") != -1 || tmp["Dial Code"].indexOf("&") != -1 || tmp["User Name"].indexOf("&") != -1 || tmp["Password"].indexOf("&") != -1 || 
        tmp["Backup APN"].indexOf("&") != -1 || tmp["Backup Dial Code"].indexOf("&") != -1 || tmp["Backup User Name"].indexOf("&") != -1 || tmp["Backup Password"].indexOf("&") != -1) {
        alert(mainvalue.i18nkeyword.Disallow_input + "&");
        request.addRequest([rqWirelessList]);
        return;
    }
	var setWirelessvalue = {data:{objectid:"plat.wirelessconnection",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.northnet_WirelessValueData)},success:regetWirelessNetconstatusValue};
	request.addRequest([setWirelessvalue]);
}

function regetWirelessNetconstatusValue(d,r){
	request.addRequest([rqWirelessList]);
    // 默认路由为是时，重新刷新其他网络参数配置
    if (vmodel.northnet_WirelessValueData[0]["Default Router"]) {
        request.addRequest([rqWiredList]);
        request.addRequest([rqVPNList]);
    }
    if(d.result ==="ok"){
        mainvalue.controlsuccess = "success";
    }
    else{
        mainvalue.controlsuccess = "failure";
    }
}


function setWiredData(){
    let ip1 = vmodel.northnet_WiredValueData[0]["IP Address"];
    let ip2 = vmodel.northnet_WiredValueData[0]["Subnet Mask"];
    let ip3 = vmodel.northnet_WiredValueData[0]["Gateway"];
    if(!checkIPAddress(0,ip1) || !checkIPAddress(0,ip3)) {
        alert (mainvalue.i18nkeyword.northnet.ip_illegal_tip);
        request.addRequest([rqWiredList]);
        return ;
    }
    if(!checkIPAddress(1,ip2)) {
        alert (mainvalue.i18nkeyword.northnet.netmask_illegal_tip);
        request.addRequest([rqWiredList]);
        return ;
    }
	var setWiredvalue = {data:{objectid:"plat.wiredconnection",type:"val_set",paranum:"1",paraval:JSON.stringify(vmodel.northnet_WiredValueData)},success:regetNetconstatusValue};
	request.addRequest([setWiredvalue]);
}

function regetNetconstatusValue(d,r){
	request.addRequest([rqWiredList]);
    // 默认路由为是时，重新刷新其他网络参数配置
    if (vmodel.northnet_WiredValueData[0]["Default Router"]) {
        request.addRequest([rqWirelessList]);
        request.addRequest([rqVPNList]);
    }
    if(d.result ==="ok"){
        mainvalue.controlsuccess = "success";
    }
    else{
        mainvalue.controlsuccess = "failure";
    }
}


northnet_info = {
    //IP V6参数管理
    wiredv6:{
        HEAD:function(paraval,paranum) {
                var req = {data:{objectid:"plat.wiredconnectionv6",type:"attr_get",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:get_wiredconnectionv6_head_succ};
                request.addRequest([req]);

                //回调函数
                function get_wiredconnectionv6_head_succ(d,r) {
                    addvalue(vmodel.northnet_Wiredv6AttrData,d.data,"name");
                }
        },
        GET:function(paraval,paranum) {
                var req = {data:{objectid:"plat.wiredconnectionv6",type:"val_get",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:get_wiredconnectionv6_succ};
                request.addRequest([req]);

                //回调函数
                function get_wiredconnectionv6_succ(d,r) {
                    addvalue(vmodel.northnet_Wiredv6ValueData,d.data,"instid");
                }
        },
        PUT:function(paraval,paranum) {
                var self = this;
                var req = {data:{objectid:"plat.wiredconnectionv6",type:"val_set",paranum:paranum.toString(),paraval:JSON.stringify(paraval)},success:set_wiredconnectionv6_succ};
                request.addRequest([req]);

                //回调函数
                function set_wiredconnectionv6_succ(d,r) {
                    if(d.result ==="ok"){
                        mainvalue.controlsuccess = "success";
                    }
                    else{
                        alert (mainvalue.i18nkeyword.northnet.ipv6_illegal_tip);
                        mainvalue.controlsuccess = "failure";
                    }
                    self.GET([{"instid":""}],1);
                }
        }
    }
};


northnet_info.wiredv6.HEAD([],0);
northnet_info.wiredv6.GET([{"instid":""}],1);

function setWiredv6Data(obj) {
    // 后续优化加入对IPv6的前端校验
    // let ip = vmodel.northnet_Wiredv6ValueData[0]["IPv6 Address"];
    // if(ip.length != 0 && !checkIPAddress(0,ip)) {
    //     alert (mainvalue.i18nkeyword.northnet.ipv6_illegal_tip);
    //     northnet_info.wiredv6.GET([{"instid":""}],1);
    //     return ;
    // }
    northnet_info.wiredv6.PUT(vmodel.northnet_Wiredv6ValueData, 1);
}

var rqRouteSettings = {data:{objectid:"plat.routesettings",type:"val_get",paranum:"1",paraval:JSON.stringify([])},success:getRouteSettings};
function getRouteSettings(d, r) {
    let item, l = 1, j = 1, k = 1;
    vmodel.northnet_WiredRouteData.clear();
    vmodel.northnet_WirelessRouteData.clear();
    vmodel.northnet_VPNRouteData.clear();
    for (let i = 0; i < d.datanum; i++) {
        item = d.data[i];
        if (item['Connection ID'] === ("plat.wiredconnection")) {
            vmodel.northnet_WiredRouteData.push(item);
        } else if (item['Connection ID'] === ("plat.wirelessconnection")) {
            vmodel.northnet_WirelessRouteData.push(item);
        } else if (item['Connection ID'] === ("plat.VPNconnection")) {
            vmodel.northnet_VPNRouteData.push(item);
        }
        else
            continue;
    }
}
request.addRequest([rqRouteSettings]);

function setRouteSucc(d,r) {
    if (d.result == 'ok') {
        mainvalue.controlsuccess = "success";
    } else {
        mainvalue.controlsuccess = "failure";
    }
    request.addRequest([rqRouteSettings]);
}

function set_wiredroute_value(obj) {
    let para = vmodel.northnet_WiredRouteData;
    if(!checkRouteValue(para)) {
        request.addRequest([rqRouteSettings]);
        return ;
    }
    var set_wiredroute_val_Rq = {data:{objectid:"plat.routesettings",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:setRouteSucc};
	request.addRequest([set_wiredroute_val_Rq]);
}

function set_wirelessroute_value(obj) {
    let para = vmodel.northnet_WirelessRouteData;
    if(!checkRouteValue(para)) {
        request.addRequest([rqRouteSettings]);
        return ;
    }
    var set_wirelessroute_val_Rq = {data:{objectid:"plat.routesettings",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:setRouteSucc};
	request.addRequest([set_wirelessroute_val_Rq]);
}

function set_VPNroute_value(obj) {
    let para = vmodel.northnet_VPNRouteData;
    if(!checkRouteValue(para)) {
        request.addRequest([rqRouteSettings]);
        return ;
    }
    var set_VPNroute_val_Rq = {data:{objectid:"plat.routesettings",type:"val_set",paranum:"1",paraval:JSON.stringify(para)},success:setRouteSucc};
	request.addRequest([set_VPNroute_val_Rq]);
}

function init_tab_select() {
    var tab = Cookies.get("tab_list");
    var tab2 = Cookies.get("tab_list2");
	if (tab == "wired" || tab == "wireless" || tab == "vpn") {
		vmodel.tab_list = tab;
	} else {
		vmodel.tab_list = "wired";
		set_cookie_with_path("tab_list", vmodel.tab_list);
    }
    if (tab2 == "ipv4_data" || tab2 == "ipv6_data") {
		vmodel.tab_list2 = tab2;
	} else {
		vmodel.tab_list2 = "ipv4_data";
		set_cookie_with_path("tab_list2", vmodel.tab_list2);
	}
}
init_tab_select();
